"""
简化回测引擎

专注于核心回测功能的轻量级回测引擎。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from .result import BacktestResult



class BacktestEngine:
    """简化回测引擎"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化回测引擎
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.backtest_config = self.config.get('backtest', {})
        self.performance_config = self.config.get('performance', {})
        
        # 回测参数
        self.initial_capital = self.backtest_config.get('initial_capital', 100000.0)
        self.commission = self.backtest_config.get('commission', 0.001)
        self.slippage = self.backtest_config.get('slippage', 0.001)
        
        # 性能参数
        self.risk_free_rate = self.performance_config.get('risk_free_rate', 0.0)
        self.trading_days_per_year = self.performance_config.get('trading_days_per_year', 252)
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        # 简化版本：直接返回默认配置
        return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'backtest': {
                'initial_capital': 100000.0,
                'commission': 0.001,
                'slippage': 0.001
            },
            'performance': {
                'risk_free_rate': 0.0,
                'trading_days_per_year': 252
            }
        }
    
    def run(self, data: pd.DataFrame, signals: pd.Series, 
            strategy_name: str = "Strategy") -> BacktestResult:
        """
        运行回测
        
        Args:
            data: 价格数据
            signals: 交易信号 (1: 买入, -1: 卖出, 0: 持有)
            strategy_name: 策略名称
            
        Returns:
            回测结果
        """
        print(f"开始回测策略: {strategy_name}")
        print(f"数据长度: {len(data)}, 信号数量: {len(signals)}")
        
        # 对齐数据和信号
        aligned_data = pd.DataFrame({
            'close': data['close'],
            'signal': signals
        }).dropna()
        
        if len(aligned_data) == 0:
            raise ValueError("数据和信号对齐后为空")
        
        # 执行回测
        equity_curve, trades = self._simulate_trading(aligned_data)
        
        # 计算性能指标
        result = self._calculate_performance(
            equity_curve, trades, strategy_name, aligned_data
        )
        
        print(f"回测完成: 总收益率 {result.total_return:.2%}, "
              f"夏普比率 {result.sharpe_ratio:.2f}, "
              f"交易次数 {result.total_trades}")
        
        return result
    
    def _simulate_trading(self, data: pd.DataFrame) -> tuple:
        """
        模拟交易过程
        
        Args:
            data: 包含价格和信号的数据
            
        Returns:
            (资金曲线, 交易记录)
        """
        capital = self.initial_capital
        position = 0  # 当前持仓 (0: 空仓, 1: 满仓)
        equity_curve = []
        trades = []
        
        for i, (date, row) in enumerate(data.iterrows()):
            price = row['close']
            signal = row['signal']
            
            # 处理交易信号
            if signal == 1 and position == 0:
                # 买入信号且当前空仓
                shares = capital / (price * (1 + self.slippage))  # 考虑滑点
                commission_cost = max(capital * self.commission, 5)  # 最低5元手续费
                capital -= commission_cost
                position = 1
                
                trades.append({
                    'date': date,
                    'action': 'BUY',
                    'price': price * (1 + self.slippage),
                    'shares': shares,
                    'commission': commission_cost,
                    'capital_before': capital + commission_cost,
                    'capital_after': capital
                })
                
            elif signal == -1 and position == 1:
                # 卖出信号且当前持仓
                sell_price = price * (1 - self.slippage)  # 考虑滑点
                capital = shares * sell_price
                commission_cost = max(capital * self.commission, 5)
                capital -= commission_cost
                position = 0
                
                trades.append({
                    'date': date,
                    'action': 'SELL',
                    'price': sell_price,
                    'shares': shares,
                    'commission': commission_cost,
                    'capital_before': shares * sell_price,
                    'capital_after': capital
                })
            
            # 计算当前资产价值
            if position == 1:
                current_value = shares * price
            else:
                current_value = capital
            
            equity_curve.append(current_value)
        
        # 如果最后还有持仓，按最后价格平仓
        if position == 1:
            final_price = data['close'].iloc[-1]
            capital = shares * final_price * (1 - self.slippage)
            commission_cost = max(capital * self.commission, 5)
            capital -= commission_cost
            
            trades.append({
                'date': data.index[-1],
                'action': 'SELL',
                'price': final_price * (1 - self.slippage),
                'shares': shares,
                'commission': commission_cost,
                'capital_before': shares * final_price,
                'capital_after': capital
            })
            
            equity_curve[-1] = capital
        
        equity_series = pd.Series(equity_curve, index=data.index)
        return equity_series, trades
    
    def _calculate_performance(self, equity_curve: pd.Series, trades: List[Dict[str, Any]],
                             strategy_name: str, data: pd.DataFrame) -> BacktestResult:
        """
        计算性能指标
        
        Args:
            equity_curve: 资金曲线
            trades: 交易记录
            strategy_name: 策略名称
            data: 原始数据
            
        Returns:
            回测结果
        """
        # 基本指标
        initial_capital = self.initial_capital
        final_capital = equity_curve.iloc[-1]
        total_return = (final_capital - initial_capital) / initial_capital
        
        # 日收益率
        daily_returns = equity_curve.pct_change().dropna()
        
        # 年化收益率
        if len(daily_returns) > 0:
            total_days = len(daily_returns)
            years = total_days / self.trading_days_per_year
            annualized_return = (final_capital / initial_capital) ** (1/years) - 1 if years > 0 else 0
        else:
            annualized_return = 0
        
        # 波动率
        volatility = daily_returns.std() * np.sqrt(self.trading_days_per_year) if len(daily_returns) > 1 else 0
        
        # 夏普比率
        if volatility > 0:
            excess_return = annualized_return - self.risk_free_rate
            sharpe_ratio = excess_return / volatility
        else:
            sharpe_ratio = 0
        
        # 最大回撤
        max_drawdown = self._calculate_max_drawdown(equity_curve)
        
        # 交易统计
        total_trades = len(trades)
        win_rate, profit_factor = self._calculate_trade_stats(trades)
        
        return BacktestResult(
            strategy_name=strategy_name,
            initial_capital=initial_capital,
            final_capital=final_capital,
            total_return=total_return,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            total_trades=total_trades,
            win_rate=win_rate,
            profit_factor=profit_factor,
            equity_curve=equity_curve,
            trades=trades,
            daily_returns=daily_returns
        )
    
    def _calculate_max_drawdown(self, equity_curve: pd.Series) -> float:
        """计算最大回撤"""
        if len(equity_curve) == 0:
            return 0.0
        
        peak = equity_curve.expanding().max()
        drawdown = (equity_curve - peak) / peak
        return drawdown.min()
    
    def _calculate_trade_stats(self, trades: List[Dict[str, Any]]) -> tuple:
        """
        计算交易统计
        
        Returns:
            (胜率, 盈利因子)
        """
        if len(trades) < 2:
            return 0.0, 0.0
        
        # 配对买卖交易
        profits = []
        i = 0
        while i < len(trades) - 1:
            if trades[i]['action'] == 'BUY' and trades[i+1]['action'] == 'SELL':
                buy_cost = trades[i]['shares'] * trades[i]['price'] + trades[i]['commission']
                sell_revenue = trades[i+1]['capital_after']
                profit = sell_revenue - buy_cost
                profits.append(profit)
                i += 2
            else:
                i += 1
        
        if not profits:
            return 0.0, 0.0
        
        # 胜率
        winning_trades = [p for p in profits if p > 0]
        losing_trades = [p for p in profits if p < 0]
        
        win_rate = len(winning_trades) / len(profits) if profits else 0
        
        # 盈利因子
        total_profit = sum(winning_trades) if winning_trades else 0
        total_loss = abs(sum(losing_trades)) if losing_trades else 0
        profit_factor = total_profit / total_loss if total_loss > 0 else float('inf') if total_profit > 0 else 0
        
        return win_rate, profit_factor
    
    def compare_strategies(self, data: pd.DataFrame, 
                          strategies: Dict[str, pd.Series]) -> pd.DataFrame:
        """
        比较多个策略
        
        Args:
            data: 价格数据
            strategies: 策略名称到信号的映射
            
        Returns:
            策略比较表
        """
        results = []
        
        for name, signals in strategies.items():
            try:
                result = self.run(data, signals, name)
                results.append({
                    'Strategy': name,
                    'Total Return': result.total_return,
                    'Annualized Return': result.annualized_return,
                    'Volatility': result.volatility,
                    'Sharpe Ratio': result.sharpe_ratio,
                    'Max Drawdown': result.max_drawdown,
                    'Total Trades': result.total_trades,
                    'Win Rate': result.win_rate,
                    'Profit Factor': result.profit_factor
                })
            except Exception as e:
                print(f"策略 {name} 回测失败: {e}")
                continue
        
        comparison_df = pd.DataFrame(results)
        if not comparison_df.empty:
            comparison_df.set_index('Strategy', inplace=True)
        
        return comparison_df


if __name__ == "__main__":
    # 测试回测引擎
    from data_generator import RandomDataGenerator
    from ml_strategy import DualMAStrategy
    
    # 生成测试数据
    generator = RandomDataGenerator()
    data = generator.generate(days=300)
    
    # 生成简单的双均线信号
    strategy = DualMAStrategy()
    signals = strategy.generate_signals(data, short_window=20, long_window=50)
    
    # 运行回测
    engine = BacktestEngine()
    result = engine.run(data, signals, "DualMA_20_50")
    
    print(f"\n回测结果:")
    print(f"策略: {result.strategy_name}")
    print(f"总收益率: {result.total_return:.2%}")
    print(f"年化收益率: {result.annualized_return:.2%}")
    print(f"夏普比率: {result.sharpe_ratio:.2f}")
    print(f"最大回撤: {result.max_drawdown:.2%}")
    print(f"交易次数: {result.total_trades}")
    print(f"胜率: {result.win_rate:.2%}")
    print(f"盈利因子: {result.profit_factor:.2f}")
