"""
基于LSTM神经网络的交易策略

LSTM（长短期记忆网络）是一种特殊的循环神经网络，专门设计用来处理时间序列数据。
该策略利用LSTM的时间序列建模能力来捕捉市场的长期依赖关系和复杂模式。

主要特点：
- 专门处理时间序列数据
- 能够捕捉长期依赖关系
- 对序列模式敏感
- 支持多变量输入
- 需要较多的训练数据
- 训练时间相对较长
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Any, Optional, List
import warnings

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    from sklearn.preprocessing import MinMaxScaler
    from sklearn.metrics import accuracy_score
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    warnings.warn("tensorflow not available. LSTMStrategy will not work.")

from ..base import BaseStrategy


class LSTMStrategy(BaseStrategy):
    """
    基于LSTM神经网络的交易策略
    
    使用LSTM网络来学习时间序列中的长期依赖关系，
    预测未来价格走势并生成交易信号。
    """
    
    def __init__(self, name: str = "LSTMStrategy"):
        """
        初始化LSTM策略
        
        Args:
            name: 策略名称
        """
        super().__init__(name)
        self.model = None
        self.scaler = MinMaxScaler()
        self.feature_columns = []
        self.model_trained = False
        self.sequence_length = 60  # 默认序列长度
        self.training_history = None
        
        if not TENSORFLOW_AVAILABLE:
            raise ImportError("tensorflow is required for LSTMStrategy")
        
        # 设置TensorFlow日志级别
        tf.get_logger().setLevel('ERROR')
    
    def prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        准备LSTM特征（时间序列特征）
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            包含特征的DataFrame
        """
        df = data.copy()
        
        # 价格特征
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        df['high_low_ratio'] = df['high'] / df['low']
        df['volume_change'] = df['volume'].pct_change()
        
        # 标准化价格特征
        df['close_norm'] = df['close'] / df['close'].rolling(100).mean()
        df['volume_norm'] = df['volume'] / df['volume'].rolling(100).mean()
        
        # 移动平均线特征
        for window in [5, 10, 20, 50]:
            df[f'sma_{window}'] = df['close'].rolling(window).mean()
            df[f'price_sma_{window}_ratio'] = df['close'] / df[f'sma_{window}']
        
        # RSI
        delta = df['close'].diff()
        up = delta.clip(lower=0)
        down = -1 * delta.clip(upper=0)
        ema_up = up.ewm(com=13, adjust=False).mean()
        ema_down = down.ewm(com=13, adjust=False).mean()
        rs = ema_up / ema_down
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema_12 - ema_26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        
        # 布林带
        bb_middle = df['close'].rolling(20).mean()
        bb_std = df['close'].rolling(20).std()
        df['bb_upper'] = bb_middle + (bb_std * 2)
        df['bb_lower'] = bb_middle - (bb_std * 2)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # 波动率
        df['volatility'] = df['returns'].rolling(20).std()
        
        # 选择LSTM输入特征
        self.feature_columns = [
            'close_norm', 'volume_norm', 'returns', 'high_low_ratio',
            'price_sma_5_ratio', 'price_sma_10_ratio', 'price_sma_20_ratio',
            'rsi', 'macd', 'bb_position', 'volatility'
        ]
        
        return df
    
    def create_sequences(self, data: np.ndarray, labels: np.ndarray, 
                        sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        创建LSTM训练序列
        
        Args:
            data: 特征数据
            labels: 标签数据
            sequence_length: 序列长度
            
        Returns:
            序列化的特征和标签
        """
        X, y = [], []
        
        for i in range(sequence_length, len(data)):
            X.append(data[i-sequence_length:i])
            y.append(labels[i])
        
        return np.array(X), np.array(y)
    
    def create_labels(self, data: pd.DataFrame, forward_periods: int = 1) -> pd.Series:
        """
        创建预测标签
        
        Args:
            data: 价格数据
            forward_periods: 前瞻期数
            
        Returns:
            标签序列 (1: 上涨, 0: 下跌)
        """
        future_returns = data['close'].shift(-forward_periods) / data['close'] - 1
        labels = (future_returns > 0).astype(int)
        return labels
    
    def build_model(self, input_shape: Tuple[int, int], **params) -> tf.keras.Model:
        """
        构建LSTM模型
        
        Args:
            input_shape: 输入形状 (sequence_length, n_features)
            **params: 模型参数
            
        Returns:
            编译后的Keras模型
        """
        model = Sequential()
        
        # 第一层LSTM
        model.add(LSTM(
            units=params.get('lstm_units_1', 50),
            return_sequences=True,
            input_shape=input_shape
        ))
        model.add(Dropout(params.get('dropout_1', 0.2)))
        model.add(BatchNormalization())
        
        # 第二层LSTM
        model.add(LSTM(
            units=params.get('lstm_units_2', 50),
            return_sequences=True
        ))
        model.add(Dropout(params.get('dropout_2', 0.2)))
        model.add(BatchNormalization())
        
        # 第三层LSTM
        model.add(LSTM(
            units=params.get('lstm_units_3', 50),
            return_sequences=False
        ))
        model.add(Dropout(params.get('dropout_3', 0.2)))
        model.add(BatchNormalization())
        
        # 全连接层
        model.add(Dense(units=params.get('dense_units', 25), activation='relu'))
        model.add(Dropout(params.get('dropout_dense', 0.2)))
        
        # 输出层
        model.add(Dense(units=1, activation='sigmoid'))
        
        # 编译模型
        model.compile(
            optimizer=Adam(learning_rate=params.get('learning_rate', 0.001)),
            loss='binary_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def train_model(self, data: pd.DataFrame, **params) -> None:
        """
        训练LSTM模型
        
        Args:
            data: 训练数据
            **params: 模型参数
        """
        # 准备特征
        features_df = self.prepare_features(data)
        
        # 创建标签
        labels = self.create_labels(data, params.get('forward_periods', 1))
        
        # 提取特征数据
        feature_data = features_df[self.feature_columns].fillna(method='ffill').fillna(0)
        
        # 标准化特征
        scaled_features = self.scaler.fit_transform(feature_data)
        
        # 设置序列长度
        self.sequence_length = params.get('sequence_length', 60)
        
        # 创建序列
        X, y = self.create_sequences(
            scaled_features, 
            labels.values, 
            self.sequence_length
        )
        
        if len(X) == 0:
            raise ValueError("No valid sequences created. Check sequence_length parameter.")
        
        # 分割训练和验证集
        split_idx = int(len(X) * 0.8)
        X_train, X_val = X[:split_idx], X[split_idx:]
        y_train, y_val = y[:split_idx], y[split_idx:]
        
        print(f"训练集大小: {X_train.shape}")
        print(f"验证集大小: {X_val.shape}")
        
        # 构建模型
        input_shape = (self.sequence_length, len(self.feature_columns))
        self.model = self.build_model(input_shape, **params)
        
        # 设置回调函数
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=params.get('patience', 10),
                restore_best_weights=True
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7
            )
        ]
        
        # 训练模型
        history = self.model.fit(
            X_train, y_train,
            batch_size=params.get('batch_size', 32),
            epochs=params.get('epochs', 100),
            validation_data=(X_val, y_val),
            callbacks=callbacks,
            verbose=0
        )
        
        self.training_history = history.history
        
        # 验证模型
        y_pred_proba = self.model.predict(X_val)
        y_pred = (y_pred_proba > 0.5).astype(int).flatten()
        accuracy = accuracy_score(y_val, y_pred)
        
        self.model_trained = True
        
        print(f"LSTM模型训练完成:")
        print(f"  验证集准确率: {accuracy:.4f}")
        print(f"  最终训练损失: {history.history['loss'][-1]:.4f}")
        print(f"  最终验证损失: {history.history['val_loss'][-1]:.4f}")
        print(f"  训练轮数: {len(history.history['loss'])}")
    
    def generate_signals(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        生成交易信号
        
        Args:
            data: 价格数据
            **params: 策略参数
            
        Returns:
            交易信号序列 (1: 买入, -1: 卖出, 0: 持有)
        """
        if not self.model_trained:
            # 如果模型未训练，先训练模型
            self.train_model(data, **params)
        
        # 准备特征
        features_df = self.prepare_features(data)
        
        # 提取特征数据
        feature_data = features_df[self.feature_columns].fillna(method='ffill').fillna(0)
        
        # 标准化特征
        scaled_features = self.scaler.transform(feature_data)
        
        # 创建序列用于预测
        predictions = []
        
        for i in range(self.sequence_length, len(scaled_features)):
            sequence = scaled_features[i-self.sequence_length:i].reshape(1, self.sequence_length, -1)
            pred = self.model.predict(sequence, verbose=0)[0][0]
            predictions.append(pred)
        
        # 创建信号序列
        signals = pd.Series(0, index=data.index)
        
        if len(predictions) > 0:
            # 设置信号阈值
            buy_threshold = params.get('buy_threshold', 0.6)
            sell_threshold = params.get('sell_threshold', 0.4)
            
            # 将预测结果对齐到正确的索引
            pred_index = data.index[self.sequence_length:]
            pred_series = pd.Series(predictions, index=pred_index)
            
            # 基于概率生成信号
            signals[pred_series > buy_threshold] = 1   # 买入
            signals[pred_series < sell_threshold] = -1  # 卖出
        
        return signals
    
    def get_param_ranges(self) -> Dict[str, Tuple[float, float]]:
        """
        获取参数搜索范围
        
        Returns:
            参数名称到范围的映射
        """
        return {
            'sequence_length': (30, 120),
            'lstm_units_1': (25, 100),
            'lstm_units_2': (25, 100),
            'lstm_units_3': (25, 100),
            'dense_units': (10, 50),
            'dropout_1': (0.1, 0.5),
            'dropout_2': (0.1, 0.5),
            'dropout_3': (0.1, 0.5),
            'learning_rate': (0.0001, 0.01),
            'batch_size': (16, 64),
            'buy_threshold': (0.55, 0.8),
            'sell_threshold': (0.2, 0.45),
            'forward_periods': (1, 5)
        }
    
    def get_training_history(self) -> Optional[Dict[str, List]]:
        """
        获取训练历史
        
        Returns:
            训练历史字典
        """
        return self.training_history
    
    def save_model(self, filepath: str) -> None:
        """
        保存模型
        
        Args:
            filepath: 保存路径
        """
        if self.model_trained and self.model is not None:
            self.model.save(filepath)
            print(f"模型已保存到: {filepath}")
        else:
            print("模型未训练，无法保存")
    
    def load_model(self, filepath: str) -> None:
        """
        加载模型
        
        Args:
            filepath: 模型路径
        """
        try:
            self.model = tf.keras.models.load_model(filepath)
            self.model_trained = True
            print(f"模型已从 {filepath} 加载")
        except Exception as e:
            print(f"加载模型失败: {e}")
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns:
            策略信息字典
        """
        info = super().get_strategy_info()
        info.update({
            'algorithm': 'LSTM Neural Network',
            'model_trained': self.model_trained,
            'n_features': len(self.feature_columns) if self.feature_columns else 0,
            'sequence_length': self.sequence_length,
            'deep_learning': True,
            'time_series_specialized': True,
            'supports_sequences': True
        })
        
        if self.model_trained and self.model is not None:
            info.update({
                'model_parameters': self.model.count_params(),
                'model_layers': len(self.model.layers)
            })
        
        return info
