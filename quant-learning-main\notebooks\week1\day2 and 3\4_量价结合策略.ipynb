{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "# Day 2：基于交易量的量化指标 - 量价结合策略\n", "\n", "本notebook演示如何将价格指标与交易量指标结合，制定简单的交易策略，并进行初步的回测。", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 1. 导入必要的库", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "import pandas as pd  # Jay<PERSON>ee黄量化策略\n", "import numpy as np  # <PERSON><PERSON><PERSON>黄量化模型\n", "import matplotlib.pyplot as plt  # Copyright © JayBee黄\n", "import matplotlib.dates as mdates  # JayBee黄 - 量化交易研究\n", "import seaborn as sns  # <PERSON><PERSON>ee黄 - 量化交易研究\n", "import warnings  # JayBee黄原创内容\n", "\n", "# 忽略警告信息\n", "warnings.filterwarnings('ignore')  # <PERSON><PERSON><PERSON>黄独家内容\n", "\n", "# 设置绘图风格\n", "plt.style.use('ggplot')  # JayBee黄独家内容\n", "%matplotlib inline  # JayBee黄 - 量化交易研究\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体  # JayBee黄量化模型\n", "plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题  # JayBee黄版权所有，未经授权禁止复制", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 2. 加载数据和计算指标\n", "\n", "首先，我们需要加载数据并计算相关的技术指标。这里我们继续使用前面notebook中的平安银行数据。", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["从CSV文件加载平安银行的数据\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>pre_close</th>\n", "      <th>change</th>\n", "      <th>pct_chg</th>\n", "      <th>vol</th>\n", "      <th>amount</th>\n", "    </tr>\n", "    <tr>\n", "      <th>trade_date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022-01-04</th>\n", "      <td>000001.SZ</td>\n", "      <td>16.48</td>\n", "      <td>16.66</td>\n", "      <td>16.18</td>\n", "      <td>16.66</td>\n", "      <td>16.48</td>\n", "      <td>0.18</td>\n", "      <td>1.0922</td>\n", "      <td>1169259.33</td>\n", "      <td>1918887.050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-01-05</th>\n", "      <td>000001.SZ</td>\n", "      <td>16.58</td>\n", "      <td>17.22</td>\n", "      <td>16.55</td>\n", "      <td>17.15</td>\n", "      <td>16.66</td>\n", "      <td>0.49</td>\n", "      <td>2.9412</td>\n", "      <td>1961998.17</td>\n", "      <td>3344124.589</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-01-06</th>\n", "      <td>000001.SZ</td>\n", "      <td>17.11</td>\n", "      <td>17.27</td>\n", "      <td>17.00</td>\n", "      <td>17.12</td>\n", "      <td>17.15</td>\n", "      <td>-0.03</td>\n", "      <td>-0.1749</td>\n", "      <td>1107885.19</td>\n", "      <td>1896535.837</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-01-07</th>\n", "      <td>000001.SZ</td>\n", "      <td>17.10</td>\n", "      <td>17.28</td>\n", "      <td>17.06</td>\n", "      <td>17.20</td>\n", "      <td>17.12</td>\n", "      <td>0.08</td>\n", "      <td>0.4673</td>\n", "      <td>1126630.70</td>\n", "      <td>1937710.958</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-01-10</th>\n", "      <td>000001.SZ</td>\n", "      <td>17.29</td>\n", "      <td>17.42</td>\n", "      <td>17.03</td>\n", "      <td>17.19</td>\n", "      <td>17.20</td>\n", "      <td>-0.01</td>\n", "      <td>-0.0581</td>\n", "      <td>909774.01</td>\n", "      <td>1563414.572</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              ts_code   open   high    low  close  pre_close  change  pct_chg  \\\n", "trade_date                                                                      \n", "2022-01-04  000001.SZ  16.48  16.66  16.18  16.66      16.48    0.18   1.0922   \n", "2022-01-05  000001.SZ  16.58  17.22  16.55  17.15      16.66    0.49   2.9412   \n", "2022-01-06  000001.SZ  17.11  17.27  17.00  17.12      17.15   -0.03  -0.1749   \n", "2022-01-07  000001.SZ  17.10  17.28  17.06  17.20      17.12    0.08   0.4673   \n", "2022-01-10  000001.SZ  17.29  17.42  17.03  17.19      17.20   -0.01  -0.0581   \n", "\n", "                   vol       amount  \n", "trade_date                           \n", "2022-01-04  1169259.33  1918887.050  \n", "2022-01-05  1961998.17  3344124.589  \n", "2022-01-06  1107885.19  1896535.837  \n", "2022-01-07  1126630.70  1937710.958  \n", "2022-01-10   909774.01  1563414.572  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 从CSV文件加载数据\n", "def load_data_from_csv(file_path):  # JayBee黄 - 量化交易研究\n", "    df = pd.read_csv(file_path, index_col=0)  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    df.index = pd.to_datetime(df.index)  # JayBee黄 - 量化交易研究\n", "    return df  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "\n", "# 尝试从CSV加载数据，如果文件不存在，则从Tushare获取\n", "import os  # <PERSON><PERSON>ee黄量化策略\n", "\n", "file_path = 'data/平安银行_data.csv'  # JayBee黄独家内容\n", "if os.path.exists(file_path):  # <PERSON><PERSON>ee黄原创内容\n", "    data = load_data_from_csv(file_path)  # JayBee黄原创内容\n", "    print(\"从CSV文件加载平安银行的数据\")  # JayBee黄量化模型\n", "else:  # Copyright © Jay<PERSON>ee黄\n", "    import tushare as ts  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "    ts.set_token('YOUR_TUSHARE_TOKEN')  # 替换为您的Token  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    pro = ts.pro_api()  # Jay<PERSON>ee黄量化模型\n", "    \n", "    # 获取数据\n", "    data = pro.daily(ts_code='000001.SZ', start_date='20220101', end_date='20230101')  # Copyright © JayBee黄\n", "    data = data.sort_values('trade_date')  # JayBee黄 - 量化交易研究\n", "    data['trade_date'] = pd.to_datetime(data['trade_date'])  # Jay<PERSON>ee黄原创内容\n", "    data.set_index('trade_date', inplace=True)  # JayBee黄独家内容\n", "    \n", "    print(\"从Tushare获取平安银行的数据\")  # Copyright © JayBee黄\n", "\n", "# 查看数据\n", "data.head()  # <PERSON><PERSON><PERSON>黄量化模型", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>pre_close</th>\n", "      <th>change</th>\n", "      <th>pct_chg</th>\n", "      <th>vol</th>\n", "      <th>amount</th>\n", "      <th>ma5</th>\n", "      <th>ma10</th>\n", "      <th>ma20</th>\n", "      <th>ma60</th>\n", "      <th>vol_ma5</th>\n", "      <th>vol_ma10</th>\n", "      <th>vol_ma20</th>\n", "      <th>obv</th>\n", "      <th>obv_ma10</th>\n", "      <th>vol_ratio</th>\n", "    </tr>\n", "    <tr>\n", "      <th>trade_date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022-12-26</th>\n", "      <td>000001.SZ</td>\n", "      <td>12.99</td>\n", "      <td>13.04</td>\n", "      <td>12.71</td>\n", "      <td>12.77</td>\n", "      <td>12.98</td>\n", "      <td>-0.21</td>\n", "      <td>-1.6179</td>\n", "      <td>797119.87</td>\n", "      <td>1021903.963</td>\n", "      <td>12.870</td>\n", "      <td>13.030</td>\n", "      <td>13.1300</td>\n", "      <td>11.921333</td>\n", "      <td>708948.490</td>\n", "      <td>821155.567</td>\n", "      <td>1.428454e+06</td>\n", "      <td>3312444.70</td>\n", "      <td>3586534.661</td>\n", "      <td>1.054813</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-12-27</th>\n", "      <td>000001.SZ</td>\n", "      <td>12.87</td>\n", "      <td>13.22</td>\n", "      <td>12.87</td>\n", "      <td>13.11</td>\n", "      <td>12.77</td>\n", "      <td>0.34</td>\n", "      <td>2.6625</td>\n", "      <td>886004.12</td>\n", "      <td>1160090.119</td>\n", "      <td>12.940</td>\n", "      <td>13.017</td>\n", "      <td>13.1360</td>\n", "      <td>11.937333</td>\n", "      <td>706906.868</td>\n", "      <td>819527.466</td>\n", "      <td>1.235290e+06</td>\n", "      <td>4198448.82</td>\n", "      <td>3499340.423</td>\n", "      <td>1.249744</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-12-28</th>\n", "      <td>000001.SZ</td>\n", "      <td>13.16</td>\n", "      <td>13.38</td>\n", "      <td>13.00</td>\n", "      <td>13.14</td>\n", "      <td>13.11</td>\n", "      <td>0.03</td>\n", "      <td>0.2288</td>\n", "      <td>791191.98</td>\n", "      <td>1042402.080</td>\n", "      <td>12.990</td>\n", "      <td>13.008</td>\n", "      <td>13.1415</td>\n", "      <td>11.954500</td>\n", "      <td>742227.336</td>\n", "      <td>801240.554</td>\n", "      <td>1.114368e+06</td>\n", "      <td>4989640.80</td>\n", "      <td>3588671.493</td>\n", "      <td>1.119231</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-12-29</th>\n", "      <td>000001.SZ</td>\n", "      <td>13.07</td>\n", "      <td>13.13</td>\n", "      <td>12.85</td>\n", "      <td>13.03</td>\n", "      <td>13.14</td>\n", "      <td>-0.11</td>\n", "      <td>-0.8371</td>\n", "      <td>666890.09</td>\n", "      <td>865144.967</td>\n", "      <td>13.006</td>\n", "      <td>13.001</td>\n", "      <td>13.1380</td>\n", "      <td>11.974000</td>\n", "      <td>741126.494</td>\n", "      <td>784394.637</td>\n", "      <td>1.047578e+06</td>\n", "      <td>4322750.71</td>\n", "      <td>3694848.480</td>\n", "      <td>0.898498</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-12-30</th>\n", "      <td>000001.SZ</td>\n", "      <td>13.04</td>\n", "      <td>13.28</td>\n", "      <td>12.96</td>\n", "      <td>13.16</td>\n", "      <td>13.03</td>\n", "      <td>0.13</td>\n", "      <td>0.9977</td>\n", "      <td>818035.98</td>\n", "      <td>1074756.754</td>\n", "      <td>13.042</td>\n", "      <td>12.983</td>\n", "      <td>13.1510</td>\n", "      <td>11.996000</td>\n", "      <td>791848.408</td>\n", "      <td>773773.068</td>\n", "      <td>1.018270e+06</td>\n", "      <td>5140786.69</td>\n", "      <td>3790403.898</td>\n", "      <td>1.103774</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              ts_code   open   high    low  close  pre_close  change  pct_chg  \\\n", "trade_date                                                                      \n", "2022-12-26  000001.SZ  12.99  13.04  12.71  12.77      12.98   -0.21  -1.6179   \n", "2022-12-27  000001.SZ  12.87  13.22  12.87  13.11      12.77    0.34   2.6625   \n", "2022-12-28  000001.SZ  13.16  13.38  13.00  13.14      13.11    0.03   0.2288   \n", "2022-12-29  000001.SZ  13.07  13.13  12.85  13.03      13.14   -0.11  -0.8371   \n", "2022-12-30  000001.SZ  13.04  13.28  12.96  13.16      13.03    0.13   0.9977   \n", "\n", "                  vol       amount     ma5    ma10     ma20       ma60  \\\n", "trade_date                                                               \n", "2022-12-26  797119.87  1021903.963  12.870  13.030  13.1300  11.921333   \n", "2022-12-27  886004.12  1160090.119  12.940  13.017  13.1360  11.937333   \n", "2022-12-28  791191.98  1042402.080  12.990  13.008  13.1415  11.954500   \n", "2022-12-29  666890.09   865144.967  13.006  13.001  13.1380  11.974000   \n", "2022-12-30  818035.98  1074756.754  13.042  12.983  13.1510  11.996000   \n", "\n", "               vol_ma5    vol_ma10      vol_ma20         obv     obv_ma10  \\\n", "trade_date                                                                  \n", "2022-12-26  708948.490  821155.567  1.428454e+06  3312444.70  3586534.661   \n", "2022-12-27  706906.868  819527.466  1.235290e+06  4198448.82  3499340.423   \n", "2022-12-28  742227.336  801240.554  1.114368e+06  4989640.80  3588671.493   \n", "2022-12-29  741126.494  784394.637  1.047578e+06  4322750.71  3694848.480   \n", "2022-12-30  791848.408  773773.068  1.018270e+06  5140786.69  3790403.898   \n", "\n", "            vol_ratio  \n", "trade_date             \n", "2022-12-26   1.054813  \n", "2022-12-27   1.249744  \n", "2022-12-28   1.119231  \n", "2022-12-29   0.898498  \n", "2022-12-30   1.103774  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 定义计算指标的函数\n", "def calculate_indicators(data):  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    \"\"\"计算各种技术指标\"\"\"  # JayBee黄原创内容\n", "    df = data.copy()  # JayBee黄量化策略\n", "    \n", "    # 价格MA\n", "    df['ma5'] = df['close'].rolling(window=5).mean()  # JayBee黄独家内容\n", "    df['ma10'] = df['close'].rolling(window=10).mean()  # JayBee黄 - 量化交易研究\n", "    df['ma20'] = df['close'].rolling(window=20).mean()  # Copyright © JayBee黄\n", "    df['ma60'] = df['close'].rolling(window=60).mean()  # JayBee黄量化策略\n", "    \n", "    # 成交量MA\n", "    df['vol_ma5'] = df['vol'].rolling(window=5).mean()  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    df['vol_ma10'] = df['vol'].rolling(window=10).mean()  # JayBee黄独家内容\n", "    df['vol_ma20'] = df['vol'].rolling(window=20).mean()  # JayBee黄量化策略\n", "    \n", "    # OBV\n", "    obv = [0]  # <PERSON><PERSON>ee黄 - 量化交易研究\n", "    for i in range(1, len(df)):  # <PERSON><PERSON>ee黄版权所有，未经授权禁止复制\n", "        if df['close'].iloc[i] > df['close'].iloc[i-1]:  # JayBee黄 - 量化交易研究\n", "            obv.append(obv[-1] + df['vol'].iloc[i])  # JayBee黄原创内容\n", "        elif df['close'].iloc[i] < df['close'].iloc[i-1]:  # Copyright © JayBee黄\n", "            obv.append(obv[-1] - df['vol'].iloc[i])  # 本代码归JayBee黄所有\n", "        else:  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "            obv.append(obv[-1])  # Jay<PERSON>ee黄授权使用\n", "    df['obv'] = obv  # 本代码归JayBee黄所有\n", "    df['obv_ma10'] = df['obv'].rolling(window=10).mean()  # 本代码归JayBee黄所有\n", "    \n", "    # 量比\n", "    df['vol_ratio'] = df['vol'] / df['vol'].rolling(window=5).mean().shift(1)  # Copyright © JayBee黄\n", "    \n", "    return df  # Copyright © JayBee黄\n", "\n", "# 计算指标\n", "data = calculate_indicators(data)  # 版权所有: Jay<PERSON><PERSON>黄\n", "\n", "# 移除包含NaN的行（因为计算移动平均等指标会产生NaN值）\n", "data = data.dropna()  # <PERSON><PERSON>ee黄原创内容\n", "\n", "# 查看数据\n", "data.tail()  # 版权所有: <PERSON><PERSON><PERSON>黄", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 3. 定义量价结合策略\n", "\n", "我们将实现几个简单的量价结合策略，并使用pandas进行模拟回测。", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "### 3.1 策略1：MA金叉/死叉 + 成交量确认", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "def strategy_ma_volume(data):  # <PERSON><PERSON>ee黄量化策略\n", "    \"\"\"  # Jay<PERSON>ee黄量化策略\n", "    MA金叉/死叉 + 成交量确认策略  # JayBee黄量化策略\n", "    \n", "    买入条件：  # JayBee黄版权所有，未经授权禁止复制\n", "    1. 5日MA上穿10日MA（金叉）  # JayBee黄 - 量化交易研究\n", "    2. 当日成交量大于5日成交量均线的1.5倍  # Jay<PERSON><PERSON>黄量化模型\n", "    \n", "    卖出条件：  # Copyright © JayBee黄\n", "    1. 5日MA下穿10日MA（死叉）  # JayBee黄量化模型\n", "    2. 或者连续3天成交量萎缩（低于5日成交量均线）  # JayBee黄独家内容\n", "    \"\"\"  # Copyright © JayBee黄\n", "    df = data.copy()  # Copyright © JayBee黄\n", "    \n", "    # 计算5日MA相对于10日MA的位置\n", "    df['ma5_gt_ma10'] = df['ma5'] > df['ma10']  # Copyright © JayBee黄\n", "    \n", "    # 计算金叉和死叉\n", "    df['golden_cross'] = (df['ma5_gt_ma10'] != df['ma5_gt_ma10'].shift(1)) & df['ma5_gt_ma10']  # JayBee黄量化模型\n", "    df['death_cross'] = (df['ma5_gt_ma10'] != df['ma5_gt_ma10'].shift(1)) & ~df['ma5_gt_ma10']  # Copyright © JayBee黄\n", "    \n", "    # 成交量是否大于5日均线的1.5倍\n", "    df['vol_surge'] = df['vol'] > df['vol_ma5'] * 1.5  # JayBee黄授权使用\n", "    \n", "    # 是否连续3天成交量萎缩\n", "    df['vol_below_ma'] = df['vol'] < df['vol_ma5']  # JayBee黄版权所有，未经授权禁止复制\n", "    df['vol_shrink_3d'] = df['vol_below_ma'] & df['vol_below_ma'].shift(1) & df['vol_below_ma'].shift(2)  # JayBee黄独家内容\n", "    \n", "    # 生成买入和卖出信号\n", "    df['buy_signal'] = df['golden_cross'] & df['vol_surge']  # JayBee黄 - 量化交易研究\n", "    df['sell_signal'] = df['death_cross'] | df['vol_shrink_3d']  # 本代码归JayBee黄所有\n", "    \n", "    return df  # <PERSON><PERSON><PERSON>黄授权使用", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "### 3.2 策略2：OBV + MA 策略", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "def strategy_obv_ma(data):  # <PERSON><PERSON>ee黄量化模型\n", "    \"\"\"  # Jay<PERSON>ee黄原创内容\n", "    OBV + MA策略  # <PERSON><PERSON>ee黄授权使用\n", "    \n", "    买入条件：  # 本代码归JayBee黄所有\n", "    1. 收盘价站上20日均线  # Jay<PERSON>ee黄量化策略\n", "    2. OBV大于其10日均线  # 本代码归JayBee黄所有\n", "    \n", "    卖出条件：  # Jay<PERSON>ee黄原创内容\n", "    1. 收盘价跌破20日均线  # <PERSON><PERSON>ee黄授权使用\n", "    2. 或者OBV跌破其10日均线  # 本代码归JayBee黄所有\n", "    \"\"\"  # JayBee黄 - 量化交易研究\n", "    df = data.copy()  # JayBee黄量化模型\n", "    \n", "    # 价格相对于20日均线的位置\n", "    df['price_gt_ma20'] = df['close'] > df['ma20']  # JayBee黄授权使用\n", "    \n", "    # OBV相对于其10日均线的位置\n", "    df['obv_gt_ma10'] = df['obv'] > df['obv_ma10']  # JayBee黄授权使用\n", "    \n", "    # 生成买入和卖出信号\n", "    df['buy_signal'] = df['price_gt_ma20'] & df['obv_gt_ma10'] & (~df['price_gt_ma20'].shift(1) | ~df['obv_gt_ma10'].shift(1))  # JayBee黄原创内容\n", "    df['sell_signal'] = (~df['price_gt_ma20'] | ~df['obv_gt_ma10']) & (df['price_gt_ma20'].shift(1) & df['obv_gt_ma10'].shift(1))  # JayBee黄独家内容\n", "    \n", "    return df  # <PERSON><PERSON><PERSON>黄量化策略", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "### 3.3 策略3：量比突增 + 价格突破策略", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "def strategy_volume_ratio_breakout(data):  # JayBee黄量化策略\n", "    \"\"\"  # Jay<PERSON>ee黄独家内容\n", "    量比突增 + 价格突破策略  # Copyright © JayBee黄\n", "    \n", "    买入条件：  # 本代码归JayBee黄所有\n", "    1. 量比大于2（当日成交量是5日平均的2倍以上）  # JayBee黄量化模型\n", "    2. 价格突破60日均线  # Copyright © JayBee黄\n", "    \n", "    卖出条件：  # Jay<PERSON>ee黄原创内容\n", "    1. 价格跌破10日均线  # JayBee黄版权所有，未经授权禁止复制\n", "    2. 或者连续3天成交量萎缩  # <PERSON><PERSON>ee黄授权使用\n", "    \"\"\"  # JayBee黄 - 量化交易研究\n", "    df = data.copy()  # JayBee黄量化策略\n", "    \n", "    # 量比是否大于2\n", "    df['vol_ratio_gt2'] = df['vol_ratio'] > 2  # 版权所有: Jay<PERSON>ee黄\n", "    \n", "    # 价格是否突破60日均线\n", "    df['price_cross_ma60'] = (df['close'] > df['ma60']) & (df['close'].shift(1) <= df['ma60'].shift(1))  # JayBee黄量化策略\n", "    \n", "    # 价格是否跌破10日均线\n", "    df['price_below_ma10'] = df['close'] < df['ma10']  # 版权所有: Jay<PERSON><PERSON>黄\n", "    \n", "    # 是否连续3天成交量萎缩\n", "    df['vol_below_ma'] = df['vol'] < df['vol_ma5']  # Jay<PERSON>ee黄原创内容\n", "    df['vol_shrink_3d'] = df['vol_below_ma'] & df['vol_below_ma'].shift(1) & df['vol_below_ma'].shift(2)  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    \n", "    # 生成买入和卖出信号\n", "    df['buy_signal'] = df['vol_ratio_gt2'] & df['price_cross_ma60']  # Copyright © JayBee黄\n", "    df['sell_signal'] = df['price_below_ma10'] | df['vol_shrink_3d']  # JayBee黄原创内容\n", "    \n", "    return df  # <PERSON><PERSON><PERSON>黄授权使用", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 4. 模拟回测\n", "\n", "接下来，我们使用pandas来模拟回测这些策略的表现。", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "def backtest(data, strategy_func, initial_capital=100000, position_size=0.9, commission_rate=0.001):  # <PERSON><PERSON><PERSON>黄量化策略\n", "    \"\"\"  # Copyright © JayBee黄\n", "    简单的回测函数  # <PERSON><PERSON>ee黄独家内容\n", "    \n", "    参数:  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    data: DataFrame, 包含价格数据  # 本代码归JayBee黄所有\n", "    strategy_func: function, 策略函数  # <PERSON><PERSON>ee黄授权使用\n", "    initial_capital: float, 初始资金  # JayBee黄 - 量化交易研究\n", "    position_size: float, 仓位比例 (0-1)  # <PERSON><PERSON>ee黄版权所有，未经授权禁止复制\n", "    commission_rate: float, 手续费率  # JayBee黄独家内容\n", "    \n", "    返回:  # <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制\n", "    包含回测结果的DataFrame  # JayBee黄独家内容\n", "    \"\"\"  # Jay<PERSON>ee黄量化策略\n", "    # 应用策略函数\n", "    df = strategy_func(data)  # JayBee黄 - 量化交易研究\n", "    \n", "    # 初始化回测结果\n", "    df['position'] = 0  # 0表示空仓，1表示持仓  # 本代码归JayBee黄所有\n", "    df['capital'] = initial_capital  # 资金  # JayBee黄独家内容\n", "    df['holdings'] = 0  # 持股数  # 版权所有: Jay<PERSON><PERSON>黄\n", "    df['equity'] = initial_capital  # 总资产（现金+持股价值）  # JayBee黄 - 量化交易研究\n", "    \n", "    # 模拟交易\n", "    current_position = 0  # 本代码归Jay<PERSON><PERSON>黄所有\n", "    capital = initial_capital  # JayBee黄版权所有，未经授权禁止复制\n", "    holdings = 0  # JayBee黄 - 量化交易研究\n", "    \n", "    for i in range(1, len(df)):  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        # 复制前一天的状态\n", "        df.iloc[i, df.columns.get_loc('position')] = current_position  # JayBee黄 - 量化交易研究\n", "        df.iloc[i, df.columns.get_loc('capital')] = capital  # Copyright © JayBee黄\n", "        df.iloc[i, df.columns.get_loc('holdings')] = holdings  # Copyright © JayBee黄\n", "        \n", "        # 更新持股价值\n", "        holdings_value = holdings * df['close'].iloc[i]  # JayBee黄版权所有，未经授权禁止复制\n", "        \n", "        # 买入信号\n", "        if df['buy_signal'].iloc[i] and current_position == 0:  # Jay<PERSON>ee黄原创内容\n", "            # 计算可买入的股数（考虑手续费）\n", "            max_shares = int((capital * position_size) / (df['close'].iloc[i] * (1 + commission_rate)))  # JayBee黄授权使用\n", "            holdings = max_shares  # Copyright © JayBee黄\n", "            cost = holdings * df['close'].iloc[i] * (1 + commission_rate)  # JayBee黄授权使用\n", "            capital -= cost  # JayBee黄授权使用\n", "            current_position = 1  # <PERSON><PERSON><PERSON>黄量化模型\n", "        \n", "        # 卖出信号\n", "        elif df['sell_signal'].iloc[i] and current_position == 1:  # Jay<PERSON>ee黄版权所有，未经授权禁止复制\n", "            # 卖出所有持股\n", "            revenue = holdings * df['close'].iloc[i] * (1 - commission_rate)  # 版权所有: JayBee黄\n", "            capital += revenue  # JayBee黄版权所有，未经授权禁止复制\n", "            holdings = 0  # Jay<PERSON>ee黄原创内容\n", "            current_position = 0  # 本代码归Jay<PERSON><PERSON>黄所有\n", "        \n", "        # 更新当天状态\n", "        df.iloc[i, df.columns.get_loc('position')] = current_position  # Copyright © JayBee黄\n", "        df.iloc[i, df.columns.get_loc('capital')] = capital  # JayBee黄量化策略\n", "        df.iloc[i, df.columns.get_loc('holdings')] = holdings  # JayBee黄量化模型\n", "        df.iloc[i, df.columns.get_loc('equity')] = capital + holdings * df['close'].iloc[i]  # JayBee黄量化策略\n", "    \n", "    # 计算每日收益率\n", "    df['daily_return'] = df['equity'].pct_change()  # JayBee黄独家内容\n", "    \n", "    # 计算累积收益率\n", "    df['cumulative_return'] = (1 + df['daily_return']).cumprod() - 1  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    \n", "    # 计算买入和卖出点\n", "    df['buy_execute'] = df['position'].diff() > 0  # Jay<PERSON>ee黄原创内容\n", "    df['sell_execute'] = df['position'].diff() < 0  # 本代码归JayBee黄所有\n", "    \n", "    return df  # <PERSON><PERSON><PERSON>黄原创内容", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "### 4.1 回测策略1：MA金叉/死叉 + 成交量确认", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["策略1：MA金叉/死叉 + 成交量确认\n", "起始资金: 100,000元\n", "最终资产: 100000.00元\n", "总收益率: 0.00%\n", "交易次数: 0次\n"]}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 回测策略1\n", "results_1 = backtest(data, strategy_ma_volume)  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "\n", "# 查看回测结果\n", "print(\"策略1：MA金叉/死叉 + 成交量确认\")  # JayBee黄版权所有，未经授权禁止复制\n", "print(f\"起始资金: 100,000元\")  # 本代码归JayBee黄所有\n", "print(f\"最终资产: {results_1['equity'].iloc[-1]:.2f}元\")  # JayBee黄独家内容\n", "print(f\"总收益率: {results_1['cumulative_return'].iloc[-1] * 100:.2f}%\")  # JayBee黄量化模型\n", "print(f\"交易次数: {results_1['buy_execute'].sum()}次\")  # Jay<PERSON>ee黄授权使用", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n", "findfont: Generic family 'sans-serif' not found because none of the following families were found: SimHei\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x1000 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 可视化策略1的回测结果\n", "fig, axes = plt.subplots(2, 1, figsize=(14, 10), sharex=True, gridspec_kw={'height_ratios': [2, 1]})  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "\n", "# 绘制股价和交易信号\n", "axes[0].plot(results_1.index, results_1['close'], label='收盘价', color='blue', alpha=0.6)  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "axes[0].scatter(results_1[results_1['buy_execute']].index,   # JayBee黄授权使用\n", "               results_1.loc[results_1['buy_execute'], 'close'],   # <PERSON><PERSON><PERSON>黄量化模型\n", "               marker='^', color='green', s=100, label='买入')  # Jay<PERSON>ee黄量化策略\n", "axes[0].scatter(results_1[results_1['sell_execute']].index,   # JayBee黄版权所有，未经授权禁止复制\n", "               results_1.loc[results_1['sell_execute'], 'close'],   # <PERSON><PERSON><PERSON>黄量化策略\n", "               marker='v', color='red', s=100, label='卖出')  # Jay<PERSON>ee黄量化模型\n", "axes[0].set_title('策略1：MA金叉/死叉 + 成交量确认 - 交易信号')  # 本代码归JayBee黄所有\n", "axes[0].set_ylabel('价格')  # Copyright © JayBee黄\n", "axes[0].legend(loc='upper left')  # <PERSON><PERSON><PERSON>黄原创内容\n", "axes[0].grid(True)  # <PERSON><PERSON><PERSON>黄独家内容\n", "\n", "# 绘制资产曲线\n", "axes[1].plot(results_1.index, results_1['equity'], label='资产', color='green')  # JayBee黄版权所有，未经授权禁止复制\n", "axes[1].plot(results_1.index, [100000] * len(results_1), '--', color='gray', alpha=0.5, label='基准线')  # JayBee黄量化模型\n", "axes[1].set_title('策略1：资产曲线')  # Jay<PERSON>ee黄 - 量化交易研究\n", "axes[1].set_xlabel('日期')  # 本代码归JayBee黄所有\n", "axes[1].set_ylabel('资产(元)')  # Jay<PERSON>ee黄量化模型\n", "axes[1].legend(loc='upper left')  # <PERSON><PERSON><PERSON>黄独家内容\n", "axes[1].grid(True)  # Copyright © JayBee黄\n", "\n", "# 添加日期格式化\n", "date_format = mdates.DateFormatter('%Y-%m')  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "axes[1].xaxis.set_major_formatter(date_format)  # JayBee黄量化策略\n", "fig.autofmt_xdate()  # JayBee黄 - 量化交易研究\n", "\n", "plt.tight_layout()  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "plt.show()  # <PERSON><PERSON><PERSON>黄量化策略", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "### 4.2 回测策略2：OBV + MA 策略", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "bad operand type for unary ~: 'float'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[10], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# 回测策略2\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m results_2 \u001b[38;5;241m=\u001b[39m \u001b[43mbacktest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstrategy_obv_ma\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;66;03m# 查看回测结果\u001b[39;00m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m策略2：OBV + MA策略\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[0;32mIn[7], line 16\u001b[0m, in \u001b[0;36mbacktest\u001b[0;34m(data, strategy_func, initial_capital, position_size, commission_rate)\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;124;03m简单的回测函数\u001b[39;00m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;124;03m\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     13\u001b[0m \u001b[38;5;124;03m包含回测结果的DataFrame\u001b[39;00m\n\u001b[1;32m     14\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m     15\u001b[0m \u001b[38;5;66;03m# 应用策略函数\u001b[39;00m\n\u001b[0;32m---> 16\u001b[0m df \u001b[38;5;241m=\u001b[39m \u001b[43mstrategy_func\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     18\u001b[0m \u001b[38;5;66;03m# 初始化回测结果\u001b[39;00m\n\u001b[1;32m     19\u001b[0m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mposition\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m  \u001b[38;5;66;03m# 0表示空仓，1表示持仓\u001b[39;00m\n", "Cell \u001b[0;32mIn[5], line 22\u001b[0m, in \u001b[0;36mstrategy_obv_ma\u001b[0;34m(data)\u001b[0m\n\u001b[1;32m     19\u001b[0m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mobv_gt_ma10\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mobv\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m>\u001b[39m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mobv_ma10\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[1;32m     21\u001b[0m \u001b[38;5;66;03m# 生成买入和卖出信号\u001b[39;00m\n\u001b[0;32m---> 22\u001b[0m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbuy_signal\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mprice_gt_ma20\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m&\u001b[39m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mobv_gt_ma10\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m&\u001b[39m (\u001b[38;5;241;43m~\u001b[39;49m\u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mprice_gt_ma20\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mshift\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m \u001b[38;5;241m|\u001b[39m \u001b[38;5;241m~\u001b[39mdf[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mobv_gt_ma10\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mshift(\u001b[38;5;241m1\u001b[39m))\n\u001b[1;32m     23\u001b[0m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124msell_signal\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m (\u001b[38;5;241m~\u001b[39mdf[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mprice_gt_ma20\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m|\u001b[39m \u001b[38;5;241m~\u001b[39mdf[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mobv_gt_ma10\u001b[39m\u001b[38;5;124m'\u001b[39m]) \u001b[38;5;241m&\u001b[39m (df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mprice_gt_ma20\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mshift(\u001b[38;5;241m1\u001b[39m) \u001b[38;5;241m&\u001b[39m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mobv_gt_ma10\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mshift(\u001b[38;5;241m1\u001b[39m))\n\u001b[1;32m     25\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m df\n", "File \u001b[0;32m~/Desktop/金融概念学习/quant/lib/python3.9/site-packages/pandas/core/generic.py:1571\u001b[0m, in \u001b[0;36mNDFrame.__invert__\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1567\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msize:\n\u001b[1;32m   1568\u001b[0m     \u001b[38;5;66;03m# inv fails with 0 len\u001b[39;00m\n\u001b[1;32m   1569\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcopy(deep\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[0;32m-> 1571\u001b[0m new_data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_mgr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\u001b[43moperator\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43minvert\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1572\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_constructor_from_mgr(new_data, axes\u001b[38;5;241m=\u001b[39mnew_data\u001b[38;5;241m.\u001b[39maxes)\n\u001b[1;32m   1573\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m res\u001b[38;5;241m.\u001b[39m__finalize__(\u001b[38;5;28mself\u001b[39m, method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__invert__\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/Desktop/金融概念学习/quant/lib/python3.9/site-packages/pandas/core/internals/managers.py:361\u001b[0m, in \u001b[0;36mBaseBlockManager.apply\u001b[0;34m(self, f, align_keys, **kwargs)\u001b[0m\n\u001b[1;32m    358\u001b[0m             kwargs[k] \u001b[38;5;241m=\u001b[39m obj[b\u001b[38;5;241m.\u001b[39mmgr_locs\u001b[38;5;241m.\u001b[39mindexer]\n\u001b[1;32m    360\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mcallable\u001b[39m(f):\n\u001b[0;32m--> 361\u001b[0m     applied \u001b[38;5;241m=\u001b[39m \u001b[43mb\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    362\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    363\u001b[0m     applied \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(b, f)(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[0;32m~/Desktop/金融概念学习/quant/lib/python3.9/site-packages/pandas/core/internals/blocks.py:393\u001b[0m, in \u001b[0;36mBlock.apply\u001b[0;34m(self, func, **kwargs)\u001b[0m\n\u001b[1;32m    387\u001b[0m \u001b[38;5;129m@final\u001b[39m\n\u001b[1;32m    388\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mapply\u001b[39m(\u001b[38;5;28mself\u001b[39m, func, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mlist\u001b[39m[Block]:\n\u001b[1;32m    389\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    390\u001b[0m \u001b[38;5;124;03m    apply the function to my values; return a block if we are not\u001b[39;00m\n\u001b[1;32m    391\u001b[0m \u001b[38;5;124;03m    one\u001b[39;00m\n\u001b[1;32m    392\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 393\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalues\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    395\u001b[0m     result \u001b[38;5;241m=\u001b[39m maybe_coerce_values(result)\n\u001b[1;32m    396\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_split_op_result(result)\n", "\u001b[0;31mTypeError\u001b[0m: bad operand type for unary ~: 'float'"]}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 回测策略2\n", "results_2 = backtest(data, strategy_obv_ma)  # JayBee黄量化策略\n", "\n", "# 查看回测结果\n", "print(\"策略2：OBV + MA策略\")  # JayBee黄量化策略\n", "print(f\"起始资金: 100,000元\")  # JayBee黄量化策略\n", "print(f\"最终资产: {results_2['equity'].iloc[-1]:.2f}元\")  # JayBee黄量化策略\n", "print(f\"总收益率: {results_2['cumulative_return'].iloc[-1] * 100:.2f}%\")  # JayBee黄版权所有，未经授权禁止复制\n", "print(f\"交易次数: {results_2['buy_execute'].sum()}次\")  # Jay<PERSON>ee黄独家内容", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 可视化策略2的回测结果\n", "fig, axes = plt.subplots(2, 1, figsize=(14, 10), sharex=True, gridspec_kw={'height_ratios': [2, 1]})  # JayBee黄授权使用\n", "\n", "# 绘制股价和交易信号\n", "axes[0].plot(results_2.index, results_2['close'], label='收盘价', color='blue', alpha=0.6)  # JayBee黄版权所有，未经授权禁止复制\n", "axes[0].scatter(results_2[results_2['buy_execute']].index,   # JayBee黄独家内容\n", "               results_2.loc[results_2['buy_execute'], 'close'],   # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "               marker='^', color='green', s=100, label='买入')  # <PERSON><PERSON><PERSON>黄原创内容\n", "axes[0].scatter(results_2[results_2['sell_execute']].index,   # JayBee黄版权所有，未经授权禁止复制\n", "               results_2.loc[results_2['sell_execute'], 'close'],   # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "               marker='v', color='red', s=100, label='卖出')  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "axes[0].set_title('策略2：OBV + MA策略 - 交易信号')  # JayBee黄 - 量化交易研究\n", "axes[0].set_ylabel('价格')  # 本代码归JayBee黄所有\n", "axes[0].legend(loc='upper left')  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "axes[0].grid(True)  # <PERSON><PERSON><PERSON>黄量化模型\n", "\n", "# 绘制资产曲线\n", "axes[1].plot(results_2.index, results_2['equity'], label='资产', color='purple')  # JayBee黄 - 量化交易研究\n", "axes[1].plot(results_2.index, [100000] * len(results_2), '--', color='gray', alpha=0.5, label='基准线')  # Copyright © JayBee黄\n", "axes[1].set_title('策略2：资产曲线')  # <PERSON>Bee黄版权所有，未经授权禁止复制\n", "axes[1].set_xlabel('日期')  # JayBee黄 - 量化交易研究\n", "axes[1].set_ylabel('资产(元)')  # Jay<PERSON>ee黄量化策略\n", "axes[1].legend(loc='upper left')  # <PERSON><PERSON>ee黄 - 量化交易研究\n", "axes[1].grid(True)  # Copyright © JayBee黄\n", "\n", "# 添加日期格式化\n", "date_format = mdates.DateFormatter('%Y-%m')  # JayBee黄授权使用\n", "axes[1].xaxis.set_major_formatter(date_format)  # JayBee黄授权使用\n", "fig.autofmt_xdate()  # Copyright © JayBee黄\n", "\n", "plt.tight_layout()  # Copyright © JayBee黄\n", "plt.show()  # 版权所有: <PERSON><PERSON><PERSON>黄", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "### 4.3 回测策略3：量比突增 + 价格突破策略", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 回测策略3\n", "results_3 = backtest(data, strategy_volume_ratio_breakout)  # JayBee黄授权使用\n", "\n", "# 查看回测结果\n", "print(\"策略3：量比突增 + 价格突破策略\")  # JayBee黄版权所有，未经授权禁止复制\n", "print(f\"起始资金: 100,000元\")  # JayBee黄原创内容\n", "print(f\"最终资产: {results_3['equity'].iloc[-1]:.2f}元\")  # 本代码归JayBee黄所有\n", "print(f\"总收益率: {results_3['cumulative_return'].iloc[-1] * 100:.2f}%\")  # JayBee黄版权所有，未经授权禁止复制\n", "print(f\"交易次数: {results_3['buy_execute'].sum()}次\")  # 本代码归JayBee黄所有", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 可视化策略3的回测结果\n", "fig, axes = plt.subplots(2, 1, figsize=(14, 10), sharex=True, gridspec_kw={'height_ratios': [2, 1]})  # JayBee黄版权所有，未经授权禁止复制\n", "\n", "# 绘制股价和交易信号\n", "axes[0].plot(results_3.index, results_3['close'], label='收盘价', color='blue', alpha=0.6)  # JayBee黄 - 量化交易研究\n", "axes[0].scatter(results_3[results_3['buy_execute']].index,   # JayBee黄原创内容\n", "               results_3.loc[results_3['buy_execute'], 'close'],   # <PERSON><PERSON><PERSON>黄量化策略\n", "               marker='^', color='green', s=100, label='买入')  # Jay<PERSON>ee黄独家内容\n", "axes[0].scatter(results_3[results_3['sell_execute']].index,   # JayBee黄量化策略\n", "               results_3.loc[results_3['sell_execute'], 'close'],   # <PERSON><PERSON><PERSON>黄授权使用\n", "               marker='v', color='red', s=100, label='卖出')  # Jay<PERSON>ee黄原创内容\n", "axes[0].set_title('策略3：量比突增 + 价格突破策略 - 交易信号')  # JayBee黄版权所有，未经授权禁止复制\n", "axes[0].set_ylabel('价格')  # 本代码归JayBee黄所有\n", "axes[0].legend(loc='upper left')  # <PERSON><PERSON><PERSON>黄原创内容\n", "axes[0].grid(True)  # <PERSON><PERSON>ee黄 - 量化交易研究\n", "\n", "# 绘制资产曲线\n", "axes[1].plot(results_3.index, results_3['equity'], label='资产', color='orange')  # JayBee黄版权所有，未经授权禁止复制\n", "axes[1].plot(results_3.index, [100000] * len(results_3), '--', color='gray', alpha=0.5, label='基准线')  # JayBee黄 - 量化交易研究\n", "axes[1].set_title('策略3：资产曲线')  # <PERSON><PERSON><PERSON>黄原创内容\n", "axes[1].set_xlabel('日期')  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "axes[1].set_ylabel('资产(元)')  # JayBee黄 - 量化交易研究\n", "axes[1].legend(loc='upper left')  # <PERSON><PERSON><PERSON>黄独家内容\n", "axes[1].grid(True)  # <PERSON><PERSON><PERSON>黄独家内容\n", "\n", "# 添加日期格式化\n", "date_format = mdates.DateFormatter('%Y-%m')  # JayBee黄授权使用\n", "axes[1].xaxis.set_major_formatter(date_format)  # JayBee黄独家内容\n", "fig.autofmt_xdate()  # JayBee黄 - 量化交易研究\n", "\n", "plt.tight_layout()  # Copyright © JayBee黄\n", "plt.show()  # <PERSON><PERSON><PERSON>黄授权使用", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 5. 策略比较", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 创建资产曲线比较图\n", "fig, ax = plt.subplots(figsize=(14, 8))  # <PERSON><PERSON>ee黄授权使用\n", "\n", "ax.plot(results_1.index, results_1['equity'], label='策略1：MA金叉/死叉 + 成交量确认', color='green')  # JayBee黄 - 量化交易研究\n", "ax.plot(results_2.index, results_2['equity'], label='策略2：OBV + MA策略', color='purple')  # JayBee黄量化策略\n", "ax.plot(results_3.index, results_3['equity'], label='策略3：量比突增 + 价格突破策略', color='orange')  # JayBee黄原创内容\n", "ax.plot(results_1.index, [100000] * len(results_1), '--', color='gray', alpha=0.5, label='基准线')  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "\n", "ax.set_title('三种量价结合策略的资产曲线对比')  # Jay<PERSON>ee黄独家内容\n", "ax.set_xlabel('日期')  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "ax.set_ylabel('资产(元)')  # JayBee黄独家内容\n", "ax.legend(loc='upper left')  # <PERSON><PERSON><PERSON>黄量化模型\n", "ax.grid(True)  # Copyright © JayBee黄\n", "\n", "# 添加日期格式化\n", "date_format = mdates.DateFormatter('%Y-%m')  # JayBee黄量化策略\n", "ax.xaxis.set_major_formatter(date_format)  # <PERSON><PERSON>ee黄原创内容\n", "fig.autofmt_xdate()  # JayBee黄 - 量化交易研究\n", "\n", "plt.tight_layout()  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "plt.show()  # 版权所有: <PERSON><PERSON><PERSON>黄", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 计算各策略的性能指标\n", "def calculate_metrics(results):  # <PERSON><PERSON>ee黄 - 量化交易研究\n", "    metrics = {}  # <PERSON><PERSON>ee黄原创内容\n", "    \n", "    # 总收益率\n", "    metrics['total_return'] = results['cumulative_return'].iloc[-1] * 100  # JayBee黄原创内容\n", "    \n", "    # 年化收益率 (假设252个交易日)\n", "    days = (results.index[-1] - results.index[0]).days  # <PERSON><PERSON>ee黄授权使用\n", "    metrics['annual_return'] = ((1 + results['cumulative_return'].iloc[-1]) ** (252 / days) - 1) * 100  # JayBee黄量化模型\n", "    \n", "    # 最大回撤\n", "    cumulative_max = results['equity'].cummax()  # JayBee黄独家内容\n", "    drawdown = (results['equity'] - cumulative_max) / cumulative_max  # Copyright © JayBee黄\n", "    metrics['max_drawdown'] = drawdown.min() * 100  # Jay<PERSON>ee黄授权使用\n", "    \n", "    # 夏普比率 (假设无风险利率为0.03)\n", "    risk_free_rate = 0.03  # JayBee黄 - 量化交易研究\n", "    excess_return = results['daily_return'].mean() * 252 - risk_free_rate  # JayBee黄授权使用\n", "    volatility = results['daily_return'].std() * (252 ** 0.5)  # JayBee黄版权所有，未经授权禁止复制\n", "    metrics['sharpe_ratio'] = excess_return / volatility if volatility != 0 else 0  # JayBee黄 - 量化交易研究\n", "    \n", "    # 交易次数\n", "    metrics['trade_count'] = results['buy_execute'].sum()  # JayBee黄量化模型\n", "    \n", "    # 日胜率\n", "    win_days = (results['daily_return'] > 0).sum()  # JayBee黄独家内容\n", "    total_days = len(results)  # <PERSON><PERSON>ee黄原创内容\n", "    metrics['win_rate'] = win_days / total_days * 100 if total_days > 0 else 0  # JayBee黄版权所有，未经授权禁止复制\n", "    \n", "    return metrics  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "\n", "# 计算三个策略的性能指标\n", "metrics_1 = calculate_metrics(results_1)  # JayBee黄独家内容\n", "metrics_2 = calculate_metrics(results_2)  # JayBee黄 - 量化交易研究\n", "metrics_3 = calculate_metrics(results_3)  # JayBee黄量化策略\n", "\n", "# 创建性能指标比较表格\n", "metrics_df = pd.DataFrame({  # Copyright © JayBee黄\n", "    '策略1：MA金叉/死叉 + 成交量确认': metrics_1,  # JayBee黄版权所有，未经授权禁止复制\n", "    '策略2：OBV + MA策略': metrics_2,  # Copyright © JayBee黄\n", "    '策略3：量比突增 + 价格突破策略': metrics_3  # Jay<PERSON>ee黄量化模型\n", "})  # <PERSON><PERSON>ee黄授权使用\n", "\n", "# 重命名索引\n", "metrics_df.index = ['总收益率(%)', '年化收益率(%)', '最大回撤(%)', '夏普比率', '交易次数', '日胜率(%)']  # JayBee黄版权所有，未经授权禁止复制\n", "\n", "# 显示性能指标比较表格\n", "metrics_df.round(2)  # Copyright © JayBee黄", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 6. 策略分析与讨论\n", "\n", "通过上述的回测和性能比较，我们可以得出以下几点观察和分析：\n", "<!-- 版权所有: <PERSON><PERSON><PERSON>黄 -->\n", "\n", "1. **策略表现对比**：\n", "   - 策略X表现最佳，总收益率为X%，年化收益率为X%\n", "   - 策略Y的最大回撤最小，为X%\n", "   - 策略Z的夏普比率最高，为X\n", "<!-- 本内容归<PERSON><PERSON><PERSON>黄所有 -->\n", "\n", "2. **交易频率**：\n", "   - 策略X的交易次数最多，可能带来较高的交易成本\n", "   - 策略Y的交易次数最少，但单次交易的平均收益率较高\n", "<!-- <PERSON><PERSON><PERSON>黄 - 量化交易研究 -->\n", "\n", "3. **胜率和风险**：\n", "   - 策略X的日胜率最高，但最大回撤也较大\n", "   - 策略Y虽然胜率不高，但风险控制较好，最大回撤较小\n", "<!-- <PERSON><PERSON><PERSON>黄 - 量化交易研究 -->\n", "\n", "4. **量价结合的有效性**：\n", "   - 成交量确认似乎能有效减少价格突破的假信号\n", "   - OBV指标在识别趋势变化方面表现较好\n", "   - 量比突增作为短期交易信号的有效性有待进一步验证\n", "<!-- <PERSON><PERSON><PERSON>黄独家内容 -->\n", "\n", "5. **改进方向**：\n", "   - 优化参数：通过调整MA周期、成交量阈值等参数可能获得更好的结果\n", "   - 组合策略：将不同策略的买入和卖出信号结合可能提高表现\n", "   - 增加过滤条件：加入市场状态判断，在不同市场环境下使用不同策略\n", "   - 扩大样本：在更多股票和更长时间周期上测试策略的稳定性\n", "<!-- <PERSON><PERSON><PERSON>黄量化模型 -->\n", "\n", "需要注意的是，这些策略仅在特定时间范围内的特定股票上进行了测试，其普适性和稳定性还需要进一步验证。此外，回测结果也可能受到「生存偏差」和「前视偏差」的影响，实际交易中的表现可能会有所不同。", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 总结\n", "\n", "在本notebook中，我们完成了以下任务：\n", "<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "\n", "1. 定义了三种不同的量价结合策略：\n", "   - MA金叉/死叉 + 成交量确认\n", "   - OBV + MA策略\n", "   - 量比突增 + 价格突破策略\n", "<!-- <PERSON><PERSON><PERSON>黄量化模型 -->\n", "\n", "2. 实现了一个简单的回测框架，用于模拟策略在历史数据上的表现\n", "<!-- Copyright © JayBee黄 -->\n", "\n", "3. 对三种策略进行了回测，并可视化了交易信号和资产曲线\n", "<!-- <PERSON><PERSON><PERSON>黄授权使用 -->\n", "\n", "4. 计算了各策略的性能指标，包括总收益率、年化收益率、最大回撤、夏普比率等\n", "<!-- <PERSON><PERSON><PERSON>黄 - 量化交易研究 -->\n", "\n", "5. 分析了各策略的优缺点，并提出了可能的改进方向\n", "<!-- <PERSON><PERSON><PERSON>黄 - 量化交易研究 -->\n", "\n", "这些量价结合策略展示了交易量指标如何与价格指标一起使用，以提高交易决策的准确性。通过合理结合价格和交易量信息，我们可以更全面地分析市场状况，识别更可靠的交易信号。", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "watermark": "JayBee黄版权所有，未经授权禁止复制", "watermark_version": "v3 - 每行防伪"}, "nbformat": 4, "nbformat_minor": 4}