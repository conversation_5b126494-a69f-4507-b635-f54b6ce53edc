"""
数据加载器模块

提供从各种数据源加载数据的功能。
"""

import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List
from pathlib import Path
import re

from ..config import get_config
from ..utils.logger import get_logger
from ..utils.exceptions import DataError, ValidationError


class BaseDataLoader(ABC):
    """数据加载器基类"""
    
    def __init__(self, **kwargs):
        """
        初始化数据加载器
        
        Args:
            **kwargs: 加载器参数
        """
        self.logger = get_logger(f"data.{self.__class__.__name__}")
        self.config = get_config()
        self._validate_params(**kwargs)
    
    @abstractmethod
    def _validate_params(self, **kwargs) -> None:
        """验证参数"""
        pass
    
    @abstractmethod
    def load(self, **kwargs) -> pd.DataFrame:
        """加载数据"""
        pass
    
    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        标准化列名
        
        Args:
            df: 原始数据框
            
        Returns:
            标准化后的数据框
        """
        # 标准列名映射
        column_mapping = {
            # 开盘价
            'Open': 'open', 'OPEN': 'open', '开盘价': 'open', 'open_price': 'open',
            # 最高价
            'High': 'high', 'HIGH': 'high', '最高价': 'high', 'high_price': 'high',
            # 最低价
            'Low': 'low', 'LOW': 'low', '最低价': 'low', 'low_price': 'low',
            # 收盘价
            'Close': 'close', 'CLOSE': 'close', '收盘价': 'close', 'close_price': 'close',
            # 成交量
            'Volume': 'volume', 'VOLUME': 'volume', '成交量': 'volume', 'vol': 'volume',
            # 成交额
            'Amount': 'amount', 'AMOUNT': 'amount', '成交额': 'amount',
            # 日期
            'Date': 'date', 'DATE': 'date', '日期': 'date', 'trade_date': 'date',
            # 股票代码
            'Symbol': 'symbol', 'SYMBOL': 'symbol', '代码': 'symbol', 'ts_code': 'symbol'
        }
        
        # 重命名列
        df_renamed = df.rename(columns=column_mapping)
        
        # 确保必要的列存在
        required_columns = ['close']
        missing_columns = [col for col in required_columns if col not in df_renamed.columns]
        
        if missing_columns:
            raise DataError(f"缺少必要的列: {missing_columns}", data_type="columns")
        
        return df_renamed


class CSVDataLoader(BaseDataLoader):
    """CSV数据加载器"""
    
    def __init__(self, file_path: Optional[str] = None, 
                 encoding: str = 'utf-8',
                 symbol_filter: Optional[str] = None):
        """
        初始化CSV数据加载器
        
        Args:
            file_path: CSV文件路径
            encoding: 文件编码
            symbol_filter: 股票代码过滤器
        """
        self.file_path = file_path
        self.encoding = encoding
        self.symbol_filter = symbol_filter
        
        super().__init__(
            file_path=file_path,
            encoding=encoding,
            symbol_filter=symbol_filter
        )
    
    def _validate_params(self, **kwargs) -> None:
        """验证参数"""
        file_path = kwargs.get('file_path', self.file_path)
        
        if file_path and not Path(file_path).exists():
            raise ValidationError(f"CSV文件不存在: {file_path}", "file_path", file_path)
    
    def load(self, file_path: Optional[str] = None, **kwargs) -> pd.DataFrame:
        """
        加载CSV数据
        
        Args:
            file_path: CSV文件路径
            **kwargs: 其他参数
            
        Returns:
            数据框
        """
        try:
            file_path = file_path or self.file_path
            if not file_path:
                raise DataError("未指定CSV文件路径", data_source="csv")
            
            self.logger.info(f"开始加载CSV文件: {file_path}")
            
            # 尝试不同编码读取
            encodings = [self.encoding, 'utf-8', 'gbk', 'gb2312']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding, low_memory=False)
                    self.logger.info(f"使用编码 {encoding} 成功读取文件")
                    break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    self.logger.warning(f"使用编码 {encoding} 读取失败: {e}")
                    continue
            
            if df is None:
                raise DataError(f"无法读取CSV文件: {file_path}", data_source="csv")
            
            # 标准化列名
            df = self._standardize_columns(df)
            
            # 过滤股票代码
            if self.symbol_filter and 'symbol' in df.columns:
                df = self._filter_by_symbol(df, self.symbol_filter)
            
            # 处理日期索引
            if 'date' in df.columns:
                df = self._process_date_index(df)
            
            self.logger.info(f"CSV数据加载完成: {len(df)}条记录")
            return df
            
        except Exception as e:
            self.logger.error(f"加载CSV数据失败: {e}")
            raise DataError(f"加载CSV数据失败: {e}", data_source="csv")
    
    def _filter_by_symbol(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """
        按股票代码过滤数据
        
        Args:
            df: 数据框
            symbol: 股票代码
            
        Returns:
            过滤后的数据框
        """
        # 清理股票代码
        df['symbol'] = df['symbol'].astype(str)
        
        # 移除交易所后缀并去除前导零
        df_symbol_cleaned = df['symbol'].str.replace(r'\.S[HZ]$', '', regex=True).str.lstrip('0')
        target_symbol_cleaned = re.sub(r'\.S[HZ]$', '', str(symbol)).lstrip('0')
        
        # 过滤数据
        filtered_df = df[df_symbol_cleaned == target_symbol_cleaned]
        
        if filtered_df.empty:
            raise DataError(f"未找到股票代码 {symbol} 的数据", data_source="csv", data_type="symbol")
        
        self.logger.info(f"找到股票 {symbol} 的数据: {len(filtered_df)}条记录")
        return filtered_df
    
    def _process_date_index(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        处理日期索引
        
        Args:
            df: 数据框
            
        Returns:
            处理后的数据框
        """
        try:
            # 尝试解析日期
            if df['date'].dtype == 'object':
                # 尝试不同的日期格式
                date_formats = ['%Y-%m-%d', '%Y%m%d', '%m/%d/%Y', '%d/%m/%Y']
                
                for fmt in date_formats:
                    try:
                        df['date'] = pd.to_datetime(df['date'], format=fmt)
                        break
                    except:
                        continue
                else:
                    # 如果所有格式都失败，使用自动推断
                    df['date'] = pd.to_datetime(df['date'])
            
            # 设置日期为索引并排序
            df = df.set_index('date').sort_index()
            
            return df
            
        except Exception as e:
            self.logger.warning(f"处理日期索引失败: {e}")
            return df


class TushareDataLoader(BaseDataLoader):
    """Tushare数据加载器"""
    
    def __init__(self, token: Optional[str] = None):
        """
        初始化Tushare数据加载器
        
        Args:
            token: Tushare API token
        """
        self.token = token
        self._pro = None
        
        super().__init__(token=token)
    
    def _validate_params(self, **kwargs) -> None:
        """验证参数"""
        # 基本验证
        pass
    
    def _init_tushare(self) -> None:
        """初始化Tushare连接"""
        try:
            import tushare as ts
            
            if self.token:
                ts.set_token(self.token)
            
            self._pro = ts.pro_api()
            self.logger.info("Tushare连接初始化成功")
            
        except ImportError:
            raise DataError("未安装tushare库，请运行: pip install tushare", data_source="tushare")
        except Exception as e:
            raise DataError(f"初始化Tushare连接失败: {e}", data_source="tushare")
    
    def load(self, symbol: str, start_date: str, end_date: str, **kwargs) -> pd.DataFrame:
        """
        从Tushare加载数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            **kwargs: 其他参数
            
        Returns:
            数据框
        """
        try:
            if self._pro is None:
                self._init_tushare()
            
            self.logger.info(f"从Tushare加载数据: {symbol}, {start_date} - {end_date}")
            
            # 转换日期格式
            start_date_ts = start_date.replace('-', '')
            end_date_ts = end_date.replace('-', '')
            
            # 获取数据
            df = self._pro.daily(
                ts_code=symbol,
                start_date=start_date_ts,
                end_date=end_date_ts
            )
            
            if df.empty:
                raise DataError(f"未获取到数据: {symbol}", data_source="tushare")
            
            # 标准化数据
            df = self._standardize_tushare_data(df)
            
            self.logger.info(f"Tushare数据加载完成: {len(df)}条记录")
            return df
            
        except Exception as e:
            self.logger.error(f"从Tushare加载数据失败: {e}")
            raise DataError(f"从Tushare加载数据失败: {e}", data_source="tushare")
    
    def _standardize_tushare_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        标准化Tushare数据
        
        Args:
            df: 原始Tushare数据
            
        Returns:
            标准化后的数据框
        """
        # 重命名列
        column_mapping = {
            'trade_date': 'date',
            'vol': 'volume'
        }
        df = df.rename(columns=column_mapping)
        
        # 处理日期
        df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')
        df = df.set_index('date').sort_index()
        
        # 选择需要的列
        columns = ['open', 'high', 'low', 'close', 'volume']
        available_columns = [col for col in columns if col in df.columns]
        df = df[available_columns]
        
        return df
