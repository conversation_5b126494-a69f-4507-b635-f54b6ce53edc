"""
策略文件生成器

将ML优化后的策略参数自动生成为独立的策略文件。
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, Any
import os


class StrategyFileGenerator:
    """策略文件生成器"""

    def __init__(self, output_dir: str = "generated_strategies"):
        """
        初始化生成器

        Args:
            output_dir: 策略文件输出目录
        """
        self.output_dir = output_dir
        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"✅ 创建策略文件夹: {output_dir}")

        # 创建__init__.py文件使其成为Python包
        init_file = os.path.join(output_dir, "__init__.py")
        if not os.path.exists(init_file):
            with open(init_file, 'w', encoding='utf-8') as f:
                f.write('"""生成的策略文件包"""\n')
            print(f"✅ 创建包初始化文件: {init_file}")
    
    def generate_strategy_file(self, strategy_name: str, params: Dict[str, Any], 
                             performance_metrics: Dict[str, float],
                             description: str = "") -> str:
        """
        生成策略文件
        
        Args:
            strategy_name: 策略名称
            params: 优化后的参数
            performance_metrics: 性能指标
            description: 策略描述
            
        Returns:
            生成的文件路径
        """
        # 根据策略类型生成不同的文件
        if 'dual_ma' in strategy_name.lower():
            return self._generate_dual_ma_strategy(strategy_name, params, performance_metrics, description)
        elif 'rsi' in strategy_name.lower():
            return self._generate_rsi_strategy(strategy_name, params, performance_metrics, description)
        elif 'bollinger' in strategy_name.lower():
            return self._generate_bollinger_strategy(strategy_name, params, performance_metrics, description)
        else:
            return self._generate_generic_strategy(strategy_name, params, performance_metrics, description)
    
    def _generate_dual_ma_strategy(self, strategy_name: str, params: Dict[str, Any],
                                 performance_metrics: Dict[str, float], description: str) -> str:
        """生成双均线策略文件"""
        
        short_window = params.get('short_window', 20)
        long_window = params.get('long_window', 50)
        
        strategy_code = f'''#!/usr/bin/env python3
"""
{strategy_name} - ML优化双均线策略

这是一个由机器学习算法优化的双均线交叉策略。
策略通过遗传算法在历史数据上进行参数优化，寻找最优的均线组合。

策略描述:
{description}

优化参数:
- 短期均线: {short_window}日
- 长期均线: {long_window}日

历史性能 (回测):
- 总收益率: {performance_metrics.get('total_return', 0):.2%}
- 夏普比率: {performance_metrics.get('sharpe_ratio', 0):.3f}
- 最大回撤: {performance_metrics.get('max_drawdown', 0):.2%}
- 胜率: {performance_metrics.get('win_rate', 0):.2%}

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

import pandas as pd
import numpy as np
from typing import Tuple


class {self._to_class_name(strategy_name)}:
    """ML优化的双均线策略"""
    
    def __init__(self):
        """初始化策略"""
        # ML优化的参数
        self.short_window = {short_window}
        self.long_window = {long_window}
        
        # 策略信息
        self.name = "{strategy_name}"
        self.description = """{description}"""
        
        # 历史性能指标
        self.historical_performance = {{
            'total_return': {performance_metrics.get('total_return', 0):.4f},
            'sharpe_ratio': {performance_metrics.get('sharpe_ratio', 0):.4f},
            'max_drawdown': {performance_metrics.get('max_drawdown', 0):.4f},
            'win_rate': {performance_metrics.get('win_rate', 0):.4f}
        }}
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算技术指标
        
        Args:
            data: 包含OHLCV的价格数据
            
        Returns:
            包含指标的数据框
        """
        df = data.copy()
        
        # 计算移动平均线
        df['MA_short'] = df['close'].rolling(window=self.short_window).mean()
        df['MA_long'] = df['close'].rolling(window=self.long_window).mean()
        
        return df
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """
        生成交易信号
        
        Args:
            data: 价格数据
            
        Returns:
            交易信号序列 (1: 买入, -1: 卖出, 0: 持有)
        """
        df = self.calculate_indicators(data)
        
        # 初始化信号
        signals = pd.Series(0, index=data.index)
        
        # 双均线交叉策略
        # 金叉：短均线上穿长均线 -> 买入
        # 死叉：短均线下穿长均线 -> 卖出
        signals[df['MA_short'] > df['MA_long']] = 1
        signals[df['MA_short'] < df['MA_long']] = -1
        
        return signals
    
    def get_strategy_info(self) -> dict:
        """获取策略信息"""
        return {{
            'name': self.name,
            'type': 'dual_moving_average',
            'parameters': {{
                'short_window': self.short_window,
                'long_window': self.long_window
            }},
            'description': self.description,
            'historical_performance': self.historical_performance,
            'optimization_method': 'genetic_algorithm'
        }}
    
    def backtest_summary(self) -> str:
        """返回回测摘要"""
        return f\"\"\"
策略回测摘要:
================
策略名称: {{self.name}}
参数设置: MA{{self.short_window}}/MA{{self.long_window}}

历史表现:
- 总收益率: {{self.historical_performance['total_return']:.2%}}
- 夏普比率: {{self.historical_performance['sharpe_ratio']:.3f}}
- 最大回撤: {{self.historical_performance['max_drawdown']:.2%}}
- 胜率: {{self.historical_performance['win_rate']:.2%}}

使用建议:
1. 该策略适用于趋势性较强的市场
2. 在震荡市场中可能产生较多假信号
3. 建议结合其他指标进行信号过滤
4. 注意控制仓位和风险管理
\"\"\"


# 使用示例
if __name__ == "__main__":
    # 创建策略实例
    strategy = {self._to_class_name(strategy_name)}()
    
    # 打印策略信息
    print(strategy.backtest_summary())
    
    # 如果有数据，可以这样使用:
    # data = pd.read_csv('your_data.csv', index_col=0, parse_dates=True)
    # signals = strategy.generate_signals(data)
    # print(f"生成了 {{len(signals[signals != 0])}} 个交易信号")
'''
        
        # 保存文件到指定目录
        filename = f"{strategy_name.lower().replace(' ', '_')}_strategy.py"
        filepath = os.path.join(self.output_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(strategy_code)

        return filepath
    
    def _generate_rsi_strategy(self, strategy_name: str, params: Dict[str, Any],
                             performance_metrics: Dict[str, float], description: str) -> str:
        """生成RSI策略文件"""
        
        period = params.get('period', 14)
        oversold = params.get('oversold', 30)
        overbought = params.get('overbought', 70)
        
        strategy_code = f'''#!/usr/bin/env python3
"""
{strategy_name} - ML优化RSI策略

RSI超买超卖策略，参数通过机器学习优化。

优化参数:
- RSI周期: {period}
- 超卖阈值: {oversold}
- 超买阈值: {overbought}

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

import pandas as pd
import numpy as np


class {self._to_class_name(strategy_name)}:
    """ML优化的RSI策略"""
    
    def __init__(self):
        self.period = {period}
        self.oversold = {oversold}
        self.overbought = {overbought}
        self.name = "{strategy_name}"
    
    def calculate_rsi(self, prices: pd.Series) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=self.period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=self.period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """生成RSI交易信号"""
        rsi = self.calculate_rsi(data['close'])
        signals = pd.Series(0, index=data.index)
        
        signals[rsi < self.oversold] = 1   # 超卖买入
        signals[rsi > self.overbought] = -1  # 超买卖出
        
        return signals


if __name__ == "__main__":
    strategy = {self._to_class_name(strategy_name)}()
    print(f"RSI策略: 周期={{strategy.period}}, 超卖={{strategy.oversold}}, 超买={{strategy.overbought}}")
'''
        
        filename = f"{strategy_name.lower().replace(' ', '_')}_strategy.py"
        filepath = os.path.join(self.output_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(strategy_code)

        return filepath
    
    def _generate_bollinger_strategy(self, strategy_name: str, params: Dict[str, Any],
                                   performance_metrics: Dict[str, float], description: str) -> str:
        """生成布林带策略文件"""
        
        window = params.get('window', 20)
        std_dev = params.get('std_dev', 2.0)
        
        strategy_code = f'''#!/usr/bin/env python3
"""
{strategy_name} - ML优化布林带策略

布林带突破策略，参数通过机器学习优化。

优化参数:
- 均线窗口: {window}
- 标准差倍数: {std_dev}

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

import pandas as pd
import numpy as np


class {self._to_class_name(strategy_name)}:
    """ML优化的布林带策略"""
    
    def __init__(self):
        self.window = {window}
        self.std_dev = {std_dev}
        self.name = "{strategy_name}"
    
    def calculate_bollinger_bands(self, prices: pd.Series) -> tuple:
        """计算布林带"""
        middle = prices.rolling(window=self.window).mean()
        std = prices.rolling(window=self.window).std()
        upper = middle + (std * self.std_dev)
        lower = middle - (std * self.std_dev)
        return upper, middle, lower
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """生成布林带交易信号"""
        upper, middle, lower = self.calculate_bollinger_bands(data['close'])
        signals = pd.Series(0, index=data.index)
        
        signals[data['close'] < lower] = 1   # 价格低于下轨买入
        signals[data['close'] > upper] = -1  # 价格高于上轨卖出
        
        return signals


if __name__ == "__main__":
    strategy = {self._to_class_name(strategy_name)}()
    print(f"布林带策略: 窗口={{strategy.window}}, 标准差={{strategy.std_dev}}")
'''
        
        filename = f"{strategy_name.lower().replace(' ', '_')}_strategy.py"
        filepath = os.path.join(self.output_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(strategy_code)

        return filepath
    
    def _generate_generic_strategy(self, strategy_name: str, params: Dict[str, Any],
                                 performance_metrics: Dict[str, float], description: str) -> str:
        """生成通用策略文件"""
        
        strategy_code = f'''#!/usr/bin/env python3
"""
{strategy_name} - ML优化策略

参数: {params}
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

import pandas as pd
import numpy as np


class {self._to_class_name(strategy_name)}:
    """ML优化策略"""
    
    def __init__(self):
        self.params = {params}
        self.name = "{strategy_name}"
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """生成交易信号 - 需要根据具体策略实现"""
        signals = pd.Series(0, index=data.index)
        # TODO: 实现具体的信号生成逻辑
        return signals


if __name__ == "__main__":
    strategy = {self._to_class_name(strategy_name)}()
    print(f"策略参数: {{strategy.params}}")
'''
        
        filename = f"{strategy_name.lower().replace(' ', '_')}_strategy.py"
        filepath = os.path.join(self.output_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(strategy_code)

        return filepath
    
    def _to_class_name(self, strategy_name: str) -> str:
        """将策略名称转换为类名"""
        # 移除特殊字符，转换为驼峰命名
        import re
        name = re.sub(r'[^a-zA-Z0-9_]', '_', strategy_name)
        parts = name.split('_')
        class_name = ''.join(word.capitalize() for word in parts if word)
        return class_name + 'Strategy' if not class_name.endswith('Strategy') else class_name


if __name__ == "__main__":
    # 测试策略文件生成
    generator = StrategyFileGenerator()
    
    # 示例参数
    test_params = {'short_window': 27, 'long_window': 200}
    test_metrics = {
        'total_return': 0.15,
        'sharpe_ratio': 1.2,
        'max_drawdown': -0.08,
        'win_rate': 0.65
    }
    
    filename = generator.generate_strategy_file(
        strategy_name="OptimizedDualMA",
        params=test_params,
        performance_metrics=test_metrics,
        description="通过遗传算法优化的双均线策略，在1000天历史数据上训练得出最优参数组合。"
    )
    
    print(f"策略文件已生成: {filename}")
