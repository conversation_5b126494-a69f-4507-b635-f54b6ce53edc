"""
遗传算法优化器

使用遗传算法优化策略参数。
"""

import numpy as np
import pandas as pd
from typing import Dict, Tuple, List, Callable, Any
from dataclasses import dataclass


@dataclass
class Individual:
    """个体类"""
    params: Dict[str, float]
    fitness: float = 0.0


class GeneticOptimizer:
    """遗传算法优化器"""
    
    def __init__(self, population_size: int = 50, generations: int = 100,
                 mutation_rate: float = 0.1, crossover_rate: float = 0.8):
        """
        初始化遗传算法优化器
        
        Args:
            population_size: 种群大小
            generations: 进化代数
            mutation_rate: 变异率
            crossover_rate: 交叉率
        """
        self.population_size = population_size
        self.generations = generations
        self.mutation_rate = mutation_rate
        self.crossover_rate = crossover_rate
        
        self.population: List[Individual] = []
        self.best_individual: Individual = None
        self.fitness_history: List[float] = []
    
    def optimize(self, param_ranges: Dict[str, Tuple[float, float]], 
                fitness_func: Callable[[Dict[str, float]], float]) -> Dict[str, float]:
        """
        执行遗传算法优化
        
        Args:
            param_ranges: 参数范围字典
            fitness_func: 适应度函数
            
        Returns:
            最优参数
        """
        # 初始化种群
        self._initialize_population(param_ranges)
        
        # 进化过程
        for generation in range(self.generations):
            # 计算适应度
            self._evaluate_population(fitness_func)
            
            # 记录最佳个体
            current_best = max(self.population, key=lambda x: x.fitness)
            if self.best_individual is None or current_best.fitness > self.best_individual.fitness:
                self.best_individual = Individual(
                    params=current_best.params.copy(),
                    fitness=current_best.fitness
                )
            
            self.fitness_history.append(self.best_individual.fitness)
            
            # 选择、交叉、变异
            new_population = self._evolve_population(param_ranges)
            self.population = new_population
        
        return self.best_individual.params
    
    def _initialize_population(self, param_ranges: Dict[str, Tuple[float, float]]):
        """初始化种群"""
        self.population = []
        
        for _ in range(self.population_size):
            params = {}
            for param_name, (min_val, max_val) in param_ranges.items():
                params[param_name] = np.random.uniform(min_val, max_val)
            
            self.population.append(Individual(params=params))
    
    def _evaluate_population(self, fitness_func: Callable[[Dict[str, float]], float]):
        """评估种群适应度"""
        for individual in self.population:
            individual.fitness = fitness_func(individual.params)
    
    def _evolve_population(self, param_ranges: Dict[str, Tuple[float, float]]) -> List[Individual]:
        """进化种群"""
        new_population = []
        
        # 保留最佳个体（精英策略）
        best_individual = max(self.population, key=lambda x: x.fitness)
        new_population.append(Individual(
            params=best_individual.params.copy(),
            fitness=best_individual.fitness
        ))
        
        # 生成新个体
        while len(new_population) < self.population_size:
            # 选择父母
            parent1 = self._tournament_selection()
            parent2 = self._tournament_selection()
            
            # 交叉
            if np.random.random() < self.crossover_rate:
                child1, child2 = self._crossover(parent1, parent2)
            else:
                child1, child2 = parent1, parent2
            
            # 变异
            child1 = self._mutate(child1, param_ranges)
            child2 = self._mutate(child2, param_ranges)
            
            new_population.extend([child1, child2])
        
        return new_population[:self.population_size]
    
    def _tournament_selection(self, tournament_size: int = 3) -> Individual:
        """锦标赛选择"""
        tournament = np.random.choice(self.population, tournament_size, replace=False)
        return max(tournament, key=lambda x: x.fitness)
    
    def _crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """交叉操作"""
        child1_params = {}
        child2_params = {}
        
        for param_name in parent1.params.keys():
            if np.random.random() < 0.5:
                child1_params[param_name] = parent1.params[param_name]
                child2_params[param_name] = parent2.params[param_name]
            else:
                child1_params[param_name] = parent2.params[param_name]
                child2_params[param_name] = parent1.params[param_name]
        
        return Individual(params=child1_params), Individual(params=child2_params)
    
    def _mutate(self, individual: Individual, 
               param_ranges: Dict[str, Tuple[float, float]]) -> Individual:
        """变异操作"""
        mutated_params = individual.params.copy()
        
        for param_name, (min_val, max_val) in param_ranges.items():
            if np.random.random() < self.mutation_rate:
                # 高斯变异
                current_val = mutated_params[param_name]
                mutation_strength = (max_val - min_val) * 0.1
                new_val = current_val + np.random.normal(0, mutation_strength)
                
                # 确保在范围内
                new_val = np.clip(new_val, min_val, max_val)
                mutated_params[param_name] = new_val
        
        return Individual(params=mutated_params)
