"""
高级机器学习策略模块

本模块包含基于机器学习算法的高级交易策略实现，包括：
- DecisionTreeStrategy: 决策树策略
- RandomForestStrategy: 随机森林策略  
- XGBoostStrategy: XGBoost梯度提升策略
- LSTMStrategy: LSTM神经网络策略

所有策略都继承自BaseStrategy基类，提供统一的接口和功能。
"""

# 导入策略类，处理可能的依赖缺失
try:
    from .decision_tree_strategy import DecisionTreeStrategy
    DECISION_TREE_AVAILABLE = True
except ImportError as e:
    DECISION_TREE_AVAILABLE = False
    DecisionTreeStrategy = None
    print(f"Warning: DecisionTreeStrategy not available: {e}")

try:
    from .random_forest_strategy import RandomForestStrategy
    RANDOM_FOREST_AVAILABLE = True
except ImportError as e:
    RANDOM_FOREST_AVAILABLE = False
    RandomForestStrategy = None
    print(f"Warning: RandomForestStrategy not available: {e}")

try:
    from .xgboost_strategy import XGBoostStrategy
    XGBOOST_AVAILABLE = True
except ImportError as e:
    XGBOOST_AVAILABLE = False
    XGBoostStrategy = None
    print(f"Warning: XGBoostStrategy not available: {e}")

try:
    from .lstm_strategy import LSTMStrategy
    LSTM_AVAILABLE = True
except ImportError as e:
    LSTM_AVAILABLE = False
    LSTMStrategy = None
    print(f"Warning: LSTMStrategy not available: {e}")

# 导出可用的策略
__all__ = []

if DECISION_TREE_AVAILABLE:
    __all__.append('DecisionTreeStrategy')

if RANDOM_FOREST_AVAILABLE:
    __all__.append('RandomForestStrategy')

if XGBOOST_AVAILABLE:
    __all__.append('XGBoostStrategy')

if LSTM_AVAILABLE:
    __all__.append('LSTMStrategy')

# 策略注册表
AVAILABLE_ML_STRATEGIES = {
    'decision_tree': DecisionTreeStrategy if DECISION_TREE_AVAILABLE else None,
    'random_forest': RandomForestStrategy if RANDOM_FOREST_AVAILABLE else None,
    'xgboost': XGBoostStrategy if XGBOOST_AVAILABLE else None,
    'lstm': LSTMStrategy if LSTM_AVAILABLE else None
}

def get_available_strategies():
    """获取所有可用的ML策略"""
    return {k: v for k, v in AVAILABLE_ML_STRATEGIES.items() if v is not None}

def get_strategy_class(strategy_name: str):
    """根据策略名称获取策略类"""
    available = get_available_strategies()
    if strategy_name not in available:
        raise ValueError(f"Strategy '{strategy_name}' not available. Available strategies: {list(available.keys())}")
    return available[strategy_name]
