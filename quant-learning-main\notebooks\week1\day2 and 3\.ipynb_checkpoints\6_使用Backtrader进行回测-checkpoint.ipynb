{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Day 2：基于交易量的量化指标 - 使用Backtrader进行回测\n", "\n", "本notebook展示如何使用Backtrader框架对之前定义的量价结合策略进行更系统化、专业化的回测。Backtrader是一个功能强大的Python回测框架，提供了更完善的回测环境和分析工具。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Backtrader简介和安装\n", "\n", "Backtrader是一个用Python编写的开源回测框架，具有以下特点：\n", "\n", "- **易于使用**：API简洁明了，上手难度低\n", "- **功能完善**：支持多种指标、策略和分析方法\n", "- **可扩展性强**：可以自定义指标、策略、分析器等\n", "- **支持多种数据源**：CSV、Pandas DataFrame、实时数据等\n", "- **支持多资产回测**：可以同时回测多种资产\n", "- **内置绘图功能**：可视化策略执行过程和结果\n", "\n", "首先，我们需要安装Backtrader库："]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# 安装backtrader（如果尚未安装）\n", "# !pip install backtrader\n", "# !pip install matplotlib==3.2.2  # Backtrader可能与最新版matplotlib不兼容\n", "\n", "# 导入必要的库\n", "import backtrader as bt\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os\n", "import warnings\n", "import datetime\n", "from dotenv import load_dotenv, find_dotenv\n", "\n", "# Find the .env file in the parent directory\n", "dotenv_path = find_dotenv(\"../../.env\")\n", "\n", "# Load it explicitly\n", "load_dotenv(dotenv_path)\n", "\n", "# 忽略警告信息\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['PingFang HK']  # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. 数据获取函数"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def get_ts_data(ts_token, ts_code, start_date, end_date, freq='30min'):\n", "    # 文件路径\n", "    file_path = f'./data/{ts_code}-{start_date}-{end_date}-{freq}.csv'\n", "    \n", "    # 检查本地是否已存在该文件\n", "    if os.path.exists(file_path):\n", "        print(f\"从本地文件加载数据: {file_path}\")\n", "        df = pd.read_csv(file_path, parse_dates=['trade_time'])  # 读取并解析时间列\n", "        return df\n", "    \n", "    # 设置Tushare token\n", "    ts.set_token(ts_token)\n", "    pro = ts.pro_api()\n", "\n", "    # 获取数据\n", "    df = ts.pro_bar(\n", "        ts_code=ts_code,\n", "        start_date=start_date,\n", "        end_date=end_date,\n", "        freq=freq,  \n", "        asset='E',       # 股票类型\n", "        adj='qfq',       # 前复权\n", "    )\n", "\n", "    if df is None or df.empty:\n", "        print(\"从 Tushare 获取的数据为空，请检查权限或参数设置。\")\n", "        return None\n", "\n", "    # 创建目录（如果不存在）\n", "    os.makedirs('./data', exist_ok=True)\n", "\n", "    # 保存数据到本地文件\n", "    df.to_csv(file_path, index=False)\n", "    print(f\"数据已保存至: {file_path}\")\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["从本地文件加载数据: ./data/002745.SZ-2022-03-03-2025-02-28-30min.csv\n"]}], "source": ["ts_token = os.getenv('TUSHARE_API_KEY')\n", "ts_code = '002745.SZ'\n", "start_date = '2022-03-03'\n", "end_date = '2025-02-28'\n", "\n", "stock_data = get_ts_data(ts_token, ts_code, start_date, end_date, freq='30min')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["stock_data = stock_data.sort_values('trade_time').reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>trade_time</th>\n", "      <th>close</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>vol</th>\n", "      <th>amount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 09:30:00</td>\n", "      <td>11.73</td>\n", "      <td>11.74</td>\n", "      <td>11.74</td>\n", "      <td>11.73</td>\n", "      <td>19700.0</td>\n", "      <td>257449.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 10:00:00</td>\n", "      <td>11.61</td>\n", "      <td>11.74</td>\n", "      <td>11.75</td>\n", "      <td>11.59</td>\n", "      <td>3537808.0</td>\n", "      <td>45884940.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 10:30:00</td>\n", "      <td>11.62</td>\n", "      <td>11.61</td>\n", "      <td>11.65</td>\n", "      <td>11.60</td>\n", "      <td>2231278.0</td>\n", "      <td>28897008.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 11:00:00</td>\n", "      <td>11.64</td>\n", "      <td>11.62</td>\n", "      <td>11.65</td>\n", "      <td>11.61</td>\n", "      <td>673100.0</td>\n", "      <td>8719710.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 11:30:00</td>\n", "      <td>11.61</td>\n", "      <td>11.63</td>\n", "      <td>11.65</td>\n", "      <td>11.61</td>\n", "      <td>1379400.0</td>\n", "      <td>17854952.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     ts_code          trade_time  close   open   high    low        vol  \\\n", "0  002745.SZ 2022-03-03 09:30:00  11.73  11.74  11.74  11.73    19700.0   \n", "1  002745.SZ 2022-03-03 10:00:00  11.61  11.74  11.75  11.59  3537808.0   \n", "2  002745.SZ 2022-03-03 10:30:00  11.62  11.61  11.65  11.60  2231278.0   \n", "3  002745.SZ 2022-03-03 11:00:00  11.64  11.62  11.65  11.61   673100.0   \n", "4  002745.SZ 2022-03-03 11:30:00  11.61  11.63  11.65  11.61  1379400.0   \n", "\n", "       amount  \n", "0    257449.0  \n", "1  45884940.0  \n", "2  28897008.0  \n", "3   8719710.0  \n", "4  17854952.0  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["stock_data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 3. DataFrame转换为Backtrader Feed的函数"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# 1. 将DataFrame转换为Backtrader Feed的函数\n", "def df_to_btfeed(df):\n", "    \"\"\"\n", "    将Pandas DataFrame转换为Backtrader的数据源\n", "    \n", "    参数:\n", "    df (pandas.DataFrame): 包含OHLCV数据的DataFrame\n", "    \n", "    返回:\n", "    backtrader.feeds.PandasData: 可用于Backtrader的数据源\n", "    \"\"\"\n", "    # 确保索引是datetime类型\n", "    if not isinstance(df.index, pd.DatetimeIndex):\n", "        df = df.copy()\n", "        df['datetime'] = pd.to_datetime(df['trade_time'])\n", "        df.set_index('datetime', inplace=True)\n", "    \n", "    # 创建用于backtrader的PandasData类\n", "    class PandasDataCustom(bt.feeds.PandasData):\n", "        params = (\n", "            ('datetime', None),  # 已设置为索引\n", "            ('open', 'open'),\n", "            ('high', 'high'),\n", "            ('low', 'low'),\n", "            ('close', 'close'),\n", "            ('volume', 'vol'),\n", "            ('openinterest', None)  # 不使用持仓量数据\n", "        )\n", "    \n", "    # 返回backtrader的数据源\n", "    return PandasDataCustom(dataname=df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 4. 回测执行函数"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# 3. 回测函数\n", "def run_backtest(df, strategy=VolumeBreakoutStrategy, \n", "                 strategy_params=None, initial_cash=100000.0,\n", "                 commission=0.001, plot=True, plot_args=None):\n", "    \"\"\"\n", "    运行回测\n", "    \n", "    参数:\n", "    df (pandas.DataFrame): 包含OHLCV数据的DataFrame\n", "    strategy (backtrader.Strategy): 回测使用的策略类\n", "    strategy_params (dict): 策略参数字典\n", "    initial_cash (float): 初始资金\n", "    commission (float): 交易佣金比例\n", "    plot (bool): 是否绘制回测结果图表\n", "    plot_args (dict): 图表参数字典，可包含:\n", "        - start_date (str): 绘图开始日期 'YYYY-MM-DD'\n", "        - end_date (str): 绘图结束日期 'YYYY-MM-DD'\n", "        - style (str): 图表风格，如'candle', 'bar', 'line'等\n", "        - width (int): 图表宽度\n", "        - height (int): 图表高度\n", "        - num_plots (int): 只显示最近的N个交易点\n", "        - skip_plotlines (bool): 是否跳过绘制交易线\n", "    \n", "    返回:\n", "    dict: 包含回测结果的字典\n", "    \"\"\"\n", "    # 创建cerebro引擎\n", "    cerebro = bt.<PERSON><PERSON><PERSON>()\n", "    \n", "    # 添加策略\n", "    if strategy_params:\n", "        cerebro.addstrategy(strategy, **strategy_params)\n", "    else:\n", "        cerebro.addstrategy(strategy)\n", "    \n", "    # 添加数据\n", "    # 如果指定了绘图时间范围，则提前过滤数据\n", "    plot_df = df.copy()\n", "    if plot_args and 'start_date' in plot_args:\n", "        start_date = pd.to_datetime(plot_args['start_date'])\n", "        if isinstance(plot_df.index, pd.DatetimeIndex):\n", "            plot_df = plot_df[plot_df.index >= start_date]\n", "        elif 'trade_time' in plot_df.columns:\n", "            plot_df = plot_df[pd.to_datetime(plot_df['trade_time']) >= start_date]\n", "    \n", "    if plot_args and 'end_date' in plot_args:\n", "        end_date = pd.to_datetime(plot_args['end_date'])\n", "        if isinstance(plot_df.index, pd.DatetimeIndex):\n", "            plot_df = plot_df[plot_df.index <= end_date]\n", "        elif 'trade_time' in plot_df.columns:\n", "            plot_df = plot_df[pd.to_datetime(plot_df['trade_time']) <= end_date]\n", "            \n", "    # 如果只需显示最近N个交易点\n", "    if plot_args and 'num_plots' in plot_args:\n", "        num_plots = plot_args['num_plots']\n", "        if len(plot_df) > num_plots:\n", "            plot_df = plot_df.iloc[-num_plots:]\n", "    \n", "    # 正常回测使用完整数据\n", "    data = df_to_btfeed(df)\n", "    cerebro.adddata(data)\n", "    \n", "    # 设置初始资金和佣金\n", "    cerebro.broker.setcash(initial_cash)\n", "    cerebro.broker.setcommission(commission=commission)\n", "    \n", "    # 添加分析器\n", "    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')\n", "    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')\n", "    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')\n", "    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trade')\n", "    \n", "    # 运行回测\n", "    print(f'初始资金: {initial_cash:.2f}')\n", "    results = cerebro.run()\n", "    strat = results[0]\n", "    \n", "    # 获取回测结果数据\n", "    final_value = cerebro.broker.getvalue()\n", "    total_return = (final_value - initial_cash) / initial_cash * 100\n", "    \n", "    sharpe_ratio = strat.analyzers.sharpe.get_analysis().get('sharperatio', 0.0)\n", "    if np.isnan(sharpe_ratio):\n", "        sharpe_ratio = 0.0\n", "    \n", "    max_drawdown = strat.analyzers.drawdown.get_analysis().get('max', {}).get('drawdown', 0.0)\n", "    \n", "    # 获取交易分析\n", "    trade_analysis = strat.analyzers.trade.get_analysis()\n", "    \n", "    total_trades = trade_analysis.get('total', {}).get('total', 0)\n", "    \n", "    winning_trades = trade_analysis.get('won', {}).get('total', 0)\n", "    losing_trades = trade_analysis.get('lost', {}).get('total', 0)\n", "    \n", "    if total_trades > 0:\n", "        win_rate = winning_trades / total_trades * 100\n", "    else:\n", "        win_rate = 0.0\n", "    \n", "    # 打印回测结果\n", "    print(f'最终资金: {final_value:.2f}')\n", "    print(f'总收益率: {total_return:.2f}%')\n", "    print(f'夏普比率: {sharpe_ratio:.2f}')\n", "    print(f'最大回撤: {max_drawdown:.2f}%')\n", "    print(f'总交易次数: {total_trades}')\n", "    print(f'胜率: {win_rate:.2f}%')\n", "    \n", "    # 绘制回测结果\n", "    if plot:\n", "        try:\n", "            # 获取默认或自定义图表参数\n", "            if plot_args is None:\n", "                plot_args = {}\n", "            \n", "            style = plot_args.get('style', 'candle')\n", "            width = plot_args.get('width', 1200)  # 更宽的默认宽度\n", "            height = plot_args.get('height', 800)  # 较合理的默认高度\n", "            \n", "            # 处理绘图选项\n", "            skip_plotlines = plot_args.get('skip_plotlines', False)\n", "            \n", "            plot_kwargs = {\n", "                'style': style,\n", "                'barup': 'red',  # 中国市场习惯\n", "                'bardown': 'green',\n", "                'width': width,\n", "                'height': height,\n", "                'volume': True\n", "            }\n", "            \n", "            # 如果需要跳过绘制交易线\n", "            if skip_plotlines:\n", "                plot_kwargs['plotlines'] = False\n", "            \n", "            # 使用过滤后的数据创建新的cerebro进行绘图\n", "            if len(plot_df) < len(df):\n", "                plot_cerebro = bt.Ce<PERSON>bro()\n", "                \n", "                # 添加策略，但禁用打印输出\n", "                class QuietStrategy(strategy):\n", "                    def __init__(self):\n", "                        super().__init__()\n", "                        \n", "                    def log(self, txt, dt=None):\n", "                        pass\n", "                        \n", "                if strategy_params:\n", "                    plot_cerebro.addstrategy(QuietStrategy, **strategy_params)\n", "                else:\n", "                    plot_cerebro.addstrategy(QuietStrategy)\n", "                \n", "                # 添加过滤后的数据\n", "                plot_data = df_to_btfeed(plot_df)\n", "                plot_cerebro.adddata(plot_data)\n", "                \n", "                # 设置与原回测相同的参数\n", "                plot_cerebro.broker.setcash(initial_cash)\n", "                plot_cerebro.broker.setcommission(commission=commission)\n", "                \n", "                # 运行并绘图\n", "                %matplotlib inline\n", "                plot_cerebro.run()\n", "                plot_cerebro.plot(**plot_kwargs,iplot=False)\n", "            else:\n", "                # 直接使用原cerebro绘图\n", "                 %matplotlib inline\n", "                cerebro.plot(**plot_kwargs,iplot=False)\n", "        except Exception as e:\n", "            print(f\"绘图错误: {e}\")\n", "            print(\"提示: 尝试减少数据量或使用plot_args参数控制绘图范围\")\n", "    \n", "    # 返回回测结果\n", "    return {\n", "        'initial_cash': initial_cash,\n", "        'final_value': final_value,\n", "        'total_return': total_return,\n", "        'sharpe_ratio': sharpe_ratio,\n", "        'max_drawdown': max_drawdown,\n", "        'total_trades': total_trades,\n", "        'winning_trades': winning_trades,\n", "        'losing_trades': losing_trades,\n", "        'win_rate': win_rate,\n", "        'strat': strat  # 返回策略实例以便进一步分析\n", "    }\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 5: 交易量突破策略"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# 2. 交易量突破策略\n", "class VolumeBreakoutStrategy(bt.Strategy):\n", "    \"\"\"\n", "    交易量突破策略\n", "    \n", "    参数:\n", "    volume_period (int): 用于计算平均交易量的周期数\n", "    volume_mult (float): 触发买入信号的交易量倍数\n", "    exit_bars (int): 持有的bar数量，过后卖出\n", "    stop_loss (float): 止损比例 (0.05 = 5%)\n", "    take_profit (float): 止盈比例 (0.10 = 10%)\n", "    \"\"\"\n", "    params = (\n", "        ('volume_period', 20),   # 计算平均交易量的周期\n", "        ('volume_mult', 2.0),    # 交易量倍数阈值\n", "        ('exit_bars', 5),        # 持有的bar数量\n", "        ('stop_loss', 0.05),     # 止损比例\n", "        ('take_profit', 0.10),   # 止盈比例\n", "    )\n", "    \n", "    def __init__(self):\n", "        # 初始化变量\n", "        self.volume_ma = bt.indicators.SimpleMovingAverage(\n", "            self.data.volume, period=self.params.volume_period)\n", "        \n", "        # 跟踪持仓和买入价格\n", "        self.bar_executed = None\n", "        self.buy_price = None\n", "        \n", "    def next(self):\n", "        # 如果没有持仓\n", "        if not self.position:\n", "            # 检查交易量是否突破\n", "            if self.data.volume[0] > self.volume_ma[0] * self.params.volume_mult:\n", "                self.buy()\n", "                self.bar_executed = len(self)\n", "                self.buy_price = self.data.close[0]\n", "                print(f'BUY: {self.data.datetime.date(0)} | 价格: {self.data.close[0]:.2f} | 交易量: {self.data.volume[0]:,.0f} | 平均交易量: {self.volume_ma[0]:,.0f}')\n", "                \n", "        # 如果有持仓，检查是否应该卖出\n", "        else:\n", "            # 基于持有期的退出策略\n", "            if len(self) >= (self.bar_executed + self.params.exit_bars):\n", "                self.sell()\n", "                print(f'SELL (时间退出): {self.data.datetime.date(0)} | 价格: {self.data.close[0]:.2f}')\n", "                return\n", "            \n", "            # 止损退出策略\n", "            if self.data.close[0] < self.buy_price * (1 - self.params.stop_loss):\n", "                self.sell()\n", "                print(f'SELL (止损): {self.data.datetime.date(0)} | 价格: {self.data.close[0]:.2f}')\n", "                return\n", "                \n", "            # 止盈退出策略\n", "            if self.data.close[0] > self.buy_price * (1 + self.params.take_profit):\n", "                self.sell()\n", "                print(f'SELL (止盈): {self.data.datetime.date(0)} | 价格: {self.data.close[0]:.2f}')\n", "                return\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "# 6. 执行回测"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>trade_time</th>\n", "      <th>close</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>vol</th>\n", "      <th>amount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 09:30:00</td>\n", "      <td>11.73</td>\n", "      <td>11.74</td>\n", "      <td>11.74</td>\n", "      <td>11.73</td>\n", "      <td>19700.0</td>\n", "      <td>257449.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 10:00:00</td>\n", "      <td>11.61</td>\n", "      <td>11.74</td>\n", "      <td>11.75</td>\n", "      <td>11.59</td>\n", "      <td>3537808.0</td>\n", "      <td>45884940.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 10:30:00</td>\n", "      <td>11.62</td>\n", "      <td>11.61</td>\n", "      <td>11.65</td>\n", "      <td>11.60</td>\n", "      <td>2231278.0</td>\n", "      <td>28897008.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 11:00:00</td>\n", "      <td>11.64</td>\n", "      <td>11.62</td>\n", "      <td>11.65</td>\n", "      <td>11.61</td>\n", "      <td>673100.0</td>\n", "      <td>8719710.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 11:30:00</td>\n", "      <td>11.61</td>\n", "      <td>11.63</td>\n", "      <td>11.65</td>\n", "      <td>11.61</td>\n", "      <td>1379400.0</td>\n", "      <td>17854952.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6520</th>\n", "      <td>002745.SZ</td>\n", "      <td>2025-02-28 11:30:00</td>\n", "      <td>9.04</td>\n", "      <td>9.05</td>\n", "      <td>9.05</td>\n", "      <td>9.00</td>\n", "      <td>2877500.0</td>\n", "      <td>25952448.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6521</th>\n", "      <td>002745.SZ</td>\n", "      <td>2025-02-28 13:30:00</td>\n", "      <td>8.96</td>\n", "      <td>9.03</td>\n", "      <td>9.03</td>\n", "      <td>8.92</td>\n", "      <td>4458100.0</td>\n", "      <td>40014516.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6522</th>\n", "      <td>002745.SZ</td>\n", "      <td>2025-02-28 14:00:00</td>\n", "      <td>8.91</td>\n", "      <td>8.96</td>\n", "      <td>8.96</td>\n", "      <td>8.88</td>\n", "      <td>4356200.0</td>\n", "      <td>38851072.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6523</th>\n", "      <td>002745.SZ</td>\n", "      <td>2025-02-28 14:30:00</td>\n", "      <td>8.79</td>\n", "      <td>8.90</td>\n", "      <td>8.90</td>\n", "      <td>8.75</td>\n", "      <td>7360100.0</td>\n", "      <td>64833540.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6524</th>\n", "      <td>002745.SZ</td>\n", "      <td>2025-02-28 15:00:00</td>\n", "      <td>8.75</td>\n", "      <td>8.79</td>\n", "      <td>8.80</td>\n", "      <td>8.70</td>\n", "      <td>8561578.0</td>\n", "      <td>74855300.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6525 rows × 8 columns</p>\n", "</div>"], "text/plain": ["        ts_code          trade_time  close   open   high    low        vol  \\\n", "0     002745.SZ 2022-03-03 09:30:00  11.73  11.74  11.74  11.73    19700.0   \n", "1     002745.SZ 2022-03-03 10:00:00  11.61  11.74  11.75  11.59  3537808.0   \n", "2     002745.SZ 2022-03-03 10:30:00  11.62  11.61  11.65  11.60  2231278.0   \n", "3     002745.SZ 2022-03-03 11:00:00  11.64  11.62  11.65  11.61   673100.0   \n", "4     002745.SZ 2022-03-03 11:30:00  11.61  11.63  11.65  11.61  1379400.0   \n", "...         ...                 ...    ...    ...    ...    ...        ...   \n", "6520  002745.SZ 2025-02-28 11:30:00   9.04   9.05   9.05   9.00  2877500.0   \n", "6521  002745.SZ 2025-02-28 13:30:00   8.96   9.03   9.03   8.92  4458100.0   \n", "6522  002745.SZ 2025-02-28 14:00:00   8.91   8.96   8.96   8.88  4356200.0   \n", "6523  002745.SZ 2025-02-28 14:30:00   8.79   8.90   8.90   8.75  7360100.0   \n", "6524  002745.SZ 2025-02-28 15:00:00   8.75   8.79   8.80   8.70  8561578.0   \n", "\n", "          amount  \n", "0       257449.0  \n", "1     45884940.0  \n", "2     28897008.0  \n", "3      8719710.0  \n", "4     17854952.0  \n", "...          ...  \n", "6520  25952448.0  \n", "6521  40014516.0  \n", "6522  38851072.0  \n", "6523  64833540.0  \n", "6524  74855300.0  \n", "\n", "[6525 rows x 8 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["stock_data"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["初始资金: 100000.00\n", "BUY: 2022-03-07 | 价格: 11.25 | 交易量: 3,838,782 | 平均交易量: 1,418,156\n", "SELL (时间退出): 2022-03-07 | 价格: 11.11\n", "BUY: 2022-03-08 | 价格: 11.01 | 交易量: 4,226,800 | 平均交易量: 1,775,308\n", "SELL (时间退出): 2022-03-08 | 价格: 10.69\n", "BUY: 2022-03-09 | 价格: 10.29 | 交易量: 5,506,274 | 平均交易量: 2,395,508\n", "SELL (时间退出): 2022-03-10 | 价格: 10.95\n", "BUY: 2022-03-10 | 价格: 10.86 | 交易量: 7,569,304 | 平均交易量: 2,495,138\n", "SELL (时间退出): 2022-03-10 | 价格: 10.79\n", "BUY: 2022-03-14 | 价格: 10.77 | 交易量: 3,489,557 | 平均交易量: 1,416,869\n", "SELL (时间退出): 2022-03-14 | 价格: 10.73\n", "BUY: 2022-03-15 | 价格: 10.26 | 交易量: 4,213,000 | 平均交易量: 1,430,419\n", "SELL (时间退出): 2022-03-15 | 价格: 10.29\n", "BUY: 2022-03-15 | 价格: 9.96 | 交易量: 4,247,326 | 平均交易量: 1,785,138\n", "SELL (时间退出): 2022-03-16 | 价格: 9.95\n", "BUY: 2022-03-17 | 价格: 10.40 | 交易量: 5,854,600 | 平均交易量: 2,394,718\n", "SELL (时间退出): 2022-03-17 | 价格: 10.58\n", "BUY: 2022-03-21 | 价格: 10.53 | 交易量: 3,471,113 | 平均交易量: 1,409,167\n", "SELL (时间退出): 2022-03-21 | 价格: 10.53\n", "BUY: 2022-03-23 | 价格: 10.60 | 交易量: 3,491,400 | 平均交易量: 1,570,932\n", "SELL (时间退出): 2022-03-23 | 价格: 10.51\n", "BUY: 2022-03-24 | 价格: 10.36 | 交易量: 3,294,400 | 平均交易量: 1,497,812\n", "SELL (时间退出): 2022-03-24 | 价格: 10.33\n", "BUY: 2022-03-28 | 价格: 10.25 | 交易量: 5,119,263 | 平均交易量: 1,609,294\n", "SELL (时间退出): 2022-03-28 | 价格: 10.41\n", "BUY: 2022-03-30 | 价格: 10.42 | 交易量: 4,937,232 | 平均交易量: 1,454,347\n", "SELL (时间退出): 2022-03-30 | 价格: 10.45\n", "BUY: 2022-04-06 | 价格: 10.53 | 交易量: 4,483,900 | 平均交易量: 1,817,156\n", "SELL (时间退出): 2022-04-06 | 价格: 10.50\n", "BUY: 2022-04-07 | 价格: 10.24 | 交易量: 3,531,238 | 平均交易量: 1,598,351\n", "SELL (时间退出): 2022-04-08 | 价格: 9.96\n", "BUY: 2022-04-08 | 价格: 9.80 | 交易量: 5,463,000 | 平均交易量: 2,543,068\n", "SELL (时间退出): 2022-04-11 | 价格: 9.60\n", "BUY: 2022-04-15 | 价格: 9.40 | 交易量: 2,127,000 | 平均交易量: 972,869\n", "SELL (时间退出): 2022-04-15 | 价格: 9.35\n", "BUY: 2022-04-18 | 价格: 9.31 | 交易量: 2,688,200 | 平均交易量: 1,147,947\n", "SELL (时间退出): 2022-04-18 | 价格: 9.38\n", "BUY: 2022-04-19 | 价格: 9.56 | 交易量: 4,684,100 | 平均交易量: 1,351,110\n", "SELL (时间退出): 2022-04-19 | 价格: 9.57\n", "BUY: 2022-04-21 | 价格: 9.47 | 交易量: 4,485,700 | 平均交易量: 1,270,710\n", "SELL (时间退出): 2022-04-21 | 价格: 9.15\n", "BUY: 2022-04-22 | 价格: 8.94 | 交易量: 4,363,424 | 平均交易量: 2,049,781\n", "SELL (时间退出): 2022-04-22 | 价格: 8.95\n", "BUY: 2022-04-25 | 价格: 8.76 | 交易量: 5,288,312 | 平均交易量: 1,806,660\n", "SELL (时间退出): 2022-04-25 | 价格: 8.62\n", "BUY: 2022-04-26 | 价格: 8.11 | 交易量: 4,728,900 | 平均交易量: 2,004,231\n", "SELL (时间退出): 2022-04-26 | 价格: 8.26\n", "BUY: 2022-04-27 | 价格: 7.84 | 交易量: 4,690,367 | 平均交易量: 2,075,672\n", "SELL (时间退出): 2022-04-27 | 价格: 8.01\n", "BUY: 2022-04-28 | 价格: 7.77 | 交易量: 12,186,244 | 平均交易量: 2,606,599\n", "SELL (时间退出): 2022-04-28 | 价格: 7.68\n", "BUY: 2022-04-29 | 价格: 7.78 | 交易量: 8,689,042 | 平均交易量: 3,859,354\n", "SELL (时间退出): 2022-04-29 | 价格: 7.78\n", "BUY: 2022-05-06 | 价格: 7.64 | 交易量: 5,420,900 | 平均交易量: 2,699,093\n", "SELL (时间退出): 2022-05-06 | 价格: 7.60\n", "BUY: 2022-05-10 | 价格: 7.50 | 交易量: 5,023,500 | 平均交易量: 1,635,641\n", "SELL (时间退出): 2022-05-10 | 价格: 7.62\n", "BUY: 2022-05-11 | 价格: 7.89 | 交易量: 5,549,732 | 平均交易量: 1,943,058\n", "SELL (时间退出): 2022-05-11 | 价格: 7.99\n", "BUY: 2022-05-16 | 价格: 7.86 | 交易量: 4,596,368 | 平均交易量: 1,925,943\n", "SELL (时间退出): 2022-05-16 | 价格: 7.81\n", "BUY: 2022-05-17 | 价格: 7.95 | 交易量: 3,694,472 | 平均交易量: 1,694,645\n", "SELL (时间退出): 2022-05-17 | 价格: 7.94\n", "BUY: 2022-05-19 | 价格: 7.94 | 交易量: 4,152,519 | 平均交易量: 2,011,327\n", "SELL (时间退出): 2022-05-19 | 价格: 8.03\n", "BUY: 2022-05-20 | 价格: 8.24 | 交易量: 5,528,241 | 平均交易量: 2,015,839\n", "SELL (时间退出): 2022-05-20 | 价格: 8.22\n", "BUY: 2022-05-24 | 价格: 8.34 | 交易量: 3,743,985 | 平均交易量: 1,668,412\n", "SELL (时间退出): 2022-05-24 | 价格: 8.13\n", "BUY: 2022-05-24 | 价格: 7.94 | 交易量: 4,122,184 | 平均交易量: 2,059,580\n", "SELL (时间退出): 2022-05-25 | 价格: 7.95\n", "BUY: 2022-05-26 | 价格: 7.87 | 交易量: 4,445,438 | 平均交易量: 1,869,893\n", "SELL (时间退出): 2022-05-26 | 价格: 8.04\n", "BUY: 2022-05-27 | 价格: 8.05 | 交易量: 4,697,800 | 平均交易量: 1,674,106\n", "SELL (时间退出): 2022-05-27 | 价格: 8.05\n", "BUY: 2022-05-30 | 价格: 8.00 | 交易量: 4,196,374 | 平均交易量: 1,757,863\n", "SELL (时间退出): 2022-05-30 | 价格: 8.06\n", "BUY: 2022-05-31 | 价格: 8.19 | 交易量: 4,489,400 | 平均交易量: 1,600,826\n", "SELL (时间退出): 2022-05-31 | 价格: 8.19\n", "BUY: 2022-05-31 | 价格: 8.25 | 交易量: 3,616,897 | 平均交易量: 1,721,654\n", "SELL (时间退出): 2022-06-01 | 价格: 8.33\n", "BUY: 2022-06-06 | 价格: 8.39 | 交易量: 3,959,440 | 平均交易量: 1,782,076\n", "SELL (时间退出): 2022-06-06 | 价格: 8.55\n", "BUY: 2022-06-07 | 价格: 8.59 | 交易量: 10,688,254 | 平均交易量: 2,674,470\n", "SELL (时间退出): 2022-06-07 | 价格: 8.60\n", "BUY: 2022-06-13 | 价格: 8.46 | 交易量: 3,180,117 | 平均交易量: 1,582,142\n", "SELL (时间退出): 2022-06-13 | 价格: 8.37\n", "BUY: 2022-06-14 | 价格: 8.22 | 交易量: 4,212,400 | 平均交易量: 1,712,498\n", "SELL (时间退出): 2022-06-14 | 价格: 8.11\n", "BUY: 2022-06-15 | 价格: 8.50 | 交易量: 6,011,205 | 平均交易量: 2,135,951\n", "SELL (时间退出): 2022-06-15 | 价格: 8.61\n", "BUY: 2022-06-22 | 价格: 8.61 | 交易量: 3,013,018 | 平均交易量: 1,328,811\n", "SELL (时间退出): 2022-06-22 | 价格: 8.55\n", "BUY: 2022-06-23 | 价格: 8.58 | 交易量: 3,836,900 | 平均交易量: 1,603,078\n", "SELL (时间退出): 2022-06-23 | 价格: 8.59\n", "BUY: 2022-06-29 | 价格: 9.02 | 交易量: 5,902,399 | 平均交易量: 2,052,043\n", "SELL (时间退出): 2022-06-29 | 价格: 8.97\n", "BUY: 2022-07-01 | 价格: 8.96 | 交易量: 3,062,700 | 平均交易量: 1,428,227\n", "SELL (时间退出): 2022-07-01 | 价格: 9.07\n", "BUY: 2022-07-04 | 价格: 8.82 | 交易量: 8,831,021 | 平均交易量: 2,121,576\n", "SELL (时间退出): 2022-07-04 | 价格: 8.83\n", "BUY: 2022-07-05 | 价格: 8.90 | 交易量: 5,543,997 | 平均交易量: 2,131,378\n", "SELL (时间退出): 2022-07-05 | 价格: 8.80\n", "BUY: 2022-07-07 | 价格: 8.86 | 交易量: 6,276,527 | 平均交易量: 1,825,861\n", "SELL (时间退出): 2022-07-07 | 价格: 8.79\n", "BUY: 2022-07-08 | 价格: 8.85 | 交易量: 3,899,166 | 平均交易量: 1,814,892\n", "SELL (时间退出): 2022-07-08 | 价格: 8.79\n", "BUY: 2022-07-11 | 价格: 8.46 | 交易量: 5,498,674 | 平均交易量: 1,900,434\n", "SELL (时间退出): 2022-07-11 | 价格: 8.42\n", "BUY: 2022-07-15 | 价格: 7.68 | 交易量: 15,502,934 | 平均交易量: 1,843,642\n", "SELL (时间退出): 2022-07-15 | 价格: 7.72\n", "BUY: 2022-07-18 | 价格: 7.81 | 交易量: 5,788,034 | 平均交易量: 2,841,092\n", "SELL (时间退出): 2022-07-18 | 价格: 8.01\n", "BUY: 2022-07-20 | 价格: 8.39 | 交易量: 11,251,450 | 平均交易量: 2,194,926\n", "SELL (时间退出): 2022-07-20 | 价格: 8.35\n", "BUY: 2022-07-21 | 价格: 8.53 | 交易量: 7,156,409 | 平均交易量: 2,401,889\n", "SELL (止盈): 2022-07-21 | 价格: 9.14\n", "BUY: 2022-07-22 | 价格: 9.24 | 交易量: 31,288,372 | 平均交易量: 6,713,649\n", "SELL (时间退出): 2022-07-22 | 价格: 9.14\n", "BUY: 2022-07-25 | 价格: 8.87 | 交易量: 14,684,300 | 平均交易量: 6,140,568\n", "SELL (时间退出): 2022-07-25 | 价格: 8.81\n", "BUY: 2022-07-28 | 价格: 9.14 | 交易量: 16,589,116 | 平均交易量: 2,879,132\n", "SELL (时间退出): 2022-07-28 | 价格: 8.97\n", "BUY: 2022-08-01 | 价格: 8.95 | 交易量: 5,869,007 | 平均交易量: 2,480,060\n", "SELL (时间退出): 2022-08-01 | 价格: 8.88\n", "BUY: 2022-08-02 | 价格: 8.80 | 交易量: 8,349,968 | 平均交易量: 2,352,270\n", "SELL (时间退出): 2022-08-02 | 价格: 8.66\n", "BUY: 2022-08-03 | 价格: 8.78 | 交易量: 6,390,852 | 平均交易量: 2,939,997\n", "SELL (时间退出): 2022-08-03 | 价格: 8.74\n", "BUY: 2022-08-04 | 价格: 8.74 | 交易量: 7,575,200 | 平均交易量: 2,991,201\n", "SELL (时间退出): 2022-08-04 | 价格: 8.79\n", "BUY: 2022-08-05 | 价格: 9.12 | 交易量: 9,353,314 | 平均交易量: 3,152,184\n", "SELL (时间退出): 2022-08-05 | 价格: 9.18\n", "BUY: 2022-08-08 | 价格: 9.02 | 交易量: 8,545,200 | 平均交易量: 3,371,972\n", "SELL (时间退出): 2022-08-08 | 价格: 9.09\n", "BUY: 2022-08-10 | 价格: 9.17 | 交易量: 4,796,453 | 平均交易量: 1,887,155\n", "SELL (时间退出): 2022-08-10 | 价格: 9.20\n", "BUY: 2022-08-11 | 价格: 9.54 | 交易量: 12,843,499 | 平均交易量: 2,890,187\n", "SELL (时间退出): 2022-08-11 | 价格: 9.59\n", "BUY: 2022-08-12 | 价格: 9.63 | 交易量: 9,501,383 | 平均交易量: 4,096,708\n", "SELL (时间退出): 2022-08-12 | 价格: 9.76\n", "BUY: 2022-08-15 | 价格: 9.71 | 交易量: 8,175,847 | 平均交易量: 4,013,335\n", "SELL (时间退出): 2022-08-15 | 价格: 10.17\n", "BUY: 2022-08-15 | 价格: 10.39 | 交易量: 14,928,477 | 平均交易量: 5,745,087\n", "SELL (时间退出): 2022-08-15 | 价格: 10.36\n", "BUY: 2022-08-16 | 价格: 11.04 | 交易量: 36,836,716 | 平均交易量: 7,349,459\n", "SELL (时间退出): 2022-08-16 | 价格: 10.98\n", "BUY: 2022-08-17 | 价格: 11.04 | 交易量: 32,950,822 | 平均交易量: 9,533,518\n", "SELL (时间退出): 2022-08-17 | 价格: 10.98\n", "BUY: 2022-08-23 | 价格: 10.89 | 交易量: 18,504,864 | 平均交易量: 5,512,132\n", "SELL (时间退出): 2022-08-23 | 价格: 10.95\n", "BUY: 2022-08-23 | 价格: 11.32 | 交易量: 16,922,634 | 平均交易量: 6,309,973\n", "SELL (时间退出): 2022-08-23 | 价格: 11.32\n", "BUY: 2022-08-24 | 价格: 10.84 | 交易量: 26,686,846 | 平均交易量: 6,980,076\n", "SELL (时间退出): 2022-08-24 | 价格: 10.38\n", "BUY: 2022-08-26 | 价格: 10.07 | 交易量: 11,019,700 | 平均交易量: 4,767,870\n", "SELL (时间退出): 2022-08-26 | 价格: 10.12\n", "BUY: 2022-08-31 | 价格: 9.42 | 交易量: 6,767,159 | 平均交易量: 2,735,781\n", "SELL (时间退出): 2022-08-31 | 价格: 9.29\n", "BUY: 2022-09-02 | 价格: 9.44 | 交易量: 4,312,625 | 平均交易量: 2,038,256\n", "SELL (时间退出): 2022-09-02 | 价格: 10.03\n", "BUY: 2022-09-06 | 价格: 10.04 | 交易量: 4,150,894 | 平均交易量: 2,057,211\n", "SELL (时间退出): 2022-09-06 | 价格: 10.14\n", "BUY: 2022-09-09 | 价格: 9.53 | 交易量: 4,923,453 | 平均交易量: 1,787,873\n", "SELL (时间退出): 2022-09-09 | 价格: 9.67\n", "BUY: 2022-09-13 | 价格: 9.58 | 交易量: 3,249,934 | 平均交易量: 1,515,031\n", "SELL (时间退出): 2022-09-13 | 价格: 9.53\n", "BUY: 2022-09-14 | 价格: 9.32 | 交易量: 3,026,400 | 平均交易量: 1,421,244\n", "SELL (时间退出): 2022-09-14 | 价格: 9.18\n", "BUY: 2022-09-15 | 价格: 8.89 | 交易量: 5,570,800 | 平均交易量: 2,137,358\n", "SELL (时间退出): 2022-09-15 | 价格: 8.72\n", "BUY: 2022-09-20 | 价格: 8.18 | 交易量: 3,345,901 | 平均交易量: 1,482,462\n", "SELL (时间退出): 2022-09-21 | 价格: 8.07\n", "BUY: 2022-09-23 | 价格: 7.88 | 交易量: 4,134,707 | 平均交易量: 1,748,521\n", "SELL (时间退出): 2022-09-23 | 价格: 7.99\n", "BUY: 2022-09-27 | 价格: 7.98 | 交易量: 3,577,148 | 平均交易量: 1,473,454\n", "SELL (时间退出): 2022-09-28 | 价格: 7.79\n", "BUY: 2022-10-10 | 价格: 7.56 | 交易量: 2,556,700 | 平均交易量: 1,133,443\n", "SELL (时间退出): 2022-10-10 | 价格: 7.47\n", "BUY: 2022-10-10 | 价格: 7.34 | 交易量: 2,408,730 | 平均交易量: 1,166,820\n", "SELL (时间退出): 2022-10-11 | 价格: 7.29\n", "BUY: 2022-10-12 | 价格: 7.47 | 交易量: 3,327,310 | 平均交易量: 1,422,240\n", "SELL (时间退出): 2022-10-13 | 价格: 7.53\n", "BUY: 2022-10-18 | 价格: 7.82 | 交易量: 4,276,058 | 平均交易量: 1,631,304\n", "SELL (时间退出): 2022-10-18 | 价格: 7.90\n", "BUY: 2022-10-19 | 价格: 7.97 | 交易量: 4,699,884 | 平均交易量: 1,683,149\n", "SELL (时间退出): 2022-10-19 | 价格: 7.94\n", "BUY: 2022-10-20 | 价格: 7.74 | 交易量: 3,214,229 | 平均交易量: 1,506,603\n", "SELL (时间退出): 2022-10-20 | 价格: 7.86\n", "BUY: 2022-10-24 | 价格: 7.90 | 交易量: 3,508,300 | 平均交易量: 1,275,891\n", "SELL (时间退出): 2022-10-24 | 价格: 7.80\n", "BUY: 2022-10-25 | 价格: 7.65 | 交易量: 3,433,840 | 平均交易量: 1,390,292\n", "SELL (时间退出): 2022-10-25 | 价格: 7.71\n", "BUY: 2022-10-27 | 价格: 7.86 | 交易量: 4,599,600 | 平均交易量: 1,527,441\n", "SELL (时间退出): 2022-10-27 | 价格: 7.99\n", "BUY: 2022-10-28 | 价格: 7.86 | 交易量: 4,285,037 | 平均交易量: 1,981,059\n", "SELL (时间退出): 2022-10-28 | 价格: 7.74\n", "BUY: 2022-11-01 | 价格: 7.66 | 交易量: 4,008,000 | 平均交易量: 1,997,676\n", "SELL (时间退出): 2022-11-01 | 价格: 7.68\n", "BUY: 2022-11-02 | 价格: 7.96 | 交易量: 4,995,546 | 平均交易量: 2,171,116\n", "SELL (时间退出): 2022-11-02 | 价格: 7.92\n", "BUY: 2022-11-03 | 价格: 7.99 | 交易量: 3,433,793 | 平均交易量: 1,703,921\n", "SELL (时间退出): 2022-11-04 | 价格: 8.03\n", "BUY: 2022-11-04 | 价格: 8.12 | 交易量: 5,274,900 | 平均交易量: 2,587,669\n", "SELL (时间退出): 2022-11-04 | 价格: 8.09\n", "BUY: 2022-11-07 | 价格: 8.10 | 交易量: 6,586,051 | 平均交易量: 2,994,352\n", "SELL (时间退出): 2022-11-07 | 价格: 8.15\n", "BUY: 2022-11-09 | 价格: 8.09 | 交易量: 3,489,100 | 平均交易量: 1,724,819\n", "SELL (时间退出): 2022-11-09 | 价格: 8.05\n", "BUY: 2022-11-11 | 价格: 8.03 | 交易量: 6,705,900 | 平均交易量: 1,846,029\n", "SELL (时间退出): 2022-11-11 | 价格: 8.03\n", "BUY: 2022-11-15 | 价格: 8.11 | 交易量: 4,159,600 | 平均交易量: 1,923,429\n", "SELL (时间退出): 2022-11-15 | 价格: 8.19\n", "BUY: 2022-11-16 | 价格: 8.24 | 交易量: 5,183,009 | 平均交易量: 2,286,621\n", "SELL (时间退出): 2022-11-16 | 价格: 8.20\n", "BUY: 2022-11-18 | 价格: 8.20 | 交易量: 3,204,100 | 平均交易量: 1,552,335\n", "SELL (时间退出): 2022-11-18 | 价格: 8.17\n", "BUY: 2022-11-18 | 价格: 8.09 | 交易量: 4,184,196 | 平均交易量: 1,682,695\n", "SELL (时间退出): 2022-11-21 | 价格: 8.02\n", "BUY: 2022-11-23 | 价格: 7.87 | 交易量: 3,085,600 | 平均交易量: 1,414,339\n", "SELL (时间退出): 2022-11-23 | 价格: 7.85\n", "BUY: 2022-11-24 | 价格: 8.03 | 交易量: 5,053,878 | 平均交易量: 1,310,890\n", "SELL (时间退出): 2022-11-25 | 价格: 7.98\n", "BUY: 2022-11-25 | 价格: 7.91 | 交易量: 2,926,300 | 平均交易量: 1,308,851\n", "SELL (时间退出): 2022-11-28 | 价格: 7.75\n", "BUY: 2022-11-29 | 价格: 7.83 | 交易量: 2,788,796 | 平均交易量: 1,181,227\n", "SELL (时间退出): 2022-11-29 | 价格: 7.94\n", "BUY: 2022-12-01 | 价格: 8.07 | 交易量: 6,414,565 | 平均交易量: 1,549,956\n", "SELL (时间退出): 2022-12-01 | 价格: 8.04\n", "BUY: 2022-12-05 | 价格: 8.06 | 交易量: 3,722,521 | 平均交易量: 1,193,033\n", "SELL (时间退出): 2022-12-05 | 价格: 8.08\n", "BUY: 2022-12-05 | 价格: 8.13 | 交易量: 2,551,022 | 平均交易量: 1,273,548\n", "SELL (时间退出): 2022-12-06 | 价格: 8.14\n", "BUY: 2022-12-08 | 价格: 7.97 | 交易量: 4,017,482 | 平均交易量: 1,464,187\n", "SELL (时间退出): 2022-12-08 | 价格: 7.97\n", "BUY: 2022-12-12 | 价格: 7.95 | 交易量: 3,221,100 | 平均交易量: 1,429,576\n", "SELL (时间退出): 2022-12-12 | 价格: 8.09\n", "BUY: 2022-12-12 | 价格: 8.13 | 交易量: 6,831,100 | 平均交易量: 1,891,099\n", "SELL (时间退出): 2022-12-12 | 价格: 8.12\n", "BUY: 2022-12-14 | 价格: 8.07 | 交易量: 4,901,200 | 平均交易量: 1,926,150\n", "SELL (时间退出): 2022-12-14 | 价格: 8.06\n", "BUY: 2022-12-15 | 价格: 8.09 | 交易量: 7,994,482 | 平均交易量: 1,801,534\n", "SELL (时间退出): 2022-12-15 | 价格: 8.08\n", "BUY: 2022-12-19 | 价格: 7.87 | 交易量: 3,831,200 | 平均交易量: 1,180,486\n", "SELL (时间退出): 2022-12-19 | 价格: 7.76\n", "BUY: 2022-12-22 | 价格: 7.59 | 交易量: 1,548,900 | 平均交易量: 773,639\n", "SELL (时间退出): 2022-12-22 | 价格: 7.59\n", "BUY: 2022-12-28 | 价格: 7.47 | 交易量: 1,919,800 | 平均交易量: 692,629\n", "SELL (时间退出): 2022-12-28 | 价格: 7.49\n", "BUY: 2022-12-29 | 价格: 7.38 | 交易量: 2,101,100 | 平均交易量: 755,107\n", "SELL (时间退出): 2022-12-29 | 价格: 7.42\n", "BUY: 2023-01-03 | 价格: 7.38 | 交易量: 1,494,603 | 平均交易量: 716,069\n", "SELL (时间退出): 2023-01-03 | 价格: 7.47\n", "BUY: 2023-01-03 | 价格: 7.54 | 交易量: 2,441,066 | 平均交易量: 1,187,352\n", "SELL (时间退出): 2023-01-04 | 价格: 7.54\n", "BUY: 2023-01-05 | 价格: 7.65 | 交易量: 3,886,064 | 平均交易量: 1,217,653\n", "SELL (时间退出): 2023-01-05 | 价格: 7.60\n", "BUY: 2023-01-05 | 价格: 7.65 | 交易量: 3,117,207 | 平均交易量: 1,248,777\n", "SELL (时间退出): 2023-01-06 | 价格: 8.06\n", "BUY: 2023-01-06 | 价格: 7.98 | 交易量: 11,499,618 | 平均交易量: 2,858,863\n", "SELL (时间退出): 2023-01-06 | 价格: 7.85\n", "BUY: 2023-01-11 | 价格: 7.65 | 交易量: 4,209,300 | 平均交易量: 1,358,825\n", "SELL (时间退出): 2023-01-12 | 价格: 7.58\n", "BUY: 2023-01-12 | 价格: 7.60 | 交易量: 3,945,622 | 平均交易量: 1,882,476\n", "SELL (时间退出): 2023-01-12 | 价格: 7.67\n", "BUY: 2023-01-16 | 价格: 7.72 | 交易量: 2,944,500 | 平均交易量: 1,274,872\n", "SELL (时间退出): 2023-01-16 | 价格: 7.80\n", "BUY: 2023-01-17 | 价格: 7.84 | 交易量: 5,420,669 | 平均交易量: 1,789,146\n", "SELL (时间退出): 2023-01-17 | 价格: 7.80\n", "BUY: 2023-01-19 | 价格: 7.70 | 交易量: 4,020,900 | 平均交易量: 1,242,696\n", "SELL (时间退出): 2023-01-19 | 价格: 7.75\n", "BUY: 2023-01-30 | 价格: 7.91 | 交易量: 5,238,900 | 平均交易量: 1,504,320\n", "SELL (时间退出): 2023-01-30 | 价格: 7.97\n", "BUY: 2023-02-01 | 价格: 8.01 | 交易量: 2,869,200 | 平均交易量: 1,374,903\n", "SELL (时间退出): 2023-02-01 | 价格: 8.00\n", "BUY: 2023-02-03 | 价格: 7.99 | 交易量: 3,973,500 | 平均交易量: 1,681,622\n", "SELL (时间退出): 2023-02-03 | 价格: 7.93\n", "BUY: 2023-02-06 | 价格: 8.07 | 交易量: 3,267,900 | 平均交易量: 1,555,920\n", "SELL (时间退出): 2023-02-06 | 价格: 7.99\n", "BUY: 2023-02-08 | 价格: 8.16 | 交易量: 7,232,100 | 平均交易量: 1,935,265\n", "SELL (时间退出): 2023-02-08 | 价格: 8.27\n", "BUY: 2023-02-10 | 价格: 8.55 | 交易量: 7,732,700 | 平均交易量: 2,934,158\n", "SELL (时间退出): 2023-02-10 | 价格: 8.40\n", "BUY: 2023-02-14 | 价格: 8.61 | 交易量: 5,410,324 | 平均交易量: 2,202,330\n", "SELL (时间退出): 2023-02-14 | 价格: 8.59\n", "BUY: 2023-02-16 | 价格: 8.69 | 交易量: 5,224,880 | 平均交易量: 2,314,113\n", "SELL (时间退出): 2023-02-16 | 价格: 8.68\n", "BUY: 2023-02-16 | 价格: 8.45 | 交易量: 9,526,400 | 平均交易量: 3,113,690\n", "SELL (时间退出): 2023-02-17 | 价格: 8.51\n", "BUY: 2023-02-23 | 价格: 8.42 | 交易量: 2,276,500 | 平均交易量: 1,095,554\n", "SELL (时间退出): 2023-02-24 | 价格: 8.46\n", "BUY: 2023-02-27 | 价格: 8.37 | 交易量: 2,803,093 | 平均交易量: 1,174,316\n", "SELL (时间退出): 2023-02-28 | 价格: 8.38\n", "BUY: 2023-02-28 | 价格: 8.47 | 交易量: 4,974,000 | 平均交易量: 1,334,092\n", "SELL (时间退出): 2023-02-28 | 价格: 8.39\n", "BUY: 2023-03-07 | 价格: 8.43 | 交易量: 2,181,600 | 平均交易量: 1,050,157\n", "SELL (时间退出): 2023-03-07 | 价格: 8.35\n", "BUY: 2023-03-13 | 价格: 8.33 | 交易量: 2,271,913 | 平均交易量: 1,020,208\n", "SELL (时间退出): 2023-03-13 | 价格: 8.35\n", "BUY: 2023-03-14 | 价格: 8.25 | 交易量: 2,419,000 | 平均交易量: 1,176,372\n", "SELL (时间退出): 2023-03-14 | 价格: 8.28\n", "BUY: 2023-03-15 | 价格: 8.50 | 交易量: 4,985,515 | 平均交易量: 1,454,511\n", "SELL (时间退出): 2023-03-15 | 价格: 8.42\n", "BUY: 2023-03-17 | 价格: 8.48 | 交易量: 2,228,300 | 平均交易量: 1,035,566\n", "SELL (时间退出): 2023-03-17 | 价格: 8.47\n", "BUY: 2023-03-21 | 价格: 8.56 | 交易量: 3,520,131 | 平均交易量: 1,193,287\n", "SELL (时间退出): 2023-03-21 | 价格: 8.56\n", "BUY: 2023-03-22 | 价格: 8.56 | 交易量: 3,402,900 | 平均交易量: 1,688,556\n", "SELL (时间退出): 2023-03-22 | 价格: 8.57\n", "BUY: 2023-03-23 | 价格: 8.76 | 交易量: 3,158,400 | 平均交易量: 1,564,685\n", "SELL (时间退出): 2023-03-24 | 价格: 8.73\n", "BUY: 2023-03-29 | 价格: 8.61 | 交易量: 3,572,200 | 平均交易量: 1,421,787\n", "SELL (时间退出): 2023-03-30 | 价格: 8.61\n", "BUY: 2023-03-31 | 价格: 8.48 | 交易量: 2,162,300 | 平均交易量: 1,005,599\n", "SELL (时间退出): 2023-03-31 | 价格: 8.55\n", "BUY: 2023-04-03 | 价格: 8.64 | 交易量: 1,944,400 | 平均交易量: 925,477\n", "SELL (时间退出): 2023-04-03 | 价格: 8.65\n", "BUY: 2023-04-04 | 价格: 8.75 | 交易量: 2,859,628 | 平均交易量: 1,140,027\n", "SELL (时间退出): 2023-04-04 | 价格: 8.74\n", "BUY: 2023-04-06 | 价格: 8.85 | 交易量: 4,138,019 | 平均交易量: 1,598,597\n", "SELL (时间退出): 2023-04-06 | 价格: 9.01\n", "BUY: 2023-04-07 | 价格: 8.90 | 交易量: 5,437,800 | 平均交易量: 2,371,186\n", "SELL (时间退出): 2023-04-07 | 价格: 8.89\n", "BUY: 2023-04-12 | 价格: 8.90 | 交易量: 4,733,880 | 平均交易量: 1,963,972\n", "SELL (时间退出): 2023-04-12 | 价格: 8.91\n", "BUY: 2023-04-13 | 价格: 8.69 | 交易量: 4,667,800 | 平均交易量: 1,874,715\n", "SELL (时间退出): 2023-04-13 | 价格: 8.65\n", "BUY: 2023-04-14 | 价格: 8.46 | 交易量: 5,381,700 | 平均交易量: 2,211,160\n", "SELL (时间退出): 2023-04-14 | 价格: 8.55\n", "BUY: 2023-04-18 | 价格: 8.10 | 交易量: 17,457,236 | 平均交易量: 2,696,725\n", "SELL (时间退出): 2023-04-18 | 价格: 7.89\n", "BUY: 2023-04-21 | 价格: 7.71 | 交易量: 4,285,980 | 平均交易量: 1,775,100\n", "SELL (时间退出): 2023-04-21 | 价格: 7.55\n", "BUY: 2023-04-27 | 价格: 7.68 | 交易量: 6,323,437 | 平均交易量: 1,499,745\n", "SELL (时间退出): 2023-04-27 | 价格: 7.58\n", "BUY: 2023-04-28 | 价格: 7.94 | 交易量: 6,937,440 | 平均交易量: 2,226,488\n", "SELL (时间退出): 2023-04-28 | 价格: 8.11\n", "BUY: 2023-05-09 | 价格: 8.13 | 交易量: 3,419,300 | 平均交易量: 1,343,033\n", "SELL (时间退出): 2023-05-09 | 价格: 8.15\n", "BUY: 2023-05-11 | 价格: 8.30 | 交易量: 4,485,700 | 平均交易量: 1,362,728\n", "SELL (时间退出): 2023-05-11 | 价格: 8.24\n", "BUY: 2023-05-15 | 价格: 8.18 | 交易量: 4,829,008 | 平均交易量: 1,245,586\n", "SELL (时间退出): 2023-05-16 | 价格: 8.10\n", "BUY: 2023-05-22 | 价格: 8.30 | 交易量: 2,241,400 | 平均交易量: 1,082,662\n", "SELL (时间退出): 2023-05-22 | 价格: 8.23\n", "BUY: 2023-05-23 | 价格: 8.34 | 交易量: 2,488,221 | 平均交易量: 1,117,921\n", "SELL (时间退出): 2023-05-23 | 价格: 8.48\n", "BUY: 2023-05-24 | 价格: 8.21 | 交易量: 3,768,837 | 平均交易量: 1,651,960\n", "SELL (时间退出): 2023-05-25 | 价格: 8.23\n", "BUY: 2023-05-29 | 价格: 8.27 | 交易量: 2,007,300 | 平均交易量: 922,521\n", "SELL (时间退出): 2023-05-29 | 价格: 8.20\n", "BUY: 2023-05-31 | 价格: 8.41 | 交易量: 5,276,800 | 平均交易量: 1,080,364\n", "SELL (时间退出): 2023-06-01 | 价格: 8.47\n", "BUY: 2023-06-02 | 价格: 8.50 | 交易量: 4,012,500 | 平均交易量: 1,720,344\n", "SELL (时间退出): 2023-06-02 | 价格: 8.42\n", "BUY: 2023-06-05 | 价格: 8.43 | 交易量: 2,898,000 | 平均交易量: 1,288,682\n", "SELL (时间退出): 2023-06-05 | 价格: 8.43\n", "BUY: 2023-06-07 | 价格: 8.14 | 交易量: 2,765,140 | 平均交易量: 1,166,476\n", "SELL (时间退出): 2023-06-07 | 价格: 8.27\n", "BUY: 2023-06-12 | 价格: 8.28 | 交易量: 3,056,150 | 平均交易量: 993,049\n", "SELL (时间退出): 2023-06-12 | 价格: 8.45\n", "BUY: 2023-06-16 | 价格: 8.48 | 交易量: 2,737,000 | 平均交易量: 1,006,365\n", "SELL (时间退出): 2023-06-16 | 价格: 8.49\n", "BUY: 2023-06-16 | 价格: 8.51 | 交易量: 3,005,400 | 平均交易量: 1,254,948\n", "SELL (时间退出): 2023-06-19 | 价格: 8.52\n", "BUY: 2023-06-21 | 价格: 8.42 | 交易量: 2,631,647 | 平均交易量: 1,296,868\n", "SELL (时间退出): 2023-06-21 | 价格: 8.40\n", "BUY: 2023-06-26 | 价格: 8.19 | 交易量: 3,093,600 | 平均交易量: 1,203,064\n", "SELL (时间退出): 2023-06-26 | 价格: 8.22\n", "BUY: 2023-06-27 | 价格: 8.20 | 交易量: 2,331,430 | 平均交易量: 1,150,756\n", "SELL (时间退出): 2023-06-27 | 价格: 8.26\n", "BUY: 2023-06-28 | 价格: 8.30 | 交易量: 6,186,322 | 平均交易量: 1,433,876\n", "SELL (时间退出): 2023-06-28 | 价格: 8.31\n", "BUY: 2023-06-29 | 价格: 8.40 | 交易量: 4,014,900 | 平均交易量: 1,538,338\n", "SELL (时间退出): 2023-06-29 | 价格: 8.48\n", "BUY: 2023-07-04 | 价格: 8.64 | 交易量: 3,553,500 | 平均交易量: 1,367,089\n", "SELL (时间退出): 2023-07-04 | 价格: 8.70\n", "BUY: 2023-07-06 | 价格: 8.71 | 交易量: 3,323,800 | 平均交易量: 1,465,243\n", "SELL (时间退出): 2023-07-06 | 价格: 8.69\n", "BUY: 2023-07-10 | 价格: 8.45 | 交易量: 3,438,791 | 平均交易量: 1,240,265\n", "SELL (时间退出): 2023-07-10 | 价格: 8.49\n", "BUY: 2023-07-12 | 价格: 8.64 | 交易量: 2,118,281 | 平均交易量: 1,026,592\n", "SELL (时间退出): 2023-07-12 | 价格: 8.66\n", "BUY: 2023-07-13 | 价格: 8.68 | 交易量: 2,597,296 | 平均交易量: 1,037,598\n", "SELL (时间退出): 2023-07-13 | 价格: 8.69\n", "BUY: 2023-07-17 | 价格: 8.83 | 交易量: 4,252,300 | 平均交易量: 1,504,585\n", "SELL (时间退出): 2023-07-17 | 价格: 8.78\n", "BUY: 2023-07-18 | 价格: 8.89 | 交易量: 3,284,238 | 平均交易量: 1,389,189\n", "SELL (时间退出): 2023-07-18 | 价格: 8.80\n", "BUY: 2023-07-24 | 价格: 8.65 | 交易量: 3,129,300 | 平均交易量: 1,052,488\n", "SELL (时间退出): 2023-07-24 | 价格: 8.60\n", "BUY: 2023-07-25 | 价格: 8.66 | 交易量: 2,412,400 | 平均交易量: 957,742\n", "SELL (时间退出): 2023-07-25 | 价格: 8.66\n", "BUY: 2023-07-26 | 价格: 8.58 | 交易量: 2,731,000 | 平均交易量: 868,108\n", "SELL (时间退出): 2023-07-26 | 价格: 8.55\n", "BUY: 2023-07-31 | 价格: 8.59 | 交易量: 2,325,831 | 平均交易量: 942,491\n", "SELL (时间退出): 2023-07-31 | 价格: 8.63\n", "BUY: 2023-08-02 | 价格: 8.64 | 交易量: 1,746,700 | 平均交易量: 661,858\n", "SELL (时间退出): 2023-08-02 | 价格: 8.60\n", "BUY: 2023-08-03 | 价格: 8.68 | 交易量: 1,853,900 | 平均交易量: 710,517\n", "SELL (时间退出): 2023-08-03 | 价格: 8.66\n", "BUY: 2023-08-04 | 价格: 8.71 | 交易量: 2,134,300 | 平均交易量: 874,101\n", "SELL (时间退出): 2023-08-04 | 价格: 8.69\n", "BUY: 2023-08-10 | 价格: 8.49 | 交易量: 1,819,100 | 平均交易量: 763,863\n", "SELL (时间退出): 2023-08-10 | 价格: 8.39\n", "BUY: 2023-08-11 | 价格: 8.29 | 交易量: 2,293,500 | 平均交易量: 928,871\n", "SELL (时间退出): 2023-08-11 | 价格: 8.29\n", "BUY: 2023-08-14 | 价格: 8.11 | 交易量: 2,943,100 | 平均交易量: 1,076,163\n", "SELL (时间退出): 2023-08-14 | 价格: 8.12\n", "BUY: 2023-08-17 | 价格: 7.94 | 交易量: 1,919,713 | 平均交易量: 748,083\n", "SELL (时间退出): 2023-08-17 | 价格: 8.02\n", "BUY: 2023-08-18 | 价格: 8.07 | 交易量: 1,563,590 | 平均交易量: 755,782\n", "SELL (时间退出): 2023-08-18 | 价格: 8.07\n", "BUY: 2023-08-21 | 价格: 8.00 | 交易量: 1,537,900 | 平均交易量: 680,450\n", "SELL (时间退出): 2023-08-21 | 价格: 7.99\n", "BUY: 2023-08-22 | 价格: 7.89 | 交易量: 1,671,500 | 平均交易量: 760,324\n", "SELL (时间退出): 2023-08-22 | 价格: 7.89\n", "BUY: 2023-08-24 | 价格: 7.83 | 交易量: 1,747,100 | 平均交易量: 752,173\n", "SELL (时间退出): 2023-08-24 | 价格: 7.89\n", "BUY: 2023-08-25 | 价格: 7.92 | 交易量: 2,806,557 | 平均交易量: 879,224\n", "SELL (时间退出): 2023-08-25 | 价格: 7.83\n", "BUY: 2023-08-28 | 价格: 7.98 | 交易量: 6,052,500 | 平均交易量: 1,348,443\n", "SELL (时间退出): 2023-08-28 | 价格: 7.97\n", "BUY: 2023-08-30 | 价格: 8.15 | 交易量: 4,081,142 | 平均交易量: 1,490,613\n", "SELL (时间退出): 2023-08-30 | 价格: 8.14\n", "BUY: 2023-09-04 | 价格: 8.31 | 交易量: 2,655,856 | 平均交易量: 1,075,771\n", "SELL (时间退出): 2023-09-04 | 价格: 8.30\n", "BUY: 2023-09-05 | 价格: 8.44 | 交易量: 2,760,300 | 平均交易量: 1,197,184\n", "SELL (时间退出): 2023-09-05 | 价格: 8.46\n", "BUY: 2023-09-06 | 价格: 8.48 | 交易量: 2,764,300 | 平均交易量: 1,279,967\n", "SELL (时间退出): 2023-09-06 | 价格: 8.51\n", "BUY: 2023-09-07 | 价格: 8.48 | 交易量: 3,045,380 | 平均交易量: 1,274,145\n", "SELL (时间退出): 2023-09-07 | 价格: 8.50\n", "BUY: 2023-09-11 | 价格: 8.46 | 交易量: 2,218,076 | 平均交易量: 1,057,818\n", "SELL (时间退出): 2023-09-11 | 价格: 8.50\n", "BUY: 2023-09-12 | 价格: 8.43 | 交易量: 2,182,000 | 平均交易量: 956,483\n", "SELL (时间退出): 2023-09-12 | 价格: 8.45\n", "BUY: 2023-09-18 | 价格: 8.29 | 交易量: 1,652,800 | 平均交易量: 663,147\n", "SELL (时间退出): 2023-09-18 | 价格: 8.39\n", "BUY: 2023-09-19 | 价格: 8.26 | 交易量: 2,411,200 | 平均交易量: 810,087\n", "SELL (时间退出): 2023-09-19 | 价格: 8.29\n", "BUY: 2023-09-20 | 价格: 8.21 | 交易量: 1,867,900 | 平均交易量: 830,782\n", "SELL (时间退出): 2023-09-20 | 价格: 8.17\n", "BUY: 2023-09-25 | 价格: 8.20 | 交易量: 1,814,200 | 平均交易量: 742,857\n", "SELL (时间退出): 2023-09-25 | 价格: 8.18\n", "BUY: 2023-09-27 | 价格: 8.13 | 交易量: 1,633,200 | 平均交易量: 679,013\n", "SELL (时间退出): 2023-09-27 | 价格: 8.10\n", "BUY: 2023-09-28 | 价格: 8.16 | 交易量: 1,848,900 | 平均交易量: 724,239\n", "SELL (时间退出): 2023-09-28 | 价格: 8.16\n", "BUY: 2023-09-28 | 价格: 8.24 | 交易量: 1,865,900 | 平均交易量: 813,580\n", "SELL (时间退出): 2023-09-28 | 价格: 8.26\n", "BUY: 2023-10-09 | 价格: 8.22 | 交易量: 2,117,000 | 平均交易量: 978,740\n", "SELL (时间退出): 2023-10-09 | 价格: 8.17\n", "BUY: 2023-10-09 | 价格: 8.27 | 交易量: 3,059,476 | 平均交易量: 1,209,593\n", "SELL (时间退出): 2023-10-10 | 价格: 8.27\n", "BUY: 2023-10-11 | 价格: 8.45 | 交易量: 3,044,300 | 平均交易量: 1,216,567\n", "SELL (时间退出): 2023-10-11 | 价格: 8.48\n", "BUY: 2023-10-13 | 价格: 8.64 | 交易量: 3,403,800 | 平均交易量: 1,345,359\n", "SELL (时间退出): 2023-10-13 | 价格: 8.65\n", "BUY: 2023-10-18 | 价格: 8.46 | 交易量: 1,692,000 | 平均交易量: 807,473\n", "SELL (时间退出): 2023-10-18 | 价格: 8.46\n", "BUY: 2023-10-19 | 价格: 8.51 | 交易量: 2,301,310 | 平均交易量: 819,852\n", "SELL (时间退出): 2023-10-19 | 价格: 8.45\n", "BUY: 2023-10-23 | 价格: 8.10 | 交易量: 2,738,000 | 平均交易量: 1,037,396\n", "SELL (时间退出): 2023-10-23 | 价格: 8.15\n", "BUY: 2023-10-23 | 价格: 8.04 | 交易量: 2,534,900 | 平均交易量: 1,097,764\n", "SELL (止损): 2023-10-24 | 价格: 7.68\n", "BUY: 2023-10-25 | 价格: 7.97 | 交易量: 4,065,000 | 平均交易量: 2,024,452\n", "SELL (时间退出): 2023-10-25 | 价格: 7.99\n", "BUY: 2023-10-27 | 价格: 8.11 | 交易量: 2,699,500 | 平均交易量: 1,032,860\n", "SELL (时间退出): 2023-10-27 | 价格: 8.19\n", "BUY: 2023-10-30 | 价格: 8.40 | 交易量: 3,761,900 | 平均交易量: 1,137,291\n", "SELL (时间退出): 2023-10-30 | 价格: 8.51\n", "BUY: 2023-10-31 | 价格: 8.56 | 交易量: 3,316,678 | 平均交易量: 1,394,534\n", "SELL (时间退出): 2023-10-31 | 价格: 8.51\n", "BUY: 2023-11-01 | 价格: 8.54 | 交易量: 2,745,600 | 平均交易量: 1,325,802\n", "SELL (时间退出): 2023-11-01 | 价格: 8.58\n", "BUY: 2023-11-03 | 价格: 8.47 | 交易量: 2,371,300 | 平均交易量: 1,151,365\n", "SELL (时间退出): 2023-11-03 | 价格: 8.56\n", "BUY: 2023-11-06 | 价格: 8.63 | 交易量: 2,836,800 | 平均交易量: 1,187,192\n", "SELL (时间退出): 2023-11-06 | 价格: 8.64\n", "BUY: 2023-11-08 | 价格: 8.61 | 交易量: 2,475,876 | 平均交易量: 1,177,546\n", "SELL (时间退出): 2023-11-08 | 价格: 8.63\n", "BUY: 2023-11-08 | 价格: 8.51 | 交易量: 2,954,200 | 平均交易量: 1,299,837\n", "SELL (时间退出): 2023-11-09 | 价格: 8.51\n", "BUY: 2023-11-14 | 价格: 8.63 | 交易量: 4,559,685 | 平均交易量: 1,278,419\n", "SELL (时间退出): 2023-11-14 | 价格: 8.62\n", "BUY: 2023-11-15 | 价格: 8.65 | 交易量: 3,796,600 | 平均交易量: 1,611,986\n", "SELL (时间退出): 2023-11-15 | 价格: 8.65\n", "BUY: 2023-11-16 | 价格: 8.68 | 交易量: 3,109,500 | 平均交易量: 1,404,433\n", "SELL (时间退出): 2023-11-16 | 价格: 8.62\n", "BUY: 2023-11-20 | 价格: 8.56 | 交易量: 2,959,500 | 平均交易量: 1,067,073\n", "SELL (时间退出): 2023-11-20 | 价格: 8.61\n", "BUY: 2023-11-28 | 价格: 8.69 | 交易量: 4,574,700 | 平均交易量: 1,480,763\n", "SELL (时间退出): 2023-11-28 | 价格: 8.71\n", "BUY: 2023-11-30 | 价格: 8.47 | 交易量: 4,717,417 | 平均交易量: 1,587,036\n", "SELL (时间退出): 2023-11-30 | 价格: 8.43\n", "BUY: 2023-12-01 | 价格: 8.29 | 交易量: 7,323,705 | 平均交易量: 2,026,217\n", "SELL (时间退出): 2023-12-01 | 价格: 8.39\n", "BUY: 2023-12-05 | 价格: 8.15 | 交易量: 3,888,400 | 平均交易量: 1,517,092\n", "SELL (时间退出): 2023-12-05 | 价格: 8.06\n", "BUY: 2023-12-06 | 价格: 7.93 | 交易量: 3,906,674 | 平均交易量: 1,908,125\n", "SELL (时间退出): 2023-12-06 | 价格: 7.97\n", "BUY: 2023-12-07 | 价格: 8.00 | 交易量: 4,056,022 | 平均交易量: 1,886,226\n", "SELL (时间退出): 2023-12-07 | 价格: 8.02\n", "BUY: 2023-12-08 | 价格: 8.11 | 交易量: 5,433,429 | 平均交易量: 1,873,543\n", "SELL (时间退出): 2023-12-08 | 价格: 8.12\n", "BUY: 2023-12-08 | 价格: 8.21 | 交易量: 6,348,106 | 平均交易量: 2,382,893\n", "SELL (止损): 2023-12-11 | 价格: 7.84\n", "BUY: 2023-12-14 | 价格: 7.99 | 交易量: 3,726,500 | 平均交易量: 1,785,705\n", "SELL (时间退出): 2023-12-14 | 价格: 8.02\n", "BUY: 2023-12-19 | 价格: 7.72 | 交易量: 2,831,000 | 平均交易量: 1,306,466\n", "SELL (时间退出): 2023-12-19 | 价格: 7.76\n", "BUY: 2023-12-21 | 价格: 7.64 | 交易量: 2,091,100 | 平均交易量: 953,144\n", "SELL (时间退出): 2023-12-21 | 价格: 7.67\n", "BUY: 2023-12-27 | 价格: 7.51 | 交易量: 1,990,000 | 平均交易量: 945,580\n", "SELL (时间退出): 2023-12-27 | 价格: 7.53\n", "BUY: 2023-12-27 | 价格: 7.56 | 交易量: 2,520,200 | 平均交易量: 1,143,129\n", "SELL (时间退出): 2023-12-28 | 价格: 7.62\n", "BUY: 2023-12-29 | 价格: 7.85 | 交易量: 4,520,473 | 平均交易量: 1,633,105\n", "SELL (时间退出): 2023-12-29 | 价格: 7.88\n", "BUY: 2024-01-05 | 价格: 7.68 | 交易量: 2,591,700 | 平均交易量: 1,287,601\n", "SELL (时间退出): 2024-01-05 | 价格: 7.62\n", "BUY: 2024-01-08 | 价格: 7.45 | 交易量: 3,824,200 | 平均交易量: 1,290,748\n", "SELL (时间退出): 2024-01-08 | 价格: 7.39\n", "BUY: 2024-01-10 | 价格: 7.20 | 交易量: 4,566,496 | 平均交易量: 2,022,112\n", "SELL (时间退出): 2024-01-10 | 价格: 7.25\n", "BUY: 2024-01-16 | 价格: 7.33 | 交易量: 1,946,400 | 平均交易量: 953,813\n", "SELL (时间退出): 2024-01-16 | 价格: 7.22\n", "BUY: 2024-01-17 | 价格: 7.07 | 交易量: 3,330,500 | 平均交易量: 1,350,995\n", "SELL (时间退出): 2024-01-18 | 价格: 7.05\n", "BUY: 2024-01-19 | 价格: 7.21 | 交易量: 5,483,665 | 平均交易量: 2,401,945\n", "SELL (时间退出): 2024-01-19 | 价格: 7.14\n", "BUY: 2024-01-22 | 价格: 6.94 | 交易量: 5,104,600 | 平均交易量: 2,029,533\n", "SELL (时间退出): 2024-01-22 | 价格: 6.84\n", "BUY: 2024-01-22 | 价格: 6.57 | 交易量: 5,987,200 | 平均交易量: 2,134,207\n", "SELL (时间退出): 2024-01-23 | 价格: 6.61\n", "BUY: 2024-01-26 | 价格: 6.91 | 交易量: 3,798,700 | 平均交易量: 1,726,365\n", "SELL (时间退出): 2024-01-29 | 价格: 6.85\n", "BUY: 2024-01-30 | 价格: 6.88 | 交易量: 4,364,555 | 平均交易量: 2,063,927\n", "SELL (时间退出): 2024-01-30 | 价格: 6.79\n", "BUY: 2024-02-01 | 价格: 6.23 | 交易量: 4,205,200 | 平均交易量: 2,033,797\n", "SELL (时间退出): 2024-02-01 | 价格: 6.45\n", "BUY: 2024-02-05 | 价格: 5.65 | 交易量: 8,065,937 | 平均交易量: 2,488,633\n", "SELL (时间退出): 2024-02-05 | 价格: 5.56\n", "BUY: 2024-02-06 | 价格: 5.63 | 交易量: 7,399,282 | 平均交易量: 3,345,145\n", "SELL (时间退出): 2024-02-06 | 价格: 5.68\n", "BUY: 2024-02-07 | 价格: 6.32 | 交易量: 7,539,522 | 平均交易量: 3,031,976\n", "SELL (时间退出): 2024-02-07 | 价格: 6.33\n", "BUY: 2024-02-08 | 价格: 6.62 | 交易量: 8,739,361 | 平均交易量: 3,189,120\n", "SELL (时间退出): 2024-02-08 | 价格: 6.70\n", "BUY: 2024-02-19 | 价格: 6.73 | 交易量: 6,863,678 | 平均交易量: 3,127,289\n", "SELL (时间退出): 2024-02-19 | 价格: 6.61\n", "BUY: 2024-02-26 | 价格: 6.86 | 交易量: 3,213,800 | 平均交易量: 1,441,692\n", "SELL (时间退出): 2024-02-26 | 价格: 6.95\n", "BUY: 2024-02-28 | 价格: 7.16 | 交易量: 3,365,100 | 平均交易量: 1,620,676\n", "SELL (时间退出): 2024-02-28 | 价格: 6.90\n", "BUY: 2024-02-29 | 价格: 6.88 | 交易量: 5,435,200 | 平均交易量: 2,328,910\n", "SELL (时间退出): 2024-02-29 | 价格: 6.85\n", "BUY: 2024-03-07 | 价格: 7.13 | 交易量: 3,774,615 | 平均交易量: 1,442,762\n", "SELL (时间退出): 2024-03-07 | 价格: 7.02\n", "BUY: 2024-03-12 | 价格: 7.18 | 交易量: 2,711,500 | 平均交易量: 1,181,546\n", "SELL (时间退出): 2024-03-12 | 价格: 7.12\n", "BUY: 2024-03-13 | 价格: 7.16 | 交易量: 3,989,344 | 平均交易量: 1,341,944\n", "SELL (时间退出): 2024-03-14 | 价格: 7.11\n", "BUY: 2024-03-18 | 价格: 7.17 | 交易量: 2,259,884 | 平均交易量: 1,044,725\n", "SELL (时间退出): 2024-03-18 | 价格: 7.18\n", "BUY: 2024-03-19 | 价格: 7.31 | 交易量: 2,428,540 | 平均交易量: 1,080,412\n", "SELL (时间退出): 2024-03-19 | 价格: 7.25\n", "BUY: 2024-03-20 | 价格: 7.43 | 交易量: 9,108,100 | 平均交易量: 1,562,883\n", "SELL (时间退出): 2024-03-20 | 价格: 7.38\n", "BUY: 2024-03-21 | 价格: 7.33 | 交易量: 4,847,400 | 平均交易量: 1,831,589\n", "SELL (时间退出): 2024-03-21 | 价格: 7.31\n", "BUY: 2024-03-25 | 价格: 7.16 | 交易量: 2,806,403 | 平均交易量: 1,214,760\n", "SELL (时间退出): 2024-03-25 | 价格: 7.26\n", "BUY: 2024-03-28 | 价格: 7.04 | 交易量: 3,629,500 | 平均交易量: 1,476,489\n", "SELL (时间退出): 2024-03-28 | 价格: 7.20\n", "BUY: 2024-04-01 | 价格: 7.38 | 交易量: 5,414,600 | 平均交易量: 1,304,100\n", "SELL (时间退出): 2024-04-01 | 价格: 7.48\n", "BUY: 2024-04-02 | 价格: 7.46 | 交易量: 3,828,078 | 平均交易量: 1,694,269\n", "SELL (时间退出): 2024-04-02 | 价格: 7.48\n", "BUY: 2024-04-03 | 价格: 7.44 | 交易量: 3,259,900 | 平均交易量: 1,438,211\n", "SELL (时间退出): 2024-04-03 | 价格: 7.46\n", "BUY: 2024-04-08 | 价格: 7.43 | 交易量: 2,375,468 | 平均交易量: 1,172,270\n", "SELL (时间退出): 2024-04-08 | 价格: 7.43\n", "BUY: 2024-04-09 | 价格: 7.43 | 交易量: 3,270,900 | 平均交易量: 1,305,797\n", "SELL (时间退出): 2024-04-09 | 价格: 7.44\n", "BUY: 2024-04-10 | 价格: 7.56 | 交易量: 4,121,885 | 平均交易量: 1,503,690\n", "SELL (时间退出): 2024-04-10 | 价格: 7.53\n", "BUY: 2024-04-15 | 价格: 7.70 | 交易量: 9,129,598 | 平均交易量: 1,781,130\n", "SELL (时间退出): 2024-04-15 | 价格: 7.80\n", "BUY: 2024-04-16 | 价格: 7.61 | 交易量: 6,804,563 | 平均交易量: 2,679,806\n", "SELL (时间退出): 2024-04-16 | 价格: 7.66\n", "BUY: 2024-04-17 | 价格: 8.04 | 交易量: 12,244,634 | 平均交易量: 3,284,038\n", "SELL (止损): 2024-04-18 | 价格: 7.75\n", "BUY: 2024-04-18 | 价格: 7.50 | 交易量: 21,318,596 | 平均交易量: 4,578,614\n", "SELL (时间退出): 2024-04-18 | 价格: 7.48\n", "BUY: 2024-04-19 | 价格: 7.59 | 交易量: 12,618,625 | 平均交易量: 5,810,540\n", "SELL (时间退出): 2024-04-19 | 价格: 7.56\n", "BUY: 2024-04-22 | 价格: 7.45 | 交易量: 10,946,171 | 平均交易量: 3,716,543\n", "SELL (时间退出): 2024-04-22 | 价格: 7.44\n", "BUY: 2024-04-26 | 价格: 7.69 | 交易量: 6,725,100 | 平均交易量: 1,789,147\n", "SELL (时间退出): 2024-04-26 | 价格: 7.75\n", "BUY: 2024-04-29 | 价格: 7.78 | 交易量: 4,271,300 | 平均交易量: 1,989,937\n", "SELL (时间退出): 2024-04-29 | 价格: 7.80\n", "BUY: 2024-04-30 | 价格: 7.70 | 交易量: 4,670,500 | 平均交易量: 2,118,911\n", "SELL (时间退出): 2024-04-30 | 价格: 7.68\n", "BUY: 2024-05-06 | 价格: 7.95 | 交易量: 6,060,200 | 平均交易量: 2,133,677\n", "SELL (时间退出): 2024-05-06 | 价格: 7.93\n", "BUY: 2024-05-08 | 价格: 7.92 | 交易量: 3,183,000 | 平均交易量: 1,425,967\n", "SELL (时间退出): 2024-05-08 | 价格: 7.80\n", "BUY: 2024-05-10 | 价格: 7.78 | 交易量: 2,654,452 | 平均交易量: 1,227,864\n", "SELL (时间退出): 2024-05-10 | 价格: 7.77\n", "BUY: 2024-05-13 | 价格: 7.68 | 交易量: 2,945,700 | 平均交易量: 1,224,923\n", "SELL (时间退出): 2024-05-13 | 价格: 7.70\n", "BUY: 2024-05-15 | 价格: 7.71 | 交易量: 2,442,300 | 平均交易量: 1,216,999\n", "SELL (时间退出): 2024-05-15 | 价格: 7.70\n", "BUY: 2024-05-17 | 价格: 7.83 | 交易量: 2,181,252 | 平均交易量: 1,026,933\n", "SELL (时间退出): 2024-05-17 | 价格: 7.80\n", "BUY: 2024-05-17 | 价格: 7.96 | 交易量: 3,399,600 | 平均交易量: 1,369,967\n", "SELL (时间退出): 2024-05-20 | 价格: 8.03\n", "BUY: 2024-05-22 | 价格: 8.13 | 交易量: 5,502,500 | 平均交易量: 1,702,935\n", "SELL (时间退出): 2024-05-22 | 价格: 8.06\n", "BUY: 2024-05-23 | 价格: 7.94 | 交易量: 3,720,000 | 平均交易量: 1,708,192\n", "SELL (时间退出): 2024-05-23 | 价格: 7.97\n", "BUY: 2024-05-30 | 价格: 7.98 | 交易量: 3,480,600 | 平均交易量: 1,050,227\n", "SELL (时间退出): 2024-05-30 | 价格: 7.95\n", "BUY: 2024-05-31 | 价格: 8.04 | 交易量: 2,280,600 | 平均交易量: 1,122,954\n", "SELL (时间退出): 2024-05-31 | 价格: 8.07\n", "BUY: 2024-06-03 | 价格: 8.05 | 交易量: 3,392,600 | 平均交易量: 1,221,007\n", "SELL (时间退出): 2024-06-03 | 价格: 7.94\n", "BUY: 2024-06-04 | 价格: 7.96 | 交易量: 2,838,300 | 平均交易量: 1,355,930\n", "SELL (时间退出): 2024-06-04 | 价格: 7.97\n", "BUY: 2024-06-06 | 价格: 7.86 | 交易量: 2,443,400 | 平均交易量: 1,148,510\n", "SELL (时间退出): 2024-06-06 | 价格: 7.77\n", "BUY: 2024-06-07 | 价格: 7.85 | 交易量: 3,696,456 | 平均交易量: 1,449,404\n", "SELL (时间退出): 2024-06-07 | 价格: 7.78\n", "BUY: 2024-06-12 | 价格: 7.86 | 交易量: 4,173,400 | 平均交易量: 1,328,373\n", "SELL (时间退出): 2024-06-12 | 价格: 7.84\n", "BUY: 2024-06-14 | 价格: 7.83 | 交易量: 2,944,600 | 平均交易量: 1,197,052\n", "SELL (时间退出): 2024-06-14 | 价格: 7.84\n", "BUY: 2024-06-17 | 价格: 7.91 | 交易量: 4,024,600 | 平均交易量: 1,331,853\n", "SELL (时间退出): 2024-06-17 | 价格: 7.95\n", "BUY: 2024-06-20 | 价格: 7.89 | 交易量: 1,877,200 | 平均交易量: 903,006\n", "SELL (时间退出): 2024-06-20 | 价格: 7.84\n", "BUY: 2024-06-21 | 价格: 7.75 | 交易量: 3,626,766 | 平均交易量: 1,071,387\n", "SELL (时间退出): 2024-06-21 | 价格: 7.75\n", "BUY: 2024-06-24 | 价格: 7.73 | 交易量: 2,483,700 | 平均交易量: 1,092,957\n", "SELL (时间退出): 2024-06-24 | 价格: 7.56\n", "BUY: 2024-06-24 | 价格: 7.38 | 交易量: 2,757,010 | 平均交易量: 1,225,121\n", "SELL (时间退出): 2024-06-25 | 价格: 7.39\n", "BUY: 2024-06-28 | 价格: 7.74 | 交易量: 4,471,500 | 平均交易量: 1,494,447\n", "SELL (时间退出): 2024-06-28 | 价格: 7.80\n", "BUY: 2024-07-04 | 价格: 7.77 | 交易量: 2,375,000 | 平均交易量: 921,013\n", "SELL (时间退出): 2024-07-04 | 价格: 7.70\n", "BUY: 2024-07-05 | 价格: 7.55 | 交易量: 2,238,200 | 平均交易量: 919,640\n", "SELL (时间退出): 2024-07-05 | 价格: 7.54\n", "BUY: 2024-07-08 | 价格: 7.46 | 交易量: 2,206,300 | 平均交易量: 1,075,647\n", "SELL (时间退出): 2024-07-08 | 价格: 7.41\n", "BUY: 2024-07-09 | 价格: 7.56 | 交易量: 3,436,900 | 平均交易量: 1,444,118\n", "SELL (时间退出): 2024-07-09 | 价格: 7.58\n", "BUY: 2024-07-10 | 价格: 7.89 | 交易量: 5,551,700 | 平均交易量: 1,707,621\n", "SELL (时间退出): 2024-07-10 | 价格: 7.80\n", "BUY: 2024-07-11 | 价格: 8.01 | 交易量: 4,773,300 | 平均交易量: 2,337,276\n", "SELL (时间退出): 2024-07-11 | 价格: 8.01\n", "BUY: 2024-07-17 | 价格: 7.89 | 交易量: 2,267,600 | 平均交易量: 985,362\n", "SELL (时间退出): 2024-07-17 | 价格: 7.85\n", "BUY: 2024-07-17 | 价格: 7.78 | 交易量: 2,641,000 | 平均交易量: 1,151,696\n", "SELL (时间退出): 2024-07-18 | 价格: 7.57\n", "BUY: 2024-07-22 | 价格: 7.60 | 交易量: 3,148,500 | 平均交易量: 1,173,967\n", "SELL (时间退出): 2024-07-22 | 价格: 7.63\n", "BUY: 2024-07-23 | 价格: 7.46 | 交易量: 2,166,345 | 平均交易量: 1,061,990\n", "SELL (时间退出): 2024-07-24 | 价格: 7.38\n", "BUY: 2024-07-25 | 价格: 7.19 | 交易量: 3,134,545 | 平均交易量: 1,227,706\n", "SELL (时间退出): 2024-07-25 | 价格: 7.29\n", "BUY: 2024-07-30 | 价格: 7.29 | 交易量: 2,001,800 | 平均交易量: 800,399\n", "SELL (时间退出): 2024-07-30 | 价格: 7.33\n", "BUY: 2024-07-31 | 价格: 7.55 | 交易量: 3,169,100 | 平均交易量: 914,665\n", "SELL (时间退出): 2024-07-31 | 价格: 7.77\n", "BUY: 2024-07-31 | 价格: 7.85 | 交易量: 3,548,800 | 平均交易量: 1,583,979\n", "SELL (时间退出): 2024-08-01 | 价格: 7.93\n", "BUY: 2024-08-05 | 价格: 7.59 | 交易量: 3,456,900 | 平均交易量: 1,720,960\n", "SELL (时间退出): 2024-08-05 | 价格: 7.63\n", "BUY: 2024-08-09 | 价格: 7.49 | 交易量: 3,014,000 | 平均交易量: 1,212,437\n", "SELL (时间退出): 2024-08-09 | 价格: 7.47\n", "BUY: 2024-08-15 | 价格: 7.50 | 交易量: 2,150,300 | 平均交易量: 739,687\n", "SELL (时间退出): 2024-08-15 | 价格: 7.66\n", "BUY: 2024-08-23 | 价格: 7.27 | 交易量: 1,375,300 | 平均交易量: 634,774\n", "SELL (时间退出): 2024-08-23 | 价格: 7.35\n", "BUY: 2024-08-26 | 价格: 7.42 | 交易量: 1,612,500 | 平均交易量: 691,520\n", "SELL (时间退出): 2024-08-26 | 价格: 7.49\n", "BUY: 2024-08-27 | 价格: 7.38 | 交易量: 1,556,578 | 平均交易量: 721,553\n", "SELL (时间退出): 2024-08-27 | 价格: 7.31\n", "BUY: 2024-08-28 | 价格: 6.94 | 交易量: 8,445,331 | 平均交易量: 1,256,529\n", "SELL (时间退出): 2024-08-28 | 价格: 7.00\n", "BUY: 2024-08-29 | 价格: 7.21 | 交易量: 4,797,500 | 平均交易量: 2,115,870\n", "SELL (时间退出): 2024-08-29 | 价格: 7.34\n", "BUY: 2024-08-30 | 价格: 7.62 | 交易量: 4,552,800 | 平均交易量: 2,022,483\n", "SELL (时间退出): 2024-08-30 | 价格: 7.58\n", "BUY: 2024-09-02 | 价格: 7.26 | 交易量: 4,104,300 | 平均交易量: 1,864,821\n", "SELL (时间退出): 2024-09-02 | 价格: 7.17\n", "BUY: 2024-09-06 | 价格: 7.13 | 交易量: 1,696,800 | 平均交易量: 813,173\n", "SELL (时间退出): 2024-09-06 | 价格: 7.04\n", "BUY: 2024-09-09 | 价格: 6.99 | 交易量: 1,719,150 | 平均交易量: 855,156\n", "SELL (时间退出): 2024-09-09 | 价格: 6.93\n", "BUY: 2024-09-10 | 价格: 6.82 | 交易量: 3,444,522 | 平均交易量: 985,119\n", "SELL (时间退出): 2024-09-10 | 价格: 6.73\n", "BUY: 2024-09-12 | 价格: 6.80 | 交易量: 2,232,369 | 平均交易量: 984,244\n", "SELL (时间退出): 2024-09-12 | 价格: 6.71\n", "BUY: 2024-09-19 | 价格: 6.55 | 交易量: 1,790,600 | 平均交易量: 871,947\n", "SELL (时间退出): 2024-09-19 | 价格: 6.70\n", "BUY: 2024-09-23 | 价格: 6.70 | 交易量: 1,977,300 | 平均交易量: 863,425\n", "SELL (时间退出): 2024-09-23 | 价格: 6.70\n", "BUY: 2024-09-24 | 价格: 6.73 | 交易量: 2,340,200 | 平均交易量: 940,853\n", "SELL (时间退出): 2024-09-24 | 价格: 6.89\n", "BUY: 2024-09-25 | 价格: 7.27 | 交易量: 5,354,278 | 平均交易量: 1,504,861\n", "SELL (时间退出): 2024-09-25 | 价格: 7.22\n", "BUY: 2024-09-27 | 价格: 7.69 | 交易量: 5,793,000 | 平均交易量: 1,953,061\n", "SELL (时间退出): 2024-09-27 | 价格: 7.76\n", "BUY: 2024-09-30 | 价格: 8.13 | 交易量: 11,253,500 | 平均交易量: 2,721,122\n", "SELL (时间退出): 2024-09-30 | 价格: 8.42\n", "BUY: 2024-09-30 | 价格: 8.47 | 交易量: 7,331,300 | 平均交易量: 3,438,900\n", "SELL (时间退出): 2024-09-30 | 价格: 8.52\n", "BUY: 2024-10-08 | 价格: 9.15 | 交易量: 15,971,438 | 平均交易量: 4,562,552\n", "SELL (止损): 2024-10-08 | 价格: 8.85\n", "BUY: 2024-10-18 | 价格: 7.83 | 交易量: 3,271,000 | 平均交易量: 1,444,575\n", "SELL (时间退出): 2024-10-18 | 价格: 7.88\n", "BUY: 2024-10-18 | 价格: 8.06 | 交易量: 4,755,600 | 平均交易量: 1,754,365\n", "SELL (时间退出): 2024-10-18 | 价格: 8.06\n", "BUY: 2024-10-21 | 价格: 8.01 | 交易量: 5,033,700 | 平均交易量: 2,179,289\n", "SELL (时间退出): 2024-10-21 | 价格: 8.24\n", "BUY: 2024-10-25 | 价格: 8.20 | 交易量: 3,933,200 | 平均交易量: 1,708,646\n", "SELL (时间退出): 2024-10-25 | 价格: 8.28\n", "BUY: 2024-10-25 | 价格: 8.24 | 交易量: 4,467,485 | 平均交易量: 1,733,143\n", "SELL (时间退出): 2024-10-25 | 价格: 8.28\n", "BUY: 2024-10-28 | 价格: 8.25 | 交易量: 4,432,646 | 平均交易量: 1,974,686\n", "SELL (时间退出): 2024-10-28 | 价格: 8.29\n", "BUY: 2024-10-29 | 价格: 8.25 | 交易量: 7,822,288 | 平均交易量: 2,546,443\n", "SELL (时间退出): 2024-10-29 | 价格: 8.23\n", "BUY: 2024-10-30 | 价格: 8.27 | 交易量: 6,254,564 | 平均交易量: 2,779,683\n", "SELL (时间退出): 2024-10-30 | 价格: 8.23\n", "BUY: 2024-11-01 | 价格: 8.09 | 交易量: 6,720,300 | 平均交易量: 2,418,620\n", "SELL (时间退出): 2024-11-01 | 价格: 8.07\n", "BUY: 2024-11-05 | 价格: 8.21 | 交易量: 4,154,870 | 平均交易量: 1,970,258\n", "SELL (时间退出): 2024-11-05 | 价格: 8.32\n", "BUY: 2024-11-06 | 价格: 8.32 | 交易量: 6,000,200 | 平均交易量: 2,392,397\n", "SELL (时间退出): 2024-11-06 | 价格: 8.33\n", "BUY: 2024-11-06 | 价格: 8.41 | 交易量: 8,818,174 | 平均交易量: 3,142,716\n", "SELL (时间退出): 2024-11-07 | 价格: 8.40\n", "BUY: 2024-11-08 | 价格: 8.61 | 交易量: 10,460,072 | 平均交易量: 3,959,683\n", "SELL (时间退出): 2024-11-08 | 价格: 8.50\n", "BUY: 2024-11-11 | 价格: 8.68 | 交易量: 7,819,600 | 平均交易量: 3,775,583\n", "SELL (时间退出): 2024-11-11 | 价格: 8.69\n", "BUY: 2024-11-12 | 价格: 8.78 | 交易量: 6,939,417 | 平均交易量: 2,831,229\n", "SELL (时间退出): 2024-11-12 | 价格: 8.84\n", "BUY: 2024-11-18 | 价格: 8.38 | 交易量: 5,860,500 | 平均交易量: 2,097,223\n", "SELL (时间退出): 2024-11-18 | 价格: 8.45\n", "BUY: 2024-11-19 | 价格: 8.45 | 交易量: 5,884,400 | 平均交易量: 2,343,638\n", "SELL (时间退出): 2024-11-19 | 价格: 8.32\n", "BUY: 2024-11-20 | 价格: 8.61 | 交易量: 5,709,100 | 平均交易量: 2,420,529\n", "SELL (时间退出): 2024-11-20 | 价格: 8.68\n", "BUY: 2024-11-22 | 价格: 8.69 | 交易量: 5,107,400 | 平均交易量: 2,127,362\n", "SELL (时间退出): 2024-11-22 | 价格: 8.63\n", "BUY: 2024-11-25 | 价格: 8.31 | 交易量: 5,875,000 | 平均交易量: 2,612,306\n", "SELL (时间退出): 2024-11-25 | 价格: 8.33\n", "BUY: 2024-11-27 | 价格: 8.09 | 交易量: 4,560,272 | 平均交易量: 1,670,155\n", "SELL (时间退出): 2024-11-27 | 价格: 8.25\n", "BUY: 2024-11-27 | 价格: 8.44 | 交易量: 6,585,400 | 平均交易量: 2,006,352\n", "SELL (时间退出): 2024-11-27 | 价格: 8.81\n", "BUY: 2024-11-28 | 价格: 8.76 | 交易量: 8,655,700 | 平均交易量: 3,850,067\n", "SELL (时间退出): 2024-11-28 | 价格: 8.77\n", "BUY: 2024-11-29 | 价格: 8.88 | 交易量: 7,750,310 | 平均交易量: 3,799,919\n", "SELL (时间退出): 2024-11-29 | 价格: 8.88\n", "BUY: 2024-12-02 | 价格: 8.97 | 交易量: 11,552,844 | 平均交易量: 4,303,007\n", "SELL (时间退出): 2024-12-02 | 价格: 9.03\n", "BUY: 2024-12-05 | 价格: 8.83 | 交易量: 5,983,679 | 平均交易量: 2,700,368\n", "SELL (时间退出): 2024-12-05 | 价格: 8.79\n", "BUY: 2024-12-09 | 价格: 8.83 | 交易量: 4,997,900 | 平均交易量: 2,101,290\n", "SELL (时间退出): 2024-12-09 | 价格: 8.75\n", "BUY: 2024-12-10 | 价格: 8.91 | 交易量: 7,681,000 | 平均交易量: 2,156,820\n", "SELL (时间退出): 2024-12-10 | 价格: 8.85\n", "BUY: 2024-12-11 | 价格: 8.92 | 交易量: 4,859,013 | 平均交易量: 2,392,426\n", "SELL (时间退出): 2024-12-11 | 价格: 8.92\n", "BUY: 2024-12-13 | 价格: 8.80 | 交易量: 6,960,700 | 平均交易量: 2,328,122\n", "SELL (时间退出): 2024-12-13 | 价格: 8.79\n", "BUY: 2024-12-17 | 价格: 8.60 | 交易量: 4,189,600 | 平均交易量: 2,076,500\n", "SELL (时间退出): 2024-12-17 | 价格: 8.49\n", "BUY: 2024-12-20 | 价格: 8.81 | 交易量: 3,697,359 | 平均交易量: 1,676,787\n", "SELL (时间退出): 2024-12-20 | 价格: 8.80\n", "BUY: 2024-12-23 | 价格: 8.77 | 交易量: 5,344,500 | 平均交易量: 2,447,413\n", "SELL (时间退出): 2024-12-23 | 价格: 8.71\n", "BUY: 2024-12-26 | 价格: 8.70 | 交易量: 4,445,700 | 平均交易量: 1,834,290\n", "SELL (时间退出): 2024-12-26 | 价格: 8.78\n", "BUY: 2024-12-27 | 价格: 8.92 | 交易量: 6,572,800 | 平均交易量: 1,897,427\n", "SELL (时间退出): 2024-12-27 | 价格: 8.97\n", "BUY: 2024-12-30 | 价格: 9.23 | 交易量: 21,850,462 | 平均交易量: 3,724,841\n", "SELL (时间退出): 2024-12-30 | 价格: 9.12\n", "BUY: 2025-01-02 | 价格: 8.76 | 交易量: 6,324,278 | 平均交易量: 3,133,678\n", "SELL (时间退出): 2025-01-02 | 价格: 8.71\n", "BUY: 2025-01-03 | 价格: 8.70 | 交易量: 16,490,137 | 平均交易量: 4,217,630\n", "SELL (时间退出): 2025-01-03 | 价格: 8.66\n", "BUY: 2025-01-06 | 价格: 8.42 | 交易量: 17,745,064 | 平均交易量: 5,543,380\n", "SELL (时间退出): 2025-01-06 | 价格: 8.20\n", "BUY: 2025-01-08 | 价格: 8.19 | 交易量: 6,107,291 | 平均交易量: 2,842,312\n", "SELL (时间退出): 2025-01-08 | 价格: 7.89\n", "BUY: 2025-01-13 | 价格: 7.52 | 交易量: 8,088,200 | 平均交易量: 2,800,453\n", "SELL (时间退出): 2025-01-13 | 价格: 7.50\n", "BUY: 2025-01-14 | 价格: 7.80 | 交易量: 5,248,700 | 平均交易量: 2,538,647\n", "SELL (时间退出): 2025-01-14 | 价格: 8.01\n", "BUY: 2025-01-14 | 价格: 8.10 | 交易量: 6,427,200 | 平均交易量: 2,788,127\n", "SELL (时间退出): 2025-01-15 | 价格: 7.92\n", "BUY: 2025-01-16 | 价格: 8.12 | 交易量: 6,456,900 | 平均交易量: 3,042,134\n", "SELL (时间退出): 2025-01-16 | 价格: 8.04\n", "BUY: 2025-01-17 | 价格: 8.23 | 交易量: 4,171,300 | 平均交易量: 1,850,107\n", "SELL (时间退出): 2025-01-17 | 价格: 8.26\n", "BUY: 2025-01-20 | 价格: 8.37 | 交易量: 5,160,200 | 平均交易量: 2,082,273\n", "SELL (时间退出): 2025-01-20 | 价格: 8.30\n", "BUY: 2025-01-22 | 价格: 8.27 | 交易量: 2,819,000 | 平均交易量: 1,374,993\n", "SELL (时间退出): 2025-01-22 | 价格: 8.27\n", "BUY: 2025-01-23 | 价格: 8.32 | 交易量: 5,277,400 | 平均交易量: 1,400,840\n", "SELL (时间退出): 2025-01-23 | 价格: 8.28\n", "BUY: 2025-02-05 | 价格: 8.19 | 交易量: 3,204,096 | 平均交易量: 1,544,886\n", "SELL (时间退出): 2025-02-05 | 价格: 8.22\n", "BUY: 2025-02-06 | 价格: 8.21 | 交易量: 3,484,937 | 平均交易量: 1,422,896\n", "SELL (时间退出): 2025-02-06 | 价格: 8.26\n", "BUY: 2025-02-07 | 价格: 8.35 | 交易量: 3,837,906 | 平均交易量: 1,789,916\n", "SELL (时间退出): 2025-02-07 | 价格: 8.44\n", "BUY: 2025-02-11 | 价格: 8.22 | 交易量: 5,173,378 | 平均交易量: 2,142,822\n", "SELL (时间退出): 2025-02-11 | 价格: 8.24\n", "BUY: 2025-02-11 | 价格: 8.32 | 交易量: 4,058,600 | 平均交易量: 1,901,151\n", "SELL (时间退出): 2025-02-11 | 价格: 8.32\n", "BUY: 2025-02-12 | 价格: 8.34 | 交易量: 5,128,700 | 平均交易量: 1,954,138\n", "SELL (时间退出): 2025-02-12 | 价格: 8.32\n", "BUY: 2025-02-13 | 价格: 8.34 | 交易量: 4,568,093 | 平均交易量: 2,255,006\n", "SELL (时间退出): 2025-02-13 | 价格: 8.31\n", "BUY: 2025-02-14 | 价格: 8.45 | 交易量: 4,787,200 | 平均交易量: 1,949,905\n", "SELL (时间退出): 2025-02-17 | 价格: 8.39\n", "BUY: 2025-02-17 | 价格: 8.25 | 交易量: 6,048,200 | 平均交易量: 2,783,682\n", "SELL (时间退出): 2025-02-17 | 价格: 8.21\n", "BUY: 2025-02-20 | 价格: 8.08 | 交易量: 4,379,000 | 平均交易量: 2,090,156\n", "SELL (时间退出): 2025-02-20 | 价格: 8.10\n", "BUY: 2025-02-20 | 价格: 8.17 | 交易量: 7,247,496 | 平均交易量: 1,909,371\n", "SELL (时间退出): 2025-02-20 | 价格: 8.17\n", "BUY: 2025-02-24 | 价格: 8.36 | 交易量: 9,732,398 | 平均交易量: 2,596,700\n", "SELL (时间退出): 2025-02-24 | 价格: 8.56\n", "BUY: 2025-02-24 | 价格: 8.76 | 交易量: 19,041,200 | 平均交易量: 4,301,085\n", "SELL (时间退出): 2025-02-24 | 价格: 8.80\n", "BUY: 2025-02-26 | 价格: 9.11 | 交易量: 20,964,040 | 平均交易量: 8,196,346\n", "SELL (时间退出): 2025-02-26 | 价格: 9.17\n", "BUY: 2025-02-26 | 价格: 9.67 | 交易量: 29,088,444 | 平均交易量: 9,322,821\n", "SELL (止损): 2025-02-26 | 价格: 9.35\n", "最终资金: 99999.09\n", "总收益率: -0.00%\n", "夏普比率: -2659.80\n", "最大回撤: 0.00%\n", "总交易次数: 425\n", "胜率: 46.12%\n", "BUY: 2024-12-09 | 价格: 8.83 | 交易量: 4,997,900 | 平均交易量: 2,101,290\n", "SELL (时间退出): 2024-12-09 | 价格: 8.75\n", "BUY: 2024-12-10 | 价格: 8.91 | 交易量: 7,681,000 | 平均交易量: 2,156,820\n", "SELL (时间退出): 2024-12-10 | 价格: 8.85\n", "BUY: 2024-12-11 | 价格: 8.92 | 交易量: 4,859,013 | 平均交易量: 2,392,426\n", "SELL (时间退出): 2024-12-11 | 价格: 8.92\n", "BUY: 2024-12-13 | 价格: 8.80 | 交易量: 6,960,700 | 平均交易量: 2,328,122\n", "SELL (时间退出): 2024-12-13 | 价格: 8.79\n", "BUY: 2024-12-17 | 价格: 8.60 | 交易量: 4,189,600 | 平均交易量: 2,076,500\n", "SELL (时间退出): 2024-12-17 | 价格: 8.49\n", "BUY: 2024-12-20 | 价格: 8.81 | 交易量: 3,697,359 | 平均交易量: 1,676,787\n", "SELL (时间退出): 2024-12-20 | 价格: 8.80\n", "BUY: 2024-12-23 | 价格: 8.77 | 交易量: 5,344,500 | 平均交易量: 2,447,413\n", "SELL (时间退出): 2024-12-23 | 价格: 8.71\n", "BUY: 2024-12-26 | 价格: 8.70 | 交易量: 4,445,700 | 平均交易量: 1,834,290\n", "SELL (时间退出): 2024-12-26 | 价格: 8.78\n", "BUY: 2024-12-27 | 价格: 8.92 | 交易量: 6,572,800 | 平均交易量: 1,897,427\n", "SELL (时间退出): 2024-12-27 | 价格: 8.97\n", "BUY: 2024-12-30 | 价格: 9.23 | 交易量: 21,850,462 | 平均交易量: 3,724,841\n", "SELL (时间退出): 2024-12-30 | 价格: 9.12\n", "BUY: 2025-01-02 | 价格: 8.76 | 交易量: 6,324,278 | 平均交易量: 3,133,678\n", "SELL (时间退出): 2025-01-02 | 价格: 8.71\n", "BUY: 2025-01-03 | 价格: 8.70 | 交易量: 16,490,137 | 平均交易量: 4,217,630\n", "SELL (时间退出): 2025-01-03 | 价格: 8.66\n", "BUY: 2025-01-06 | 价格: 8.42 | 交易量: 17,745,064 | 平均交易量: 5,543,380\n", "SELL (时间退出): 2025-01-06 | 价格: 8.20\n", "BUY: 2025-01-08 | 价格: 8.19 | 交易量: 6,107,291 | 平均交易量: 2,842,312\n", "SELL (时间退出): 2025-01-08 | 价格: 7.89\n", "BUY: 2025-01-13 | 价格: 7.52 | 交易量: 8,088,200 | 平均交易量: 2,800,453\n", "SELL (时间退出): 2025-01-13 | 价格: 7.50\n", "BUY: 2025-01-14 | 价格: 7.80 | 交易量: 5,248,700 | 平均交易量: 2,538,647\n", "SELL (时间退出): 2025-01-14 | 价格: 8.01\n", "BUY: 2025-01-14 | 价格: 8.10 | 交易量: 6,427,200 | 平均交易量: 2,788,127\n", "SELL (时间退出): 2025-01-15 | 价格: 7.92\n", "BUY: 2025-01-16 | 价格: 8.12 | 交易量: 6,456,900 | 平均交易量: 3,042,134\n", "SELL (时间退出): 2025-01-16 | 价格: 8.04\n", "BUY: 2025-01-17 | 价格: 8.23 | 交易量: 4,171,300 | 平均交易量: 1,850,107\n", "SELL (时间退出): 2025-01-17 | 价格: 8.26\n", "BUY: 2025-01-20 | 价格: 8.37 | 交易量: 5,160,200 | 平均交易量: 2,082,273\n", "SELL (时间退出): 2025-01-20 | 价格: 8.30\n", "BUY: 2025-01-22 | 价格: 8.27 | 交易量: 2,819,000 | 平均交易量: 1,374,993\n", "SELL (时间退出): 2025-01-22 | 价格: 8.27\n", "BUY: 2025-01-23 | 价格: 8.32 | 交易量: 5,277,400 | 平均交易量: 1,400,840\n", "SELL (时间退出): 2025-01-23 | 价格: 8.28\n", "BUY: 2025-02-05 | 价格: 8.19 | 交易量: 3,204,096 | 平均交易量: 1,544,886\n", "SELL (时间退出): 2025-02-05 | 价格: 8.22\n", "BUY: 2025-02-06 | 价格: 8.21 | 交易量: 3,484,937 | 平均交易量: 1,422,896\n", "SELL (时间退出): 2025-02-06 | 价格: 8.26\n", "BUY: 2025-02-07 | 价格: 8.35 | 交易量: 3,837,906 | 平均交易量: 1,789,916\n", "SELL (时间退出): 2025-02-07 | 价格: 8.44\n", "BUY: 2025-02-11 | 价格: 8.22 | 交易量: 5,173,378 | 平均交易量: 2,142,822\n", "SELL (时间退出): 2025-02-11 | 价格: 8.24\n", "BUY: 2025-02-11 | 价格: 8.32 | 交易量: 4,058,600 | 平均交易量: 1,901,151\n", "SELL (时间退出): 2025-02-11 | 价格: 8.32\n", "BUY: 2025-02-12 | 价格: 8.34 | 交易量: 5,128,700 | 平均交易量: 1,954,138\n", "SELL (时间退出): 2025-02-12 | 价格: 8.32\n", "BUY: 2025-02-13 | 价格: 8.34 | 交易量: 4,568,093 | 平均交易量: 2,255,006\n", "SELL (时间退出): 2025-02-13 | 价格: 8.31\n", "BUY: 2025-02-14 | 价格: 8.45 | 交易量: 4,787,200 | 平均交易量: 1,949,905\n", "SELL (时间退出): 2025-02-17 | 价格: 8.39\n", "BUY: 2025-02-17 | 价格: 8.25 | 交易量: 6,048,200 | 平均交易量: 2,783,682\n", "SELL (时间退出): 2025-02-17 | 价格: 8.21\n", "BUY: 2025-02-20 | 价格: 8.08 | 交易量: 4,379,000 | 平均交易量: 2,090,156\n", "SELL (时间退出): 2025-02-20 | 价格: 8.10\n", "BUY: 2025-02-20 | 价格: 8.17 | 交易量: 7,247,496 | 平均交易量: 1,909,371\n", "SELL (时间退出): 2025-02-20 | 价格: 8.17\n", "BUY: 2025-02-24 | 价格: 8.36 | 交易量: 9,732,398 | 平均交易量: 2,596,700\n", "SELL (时间退出): 2025-02-24 | 价格: 8.56\n", "BUY: 2025-02-24 | 价格: 8.76 | 交易量: 19,041,200 | 平均交易量: 4,301,085\n", "SELL (时间退出): 2025-02-24 | 价格: 8.80\n", "BUY: 2025-02-26 | 价格: 9.11 | 交易量: 20,964,040 | 平均交易量: 8,196,346\n", "SELL (时间退出): 2025-02-26 | 价格: 9.17\n", "BUY: 2025-02-26 | 价格: 9.67 | 交易量: 29,088,444 | 平均交易量: 9,322,821\n", "SELL (止损): 2025-02-26 | 价格: 9.35\n"]}, {"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "/* global mpl */\n", "window.mpl = {};\n", "\n", "mpl.get_websocket_type = function () {\n", "    if (typeof WebSocket !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof MozWebSocket !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert(\n", "            'Your browser does not have WebSocket support. ' +\n", "                'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "                'Firefox 4 and 5 are also supported but you ' +\n", "                'have to enable WebSockets in about:config.'\n", "        );\n", "    }\n", "};\n", "\n", "mpl.figure = function (figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = this.ws.binaryType !== undefined;\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById('mpl-warnings');\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent =\n", "                'This browser does not support binary websocket messages. ' +\n", "                'Performance may be slow.';\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = document.createElement('div');\n", "    this.root.setAttribute('style', 'display: inline-block');\n", "    this._root_extra_style(this.root);\n", "\n", "    parent_element.appendChild(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen = function () {\n", "        fig.send_message('supports_binary', { value: fig.supports_binary });\n", "        fig.send_message('send_image_mode', {});\n", "        if (fig.ratio !== 1) {\n", "            fig.send_message('set_device_pixel_ratio', {\n", "                device_pixel_ratio: fig.ratio,\n", "            });\n", "        }\n", "        fig.send_message('refresh', {});\n", "    };\n", "\n", "    this.imageObj.onload = function () {\n", "        if (fig.image_mode === 'full') {\n", "            // Full images could contain transparency (where diff images\n", "            // almost always do), so we need to clear the canvas so that\n", "            // there is no ghosting.\n", "            fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "        }\n", "        fig.context.drawImage(fig.imageObj, 0, 0);\n", "    };\n", "\n", "    this.imageObj.onunload = function () {\n", "        fig.ws.close();\n", "    };\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "};\n", "\n", "mpl.figure.prototype._init_header = function () {\n", "    var titlebar = document.createElement('div');\n", "    titlebar.classList =\n", "        'ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix';\n", "    var titletext = document.createElement('div');\n", "    titletext.classList = 'ui-dialog-title';\n", "    titletext.setAttribute(\n", "        'style',\n", "        'width: 100%; text-align: center; padding: 3px;'\n", "    );\n", "    titlebar.appendChild(titletext);\n", "    this.root.appendChild(titlebar);\n", "    this.header = titletext;\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._root_extra_style = function (_canvas_div) {};\n", "\n", "mpl.figure.prototype._init_canvas = function () {\n", "    var fig = this;\n", "\n", "    var canvas_div = (this.canvas_div = document.createElement('div'));\n", "    canvas_div.setAttribute('tabindex', '0');\n", "    canvas_div.setAttribute(\n", "        'style',\n", "        'border: 1px solid #ddd;' +\n", "            'box-sizing: content-box;' +\n", "            'clear: both;' +\n", "            'min-height: 1px;' +\n", "            'min-width: 1px;' +\n", "            'outline: 0;' +\n", "            'overflow: hidden;' +\n", "            'position: relative;' +\n", "            'resize: both;' +\n", "            'z-index: 2;'\n", "    );\n", "\n", "    function on_keyboard_event_closure(name) {\n", "        return function (event) {\n", "            return fig.key_event(event, name);\n", "        };\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'keydown',\n", "        on_keyboard_event_closure('key_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'keyup',\n", "        on_keyboard_event_closure('key_release')\n", "    );\n", "\n", "    this._canvas_extra_style(canvas_div);\n", "    this.root.appendChild(canvas_div);\n", "\n", "    var canvas = (this.canvas = document.createElement('canvas'));\n", "    canvas.classList.add('mpl-canvas');\n", "    canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'pointer-events: none;' +\n", "            'position: relative;' +\n", "            'z-index: 0;'\n", "    );\n", "\n", "    this.context = canvas.getContext('2d');\n", "\n", "    var backingStore =\n", "        this.context.backingStorePixelRatio ||\n", "        this.context.webkitBackingStorePixelRatio ||\n", "        this.context.mozBackingStorePixelRatio ||\n", "        this.context.msBackingStorePixelRatio ||\n", "        this.context.oBackingStorePixelRatio ||\n", "        this.context.backingStorePixelRatio ||\n", "        1;\n", "\n", "    this.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband_canvas = (this.rubberband_canvas = document.createElement(\n", "        'canvas'\n", "    ));\n", "    rubberband_canvas.setAttribute(\n", "        'style',\n", "        'box-sizing: content-box;' +\n", "            'left: 0;' +\n", "            'pointer-events: none;' +\n", "            'position: absolute;' +\n", "            'top: 0;' +\n", "            'z-index: 1;'\n", "    );\n", "\n", "    // Apply a ponyfill if ResizeObserver is not implemented by browser.\n", "    if (this.ResizeObserver === undefined) {\n", "        if (window.ResizeObserver !== undefined) {\n", "            this.ResizeObserver = window.ResizeObserver;\n", "        } else {\n", "            var obs = _JSXTOOLS_RESIZE_OBSERVER({});\n", "            this.ResizeObserver = obs.ResizeObserver;\n", "        }\n", "    }\n", "\n", "    this.resizeObserverInstance = new this.ResizeObserver(function (entries) {\n", "        // There's no need to resize if the WebSocket is not connected:\n", "        // - If it is still connecting, then we will get an initial resize from\n", "        //   Python once it connects.\n", "        // - If it has disconnected, then resizing will clear the canvas and\n", "        //   never get anything back to refill it, so better to not resize and\n", "        //   keep something visible.\n", "        if (fig.ws.readyState != 1) {\n", "            return;\n", "        }\n", "        var nentries = entries.length;\n", "        for (var i = 0; i < nentries; i++) {\n", "            var entry = entries[i];\n", "            var width, height;\n", "            if (entry.contentBoxSize) {\n", "                if (entry.contentBoxSize instanceof Array) {\n", "                    // Chrome 84 implements new version of spec.\n", "                    width = entry.contentBoxSize[0].inlineSize;\n", "                    height = entry.contentBoxSize[0].blockSize;\n", "                } else {\n", "                    // Firefox implements old version of spec.\n", "                    width = entry.contentBoxSize.inlineSize;\n", "                    height = entry.contentBoxSize.blockSize;\n", "                }\n", "            } else {\n", "                // Chrome <84 implements even older version of spec.\n", "                width = entry.contentRect.width;\n", "                height = entry.contentRect.height;\n", "            }\n", "\n", "            // Keep the size of the canvas and rubber band canvas in sync with\n", "            // the canvas container.\n", "            if (entry.devicePixelContentBoxSize) {\n", "                // Chrome 84 implements new version of spec.\n", "                canvas.setAttribute(\n", "                    'width',\n", "                    entry.devicePixelContentBoxSize[0].inlineSize\n", "                );\n", "                canvas.setAttribute(\n", "                    'height',\n", "                    entry.devicePixelContentBoxSize[0].blockSize\n", "                );\n", "            } else {\n", "                canvas.setAttribute('width', width * fig.ratio);\n", "                canvas.setAttribute('height', height * fig.ratio);\n", "            }\n", "            /* This rescales the canvas back to display pixels, so that it\n", "             * appears correct on HiDPI screens. */\n", "            canvas.style.width = width + 'px';\n", "            canvas.style.height = height + 'px';\n", "\n", "            rubberband_canvas.setAttribute('width', width);\n", "            rubberband_canvas.setAttribute('height', height);\n", "\n", "            // And update the size in Python. We ignore the initial 0/0 size\n", "            // that occurs as the element is placed into the DOM, which should\n", "            // otherwise not happen due to the minimum size styling.\n", "            if (width != 0 && height != 0) {\n", "                fig.request_resize(width, height);\n", "            }\n", "        }\n", "    });\n", "    this.resizeObserverInstance.observe(canvas_div);\n", "\n", "    function on_mouse_event_closure(name) {\n", "        /* User Agent sniffing is bad, but WebKit is busted:\n", "         * https://bugs.webkit.org/show_bug.cgi?id=144526\n", "         * https://bugs.webkit.org/show_bug.cgi?id=181818\n", "         * The worst that happens here is that they get an extra browser\n", "         * selection when dragging, if this check fails to catch them.\n", "         */\n", "        var UA = navigator.userAgent;\n", "        var isWebKit = /AppleWebKit/.test(UA) && !/Chrome/.test(UA);\n", "        if(isWebKit) {\n", "            return function (event) {\n", "                /* This prevents the web browser from automatically changing to\n", "                 * the text insertion cursor when the button is pressed. We\n", "                 * want to control all of the cursor setting manually through\n", "                 * the 'cursor' event from matplotlib */\n", "                event.preventDefault()\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        } else {\n", "            return function (event) {\n", "                return fig.mouse_event(event, name);\n", "            };\n", "        }\n", "    }\n", "\n", "    canvas_div.addEventListener(\n", "        'mousedown',\n", "        on_mouse_event_closure('button_press')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseup',\n", "        on_mouse_event_closure('button_release')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'dblclick',\n", "        on_mouse_event_closure('dblclick')\n", "    );\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    canvas_div.addEventListener(\n", "        'mousemove',\n", "        on_mouse_event_closure('motion_notify')\n", "    );\n", "\n", "    canvas_div.addEventListener(\n", "        'mouseenter',\n", "        on_mouse_event_closure('figure_enter')\n", "    );\n", "    canvas_div.addEventListener(\n", "        'mouseleave',\n", "        on_mouse_event_closure('figure_leave')\n", "    );\n", "\n", "    canvas_div.addEventListener('wheel', function (event) {\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        on_mouse_event_closure('scroll')(event);\n", "    });\n", "\n", "    canvas_div.appendChild(canvas);\n", "    canvas_div.appendChild(rubberband_canvas);\n", "\n", "    this.rubberband_context = rubberband_canvas.getContext('2d');\n", "    this.rubberband_context.strokeStyle = '#000000';\n", "\n", "    this._resize_canvas = function (width, height, forward) {\n", "        if (forward) {\n", "            canvas_div.style.width = width + 'px';\n", "            canvas_div.style.height = height + 'px';\n", "        }\n", "    };\n", "\n", "    // Disable right mouse context menu.\n", "    canvas_div.addEventListener('contextmenu', function (_e) {\n", "        event.preventDefault();\n", "        return false;\n", "    });\n", "\n", "    function set_focus() {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'mpl-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'mpl-button-group';\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'mpl-button-group';\n", "            continue;\n", "        }\n", "\n", "        var button = (fig.buttons[name] = document.createElement('button'));\n", "        button.classList = 'mpl-widget';\n", "        button.setAttribute('role', 'button');\n", "        button.setAttribute('aria-disabled', 'false');\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "\n", "        var icon_img = document.createElement('img');\n", "        icon_img.src = '_images/' + image + '.png';\n", "        icon_img.srcset = '_images/' + image + '_large.png 2x';\n", "        icon_img.alt = tooltip;\n", "        button.appendChild(icon_img);\n", "\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    var fmt_picker = document.createElement('select');\n", "    fmt_picker.classList = 'mpl-widget';\n", "    toolbar.appendChild(fmt_picker);\n", "    this.format_dropdown = fmt_picker;\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = document.createElement('option');\n", "        option.selected = fmt === mpl.default_extension;\n", "        option.innerHTML = fmt;\n", "        fmt_picker.appendChild(option);\n", "    }\n", "\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "};\n", "\n", "mpl.figure.prototype.request_resize = function (x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', { width: x_pixels, height: y_pixels });\n", "};\n", "\n", "mpl.figure.prototype.send_message = function (type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "};\n", "\n", "mpl.figure.prototype.send_draw_message = function () {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({ type: 'draw', figure_id: this.id }));\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "};\n", "\n", "mpl.figure.prototype.handle_resize = function (fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] !== fig.canvas.width || size[1] !== fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1], msg['forward']);\n", "        fig.send_message('refresh', {});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_rubberband = function (fig, msg) {\n", "    var x0 = msg['x0'] / fig.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / fig.ratio;\n", "    var x1 = msg['x1'] / fig.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / fig.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0,\n", "        0,\n", "        fig.canvas.width / fig.ratio,\n", "        fig.canvas.height / fig.ratio\n", "    );\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "};\n", "\n", "mpl.figure.prototype.handle_figure_label = function (fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "};\n", "\n", "mpl.figure.prototype.handle_cursor = function (fig, msg) {\n", "    fig.canvas_div.style.cursor = msg['cursor'];\n", "};\n", "\n", "mpl.figure.prototype.handle_message = function (fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "};\n", "\n", "mpl.figure.prototype.handle_draw = function (fig, _msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "};\n", "\n", "mpl.figure.prototype.handle_image_mode = function (fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "};\n", "\n", "mpl.figure.prototype.handle_history_buttons = function (fig, msg) {\n", "    for (var key in msg) {\n", "        if (!(key in fig.buttons)) {\n", "            continue;\n", "        }\n", "        fig.buttons[key].disabled = !msg[key];\n", "        fig.buttons[key].setAttribute('aria-disabled', !msg[key]);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_navigate_mode = function (fig, msg) {\n", "    if (msg['mode'] === 'PAN') {\n", "        fig.buttons['Pan'].classList.add('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    } else if (msg['mode'] === 'ZOOM') {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.add('active');\n", "    } else {\n", "        fig.buttons['Pan'].classList.remove('active');\n", "        fig.buttons['Zoom'].classList.remove('active');\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message('ack', {});\n", "};\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function (fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            var img = evt.data;\n", "            if (img.type !== 'image/png') {\n", "                /* FIXME: We get \"Resource interpreted as Image but\n", "                 * transferred with MIME type text/plain:\" errors on\n", "                 * Chrome.  But how to set the MIME type?  It doesn't seem\n", "                 * to be part of the websocket stream */\n", "                img.type = 'image/png';\n", "            }\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src\n", "                );\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                img\n", "            );\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        } else if (\n", "            typeof evt.data === 'string' &&\n", "            evt.data.slice(0, 21) === 'data:image/png;base64'\n", "        ) {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig['handle_' + msg_type];\n", "        } catch (e) {\n", "            console.log(\n", "                \"No handler for the '\" + msg_type + \"' message type: \",\n", "                msg\n", "            );\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\n", "                    \"Exception inside the 'handler_\" + msg_type + \"' callback:\",\n", "                    e,\n", "                    e.stack,\n", "                    msg\n", "                );\n", "            }\n", "        }\n", "    };\n", "};\n", "\n", "function getModifiers(event) {\n", "    var mods = [];\n", "    if (event.ctrlKey) {\n", "        mods.push('ctrl');\n", "    }\n", "    if (event.altKey) {\n", "        mods.push('alt');\n", "    }\n", "    if (event.shiftKey) {\n", "        mods.push('shift');\n", "    }\n", "    if (event.metaKey) {\n", "        mods.push('meta');\n", "    }\n", "    return mods;\n", "}\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * https://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s(original) {\n", "    return Object.keys(original).reduce(function (obj, key) {\n", "        if (typeof original[key] !== 'object') {\n", "            obj[key] = original[key];\n", "        }\n", "        return obj;\n", "    }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function (event, name) {\n", "    if (name === 'button_press') {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    // from https://stackoverflow.com/q/1114465\n", "    var boundingRect = this.canvas.getBoundingClientRect();\n", "    var x = (event.clientX - boundingRect.left) * this.ratio;\n", "    var y = (event.clientY - boundingRect.top) * this.ratio;\n", "\n", "    this.send_message(name, {\n", "        x: x,\n", "        y: y,\n", "        button: event.button,\n", "        step: event.step,\n", "        modifiers: getModifiers(event),\n", "        guiEvent: simple<PERSON>eys(event),\n", "    });\n", "\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (_event, _name) {\n", "    // Handle any extra behaviour associated with a key event\n", "};\n", "\n", "mpl.figure.prototype.key_event = function (event, name) {\n", "    // Prevent repeat events\n", "    if (name === 'key_press') {\n", "        if (event.key === this._key) {\n", "            return;\n", "        } else {\n", "            this._key = event.key;\n", "        }\n", "    }\n", "    if (name === 'key_release') {\n", "        this._key = null;\n", "    }\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.key !== 'Control') {\n", "        value += 'ctrl+';\n", "    }\n", "    else if (event.altKey && event.key !== 'Alt') {\n", "        value += 'alt+';\n", "    }\n", "    else if (event.shiftKey && event.key !== 'Shift') {\n", "        value += 'shift+';\n", "    }\n", "\n", "    value += 'k' + event.key;\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, { key: value, guiEvent: simpleKeys(event) });\n", "    return false;\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function (name) {\n", "    if (name === 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message('toolbar_button', { name: name });\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function (tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "\n", "///////////////// REMAINING CONTENT GENERATED BY embed_js.py /////////////////\n", "// prettier-ignore\n", "var _JSXTOOLS_RESIZE_OBSERVER=function(A){var t,i=new WeakMap,n=new WeakMap,a=new WeakMap,r=new WeakMap,o=new Set;function s(e){if(!(this instanceof s))throw new TypeError(\"Constructor requires 'new' operator\");i.set(this,e)}function h(){throw new TypeError(\"Function is not a constructor\")}function c(e,t,i,n){e=0 in arguments?Number(arguments[0]):0,t=1 in arguments?Number(arguments[1]):0,i=2 in arguments?Number(arguments[2]):0,n=3 in arguments?Number(arguments[3]):0,this.right=(this.x=this.left=e)+(this.width=i),this.bottom=(this.y=this.top=t)+(this.height=n),Object.freeze(this)}function d(){t=requestAnimationFrame(d);var s=new WeakMap,p=new Set;o.forEach((function(t){r.get(t).forEach((function(i){var r=t instanceof window.SVGElement,o=a.get(t),d=r?0:parseFloat(o.paddingTop),f=r?0:parseFloat(o.paddingRight),l=r?0:parseFloat(o.paddingBottom),u=r?0:parseFloat(o.paddingLeft),g=r?0:parseFloat(o.borderTopWidth),m=r?0:parseFloat(o.borderRightWidth),w=r?0:parseFloat(o.borderBottomWidth),b=u+f,F=d+l,v=(r?0:parseFloat(o.borderLeftWidth))+m,W=g+w,y=r?0:t.offsetHeight-W-t.clientHeight,E=r?0:t.offsetWidth-v-t.clientWidth,R=b+v,z=F+W,M=r?t.width:parseFloat(o.width)-R-E,O=r?t.height:parseFloat(o.height)-z-y;if(n.has(t)){var k=n.get(t);if(k[0]===M&&k[1]===O)return}n.set(t,[M,O]);var S=Object.create(h.prototype);S.target=t,S.contentRect=new c(u,d,M,O),s.has(i)||(s.set(i,[]),p.add(i)),s.get(i).push(S)}))})),p.forEach((function(e){i.get(e).call(e,s.get(e),e)}))}return s.prototype.observe=function(i){if(i instanceof window.Element){r.has(i)||(r.set(i,new Set),o.add(i),a.set(i,window.getComputedStyle(i)));var n=r.get(i);n.has(this)||n.add(this),cancelAnimationFrame(t),t=requestAnimationFrame(d)}},s.prototype.unobserve=function(i){if(i instanceof window.Element&&r.has(i)){var n=r.get(i);n.has(this)&&(n.delete(this),n.size||(r.delete(i),o.delete(i))),n.size||r.delete(i),o.size||cancelAnimationFrame(t)}},A.DOMRectReadOnly=c,A.ResizeObserver=s,A.ResizeObserverEntry=h,A}; // eslint-disable-line\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Left button pans, Right button zooms\\nx/y fixes axis, CTRL fixes aspect\", \"fa fa-arrows\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\\nx/y fixes axis\", \"fa fa-square-o\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pgf\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\", \"webp\"];\n", "\n", "mpl.default_extension = \"png\";/* global mpl */\n", "\n", "var comm_websocket_adapter = function (comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.binaryType = comm.kernel.ws.binaryType;\n", "    ws.readyState = comm.kernel.ws.readyState;\n", "    function updateReadyState(_event) {\n", "        if (comm.kernel.ws) {\n", "            ws.readyState = comm.kernel.ws.readyState;\n", "        } else {\n", "            ws.readyState = 3; // Closed state.\n", "        }\n", "    }\n", "    comm.kernel.ws.addEventListener('open', updateReadyState);\n", "    comm.kernel.ws.addEventListener('close', updateReadyState);\n", "    comm.kernel.ws.addEventListener('error', updateReadyState);\n", "\n", "    ws.close = function () {\n", "        comm.close();\n", "    };\n", "    ws.send = function (m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function (msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        var data = msg['content']['data'];\n", "        if (data['blob'] !== undefined) {\n", "            data = {\n", "                data: new Blob(msg['buffers'], { type: data['blob'] }),\n", "            };\n", "        }\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(data);\n", "    });\n", "    return ws;\n", "};\n", "\n", "mpl.mpl_figure_comm = function (comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = document.getElementById(id);\n", "    var ws_proxy = comm_websocket_adapter(comm);\n", "\n", "    function ondownload(figure, _format) {\n", "        window.open(figure.canvas.toDataURL());\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy, ondownload, element);\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element;\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error('Failed to find cell for figure', id, fig);\n", "        return;\n", "    }\n", "    fig.cell_info[0].output_area.element.on(\n", "        'cleared',\n", "        { fig: fig },\n", "        fig._remove_fig_handler\n", "    );\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function (fig, msg) {\n", "    var width = fig.canvas.width / fig.ratio;\n", "    fig.cell_info[0].output_area.element.off(\n", "        'cleared',\n", "        fig._remove_fig_handler\n", "    );\n", "    fig.resizeObserverInstance.unobserve(fig.canvas_div);\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable();\n", "    fig.parent_element.innerHTML =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "    fig.close_ws(fig, msg);\n", "};\n", "\n", "mpl.figure.prototype.close_ws = function (fig, msg) {\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "};\n", "\n", "mpl.figure.prototype.push_to_output = function (_remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width / this.ratio;\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] =\n", "        '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "};\n", "\n", "mpl.figure.prototype.updated_canvas_event = function () {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message('ack', {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () {\n", "        fig.push_to_output();\n", "    }, 1000);\n", "};\n", "\n", "mpl.figure.prototype._init_toolbar = function () {\n", "    var fig = this;\n", "\n", "    var toolbar = document.createElement('div');\n", "    toolbar.classList = 'btn-toolbar';\n", "    this.root.appendChild(toolbar);\n", "\n", "    function on_click_closure(name) {\n", "        return function (_event) {\n", "            return fig.toolbar_button_onclick(name);\n", "        };\n", "    }\n", "\n", "    function on_mouseover_closure(tooltip) {\n", "        return function (event) {\n", "            if (!event.currentTarget.disabled) {\n", "                return fig.toolbar_button_onmouseover(tooltip);\n", "            }\n", "        };\n", "    }\n", "\n", "    fig.buttons = {};\n", "    var buttonGroup = document.createElement('div');\n", "    buttonGroup.classList = 'btn-group';\n", "    var button;\n", "    for (var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            /* Instead of a spacer, we start a new button group. */\n", "            if (buttonGroup.hasChildNodes()) {\n", "                toolbar.appendChild(buttonGroup);\n", "            }\n", "            buttonGroup = document.createElement('div');\n", "            buttonGroup.classList = 'btn-group';\n", "            continue;\n", "        }\n", "\n", "        button = fig.buttons[name] = document.createElement('button');\n", "        button.classList = 'btn btn-default';\n", "        button.href = '#';\n", "        button.title = name;\n", "        button.innerHTML = '<i class=\"fa ' + image + ' fa-lg\"></i>';\n", "        button.addEventListener('click', on_click_closure(method_name));\n", "        button.addEventListener('mouseover', on_mouseover_closure(tooltip));\n", "        buttonGroup.appendChild(button);\n", "    }\n", "\n", "    if (buttonGroup.hasChildNodes()) {\n", "        toolbar.appendChild(buttonGroup);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = document.createElement('span');\n", "    status_bar.classList = 'mpl-message pull-right';\n", "    toolbar.appendChild(status_bar);\n", "    this.message = status_bar;\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = document.createElement('div');\n", "    buttongrp.classList = 'btn-group inline pull-right';\n", "    button = document.createElement('button');\n", "    button.classList = 'btn btn-mini btn-primary';\n", "    button.href = '#';\n", "    button.title = 'Stop Interaction';\n", "    button.innerHTML = '<i class=\"fa fa-power-off icon-remove icon-large\"></i>';\n", "    button.addEventListener('click', function (_evt) {\n", "        fig.handle_close(fig, {});\n", "    });\n", "    button.addEventListener(\n", "        'mouseover',\n", "        on_mouseover_closure('Stop Interaction')\n", "    );\n", "    buttongrp.appendChild(button);\n", "    var titlebar = this.root.querySelector('.ui-dialog-titlebar');\n", "    titlebar.insertBefore(buttongrp, titlebar.firstChild);\n", "};\n", "\n", "mpl.figure.prototype._remove_fig_handler = function (event) {\n", "    var fig = event.data.fig;\n", "    if (event.target !== this) {\n", "        // Ignore bubbled events from children.\n", "        return;\n", "    }\n", "    fig.close_ws(fig, {});\n", "};\n", "\n", "mpl.figure.prototype._root_extra_style = function (el) {\n", "    el.style.boxSizing = 'content-box'; // override notebook setting of border-box.\n", "};\n", "\n", "mpl.figure.prototype._canvas_extra_style = function (el) {\n", "    // this is important to make the div 'focusable\n", "    el.setAttribute('tabindex', 0);\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    } else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype._key_event_extra = function (event, _name) {\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which === 13) {\n", "        this.canvas_div.blur();\n", "        // select the cell after this one\n", "        var index = IPython.notebook.find_cell_index(this.cell_info[0]);\n", "        IPython.notebook.select(index + 1);\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.handle_save = function (fig, _msg) {\n", "    fig.ondownload(fig, null);\n", "};\n", "\n", "mpl.find_output_cell = function (html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i = 0; i < ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code') {\n", "            for (var j = 0; j < cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] === html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "};\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel !== null) {\n", "    IPython.notebook.kernel.comm_manager.register_target(\n", "        'matplotlib',\n", "        mpl.mpl_figure_comm\n", "    );\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div id='0732f1fa-8ffb-45d2-bc78-1e92e7030eb6'></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 数据预处理\n", "stock_data['trade_time'] = pd.to_datetime(stock_data['trade_time'])\n", "\n", "# 设置策略参数\n", "strategy_params = {\n", "    'volume_period': 15,   # 计算平均交易量的周期\n", "    'volume_mult': 2.0,    # 交易量倍数阈值\n", "    'exit_bars': 3,        # 持有的bar数量\n", "    'stop_loss': 0.03,     # 止损比例\n", "    'take_profit': 0.06    # 止盈比例\n", "}\n", "\n", "# 绘图参数 - 解决图表过大问题\n", "plot_args = {\n", "    'width': 1200,          # 图表宽度\n", "    'height': 800,          # 图表高度\n", "    'num_plots': 500,       # 只显示最近的500个数据点\n", "    'style': 'candle',      # 图表风格\n", "    'skip_plotlines': True  # 跳过绘制交易线，减少图表复杂度\n", "}\n", "\n", "# 运行回测\n", "results = run_backtest(stock_data, \n", "       strategy=VolumeBreakoutStrategy,\n", "       strategy_params=strategy_params,\n", "       initial_cash=100000.0,\n", "       commission=0.0003,  # 0.03% 佣金\n", "       plot=True,\n", "       plot_args=plot_args)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["datetime64[ns]\n"]}], "source": ["stock_data['trade_time'] = pd.to_datetime(stock_data['trade_time'])\n", "print(stock_data['trade_time'].dtype)  # 确认输出为 datetime64[ns]\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "KeyboardInterrupt\n", "\n"]}], "source": ["from IPython.display import display\n", "figs = cerebro.plot(style='candlestick', iplot=False)\n", "for fig_group in figs:\n", "    for fig in fig_group:\n", "        display(fig)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"scrolled": true}, "outputs": [], "source": ["# 绘制回测结果图形（可以选择不同的样式，如 'candlestick' 或 'bar'）\n", "%matplotlib inline\n", "cerebro.plot(style='candlestick', iplot=False)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["<backtrader.cerebro.Cerebro at 0x1357d93d0>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["cerebro"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["plt.show()"]}, {"cell_type": "code", "execution_count": 119, "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 119, "metadata": {}, "output_type": "execute_result"}], "source": ["%matplotlib inline\n", "# 使用Backtrader的特定参数来控制绘图\n", "figs = cerebro.plot(\n", "    style='candlestick',\n", "    volume=False,\n", "    iplot=False,\n", "    figsize=(12, 8),\n", "    plotdist=0.1,  # 减小子图之间的距离\n", "    barup='g',     # 简化上涨蜡烛图样式\n", "    bardown='r',   # 简化下跌蜡烛图样式\n", "    grid=False,    # 关闭网格线\n", "    rows=1,        # 限制为单行图表\n", "    cols=1,        # 限制为单列图表\n", "    **{'start': len(stock_data) - 100, 'end': len(stock_data)}  # 只显示最后100个数据点\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# 8. 参数优化"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["# 这个单元格是可选的，如果您想优化策略参数\n", "def optimize_ma_strategy(data, ma_fast_range, ma_slow_range):\n", "    \"\"\"优化MA策略参数\"\"\"\n", "    results = []\n", "    \n", "    for ma_fast in ma_fast_range:\n", "        for ma_slow in ma_slow_range:\n", "            if ma_fast >= ma_slow:\n", "                continue  # 快速MA周期不应大于或等于慢速MA周期\n", "                \n", "            # 创建Cerebro引擎\n", "            cerebro = bt.<PERSON><PERSON><PERSON>(stdstats=False)\n", "            \n", "            # 添加数据\n", "            if isinstance(data, pd.DataFrame):\n", "                data_feed = df_to_btfeed(data)\n", "                cerebro.adddata(data_feed)\n", "            else:\n", "                cerebro.adddata(data)\n", "                \n", "            # 添加策略\n", "            cerebro.addstrategy(MAStrategy, ma_fast=ma_fast, ma_slow=ma_slow, printlog=False)\n", "            \n", "            # 设置初始资金和佣金\n", "            cerebro.broker.setcash(100000.0)\n", "            cerebro.broker.setcommission(commission=0.001)\n", "            \n", "            # 添加分析器\n", "            cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')\n", "            cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')\n", "            cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')\n", "            \n", "            # 执行回测\n", "            print(f\"测试参数: MA快速={ma_fast}, MA慢速={ma_slow}\")\n", "            res = cerebro.run()\n", "            strat = res[0]\n", "            \n", "            # 收集结果\n", "            ret = strat.analyzers.returns.get_analysis()\n", "            dd = strat.analyzers.drawdown.get_analysis()\n", "            sharpe = strat.analyzers.sharpe.get_analysis()\n", "            \n", "            # 存储结果\n", "            results.append({\n", "                'ma_fast': ma_fast,\n", "                'ma_slow': ma_slow,\n", "                'return': ret.get('rtot', 0) * 100,  # 总收益率(%)\n", "                'annual_return': ret.get('rnorm100', 0),  # 年化收益率(%)\n", "                'max_drawdown': dd.get('max', {}).get('drawdown', 0),  # 最大回撤(%)\n", "                'sharpe': sharpe.get('sharperatio', 0),  # 夏普比率\n", "                'final_value': cerebro.broker.getvalue()\n", "            })\n", "            \n", "    # 转换为DataFrame并按总收益率排序\n", "    results_df = pd.DataFrame(results)\n", "    results_df = results_df.sort_values('return', ascending=False)\n", "    \n", "    return results_df\n", "\n", "# 示例使用\n", "# optimize_results = optimize_ma_strategy(\n", "#     data=pingan_data,\n", "#     ma_fast_range=[5, 10, 15, 20],\n", "#     ma_slow_range=[20, 30, 40, 50]\n", "# )\n", "# optimize_results.head()"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["测试参数: MA快速=5, MA慢速=20\n", "2022-12-30, (MA周期 快速/慢速) 5/20\n", "2022-12-30, 期末资金: 100000.59\n", "测试参数: MA快速=5, MA慢速=30\n", "2022-12-30, (MA周期 快速/慢速) 5/30\n", "2022-12-30, 期末资金: 100001.55\n", "测试参数: MA快速=5, MA慢速=40\n", "2022-12-30, (MA周期 快速/慢速) 5/40\n", "2022-12-30, 期末资金: 100002.62\n", "测试参数: MA快速=5, MA慢速=50\n", "2022-12-30, (MA周期 快速/慢速) 5/50\n", "2022-12-30, 期末资金: 99998.82\n", "测试参数: MA快速=10, MA慢速=20\n", "2022-12-30, (MA周期 快速/慢速) 10/20\n", "2022-12-30, 期末资金: 99998.30\n", "测试参数: MA快速=10, MA慢速=30\n", "2022-12-30, (MA周期 快速/慢速) 10/30\n", "2022-12-30, 期末资金: 99998.00\n", "测试参数: MA快速=10, MA慢速=40\n", "2022-12-30, (MA周期 快速/慢速) 10/40\n", "2022-12-30, 期末资金: 99999.15\n", "测试参数: MA快速=10, MA慢速=50\n", "2022-12-30, (MA周期 快速/慢速) 10/50\n", "2022-12-30, 期末资金: 100000.89\n", "测试参数: MA快速=15, MA慢速=20\n", "2022-12-30, (MA周期 快速/慢速) 15/20\n", "2022-12-30, 期末资金: 99994.15\n", "测试参数: MA快速=15, MA慢速=30\n", "2022-12-30, (MA周期 快速/慢速) 15/30\n", "2022-12-30, 期末资金: 99997.62\n", "测试参数: MA快速=15, MA慢速=40\n", "2022-12-30, (MA周期 快速/慢速) 15/40\n", "2022-12-30, 期末资金: 99999.58\n", "测试参数: MA快速=15, MA慢速=50\n", "2022-12-30, (MA周期 快速/慢速) 15/50\n", "2022-12-30, 期末资金: 99999.39\n", "测试参数: MA快速=20, MA慢速=30\n", "2022-12-30, (MA周期 快速/慢速) 20/30\n", "2022-12-30, 期末资金: 99996.02\n", "测试参数: MA快速=20, MA慢速=40\n", "2022-12-30, (MA周期 快速/慢速) 20/40\n", "2022-12-30, 期末资金: 99999.55\n", "测试参数: MA快速=20, MA慢速=50\n", "2022-12-30, (MA周期 快速/慢速) 20/50\n", "2022-12-30, 期末资金: 99992.44\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ma_fast</th>\n", "      <th>ma_slow</th>\n", "      <th>return</th>\n", "      <th>annual_return</th>\n", "      <th>max_drawdown</th>\n", "      <th>sharpe</th>\n", "      <th>final_value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5</td>\n", "      <td>40</td>\n", "      <td>0.002624</td>\n", "      <td>0.002733</td>\n", "      <td>0.005771</td>\n", "      <td>None</td>\n", "      <td>100002.62416</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5</td>\n", "      <td>30</td>\n", "      <td>0.001547</td>\n", "      <td>0.001611</td>\n", "      <td>0.004243</td>\n", "      <td>None</td>\n", "      <td>100001.54740</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>10</td>\n", "      <td>50</td>\n", "      <td>0.000893</td>\n", "      <td>0.000930</td>\n", "      <td>0.005831</td>\n", "      <td>None</td>\n", "      <td>100000.89321</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5</td>\n", "      <td>20</td>\n", "      <td>0.000587</td>\n", "      <td>0.000611</td>\n", "      <td>0.005539</td>\n", "      <td>None</td>\n", "      <td>100000.58683</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>15</td>\n", "      <td>40</td>\n", "      <td>-0.000417</td>\n", "      <td>-0.000434</td>\n", "      <td>0.006621</td>\n", "      <td>None</td>\n", "      <td>99999.58284</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ma_fast  ma_slow    return  annual_return  max_drawdown sharpe  \\\n", "2         5       40  0.002624       0.002733      0.005771   None   \n", "1         5       30  0.001547       0.001611      0.004243   None   \n", "7        10       50  0.000893       0.000930      0.005831   None   \n", "0         5       20  0.000587       0.000611      0.005539   None   \n", "10       15       40 -0.000417      -0.000434      0.006621   None   \n", "\n", "     final_value  \n", "2   100002.62416  \n", "1   100001.54740  \n", "7   100000.89321  \n", "0   100000.58683  \n", "10   99999.58284  "]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["optimize_results = optimize_ma_strategy(\n", "    data=pingan_data,\n", "    ma_fast_range=[5, 10, 15, 20],\n", "    ma_slow_range=[20, 30, 40, 50]\n", ")\n", "optimize_results.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}