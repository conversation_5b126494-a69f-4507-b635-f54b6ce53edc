"""
随机K线数据生成器

专为机器学习策略训练设计的简化数据生成模块。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional


class RandomDataGenerator:
    """随机K线数据生成器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化数据生成器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.data_config = self.config.get('data', {})
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        # 简化版本：直接返回默认配置
        return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'data': {
                'days': 1000,
                'initial_price': 100.0,
                'drift': 0.0001,
                'volatility': 0.015,
                'random_seed': 42
            }
        }
    
    def generate(self, days: Optional[int] = None, 
                 initial_price: Optional[float] = None,
                 drift: Optional[float] = None,
                 volatility: Optional[float] = None,
                 random_seed: Optional[int] = None) -> pd.DataFrame:
        """
        生成随机K线数据
        
        Args:
            days: 生成天数
            initial_price: 初始价格
            drift: 日收益率均值
            volatility: 日收益率波动率
            random_seed: 随机种子
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        # 使用参数或配置文件中的值
        days = days or self.data_config.get('days', 1000)
        initial_price = initial_price or self.data_config.get('initial_price', 100.0)
        drift = drift or self.data_config.get('drift', 0.0001)
        volatility = volatility or self.data_config.get('volatility', 0.015)
        random_seed = random_seed or self.data_config.get('random_seed', 42)
        
        # 设置随机种子
        np.random.seed(random_seed)
        
        print(f"生成随机K线数据: {days}天, 初始价格: {initial_price}")
        
        # 生成日对数收益率
        log_returns = np.random.normal(drift, volatility, days)
        
        # 计算收盘价路径
        close_prices = np.exp(np.cumsum(log_returns)) * initial_price
        
        # 生成OHLCV数据
        data = self._generate_ohlcv(close_prices)
        
        # 添加技术指标
        data = self._add_technical_indicators(data)
        
        print(f"数据生成完成: {len(data)}条记录, 价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
        
        return data
    
    def _generate_ohlcv(self, close_prices: np.ndarray) -> pd.DataFrame:
        """
        从收盘价生成完整的OHLCV数据
        
        Args:
            close_prices: 收盘价数组
            
        Returns:
            OHLCV数据框
        """
        num_days = len(close_prices)
        
        # 创建日期索引
        dates = pd.date_range(start='2020-01-01', periods=num_days, freq='D')
        
        # 生成开盘价（前一日收盘价加上小幅随机波动）
        open_prices = np.zeros(num_days)
        open_prices[0] = close_prices[0] * (1 + np.random.normal(0, 0.005))
        for i in range(1, num_days):
            open_prices[i] = close_prices[i-1] * (1 + np.random.normal(0, 0.005))
        
        # 生成最高价和最低价
        high_prices = np.zeros(num_days)
        low_prices = np.zeros(num_days)
        
        for i in range(num_days):
            # 最高价是开盘价和收盘价的最大值加上随机波动
            base_high = max(open_prices[i], close_prices[i])
            high_prices[i] = base_high * (1 + abs(np.random.normal(0, 0.01)))
            
            # 最低价是开盘价和收盘价的最小值减去随机波动
            base_low = min(open_prices[i], close_prices[i])
            low_prices[i] = base_low * (1 - abs(np.random.normal(0, 0.01)))
        
        # 生成成交量（基于价格波动）
        price_changes = np.abs(np.diff(close_prices, prepend=close_prices[0]))
        base_volume = 1000000  # 基础成交量
        volumes = base_volume * (1 + price_changes / close_prices * 10) * np.random.lognormal(0, 0.5, num_days)
        volumes = volumes.astype(int)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices,
            'volume': volumes
        }, index=dates)
        
        return df
    
    def _add_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        添加常用技术指标
        
        Args:
            data: 原始OHLCV数据
            
        Returns:
            包含技术指标的数据框
        """
        # 移动平均线
        for window in [5, 10, 20, 50]:
            data[f'MA{window}'] = data['close'].rolling(window=window).mean()
        
        # RSI
        data['RSI'] = self._calculate_rsi(data['close'], period=14)
        
        # 布林带
        data['BB_upper'], data['BB_middle'], data['BB_lower'] = self._calculate_bollinger_bands(
            data['close'], window=20, std_dev=2
        )
        
        # MACD
        data['MACD'], data['MACD_signal'], data['MACD_hist'] = self._calculate_macd(data['close'])
        
        # 价格变化率
        data['returns'] = data['close'].pct_change()
        
        return data
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_bollinger_bands(self, prices: pd.Series, window: int = 20, 
                                 std_dev: float = 2) -> tuple:
        """计算布林带"""
        middle = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper = middle + (std * std_dev)
        lower = middle - (std * std_dev)
        return upper, middle, lower
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, 
                       slow: int = 26, signal: int = 9) -> tuple:
        """计算MACD指标"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        macd_hist = macd - macd_signal
        return macd, macd_signal, macd_hist
    
    def generate_multiple_scenarios(self, n_scenarios: int = 10, 
                                  base_days: int = 1000) -> Dict[str, pd.DataFrame]:
        """
        生成多个不同市场情景的数据集
        
        Args:
            n_scenarios: 情景数量
            base_days: 基础天数
            
        Returns:
            情景名称到数据框的映射
        """
        scenarios = {}
        
        # 定义不同的市场情景
        market_scenarios = [
            {"name": "牛市", "drift": 0.0005, "volatility": 0.012},
            {"name": "熊市", "drift": -0.0003, "volatility": 0.018},
            {"name": "震荡市", "drift": 0.0001, "volatility": 0.015},
            {"name": "高波动", "drift": 0.0001, "volatility": 0.025},
            {"name": "低波动", "drift": 0.0001, "volatility": 0.008},
        ]
        
        for i in range(n_scenarios):
            scenario = market_scenarios[i % len(market_scenarios)]
            scenario_name = f"{scenario['name']}_{i+1}"
            
            data = self.generate(
                days=base_days,
                drift=scenario['drift'],
                volatility=scenario['volatility'],
                random_seed=42 + i
            )
            
            scenarios[scenario_name] = data
            print(f"生成情景: {scenario_name}")
        
        return scenarios


if __name__ == "__main__":
    # 测试数据生成器
    generator = RandomDataGenerator()
    
    # 生成单个数据集
    data = generator.generate(days=500)
    print(f"\n数据形状: {data.shape}")
    print(f"列名: {list(data.columns)}")
    print(f"\n前5行数据:")
    print(data.head())
    
    # 生成多个情景
    scenarios = generator.generate_multiple_scenarios(n_scenarios=3, base_days=200)
    print(f"\n生成了 {len(scenarios)} 个市场情景")
    for name in scenarios.keys():
        print(f"- {name}: {len(scenarios[name])} 条记录")
