"""
模拟经纪商

实现交易成本、滑点等真实交易环境的模拟。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

from ..config import get_config
from ..utils.logger import get_logger
from ..utils.exceptions import BacktestError, ValidationError


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


@dataclass
class Order:
    """订单类"""
    id: str
    symbol: str
    side: str  # 'buy' or 'sell'
    quantity: float
    order_type: OrderType
    price: Optional[float] = None
    stop_price: Optional[float] = None
    status: OrderStatus = OrderStatus.PENDING
    fill_price: Optional[float] = None
    fill_quantity: float = 0.0
    commission: float = 0.0
    timestamp: Optional[pd.Timestamp] = None
    fill_timestamp: Optional[pd.Timestamp] = None


@dataclass
class Position:
    """持仓类"""
    symbol: str
    quantity: float = 0.0
    avg_price: float = 0.0
    market_value: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0


class SimulatedBroker:
    """模拟经纪商"""
    
    def __init__(self, initial_capital: float = 100000.0, **kwargs):
        """
        初始化模拟经纪商
        
        Args:
            initial_capital: 初始资金
            **kwargs: 其他配置参数
        """
        self.logger = get_logger("backtest.SimulatedBroker")
        self.config = get_config()
        
        # 资金管理
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.total_value = initial_capital
        
        # 交易成本配置
        cost_config = self.config.get('backtest.costs', {})
        self.commission_rate_buy = kwargs.get('commission_rate_buy',
                                            cost_config.get('commission_rate_buy', 0.0001))
        self.commission_rate_sell = kwargs.get('commission_rate_sell',
                                             cost_config.get('commission_rate_sell', 0.0001))
        self.stamp_duty_rate = kwargs.get('stamp_duty_rate',
                                        cost_config.get('stamp_duty_rate', 0.0005))
        self.min_commission = kwargs.get('min_commission',
                                       cost_config.get('min_commission', 5.0))
        self.slippage_rate = kwargs.get('slippage_rate',
                                      cost_config.get('slippage_rate', 0.002))
        
        # 交易记录
        self.orders: List[Order] = []
        self.positions: Dict[str, Position] = {}
        self.trades: List[Dict[str, Any]] = []
        self.portfolio_history: List[Dict[str, Any]] = []
        
        # 统计信息
        self.total_commission = 0.0
        self.total_stamp_duty = 0.0
        self.total_slippage = 0.0
        self.order_counter = 0
        
        self.logger.info(f"模拟经纪商初始化完成: 初始资金 {initial_capital:,.2f}")
    
    def place_order(self, symbol: str, side: str, quantity: float,
                   order_type: OrderType = OrderType.MARKET,
                   price: Optional[float] = None,
                   timestamp: Optional[pd.Timestamp] = None) -> str:
        """
        下单
        
        Args:
            symbol: 交易标的
            side: 买卖方向 ('buy' or 'sell')
            quantity: 数量
            order_type: 订单类型
            price: 限价单价格
            timestamp: 时间戳
            
        Returns:
            订单ID
        """
        try:
            # 生成订单ID
            self.order_counter += 1
            order_id = f"ORDER_{self.order_counter:06d}"
            
            # 创建订单
            order = Order(
                id=order_id,
                symbol=symbol,
                side=side,
                quantity=quantity,
                order_type=order_type,
                price=price,
                timestamp=timestamp
            )
            
            # 验证订单
            self._validate_order(order)
            
            # 添加到订单列表
            self.orders.append(order)
            
            self.logger.debug(f"下单成功: {order_id} {side} {quantity} {symbol}")
            return order_id
            
        except Exception as e:
            self.logger.error(f"下单失败: {e}")
            raise BacktestError(f"下单失败: {e}")
    
    def execute_order(self, order_id: str, market_price: float,
                     timestamp: Optional[pd.Timestamp] = None) -> bool:
        """
        执行订单
        
        Args:
            order_id: 订单ID
            market_price: 市场价格
            timestamp: 执行时间
            
        Returns:
            是否执行成功
        """
        try:
            # 查找订单
            order = self._find_order(order_id)
            if not order or order.status != OrderStatus.PENDING:
                return False
            
            # 计算执行价格（包含滑点）
            fill_price = self._calculate_fill_price(order, market_price)
            
            # 计算交易成本
            commission, stamp_duty, slippage_cost = self._calculate_costs(
                order, fill_price, market_price
            )
            
            # 检查资金是否充足
            if order.side == 'buy':
                required_cash = order.quantity * fill_price + commission
                if required_cash > self.cash:
                    order.status = OrderStatus.REJECTED
                    self.logger.warning(f"资金不足，订单被拒绝: {order_id}")
                    return False
            
            # 执行交易
            order.status = OrderStatus.FILLED
            order.fill_price = fill_price
            order.fill_quantity = order.quantity
            order.commission = commission
            order.fill_timestamp = timestamp
            
            # 更新持仓
            self._update_position(order, stamp_duty)
            
            # 更新现金
            if order.side == 'buy':
                self.cash -= order.quantity * fill_price + commission
            else:
                self.cash += order.quantity * fill_price - commission - stamp_duty
            
            # 记录交易
            trade = {
                'order_id': order_id,
                'symbol': order.symbol,
                'side': order.side,
                'quantity': order.quantity,
                'price': fill_price,
                'market_price': market_price,
                'commission': commission,
                'stamp_duty': stamp_duty,
                'slippage_cost': slippage_cost,
                'timestamp': timestamp,
                'cash_after': self.cash
            }
            self.trades.append(trade)
            
            # 更新统计
            self.total_commission += commission
            self.total_stamp_duty += stamp_duty
            self.total_slippage += slippage_cost
            
            self.logger.debug(f"订单执行成功: {order_id} @ {fill_price:.2f}")
            return True
            
        except Exception as e:
            self.logger.error(f"执行订单失败: {e}")
            return False
    
    def _validate_order(self, order: Order) -> None:
        """验证订单"""
        if order.quantity <= 0:
            raise ValidationError("订单数量必须大于0", "quantity", order.quantity)
        
        if order.side not in ['buy', 'sell']:
            raise ValidationError("订单方向必须是buy或sell", "side", order.side)
    
    def _find_order(self, order_id: str) -> Optional[Order]:
        """查找订单"""
        for order in self.orders:
            if order.id == order_id:
                return order
        return None
    
    def _calculate_fill_price(self, order: Order, market_price: float) -> float:
        """计算成交价格（包含滑点）"""
        if order.order_type == OrderType.MARKET:
            if order.side == 'buy':
                # 买入时价格上滑
                return market_price * (1 + self.slippage_rate)
            else:
                # 卖出时价格下滑
                return market_price * (1 - self.slippage_rate)
        else:
            # 限价单等其他类型暂时按市价处理
            return market_price
    
    def _calculate_costs(self, order: Order, fill_price: float, 
                        market_price: float) -> tuple:
        """
        计算交易成本
        
        Returns:
            (佣金, 印花税, 滑点成本)
        """
        trade_value = order.quantity * fill_price
        
        # 计算佣金
        if order.side == 'buy':
            commission_rate = self.commission_rate_buy
        else:
            commission_rate = self.commission_rate_sell
        
        commission = max(trade_value * commission_rate, self.min_commission)
        
        # 计算印花税（仅卖出时收取）
        stamp_duty = 0.0
        if order.side == 'sell':
            stamp_duty = trade_value * self.stamp_duty_rate
        
        # 计算滑点成本
        slippage_cost = abs(fill_price - market_price) * order.quantity
        
        return commission, stamp_duty, slippage_cost
    
    def _update_position(self, order: Order, stamp_duty: float) -> None:
        """更新持仓"""
        symbol = order.symbol
        
        if symbol not in self.positions:
            self.positions[symbol] = Position(symbol=symbol)
        
        position = self.positions[symbol]
        
        if order.side == 'buy':
            # 买入：增加持仓
            if position.quantity >= 0:
                # 原来是多头或空仓，继续增加多头
                total_cost = position.quantity * position.avg_price + order.quantity * order.fill_price
                position.quantity += order.quantity
                position.avg_price = total_cost / position.quantity if position.quantity > 0 else 0
            else:
                # 原来是空头，减少空头
                if order.quantity >= abs(position.quantity):
                    # 完全平仓并转为多头
                    remaining = order.quantity - abs(position.quantity)
                    position.realized_pnl += abs(position.quantity) * (position.avg_price - order.fill_price)
                    position.quantity = remaining
                    position.avg_price = order.fill_price if remaining > 0 else 0
                else:
                    # 部分平仓
                    position.realized_pnl += order.quantity * (position.avg_price - order.fill_price)
                    position.quantity += order.quantity  # 注意：position.quantity是负数
        else:
            # 卖出：减少持仓
            if position.quantity > 0:
                # 原来是多头，减少多头
                if order.quantity >= position.quantity:
                    # 完全平仓并转为空头
                    remaining = order.quantity - position.quantity
                    position.realized_pnl += position.quantity * (order.fill_price - position.avg_price)
                    position.quantity = -remaining
                    position.avg_price = order.fill_price if remaining > 0 else 0
                else:
                    # 部分平仓
                    position.realized_pnl += order.quantity * (order.fill_price - position.avg_price)
                    position.quantity -= order.quantity
            else:
                # 原来是空头或空仓，继续增加空头
                total_cost = abs(position.quantity) * position.avg_price + order.quantity * order.fill_price
                position.quantity -= order.quantity
                position.avg_price = total_cost / abs(position.quantity) if position.quantity != 0 else 0
    
    def update_portfolio(self, market_data: Dict[str, float], 
                        timestamp: Optional[pd.Timestamp] = None) -> None:
        """
        更新投资组合价值
        
        Args:
            market_data: 市场数据 {symbol: price}
            timestamp: 时间戳
        """
        total_market_value = 0.0
        
        # 更新各持仓的市值和未实现盈亏
        for symbol, position in self.positions.items():
            if symbol in market_data and position.quantity != 0:
                current_price = market_data[symbol]
                position.market_value = abs(position.quantity) * current_price
                
                if position.quantity > 0:
                    # 多头持仓
                    position.unrealized_pnl = position.quantity * (current_price - position.avg_price)
                else:
                    # 空头持仓
                    position.unrealized_pnl = abs(position.quantity) * (position.avg_price - current_price)
                
                total_market_value += position.market_value
        
        # 更新总价值
        self.total_value = self.cash + total_market_value
        
        # 记录投资组合历史
        portfolio_snapshot = {
            'timestamp': timestamp,
            'cash': self.cash,
            'market_value': total_market_value,
            'total_value': self.total_value,
            'positions': {symbol: pos.quantity for symbol, pos in self.positions.items()}
        }
        self.portfolio_history.append(portfolio_snapshot)
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """获取投资组合摘要"""
        total_realized_pnl = sum(pos.realized_pnl for pos in self.positions.values())
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        
        return {
            'initial_capital': self.initial_capital,
            'current_cash': self.cash,
            'total_value': self.total_value,
            'total_return': (self.total_value - self.initial_capital) / self.initial_capital,
            'realized_pnl': total_realized_pnl,
            'unrealized_pnl': total_unrealized_pnl,
            'total_pnl': total_realized_pnl + total_unrealized_pnl,
            'total_commission': self.total_commission,
            'total_stamp_duty': self.total_stamp_duty,
            'total_slippage': self.total_slippage,
            'total_costs': self.total_commission + self.total_stamp_duty + self.total_slippage,
            'total_trades': len(self.trades),
            'total_orders': len(self.orders),
            'positions_count': len([pos for pos in self.positions.values() if pos.quantity != 0])
        }
