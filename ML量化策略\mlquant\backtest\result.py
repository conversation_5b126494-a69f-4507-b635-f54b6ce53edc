"""
回测结果类

定义回测结果的数据结构。
"""

import pandas as pd
from typing import Dict, Any, List
from dataclasses import dataclass


@dataclass
class BacktestResult:
    """回测结果类"""
    strategy_name: str
    initial_capital: float
    final_capital: float
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    total_trades: int
    win_rate: float
    profit_factor: float
    
    # 详细数据
    equity_curve: pd.Series
    trades: List[Dict[str, Any]]
    daily_returns: pd.Series
    
    def __repr__(self):
        return (f"BacktestResult(strategy={self.strategy_name}, "
                f"return={self.total_return:.2%}, "
                f"sharpe={self.sharpe_ratio:.2f}, "
                f"trades={self.total_trades})")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'strategy_name': self.strategy_name,
            'initial_capital': self.initial_capital,
            'final_capital': self.final_capital,
            'total_return': self.total_return,
            'annualized_return': self.annualized_return,
            'volatility': self.volatility,
            'sharpe_ratio': self.sharpe_ratio,
            'max_drawdown': self.max_drawdown,
            'total_trades': self.total_trades,
            'win_rate': self.win_rate,
            'profit_factor': self.profit_factor
        }
    
    def summary(self) -> str:
        """返回结果摘要"""
        return f"""
回测结果摘要
============
策略名称: {self.strategy_name}
初始资金: {self.initial_capital:,.2f}
最终资金: {self.final_capital:,.2f}
总收益率: {self.total_return:.2%}
年化收益率: {self.annualized_return:.2%}
年化波动率: {self.volatility:.2%}
夏普比率: {self.sharpe_ratio:.3f}
最大回撤: {self.max_drawdown:.2%}
交易次数: {self.total_trades}
胜率: {self.win_rate:.2%}
盈利因子: {self.profit_factor:.2f}
        """
