# MLQuant 架构文档

## 系统架构概览

MLQuant 采用模块化架构设计，将量化交易系统的各个功能组件进行清晰分离，提高代码的可维护性和可扩展性。

## 架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                    用户接口层                                │
├─────────────────────────────────────────────────────────────┤
│  run.py (CLI)  │  examples/  │  generated_strategies/      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    核心业务层                                │
├─────────────────────────────────────────────────────────────┤
│  mlquant.optimization  │  mlquant.generators               │
│  (ML优化算法)          │  (策略文件生成)                    │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    策略执行层                                │
├─────────────────────────────────────────────────────────────┤
│  mlquant.strategies    │  mlquant.backtest                 │
│  (交易策略)            │  (回测引擎)                        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    数据分析层                                │
├─────────────────────────────────────────────────────────────┤
│  mlquant.data         │  mlquant.performance               │
│  (数据处理)           │  (性能分析)                         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    基础设施层                                │
├─────────────────────────────────────────────────────────────┤
│  mlquant.utils (配置管理、日志记录、辅助函数)                │
└─────────────────────────────────────────────────────────────┘
```

## 模块依赖关系

```
mlquant/
├── utils/          # 基础工具模块 (被所有模块依赖)
├── data/           # 数据模块 (依赖 utils)
├── strategies/     # 策略模块 (依赖 utils)
├── backtest/       # 回测模块 (依赖 utils, strategies)
├── performance/    # 性能分析模块 (依赖 utils, backtest)
├── optimization/   # 优化模块 (依赖 utils, strategies, backtest)
└── generators/     # 生成器模块 (依赖 utils, optimization)
```

## 核心设计原则

### 1. 单一职责原则
每个模块只负责一个特定的功能领域：
- `data`: 专注数据生成和处理
- `strategies`: 专注交易策略实现
- `backtest`: 专注回测逻辑
- `performance`: 专注性能分析
- `optimization`: 专注参数优化
- `generators`: 专注策略文件生成

### 2. 开闭原则
系统对扩展开放，对修改封闭：
- 新策略通过继承 `BaseStrategy` 添加
- 新优化算法通过实现标准接口添加
- 新性能指标通过扩展 `PerformanceMetrics` 添加

### 3. 依赖倒置原则
高层模块不依赖低层模块，都依赖抽象：
- 策略通过抽象基类定义接口
- 优化器通过回调函数与策略解耦
- 回测引擎通过标准信号接口工作

### 4. 接口隔离原则
客户端不应该依赖它不需要的接口：
- 策略只需实现必要的信号生成方法
- 优化器只需要适应度函数接口
- 性能分析器只需要回测结果接口

## 数据流

```
数据生成 → 策略优化 → 回测验证 → 性能分析 → 策略生成
    ↓           ↓           ↓           ↓           ↓
RandomData  MLOptimizer  BacktestEngine  Analyzer  Generator
    ↓           ↓           ↓           ↓           ↓
  K线数据    最优参数     回测结果     性能报告    策略文件
```

## 扩展指南

### 添加新策略
1. 继承 `BaseStrategy` 类
2. 实现 `generate_signals` 方法
3. 实现 `get_param_ranges` 方法
4. 在 `MLStrategyOptimizer` 中注册

### 添加新优化算法
1. 实现优化器接口
2. 在 `optimization` 模块中添加
3. 在 `MLStrategyOptimizer` 中集成

### 添加新性能指标
1. 在 `PerformanceMetrics` 中添加计算方法
2. 在 `PerformanceReport` 中添加显示逻辑
3. 更新 `PerformanceAnalyzer` 的分析流程

## 配置管理

系统采用分层配置管理：
- 默认配置：代码中的默认值
- 文件配置：`config/config.yaml`
- 环境配置：环境变量覆盖
- 运行时配置：命令行参数

## 日志系统

统一的日志记录系统：
- 分级日志：DEBUG, INFO, WARNING, ERROR
- 多输出：控制台 + 文件
- 结构化：包含时间戳、模块名、级别
- 可配置：支持动态调整日志级别

## 错误处理

分层错误处理机制：
- 数据层：数据验证和格式错误
- 策略层：参数验证和计算错误
- 回测层：交易逻辑和资金管理错误
- 优化层：算法收敛和性能评估错误

## 性能优化

### 计算性能
- 向量化计算：使用 NumPy 和 Pandas
- 并行处理：支持多进程优化
- 内存管理：及时释放大型数据结构

### 存储性能
- 数据缓存：避免重复计算
- 结果持久化：保存优化结果
- 增量更新：只更新变化的部分

## 测试策略

### 单元测试
- 每个模块都有对应的测试文件
- 覆盖核心功能和边界情况
- 使用模拟数据进行测试

### 集成测试
- 端到端的工作流测试
- 模块间接口测试
- 性能基准测试

### 回归测试
- 确保新功能不破坏现有功能
- 自动化测试流程
- 持续集成支持
