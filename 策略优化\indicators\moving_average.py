"""
移动平均线指标

实现各种移动平均线指标的计算。
"""

import pandas as pd
import numpy as np
from typing import Union, Optional

from .base import MovingAverageBase
from ..utils.exceptions import IndicatorError


class SimpleMovingAverage(MovingAverageBase):
    """简单移动平均线 (SMA)"""
    
    def __init__(self, window: int, column: str = 'close'):
        """
        初始化简单移动平均线
        
        Args:
            window: 窗口大小
            column: 计算列名
        """
        super().__init__(window=window, column=column)
        self.column = column
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        计算简单移动平均线
        
        Args:
            data: 价格数据
            **kwargs: 额外参数
            
        Returns:
            SMA序列
        """
        try:
            # 验证数据
            self._validate_data(data, required_columns=[self.column])
            self._check_minimum_periods(data, self.window)
            
            # 计算SMA
            sma = data[self.column].rolling(window=self.window, min_periods=self.window).mean()
            
            self.logger.debug(f"计算SMA完成: window={self.window}, column={self.column}")
            return sma
            
        except Exception as e:
            raise IndicatorError(f"计算SMA失败: {e}", self.name, self.params)


class ExponentialMovingAverage(MovingAverageBase):
    """指数移动平均线 (EMA)"""
    
    def __init__(self, window: int, column: str = 'close', alpha: Optional[float] = None):
        """
        初始化指数移动平均线
        
        Args:
            window: 窗口大小
            column: 计算列名
            alpha: 平滑因子，如果为None则使用 2/(window+1)
        """
        super().__init__(window=window, column=column, alpha=alpha)
        self.column = column
        self.alpha = alpha if alpha is not None else 2.0 / (window + 1)
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        计算指数移动平均线
        
        Args:
            data: 价格数据
            **kwargs: 额外参数
            
        Returns:
            EMA序列
        """
        try:
            # 验证数据
            self._validate_data(data, required_columns=[self.column])
            self._check_minimum_periods(data, self.window)
            
            # 计算EMA
            ema = data[self.column].ewm(span=self.window, adjust=False).mean()
            
            self.logger.debug(f"计算EMA完成: window={self.window}, alpha={self.alpha}")
            return ema
            
        except Exception as e:
            raise IndicatorError(f"计算EMA失败: {e}", self.name, self.params)


class WeightedMovingAverage(MovingAverageBase):
    """加权移动平均线 (WMA)"""
    
    def __init__(self, window: int, column: str = 'close', weights: Optional[list] = None):
        """
        初始化加权移动平均线
        
        Args:
            window: 窗口大小
            column: 计算列名
            weights: 权重列表，如果为None则使用线性递增权重
        """
        if weights is None:
            weights = list(range(1, window + 1))
        elif len(weights) != window:
            raise ValueError(f"权重列表长度 {len(weights)} 必须等于窗口大小 {window}")
        
        super().__init__(window=window, column=column, weights=weights)
        self.column = column
        self.weights = np.array(weights)
        self.weights_sum = np.sum(self.weights)
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        计算加权移动平均线
        
        Args:
            data: 价格数据
            **kwargs: 额外参数
            
        Returns:
            WMA序列
        """
        try:
            # 验证数据
            self._validate_data(data, required_columns=[self.column])
            self._check_minimum_periods(data, self.window)
            
            # 计算WMA
            prices = data[self.column].values
            wma_values = np.full(len(prices), np.nan)
            
            for i in range(self.window - 1, len(prices)):
                window_prices = prices[i - self.window + 1:i + 1]
                wma_values[i] = np.sum(window_prices * self.weights) / self.weights_sum
            
            wma = pd.Series(wma_values, index=data.index, name=f'WMA_{self.window}')
            
            self.logger.debug(f"计算WMA完成: window={self.window}")
            return wma
            
        except Exception as e:
            raise IndicatorError(f"计算WMA失败: {e}", self.name, self.params)


class TripleExponentialMovingAverage(MovingAverageBase):
    """三重指数移动平均线 (TEMA)"""
    
    def __init__(self, window: int, column: str = 'close'):
        """
        初始化三重指数移动平均线
        
        Args:
            window: 窗口大小
            column: 计算列名
        """
        super().__init__(window=window, column=column)
        self.column = column
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        计算三重指数移动平均线
        
        Args:
            data: 价格数据
            **kwargs: 额外参数
            
        Returns:
            TEMA序列
        """
        try:
            # 验证数据
            self._validate_data(data, required_columns=[self.column])
            self._check_minimum_periods(data, self.window * 3)  # TEMA需要更多数据
            
            # 计算三重EMA
            ema1 = data[self.column].ewm(span=self.window, adjust=False).mean()
            ema2 = ema1.ewm(span=self.window, adjust=False).mean()
            ema3 = ema2.ewm(span=self.window, adjust=False).mean()
            
            # TEMA公式: 3*EMA1 - 3*EMA2 + EMA3
            tema = 3 * ema1 - 3 * ema2 + ema3
            tema.name = f'TEMA_{self.window}'
            
            self.logger.debug(f"计算TEMA完成: window={self.window}")
            return tema
            
        except Exception as e:
            raise IndicatorError(f"计算TEMA失败: {e}", self.name, self.params)


class HullMovingAverage(MovingAverageBase):
    """赫尔移动平均线 (HMA)"""
    
    def __init__(self, window: int, column: str = 'close'):
        """
        初始化赫尔移动平均线
        
        Args:
            window: 窗口大小
            column: 计算列名
        """
        super().__init__(window=window, column=column)
        self.column = column
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        计算赫尔移动平均线
        
        Args:
            data: 价格数据
            **kwargs: 额外参数
            
        Returns:
            HMA序列
        """
        try:
            # 验证数据
            self._validate_data(data, required_columns=[self.column])
            self._check_minimum_periods(data, self.window)
            
            # HMA计算步骤
            half_window = self.window // 2
            sqrt_window = int(np.sqrt(self.window))
            
            # 计算WMA
            wma_half = data[self.column].rolling(window=half_window).apply(
                lambda x: np.sum(x * np.arange(1, len(x) + 1)) / np.sum(np.arange(1, len(x) + 1)),
                raw=True
            )
            
            wma_full = data[self.column].rolling(window=self.window).apply(
                lambda x: np.sum(x * np.arange(1, len(x) + 1)) / np.sum(np.arange(1, len(x) + 1)),
                raw=True
            )
            
            # 计算差值的WMA
            diff = 2 * wma_half - wma_full
            hma = diff.rolling(window=sqrt_window).apply(
                lambda x: np.sum(x * np.arange(1, len(x) + 1)) / np.sum(np.arange(1, len(x) + 1)),
                raw=True
            )
            
            hma.name = f'HMA_{self.window}'
            
            self.logger.debug(f"计算HMA完成: window={self.window}")
            return hma
            
        except Exception as e:
            raise IndicatorError(f"计算HMA失败: {e}", self.name, self.params)


class AdaptiveMovingAverage(MovingAverageBase):
    """自适应移动平均线 (AMA)"""
    
    def __init__(self, window: int, column: str = 'close', 
                 fast_sc: float = 2.0, slow_sc: float = 30.0):
        """
        初始化自适应移动平均线
        
        Args:
            window: 窗口大小
            column: 计算列名
            fast_sc: 快速平滑常数
            slow_sc: 慢速平滑常数
        """
        super().__init__(window=window, column=column, fast_sc=fast_sc, slow_sc=slow_sc)
        self.column = column
        self.fast_sc = 2.0 / (fast_sc + 1)
        self.slow_sc = 2.0 / (slow_sc + 1)
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        计算自适应移动平均线
        
        Args:
            data: 价格数据
            **kwargs: 额外参数
            
        Returns:
            AMA序列
        """
        try:
            # 验证数据
            self._validate_data(data, required_columns=[self.column])
            self._check_minimum_periods(data, self.window + 1)
            
            prices = data[self.column].values
            ama_values = np.full(len(prices), np.nan)
            
            # 初始化第一个AMA值
            ama_values[self.window] = prices[self.window]
            
            for i in range(self.window + 1, len(prices)):
                # 计算效率比率 (Efficiency Ratio)
                change = abs(prices[i] - prices[i - self.window])
                volatility = np.sum(np.abs(np.diff(prices[i - self.window:i + 1])))
                
                if volatility != 0:
                    er = change / volatility
                else:
                    er = 0
                
                # 计算平滑常数
                sc = (er * (self.fast_sc - self.slow_sc) + self.slow_sc) ** 2
                
                # 计算AMA
                ama_values[i] = ama_values[i - 1] + sc * (prices[i] - ama_values[i - 1])
            
            ama = pd.Series(ama_values, index=data.index, name=f'AMA_{self.window}')
            
            self.logger.debug(f"计算AMA完成: window={self.window}")
            return ama
            
        except Exception as e:
            raise IndicatorError(f"计算AMA失败: {e}", self.name, self.params)
