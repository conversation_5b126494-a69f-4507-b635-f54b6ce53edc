#!/usr/bin/env python3
"""
ML量化策略系统完整演示

展示系统的核心功能：随机数据生成 → ML策略优化 → 回测验证
"""

import sys
import time
import os
from pathlib import Path

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mlquant.data import RandomDataGenerator
from mlquant.optimization import MLStrategyOptimizer
from mlquant.backtest import BacktestEngine
from mlquant.performance import PerformanceAnalyzer
from mlquant.generators import StrategyFileGenerator, StrategyManager
from mlquant.utils.helpers import print_header, print_step





def main():
    """主演示程序"""
    print_header("ML量化策略系统演示")
    print("专注于机器学习驱动的量化交易策略开发")
    print("核心流程: 随机数据生成 → ML策略优化 → 回测验证")
    
    try:
        # ==================== 步骤1: 初始化系统 ====================
        print_step(1, "初始化系统组件")
        
        data_generator = RandomDataGenerator()
        ml_optimizer = MLStrategyOptimizer()
        backtest_engine = BacktestEngine()
        performance_analyzer = PerformanceAnalyzer()
        strategy_generator = StrategyFileGenerator()
        strategy_manager = StrategyManager()
        
        print("✅ 数据生成器初始化完成")
        print("✅ ML优化器初始化完成")
        print("✅ 回测引擎初始化完成")
        print("✅ 性能分析器初始化完成")
        print("✅ 策略文件生成器初始化完成")
        print("✅ 策略管理器初始化完成")
        
        # ==================== 步骤2: 生成训练数据 ====================
        print_step(2, "生成随机K线训练数据")
        
        print("📊 生成1000天的随机K线数据...")
        train_data = data_generator.generate(days=1000)
        
        print(f"✅ 训练数据生成完成:")
        print(f"   - 数据长度: {len(train_data)} 天")
        print(f"   - 时间范围: {train_data.index[0].strftime('%Y-%m-%d')} 到 {train_data.index[-1].strftime('%Y-%m-%d')}")
        print(f"   - 价格范围: {train_data['close'].min():.2f} - {train_data['close'].max():.2f}")
        print(f"   - 包含指标: {', '.join([col for col in train_data.columns if col not in ['open', 'high', 'low', 'close', 'volume']])}")
        
        # ==================== 步骤3: ML策略优化 ====================
        print_step(3, "机器学习策略参数优化")
        
        print("🧠 使用遗传算法优化双均线策略参数...")
        print("   - 优化目标: 最大化夏普比率")
        print("   - 搜索空间: 短均线[5-50], 长均线[20-200]")
        
        start_time = time.time()
        best_strategy = ml_optimizer.optimize(train_data, 'dual_ma')
        optimization_time = time.time() - start_time
        
        print(f"✅ 策略优化完成 (耗时: {optimization_time:.1f}秒)")
        print(f"   - 最优参数: {best_strategy.params}")
        print(f"   - 训练适应度: {best_strategy.fitness:.4f}")
        
        # ==================== 步骤4: 生成测试数据 ====================
        print_step(4, "生成独立测试数据")
        
        print("📈 生成500天的测试数据进行验证...")
        test_data = data_generator.generate(days=500, random_seed=999)
        
        print(f"✅ 测试数据生成完成:")
        print(f"   - 数据长度: {len(test_data)} 天")
        print(f"   - 价格范围: {test_data['close'].min():.2f} - {test_data['close'].max():.2f}")
        
        # ==================== 步骤5: 策略回测验证 ====================
        print_step(5, "策略回测验证")
        
        print("🔄 在测试数据上运行优化后的策略...")
        
        # 生成交易信号
        strategy_obj = ml_optimizer.strategies['dual_ma']
        test_signals = strategy_obj.generate_signals(test_data, **best_strategy.params)
        
        print(f"   - 生成信号: 买入={sum(test_signals==1)}, 卖出={sum(test_signals==-1)}, 持有={sum(test_signals==0)}")
        
        # 执行回测
        backtest_result = backtest_engine.run(test_data, test_signals, "OptimizedDualMA")
        
        print(f"✅ 回测完成:")
        print(f"   - 总收益率: {backtest_result.total_return:.2%}")
        print(f"   - 年化收益率: {backtest_result.annualized_return:.2%}")
        print(f"   - 夏普比率: {backtest_result.sharpe_ratio:.3f}")
        print(f"   - 最大回撤: {backtest_result.max_drawdown:.2%}")
        print(f"   - 交易次数: {backtest_result.total_trades}")
        
        # ==================== 步骤6: 生成策略文件 ====================
        print_step(6, "生成独立策略文件")

        print("📄 将优化后的策略生成为独立的Python文件...")

        # 准备性能指标
        strategy_metrics = {
            'total_return': backtest_result.total_return,
            'sharpe_ratio': backtest_result.sharpe_ratio,
            'max_drawdown': backtest_result.max_drawdown,
            'win_rate': backtest_result.win_rate,
            'total_trades': backtest_result.total_trades
        }

        # 生成策略文件
        strategy_description = f"""
这是一个通过遗传算法在{len(train_data)}天历史数据上优化得出的双均线策略。
算法通过{ml_optimizer.ml_config.get('genetic', {}).get('generations', 100)}代进化，
从{ml_optimizer.ml_config.get('genetic', {}).get('population_size', 50)}个个体中筛选出最优参数组合。

策略特点:
- 使用{best_strategy.params['short_window']}日短期均线和{best_strategy.params['long_window']}日长期均线
- 金叉时买入，死叉时卖出
- 训练期适应度(夏普比率): {best_strategy.fitness:.4f}
- 适用于趋势性市场，震荡市场需谨慎使用
        """.strip()

        strategy_filename = strategy_generator.generate_strategy_file(
            strategy_name=best_strategy.name,
            params=best_strategy.params,
            performance_metrics=strategy_metrics,
            description=strategy_description
        )

        print(f"✅ 策略文件生成完成: {strategy_filename}")
        print(f"   - 包含完整的策略代码和使用说明")
        print(f"   - 文件位置: {strategy_filename}")
        print(f"   - 包含历史性能数据和参数说明")

        # 注册策略到管理器
        strategy_manager.register_strategy(
            strategy_name=best_strategy.name,
            filepath=strategy_filename,
            params=best_strategy.params,
            performance=strategy_metrics,
            description=strategy_description
        )

        # ==================== 步骤7: 性能分析报告 ====================
        print_step(7, "生成详细性能分析报告")

        performance_report = performance_analyzer.analyze(backtest_result)
        performance_report.print_report()
        
        # ==================== 步骤8: 基准比较 ====================
        print_step(8, "与买入持有策略对比")
        
        print("📊 计算买入持有基准...")
        
        # 买入持有策略
        buy_hold_signals = pd.Series(1, index=test_data.index)  # 始终持有
        buy_hold_result = backtest_engine.run(test_data, buy_hold_signals, "BuyAndHold")
        
        # 比较结果
        comparison_results = [backtest_result, buy_hold_result]
        comparison_table = performance_analyzer.compare_strategies(comparison_results)
        
        print("📈 策略对比结果:")
        print(comparison_table.round(4))
        
        # 创建收益曲线对比图
        equity_curves = {
            "优化策略": backtest_result.equity_curve,
            "买入持有": buy_hold_result.equity_curve
        }
        chart = create_simple_chart(equity_curves, "策略收益对比")
        print(chart)
        
        # ==================== 步骤9: 多策略对比演示 ====================
        print_step(9, "多策略ML优化对比")
        
        strategies_to_test = ['dual_ma', 'rsi', 'bollinger']
        multi_results = []
        
        print("🏆 优化并测试多个策略...")
        
        for strategy_name in strategies_to_test:
            try:
                print(f"   🔧 优化 {strategy_name} 策略...")
                
                # 优化策略
                optimized = ml_optimizer.optimize(train_data, strategy_name)
                
                # 回测验证
                strategy_obj = ml_optimizer.strategies[strategy_name]
                signals = strategy_obj.generate_signals(test_data, **optimized.params)
                result = backtest_engine.run(test_data, signals, f"{strategy_name}_optimized")
                
                multi_results.append(result)
                print(f"   ✅ {strategy_name}: 收益率 {result.total_return:.2%}, 夏普 {result.sharpe_ratio:.3f}")
                
            except Exception as e:
                print(f"   ❌ {strategy_name} 优化失败: {e}")
                continue
        
        if len(multi_results) > 1:
            print("\n📊 多策略性能对比:")
            multi_comparison = performance_analyzer.compare_strategies(multi_results)
            print(multi_comparison.round(4))
            
            # 汇总统计
            performance_analyzer.print_summary_stats(multi_results)
        
        # ==================== 总结 ====================
        print_header("演示总结")
        
        print("🎉 ML量化策略系统演示完成!")
        print("\n✅ 验证的核心功能:")
        print("   • 随机K线数据生成 - 为ML提供训练数据")
        print("   • 遗传算法参数优化 - 自动发现最优策略参数") 
        print("   • 精确回测验证 - 包含交易成本的真实回测")
        print("   • 全面性能分析 - 多维度策略评估")
        print("   • 多策略对比 - 系统性策略筛选")
        
        print(f"\n📊 最终结果:")
        print(f"   • 最优策略: {best_strategy.name}")
        print(f"   • 最优参数: {best_strategy.params}")
        print(f"   • 测试收益率: {backtest_result.total_return:.2%}")
        print(f"   • 夏普比率: {backtest_result.sharpe_ratio:.3f}")
        
        print(f"\n💡 系统特点:")
        print(f"   • 专注核心需求: 随机数据 + ML优化 + 回测")
        print(f"   • 代码简洁: 仅6个核心文件")
        print(f"   • 易于扩展: 支持新策略和ML算法")
        print(f"   • 快速迭代: 完整流程仅需几分钟")
        
        print(f"\n🚀 项目位置: {Path.cwd()}")
        print("   可通过修改config.yaml调整参数")
        print("   可通过python main.py --help查看更多选项")
        
        # 保存结果
        print(f"\n💾 保存结果数据...")
        
        # 保存最优策略参数到文本文件
        result_summary = f"""ML量化策略优化结果

最优策略: {best_strategy.name}
策略参数: {best_strategy.params}
训练适应度: {best_strategy.fitness:.4f}

回测性能:
- 总收益率: {backtest_result.total_return:.2%}
- 夏普比率: {backtest_result.sharpe_ratio:.3f}
- 最大回撤: {backtest_result.max_drawdown:.2%}
- 交易次数: {backtest_result.total_trades}
"""

        with open('demo_results.txt', 'w', encoding='utf-8') as f:
            f.write(result_summary)
        
        # 保存详细数据
        backtest_result.equity_curve.to_csv('equity_curve.csv')
        test_data.to_csv('test_data.csv')
        
        print("   ✅ 结果已保存到:")
        print("      - demo_results.txt (策略参数和性能)")
        print(f"      - {os.path.basename(strategy_filename)} (独立策略文件)")
        print("      - equity_curve.csv (资金曲线)")
        print("      - test_data.csv (测试数据)")

        # ==================== 步骤10: 策略管理展示 ====================
        print_step(10, "策略管理功能展示")

        print("📁 展示策略管理功能...")

        # 显示策略管理摘要
        strategy_manager.print_summary()

        # 列出所有策略
        strategies_df = strategy_manager.list_strategies()
        if not strategies_df.empty:
            print("\n📊 已生成的策略列表:")
            print(strategies_df.to_string(index=False))

        # 创建策略索引
        strategy_manager.create_strategy_index()

        # 导出策略摘要
        strategy_manager.export_strategy_summary()

        print(f"\n✅ 策略管理功能:")
        print(f"   - 策略文件统一存放在 generated_strategies/ 目录")
        print(f"   - 自动生成策略索引和元数据")
        print(f"   - 支持策略查找、删除和摘要导出")
        print(f"   - 可通过 strategy_manager.py 管理所有策略")
        
        print(f"\n{'='*60}")
        print("🎊 演示程序成功完成! 系统运行正常!")
        print(f"{'='*60}")
        
    except Exception as e:
        print(f"\n❌ 演示程序运行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    # 导入pandas用于买入持有策略
    import pandas as pd
    main()
