import backtrader as bt
import pandas as pd
import numpy as np
import datetime
import logging # 导入标准日志库 (可选, 用于日志级别)
from collections import defaultdict

# --- 配置参数 ---
INITIAL_CAPITAL = 100000 # 初始资金
COMMISSION_RATE_OPEN = 0.0001 # 买入佣金费率
COMMISSION_RATE_CLOSE = 0.0001 # 卖出佣金费率
STAMP_DUTY_RATE_CLOSE = 0.0005 # 卖出印花税费率
MIN_COMMISSION = 5.0 # 最低佣金
SLIPPAGE_PERC = 0.002 # 滑点百分比
RISK_FREE_RATE = 0.0 # 无风险利率
ANALYZER_TIMEFRAME = bt.TimeFrame.Years # 分析器时间框架

# --- 基础策略类 ---
class BaseStrategy(bt.Strategy):
    """
    基础策略类，提供通用的日志记录和通知处理功能。
    """
    params = (
        ('printlog', False), # 默认为False，以便在运行时保持简洁，可根据每个策略的需要启用
    )

    def __init__(self):
        # 保留对数据线的引用 - 子类可能需要更多
        self.dataclose = self.datas[0].close
        self.datavolume = self.datas[0].volume

        # 订单跟踪
        self.order = None

        # 日志存储
        self.log_records = []

        # 可选：如果其他逻辑需要，可以跟踪买入价格和佣金
        self.buyprice = None
        self.buycomm = None

    def log(self, txt, level=logging.INFO):
        """ 记录消息并将其存储在 log_records 中 """
        # 如果以后实现不同的日志级别，可以在此处添加级别检查
        dt_str = self.datas[0].datetime.date(0).isoformat()
        log_entry = f'{dt_str}: {txt}'
        self.log_records.append(log_entry)
        if self.params.printlog: # 仅在显式启用时打印
             print(log_entry)

    def notify_order(self, order):
        """ 处理订单通知并记录执行详情 """
        if order.status in [order.Submitted, order.Accepted]:
            return # 忽略已提交/已接受状态的日志记录

        if order.status == order.Completed:
            dt_str = self.datas[0].datetime.date(0).isoformat() # 获取日志日期
            if order.isbuy():
                self.log(
                    f'买入执行: 价格={order.executed.price:.2f}, 数量={order.executed.size}, 成本={order.executed.value:.2f}, 手续费={order.executed.comm:.2f}'
                )
                self.buyprice = order.executed.price
                self.buycomm = order.executed.comm
            elif order.issell():
                self.log(
                     f'卖出执行: 价格={order.executed.price:.2f}, 数量={abs(order.executed.size)}, 收入={abs(order.executed.value):.2f}, 手续费={order.executed.comm:.2f}'
                )
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log(f'订单失败/取消: Status={order.getstatusname()}, Ref={order.ref}')

        self.order = None # 重置订单状态

    def notify_trade(self, trade):
        """ 处理交易通知并记录盈亏 """
        if trade.isclosed:
            self.log(
                f'交易利润: 毛利={trade.pnl:.2f}, 净利={trade.pnlcomm:.2f}'
                )

    def stop(self):
        """ 在回测结束时调用 """
        # 记录此策略运行的最终投资组合价值
        self.log(f'策略结束. Final Portfolio Value: {self.broker.getvalue():.2f}')

    # --- 抽象方法占位符 ---
    # def next(self):
    #     #核心策略逻辑必须由子类实现
    #     raise NotImplementedError

# --- 策略实现 ---

class DualMAStrategy(BaseStrategy):
    """ 双均线交叉策略 """
    params = (
        ('ma_short_period', 20), # 短期均线周期
        ('ma_long_period', 50),  # 长期均线周期
        ('printlog', False),     # 继承自父类，可以覆盖默认值
    )

    def __init__(self):
        # 调用基类初始化
        super().__init__()

        # 定义此策略特有的指标
        self.sma_short = bt.indicators.SimpleMovingAverage(
            self.datas[0], period=self.params.ma_short_period)
        self.sma_long = bt.indicators.SimpleMovingAverage(
            self.datas[0], period=self.params.ma_long_period)
        self.crossover = bt.indicators.CrossOver(self.sma_short, self.sma_long) # 均线交叉指标

    def next(self):
        # 核心策略逻辑
        if self.order: # 检查是否有挂单
            return

        if not self.position: # 如果未持仓
            if self.crossover > 0: # 买入信号 (短期均线上穿长期均线)
                self.log(f'买入信号 (MA Crossover): 价格={self.dataclose[0]:.2f}')
                self.order = self.buy()
        else: # 如果已持仓
            if self.crossover < 0: # 卖出信号 (短期均线下穿长期均线)
                self.log(f'卖出信号 (MA Crossover): 价格={self.dataclose[0]:.2f}, 持仓={self.position.size}')
                self.order = self.sell()


class MomentumStrategy(BaseStrategy):
    """ 简单动量策略：如果价格在N个周期内上涨则买入 """
    params = (
        ('momentum_period', 10), # 回看N个周期计算动量
        ('printlog', False),
    )

    def __init__(self):
        super().__init__()
        # 使用变化率 (Rate of Change) 指标
        self.roc = bt.indicators.RateOfChange(self.datas[0], period=self.params.momentum_period)

    def next(self):
        if self.order: # 检查是否有挂单
            return

        # 计算动量信号 (正的ROC表示上涨动量)
        current_roc = self.roc[0]

        if not self.position: # 如果未持仓
            if current_roc > 0: # 当动量为正时买入
                self.log(f'买入信号 (Momentum): ROC={current_roc:.4f}, 价格={self.dataclose[0]:.2f}')
                self.order = self.buy()
        else: # 如果已持仓
            if current_roc < 0: # 当动量转为负时卖出
                self.log(f'卖出信号 (Momentum): ROC={current_roc:.4f}, 价格={self.dataclose[0]:.2f}, 持仓={self.position.size}')
                self.order = self.sell()

# --- 自定义佣金方案 (未更改) ---
class CustomCommission(bt.CommInfoBase):
    params = (
        ('commission_rate_open', COMMISSION_RATE_OPEN),
        ('commission_rate_close', COMMISSION_RATE_CLOSE),
        ('stamp_duty_rate_close', STAMP_DUTY_RATE_CLOSE),
        ('min_commission', MIN_COMMISSION),
        ('stocklike', True), # 股票类成本按股计算
        ('commtype', bt.CommInfoBase.COMM_PERC), # 佣金类型：百分比
        ('percabs', True), # 百分比是绝对值 (例如 0.01 代表 1%)
    )
    def _getcommission(self, size, price, pseudoexec):
        if size > 0: # 买入订单
            comm = max(abs(size) * price * self.p.commission_rate_open, self.p.min_commission)
        elif size < 0: # 卖出订单
            comm = max(abs(size) * price * self.p.commission_rate_close, self.p.min_commission) + (abs(size) * price * self.p.stamp_duty_rate_close)
        else: # 订单数量为0
            comm = 0.0
        return comm

# --- 辅助函数 ---

def generate_data(num_years=3, initial_price=100, drift=0.0001, volatility=0.015, start_date='2020-01-01'):
    """ 生成用于回测的模拟OHLCV数据 """
    num_days = 252 * num_years # 每年大约252个交易日
    log_returns = np.random.normal(drift, volatility, num_days) # 生成日对数收益率
    price_path = np.exp(np.cumsum(log_returns)) * initial_price # 计算价格路径
    dates = pd.date_range(start=start_date, periods=num_days, freq='B') # 生成交易日日期序列 ('B' 代表工作日)

    df = pd.DataFrame(index=dates)
    # 为简化起见，基于收盘价模拟OHL数据，并添加一些噪音
    price_noise_low = price_path * (1 - np.random.uniform(0, volatility*2, size=num_days))
    price_noise_high = price_path * (1 + np.random.uniform(0, volatility*2, size=num_days))
    df['open'] = np.roll(price_path, 1) # 使用前一天的收盘价作为近似开盘价
    df.iloc[0, df.columns.get_loc('open')] = initial_price # 设置第一个开盘价
    df['high'] = np.maximum.reduce([df['open'], price_path, price_noise_high])
    df['low'] = np.minimum.reduce([df['open'], price_path, price_noise_low])
    df['close'] = price_path
    df['volume'] = np.random.randint(50000, 200000, size=num_days) # 随机生成交易量
    df['openinterest'] = 0 # 持仓量，Backtrader必需列

    # 确保 最高价 >= 开盘价/收盘价 且 最低价 <= 开盘价/收盘价
    df['high'] = df[['open', 'close', 'high']].max(axis=1)
    df['low'] = df[['open', 'close', 'low']].min(axis=1)

    return df

def setup_cerebro(strategy_class, data_feed, strategy_params=None):
    """ 为给定策略创建并配置 Cerebro 实例 """
    cerebro = bt.Cerebro(stdstats=False) # 禁用标准观察器，以便稍后获得更清晰的绘图

    # 添加策略，可选择传递参数
    if strategy_params:
        cerebro.addstrategy(strategy_class, **strategy_params)
    else:
        cerebro.addstrategy(strategy_class)

    # 添加数据源
    cerebro.adddata(data_feed)

    # 设置初始资金
    cerebro.broker.setcash(INITIAL_CAPITAL)

    # 添加自定义佣金和滑点
    comminfo = CustomCommission()
    cerebro.broker.addcommissioninfo(comminfo)
    cerebro.broker.set_slippage_perc(perc=SLIPPAGE_PERC) # 设置滑点

    # 添加资金管理策略 (Sizer)
    cerebro.addsizer(bt.sizers.PercentSizer, percents=95) # 投入95%的资金

    # 添加分析器
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe', timeframe=ANALYZER_TIMEFRAME, riskfreerate=RISK_FREE_RATE)
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns', timeframe=ANALYZER_TIMEFRAME)
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='tradeanalyzer') # 交易分析器
    cerebro.addanalyzer(bt.analyzers.SQN, _name='sqn') # 系统质量数 (SQN)

    return cerebro

def run_and_analyze(cerebro, strategy_name):
    """ 运行 cerebro 并提取关键性能指标 (KPI) """
    print(f"\n--- 运行策略: {strategy_name} ---")
    print(f'起始投资组合价值: {cerebro.broker.getvalue():,.2f}')

    results = cerebro.run() # 执行回测

    final_value = cerebro.broker.getvalue()
    print(f'最终投资组合价值:  {final_value:,.2f}')
    print("--------------------------------------")

    analysis_results = {} # 用于存储KPI的字典

    # --- 安全地访问结果和分析器 ---
    if results:
        try:
            strat = results[0] # 访问策略实例
            analysis = strat.analyzers # 访问分析器

            # 提取KPI
            analysis_results['Strategy'] = strategy_name
            analysis_results['Final Value'] = final_value
            analysis_results['Total Return %'] = (final_value / INITIAL_CAPITAL - 1) * 100

            sharpe = analysis.sharpe.get_analysis()
            analysis_results['Sharpe Ratio'] = sharpe.get('sharperatio', 0.0)

            returns = analysis.returns.get_analysis()
            analysis_results['Annualized Return %'] = returns.get('rnorm100', 0.0) # 年化收益率 (%)

            drawdown = analysis.drawdown.get_analysis()
            analysis_results['Max Drawdown %'] = drawdown.max.drawdown # 最大回撤 (%)

            sqn = analysis.sqn.get_analysis()
            analysis_results['SQN'] = sqn.get('sqn', 0.0)

            # 交易分析 (处理无交易的情况)
            trade_analysis = analysis.tradeanalyzer.get_analysis()
            total_trades = trade_analysis.total.total if trade_analysis and 'total' in trade_analysis else 0 # 总交易次数
            winning_trades = trade_analysis.won.total if total_trades > 0 and 'won' in trade_analysis and trade_analysis.won else 0 # 盈利交易次数
            losing_trades = trade_analysis.lost.total if total_trades > 0 and 'lost' in trade_analysis and trade_analysis.lost else 0 # 亏损交易次数

            analysis_results['Total Trades'] = total_trades
            analysis_results['Win Rate %'] = (winning_trades / total_trades * 100) if total_trades > 0 else 0 # 胜率 (%)
            analysis_results['Avg PnL'] = trade_analysis.pnl.net.average if total_trades > 0 and 'pnl' in trade_analysis and 'net' in trade_analysis.pnl else 0 # 平均每笔交易净盈亏

            avg_win_pnl = trade_analysis.won.pnl.average if winning_trades > 0 and 'won' in trade_analysis and trade_analysis.won and 'pnl' in trade_analysis.won else 0 # 平均盈利金额
            avg_loss_pnl = trade_analysis.lost.pnl.average if losing_trades > 0 and 'lost' in trade_analysis and trade_analysis.lost and 'pnl' in trade_analysis.lost else 0 # 平均亏损金额
            analysis_results['Payoff Ratio'] = abs(avg_win_pnl / avg_loss_pnl) if avg_loss_pnl != 0 and avg_win_pnl is not None else float('inf') if avg_win_pnl is not None else 0 # 盈亏比

            # 打印日志 (可选)
            # print("\n--- 策略日志 ---")
            # if hasattr(strat, 'log_records'):
            #     for log_entry in strat.log_records:
            #         print(log_entry)
            # print("-" * 30)

        except IndexError:
             print(f"错误: 在 {strategy_name} 的结果中未找到策略实例。")
             return None
        except AttributeError as e:
             print(f"错误: 访问 {strategy_name} 的分析器时出错: {e}")
             # 如果在访问分析器期间发生 'NoneType' 错误，这可能会捕获到它
             return None
        except Exception as e:
            print(f"在分析 {strategy_name} 期间发生意外错误: {e}")
            import traceback
            traceback.print_exc()
            return None
    else:
        print(f"Cerebro 运行未返回 {strategy_name} 的结果。")
        return None

    return analysis_results


# --- 主执行逻辑 ---
if __name__ == '__main__':
    # 1. 生成数据
    data_df = generate_data(num_years=3) # 生成3年的模拟数据
    data_feed = bt.feeds.PandasData(dataname=data_df) # 创建Backtrader数据源

    # 2. 定义要运行的策略
    strategies_to_run = [
        {'name': '双均线 (20, 50)', 'class': DualMAStrategy, 'params': {'ma_short_period': 20, 'ma_long_period': 50}},
        {'name': '双均线 (10, 30)', 'class': DualMAStrategy, 'params': {'ma_short_period': 10, 'ma_long_period': 30}},
        {'name': '动量 (10)', 'class': MomentumStrategy, 'params': {'momentum_period': 10}},
        {'name': '动量 (20)', 'class': MomentumStrategy, 'params': {'momentum_period': 20}},
    ]

    all_results = [] # 用于存储所有策略的分析结果

    # 3. 运行每个策略
    for strat_info in strategies_to_run:
        # 为每次运行创建一个新的数据源克隆，以避免状态问题
        data_feed_clone = bt.feeds.PandasData(dataname=data_df.copy()) # 注意：使用 .copy() 确保数据独立

        cerebro = setup_cerebro(strat_info['class'], data_feed_clone, strat_info.get('params', None))
        analysis = run_and_analyze(cerebro, strat_info['name'])
        if analysis:
            all_results.append(analysis)

        # 可选：绘制单个策略的结果图
        # try:
        #     if cerebro:
        #         print(f"正在为 {strat_info['name']} 生成图表...")
        #         cerebro.plot(style='candlestick', barup='red', bardown='green', numfigs=1, volume=True, iplot=False)
        # except Exception as e:
        #     print(f"无法为 {strat_info['name']} 绘图: {e}")


    # 4. 比较结果
    if all_results:
        results_df = pd.DataFrame(all_results) # 将结果列表转换为DataFrame
        results_df.set_index('Strategy', inplace=True) # 将策略名称设为索引

        # 格式化列以提高可读性
        float_cols = ['Final Value', 'Total Return %', 'Sharpe Ratio', 'Annualized Return %', 'Max Drawdown %', 'SQN', 'Win Rate %', 'Avg PnL', 'Payoff Ratio']
        for col in float_cols:
            if col in results_df.columns:
                 # 应用格式化，优雅地处理潜在的非数字值
                 try:
                     results_df[col] = pd.to_numeric(results_df[col], errors='coerce').map('{:,.2f}'.format)
                 except (TypeError, ValueError):
                     print(f"警告: 无法将列 '{col}' 格式化为数字。")


        print("\n\n--- 策略性能比较 ---")
        print(results_df.to_string()) # 使用 to_string() 以获得更好的控制台输出
        print("---------------------------------------\n")

        # 可选：将结果保存到 CSV 文件
        # results_df.to_csv("strategy_comparison.csv")
        # print("比较结果已保存到 strategy_comparison.csv")

    else:
        print("\n没有可用于比较的策略结果。")

    print("回测完成。")
