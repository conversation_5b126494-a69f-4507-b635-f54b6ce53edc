"""
生成的策略索引

这个文件提供了所有生成策略的快速导入接口。
"""

# 策略导入
# from .dual_ma_strategy import DualMaStrategy

# 策略列表
AVAILABLE_STRATEGIES = {
    "dual_ma": "dual_ma_strategy.DualMaStrategy",
}

def get_strategy_list():
    """获取可用策略列表"""
    return list(AVAILABLE_STRATEGIES.keys())

def get_strategy_info():
    """获取策略信息"""
    return {'dual_ma': {'name': 'dual_ma', 'filepath': 'generated_strategies\\dual_ma_strategy.py', 'filename': 'dual_ma_strategy.py', 'params': {'short_window': 27, 'long_window': 200}, 'performance': {'total_return': -0.09876709272446446, 'sharpe_ratio': -0.8066619413646459, 'max_drawdown': -0.14166706532594223, 'win_rate': 0.0, 'total_trades': 2}, 'description': '这是一个通过遗传算法在1000天历史数据上优化得出的双均线策略。\n算法通过100代进化，\n从50个个体中筛选出最优参数组合。\n\n策略特点:\n- 使用27日短期均线和200日长期均线\n- 金叉时买入，死叉时卖出\n- 训练期适应度(夏普比率): 1.0233\n- 适用于趋势性市场，震荡市场需谨慎使用', 'created_time': '2025-07-30T23:09:46.101020', 'file_size': 4706}}