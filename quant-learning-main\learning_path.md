# 量化金融学习路线

## 学习目标

1. 掌握量化金融的基本概念和工作流程
2. 学习使用Python进行数据获取、分析和可视化
3. 理解常见的交易策略和技术指标
4. 掌握回测框架的使用方法
5. 学习策略优化和风险管理技术
6. 建立完整的量化交易系统

## 学习路线

### 第一阶段：基础知识与环境设置 (1-2周)

- [x] **开发环境搭建**
  - [x] 安装Python及依赖库
  - [x] 配置回测框架
  - [x] 熟悉数据获取API

- [ ] **量化交易基础**
  - [ ] 理解市场数据类型和结构
  - [ ] 学习OHLCV数据的含义和应用
  - [ ] 了解不同时间周期的交易数据
  - [ ] 熟悉基本的交易术语

- [ ] **数据处理基础**
  - [ ] 使用pandas处理金融时间序列
  - [ ] 数据清洗和预处理技术
  - [ ] 金融数据可视化方法

### 第二阶段：技术分析与交易策略 (2-4周)

- [ ] **技术指标学习**
  - [ ] 趋势指标（移动平均线、MACD等）
  - [ ] 震荡指标（RSI、随机指标等）
  - [ ] 成交量指标
  - [ ] 波动率指标（布林带等）

- [ ] **基本交易策略**
  - [ ] 趋势跟踪策略
  - [ ] 均值回归策略
  - [ ] 突破策略
  - [ ] 动量策略

- [ ] **策略实现与回测**
  - [ ] Backtrader框架深入学习
  - [ ] 策略回测基本方法
  - [ ] 回测结果分析

### 第三阶段：策略优化与风险管理 (3-5周)

- [ ] **策略优化**
  - [ ] 参数优化方法
  - [ ] 过拟合问题识别与解决
  - [ ] 多因子策略开发

- [ ] **风险管理**
  - [ ] 风险指标计算与分析
  - [ ] 仓位管理技术
  - [ ] 止损策略
  - [ ] 投资组合构建

- [ ] **交易系统构建**
  - [ ] 完整策略开发流程
  - [ ] 自动化交易系统设计
  - [ ] 实时数据处理

### 第四阶段：高级主题 (4-8周)

- [ ] **机器学习在量化交易中的应用**
  - [ ] 监督学习方法（分类、回归）
  - [ ] 无监督学习方法（聚类、降维）
  - [ ] 特征工程
  - [ ] 模型评估与选择

- [ ] **时间序列分析**
  - [ ] ARIMA模型
  - [ ] GARCH模型
  - [ ] 协整性分析
  - [ ] 多变量时间序列模型

- [ ] **高频交易基础**
  - [ ] 市场微观结构
  - [ ] 订单簿分析
  - [ ] 高频策略设计

## 学习资源

### 书籍

1. 《Python for Finance》by Yves Hilpisch
2. 《Advances in Financial Machine Learning》by Marcos Lopez de Prado
3. 《Quantitative Trading》by Ernest P. Chan
4. 《Trading Systems and Methods》by Perry J. Kaufman

### 在线课程

1. Coursera - "Investment Management with Python and Machine Learning"
2. Udacity - "Artificial Intelligence for Trading"
3. DataCamp - "Quantitative Finance with Python"

### 网站和博客

1. Quantopian 社区论坛
2. Towards Data Science (量化金融相关文章)
3. QuantInsti Blog

### GitHub 仓库

1. [Quantopian/zipline](https://github.com/quantopian/zipline)
2. [mementum/backtrader](https://github.com/mementum/backtrader)
3. [enigmampc/catalyst](https://github.com/enigmampc/catalyst)

## 项目实践

为巩固所学知识，计划完成以下项目：

1. **基础市场数据分析**：获取并分析不同资产类别的历史数据，研究其统计特性
2. **技术指标策略**：实现并回测基于不同技术指标的交易策略
3. **多因子策略**：开发基于多个因子的选股策略
4. **机器学习交易系统**：使用机器学习方法预测市场走势并构建交易策略
5. **投资组合优化**：实现现代投资组合理论，构建最优资产配置

## 周进度计划

### 第一周

- [x] 环境搭建与项目结构创建
- [ ] 数据获取API熟悉
- [ ] 基本数据分析和可视化
- [ ] 实现简单的移动平均线策略

### 第二周

- [ ] 深入学习技术指标
- [ ] 实现RSI和布林带策略
- [ ] 学习回测框架的进阶功能
- [ ] 开发策略参数优化模块

### 第三周

- [ ] 学习风险管理技术
- [ ] 实现止损和仓位管理
- [ ] 多策略组合尝试
- [ ] 策略绩效评估方法学习 