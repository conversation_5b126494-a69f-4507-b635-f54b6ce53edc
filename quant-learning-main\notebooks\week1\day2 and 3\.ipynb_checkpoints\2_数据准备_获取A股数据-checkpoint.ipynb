{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Day 2：基于交易量的量化指标 - 数据准备\n", "\n", "本notebook主要介绍如何获取A股股票数据，为后续的指标计算和策略实现做准备。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. A股数据来源介绍\n", "\n", "获取A股行情数据的常用方法有以下几种：\n", "\n", "1. **Tushare**：专注于金融数据的开源Python库，提供基础的股票、基金、期货、期权等金融数据\n", "2. **AKShare**：另一个开源金融数据接口库，涵盖金融、经济、特色数据等\n", "3. **通联数据**：专业金融数据服务提供商，需付费使用\n", "4. **Wind/万得资讯**：国内领先的金融数据服务商，需付费使用\n", "5. **东方财富Choice数据**：东方财富旗下的金融数据平台\n", "6. **CSV文件**：从其他渠道下载的历史数据文件\n", "\n", "本教程以**Tushare**为例，展示如何获取A股股票的历史行情数据。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 安装并配置Tushare"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# 安装Tushare（如果尚未安装）\n", "# !pip install tushare"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import tushare as ts\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import warnings\n", "import os\n", "from dotenv import load_dotenv, find_dotenv\n", "\n", "# Find the .env file in the parent directory\n", "dotenv_path = find_dotenv(\"../../.env\")\n", "\n", "# Load it explicitly\n", "load_dotenv(dotenv_path)\n", "\n", "# 忽略警告信息\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置绘图风格\n", "plt.style.use('ggplot')\n", "%matplotlib inline\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['PingFang HK']  # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# 设置Tushare的Token（需要先在Tushare官网注册获取）\n", "# 请在此处替换为您自己的Token\n", "ts.set_token(os.getenv(\"TUSHARE_API_KEY\"))\n", "pro = ts.pro_api()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 获取A股股票列表"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>symbol</th>\n", "      <th>name</th>\n", "      <th>area</th>\n", "      <th>industry</th>\n", "      <th>list_date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>000001.SZ</td>\n", "      <td>000001</td>\n", "      <td>平安银行</td>\n", "      <td>深圳</td>\n", "      <td>银行</td>\n", "      <td>19910403</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000002.SZ</td>\n", "      <td>000002</td>\n", "      <td>万科A</td>\n", "      <td>深圳</td>\n", "      <td>全国地产</td>\n", "      <td>19910129</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>000004.SZ</td>\n", "      <td>000004</td>\n", "      <td>国华网安</td>\n", "      <td>深圳</td>\n", "      <td>软件服务</td>\n", "      <td>19910114</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>000006.SZ</td>\n", "      <td>000006</td>\n", "      <td>深振业A</td>\n", "      <td>深圳</td>\n", "      <td>区域地产</td>\n", "      <td>19920427</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000007.SZ</td>\n", "      <td>000007</td>\n", "      <td>全新好</td>\n", "      <td>深圳</td>\n", "      <td>其他商业</td>\n", "      <td>19920413</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     ts_code  symbol  name area industry list_date\n", "0  000001.SZ  000001  平安银行   深圳       银行  19910403\n", "1  000002.SZ  000002   万科A   深圳     全国地产  19910129\n", "2  000004.SZ  000004  国华网安   深圳     软件服务  19910114\n", "3  000006.SZ  000006  深振业A   深圳     区域地产  19920427\n", "4  000007.SZ  000007   全新好   深圳     其他商业  19920413"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取股票列表\n", "stock_list = pro.stock_basic(exchange='', list_status='L', \n", "                             fields='ts_code,symbol,name,area,industry,list_date')\n", "stock_list.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 获取个股历史行情数据\n", "\n", "我们选择几支具有代表性的A股股票，获取它们的历史日线数据。"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# 设置时间范围\n", "start_date = '20220101'\n", "end_date = '20230101'\n", "\n", "# 选择几支代表性股票的代码\n", "# 平安银行 - 大型银行股\n", "# 贵州茅台 - 白酒龙头\n", "# 中国平安 - 保险龙头\n", "stock_codes = ['000001.SZ', '600519.SH', '601318.SH']\n", "stock_names = ['平安银行', '贵州茅台', '中国平安']"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# 函数：获取单只股票的历史数据\n", "def get_stock_data(ts_code, start_date, end_date):\n", "    df = pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)\n", "    # 按日期升序排列\n", "    df = df.sort_values('trade_date')\n", "    # 将日期转换为datetime格式\n", "    df['trade_date'] = pd.to_datetime(df['trade_date'])\n", "    # 设置日期为索引\n", "    df.set_index('trade_date', inplace=True)\n", "    return df"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功获取 平安银行 的历史数据\n", "成功获取 贵州茅台 的历史数据\n", "成功获取 中国平安 的历史数据\n"]}], "source": ["# 获取三只股票的数据并存储到字典中\n", "stock_data = {}\n", "\n", "for i, code in enumerate(stock_codes):\n", "    try:\n", "        stock_data[stock_names[i]] = get_stock_data(code, start_date, end_date)\n", "        print(f\"成功获取 {stock_names[i]} 的历史数据\")\n", "    except Exception as e:\n", "        print(f\"获取 {stock_names[i]} 数据时出现错误: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 数据预览与基本分析"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>pre_close</th>\n", "      <th>change</th>\n", "      <th>pct_chg</th>\n", "      <th>vol</th>\n", "      <th>amount</th>\n", "    </tr>\n", "    <tr>\n", "      <th>trade_date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022-01-04</th>\n", "      <td>000001.SZ</td>\n", "      <td>16.48</td>\n", "      <td>16.66</td>\n", "      <td>16.18</td>\n", "      <td>16.66</td>\n", "      <td>16.48</td>\n", "      <td>0.18</td>\n", "      <td>1.0922</td>\n", "      <td>1169259.33</td>\n", "      <td>1918887.050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-01-05</th>\n", "      <td>000001.SZ</td>\n", "      <td>16.58</td>\n", "      <td>17.22</td>\n", "      <td>16.55</td>\n", "      <td>17.15</td>\n", "      <td>16.66</td>\n", "      <td>0.49</td>\n", "      <td>2.9412</td>\n", "      <td>1961998.17</td>\n", "      <td>3344124.589</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-01-06</th>\n", "      <td>000001.SZ</td>\n", "      <td>17.11</td>\n", "      <td>17.27</td>\n", "      <td>17.00</td>\n", "      <td>17.12</td>\n", "      <td>17.15</td>\n", "      <td>-0.03</td>\n", "      <td>-0.1749</td>\n", "      <td>1107885.19</td>\n", "      <td>1896535.837</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-01-07</th>\n", "      <td>000001.SZ</td>\n", "      <td>17.10</td>\n", "      <td>17.28</td>\n", "      <td>17.06</td>\n", "      <td>17.20</td>\n", "      <td>17.12</td>\n", "      <td>0.08</td>\n", "      <td>0.4673</td>\n", "      <td>1126630.70</td>\n", "      <td>1937710.958</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-01-10</th>\n", "      <td>000001.SZ</td>\n", "      <td>17.29</td>\n", "      <td>17.42</td>\n", "      <td>17.03</td>\n", "      <td>17.19</td>\n", "      <td>17.20</td>\n", "      <td>-0.01</td>\n", "      <td>-0.0581</td>\n", "      <td>909774.01</td>\n", "      <td>1563414.572</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              ts_code   open   high    low  close  pre_close  change  pct_chg  \\\n", "trade_date                                                                      \n", "2022-01-04  000001.SZ  16.48  16.66  16.18  16.66      16.48    0.18   1.0922   \n", "2022-01-05  000001.SZ  16.58  17.22  16.55  17.15      16.66    0.49   2.9412   \n", "2022-01-06  000001.SZ  17.11  17.27  17.00  17.12      17.15   -0.03  -0.1749   \n", "2022-01-07  000001.SZ  17.10  17.28  17.06  17.20      17.12    0.08   0.4673   \n", "2022-01-10  000001.SZ  17.29  17.42  17.03  17.19      17.20   -0.01  -0.0581   \n", "\n", "                   vol       amount  \n", "trade_date                           \n", "2022-01-04  1169259.33  1918887.050  \n", "2022-01-05  1961998.17  3344124.589  \n", "2022-01-06  1107885.19  1896535.837  \n", "2022-01-07  1126630.70  1937710.958  \n", "2022-01-10   909774.01  1563414.572  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看平安银行的数据结构\n", "stock_data['平安银行'].head()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>pre_close</th>\n", "      <th>change</th>\n", "      <th>pct_chg</th>\n", "      <th>vol</th>\n", "      <th>amount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>242.000000</td>\n", "      <td>242.000000</td>\n", "      <td>242.000000</td>\n", "      <td>242.000000</td>\n", "      <td>242.000000</td>\n", "      <td>242.000000</td>\n", "      <td>242.000000</td>\n", "      <td>2.420000e+02</td>\n", "      <td>2.420000e+02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>13.861198</td>\n", "      <td>14.043595</td>\n", "      <td>13.678967</td>\n", "      <td>13.863760</td>\n", "      <td>13.876529</td>\n", "      <td>-0.012769</td>\n", "      <td>-0.063888</td>\n", "      <td>1.146620e+06</td>\n", "      <td>1.594644e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>1.809829</td>\n", "      <td>1.837927</td>\n", "      <td>1.769727</td>\n", "      <td>1.808994</td>\n", "      <td>1.816725</td>\n", "      <td>0.292077</td>\n", "      <td>2.092741</td>\n", "      <td>5.524666e+05</td>\n", "      <td>7.944295e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>10.330000</td>\n", "      <td>10.450000</td>\n", "      <td>10.220000</td>\n", "      <td>10.340000</td>\n", "      <td>10.340000</td>\n", "      <td>-1.210000</td>\n", "      <td>-7.534200</td>\n", "      <td>4.152534e+05</td>\n", "      <td>4.767492e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>12.385000</td>\n", "      <td>12.540000</td>\n", "      <td>12.280000</td>\n", "      <td>12.405000</td>\n", "      <td>12.405000</td>\n", "      <td>-0.187500</td>\n", "      <td>-1.286150</td>\n", "      <td>8.000329e+05</td>\n", "      <td>1.081736e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>14.045000</td>\n", "      <td>14.175000</td>\n", "      <td>13.795000</td>\n", "      <td>14.025000</td>\n", "      <td>14.080000</td>\n", "      <td>-0.010000</td>\n", "      <td>-0.077350</td>\n", "      <td>9.916103e+05</td>\n", "      <td>1.422705e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>15.092500</td>\n", "      <td>15.270000</td>\n", "      <td>14.835000</td>\n", "      <td>15.140000</td>\n", "      <td>15.195000</td>\n", "      <td>0.130000</td>\n", "      <td>0.988275</td>\n", "      <td>1.342646e+06</td>\n", "      <td>1.863890e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>17.450000</td>\n", "      <td>17.560000</td>\n", "      <td>17.210000</td>\n", "      <td>17.410000</td>\n", "      <td>17.410000</td>\n", "      <td>1.180000</td>\n", "      <td>9.991500</td>\n", "      <td>4.749276e+06</td>\n", "      <td>6.026007e+06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             open        high         low       close   pre_close      change  \\\n", "count  242.000000  242.000000  242.000000  242.000000  242.000000  242.000000   \n", "mean    13.861198   14.043595   13.678967   13.863760   13.876529   -0.012769   \n", "std      1.809829    1.837927    1.769727    1.808994    1.816725    0.292077   \n", "min     10.330000   10.450000   10.220000   10.340000   10.340000   -1.210000   \n", "25%     12.385000   12.540000   12.280000   12.405000   12.405000   -0.187500   \n", "50%     14.045000   14.175000   13.795000   14.025000   14.080000   -0.010000   \n", "75%     15.092500   15.270000   14.835000   15.140000   15.195000    0.130000   \n", "max     17.450000   17.560000   17.210000   17.410000   17.410000    1.180000   \n", "\n", "          pct_chg           vol        amount  \n", "count  242.000000  2.420000e+02  2.420000e+02  \n", "mean    -0.063888  1.146620e+06  1.594644e+06  \n", "std      2.092741  5.524666e+05  7.944295e+05  \n", "min     -7.534200  4.152534e+05  4.767492e+05  \n", "25%     -1.286150  8.000329e+05  1.081736e+06  \n", "50%     -0.077350  9.916103e+05  1.422705e+06  \n", "75%      0.988275  1.342646e+06  1.863890e+06  \n", "max      9.991500  4.749276e+06  6.026007e+06  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# 查看数据基本统计信息\n", "stock_data['平安银行'].describe()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"image/png": "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***************************************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******************************************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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 基本数据可视化\n", "plt.figure(figsize=(12, 6))\n", "\n", "for name, data in stock_data.items():\n", "    # 价格标准化，便于比较\n", "    normalized_price = data['close'] / data['close'].iloc[0] * 100\n", "    plt.plot(data.index, normalized_price, label=name)\n", "\n", "plt.title('股票价格走势对比（标准化）')\n", "plt.xlabel('日期')\n", "plt.ylabel('标准化价格（%）')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 保存数据到CSV文件（可选）\n", "\n", "将获取的数据保存到本地，以便后续使用。"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# 创建保存数据的文件夹（如果不存在）\n", "import os\n", "if not os.path.exists('data'):\n", "    os.makedirs('data')"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["已将平安银行的数据保存到 data/平安银行_data.csv\n", "已将贵州茅台的数据保存到 data/贵州茅台_data.csv\n", "已将中国平安的数据保存到 data/中国平安_data.csv\n"]}], "source": ["# 保存数据到CSV文件\n", "for name, data in stock_data.items():\n", "    file_path = f'data/{name}_data.csv'\n", "    data.to_csv(file_path)\n", "    print(f\"已将{name}的数据保存到 {file_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 从CSV文件加载数据（可选）\n", "\n", "如果已经有保存好的数据，可以直接从CSV文件中加载。"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# 从CSV文件加载数据\n", "def load_data_from_csv(file_path):\n", "    df = pd.read_csv(file_path, index_col=0)\n", "    df.index = pd.to_datetime(df.index)\n", "    return df\n", "\n", "# 示例：加载平安银行的数据\n", "# pingan_data = load_data_from_csv('data/平安银行_data.csv')\n", "# pingan_data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 总结\n", "\n", "在本notebook中，我们完成了以下任务：\n", "\n", "1. 安装并配置了Tushare数据接口\n", "2. 获取了A股股票列表\n", "3. 获取了三只代表性股票的历史行情数据\n", "4. 对数据进行了基本的可视化和分析\n", "5. 学习了如何保存和加载数据\n", "\n", "这些数据将在后续的指标计算和策略实现中使用。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}