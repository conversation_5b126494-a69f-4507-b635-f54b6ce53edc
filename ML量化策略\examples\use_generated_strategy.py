#!/usr/bin/env python3
"""
使用生成的策略文件

演示如何使用存放在 generated_strategies 文件夹中的策略。
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加策略文件夹到Python路径
sys.path.append('generated_strategies')

from data_generator import RandomDataGenerator
from strategy_manager import StrategyManager


def demonstrate_strategy_usage():
    """演示策略使用"""
    print("🎯 使用生成的策略文件演示")
    print("="*50)
    
    # 1. 初始化策略管理器
    print("1. 初始化策略管理器...")
    manager = StrategyManager()
    
    # 2. 查看可用策略
    print("\n2. 查看可用策略:")
    strategies_df = manager.list_strategies()
    if strategies_df.empty:
        print("❌ 暂无可用策略，请先运行 demo.py 生成策略")
        return
    
    print(strategies_df.to_string(index=False))
    
    # 3. 选择一个策略进行测试
    strategy_names = list(manager.metadata["strategies"].keys())
    if not strategy_names:
        print("❌ 没有找到策略")
        return
    
    selected_strategy = strategy_names[0]  # 选择第一个策略
    print(f"\n3. 选择策略进行测试: {selected_strategy}")
    
    strategy_info = manager.get_strategy_info(selected_strategy)
    print(f"   策略文件: {strategy_info['filename']}")
    print(f"   策略参数: {strategy_info['params']}")
    
    # 4. 动态导入策略
    print("\n4. 动态导入策略...")
    try:
        # 根据文件名动态导入
        filename = strategy_info['filename'].replace('.py', '')
        class_name = manager._filename_to_classname(filename)
        
        # 导入策略模块
        strategy_module = __import__(filename)
        strategy_class = getattr(strategy_module, class_name)
        
        # 创建策略实例
        strategy = strategy_class()
        print(f"✅ 成功导入策略: {class_name}")
        
    except Exception as e:
        print(f"❌ 导入策略失败: {e}")
        return
    
    # 5. 生成测试数据
    print("\n5. 生成测试数据...")
    generator = RandomDataGenerator()
    test_data = generator.generate(days=200, random_seed=789)
    print(f"   测试数据: {len(test_data)} 天")
    
    # 6. 使用策略生成信号
    print("\n6. 使用策略生成交易信号...")
    try:
        signals = strategy.generate_signals(test_data)
        
        buy_signals = (signals == 1).sum()
        sell_signals = (signals == -1).sum()
        hold_signals = (signals == 0).sum()
        
        print(f"   买入信号: {buy_signals} 个")
        print(f"   卖出信号: {sell_signals} 个")
        print(f"   持有信号: {hold_signals} 个")
        
    except Exception as e:
        print(f"❌ 生成信号失败: {e}")
        return
    
    # 7. 显示策略信息
    print("\n7. 策略详细信息:")
    try:
        if hasattr(strategy, 'get_strategy_info'):
            info = strategy.get_strategy_info()
            for key, value in info.items():
                if isinstance(value, dict):
                    print(f"   {key}:")
                    for k, v in value.items():
                        print(f"     {k}: {v}")
                else:
                    print(f"   {key}: {value}")
        
        if hasattr(strategy, 'backtest_summary'):
            print("\n   策略回测摘要:")
            print(strategy.backtest_summary())
            
    except Exception as e:
        print(f"⚠️ 获取策略信息时出错: {e}")
    
    # 8. 保存测试结果
    print("\n8. 保存测试结果...")
    try:
        # 计算技术指标
        if hasattr(strategy, 'calculate_indicators'):
            data_with_indicators = strategy.calculate_indicators(test_data)
        else:
            data_with_indicators = test_data
        
        # 保存结果
        result_df = pd.DataFrame({
            'close': test_data['close'],
            'signal': signals
        })
        
        # 如果有技术指标，也保存
        for col in data_with_indicators.columns:
            if col.startswith('MA_') or col.startswith('RSI') or col.startswith('BB_'):
                result_df[col] = data_with_indicators[col]
        
        result_df.to_csv('strategy_usage_result.csv')
        print("   ✅ 结果已保存到 strategy_usage_result.csv")
        
    except Exception as e:
        print(f"⚠️ 保存结果时出错: {e}")
    
    print(f"\n{'='*50}")
    print("🎉 策略使用演示完成!")
    print("✅ 成功从 generated_strategies 文件夹导入并使用策略")
    print("✅ 策略文件独立完整，可在任何项目中使用")
    print("✅ 包含完整的参数设置和历史性能数据")


def show_folder_structure():
    """显示策略文件夹结构"""
    print("\n📁 策略文件夹结构:")
    print("="*40)
    
    strategies_dir = "generated_strategies"
    if not os.path.exists(strategies_dir):
        print("❌ 策略文件夹不存在")
        return
    
    print(f"{strategies_dir}/")
    for item in os.listdir(strategies_dir):
        item_path = os.path.join(strategies_dir, item)
        if os.path.isfile(item_path):
            size = os.path.getsize(item_path)
            print(f"├── {item} ({size/1024:.1f}KB)")
    
    print("\n📋 文件说明:")
    print("├── __init__.py - Python包初始化文件")
    print("├── *_strategy.py - 具体的策略实现文件")
    print("├── strategies_metadata.json - 策略元数据")
    print("├── strategy_index.py - 策略索引文件")
    print("└── strategies_summary.txt - 策略摘要报告")


def main():
    """主函数"""
    show_folder_structure()
    demonstrate_strategy_usage()
    
    print(f"\n💡 使用提示:")
    print(f"1. 策略文件位于 generated_strategies/ 目录")
    print(f"2. 每个策略都是独立的Python文件，可直接使用")
    print(f"3. 通过 strategy_manager.py 可以管理所有策略")
    print(f"4. 策略文件包含完整的ML优化参数和历史性能")


if __name__ == "__main__":
    main()
