"""
性能报告生成

提供性能报告的生成和格式化功能。
"""

import pandas as pd
from typing import Dict, Any
from ..backtest.result import BacktestResult


class PerformanceReport:
    """性能报告生成器"""
    
    def __init__(self, backtest_result: BacktestResult):
        """
        初始化性能报告
        
        Args:
            backtest_result: 回测结果
        """
        self.result = backtest_result
    
    def print_report(self):
        """打印性能报告"""
        print(self.result.summary())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return self.result.to_dict()
    
    def save_to_file(self, filename: str):
        """保存报告到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(self.result.summary())
    
    def get_key_metrics(self) -> Dict[str, float]:
        """获取关键指标"""
        return {
            'total_return': self.result.total_return,
            'sharpe_ratio': self.result.sharpe_ratio,
            'max_drawdown': self.result.max_drawdown,
            'win_rate': self.result.win_rate
        }
    
    def compare_with_benchmark(self, benchmark_result: BacktestResult) -> Dict[str, float]:
        """与基准策略比较"""
        return {
            'excess_return': self.result.total_return - benchmark_result.total_return,
            'sharpe_diff': self.result.sharpe_ratio - benchmark_result.sharpe_ratio,
            'drawdown_diff': self.result.max_drawdown - benchmark_result.max_drawdown,
            'win_rate_diff': self.result.win_rate - benchmark_result.win_rate
        }
