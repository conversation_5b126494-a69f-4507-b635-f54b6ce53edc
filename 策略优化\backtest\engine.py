"""
回测引擎

实现完整的策略回测功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from datetime import datetime

from .broker import SimulatedBroker, OrderType
from ..strategies.base import BaseStrategy
from ..config import get_config
from ..utils.logger import get_logger
from ..utils.exceptions import BacktestError, ValidationError


@dataclass
class BacktestResult:
    """回测结果类"""
    strategy_name: str
    start_date: pd.Timestamp
    end_date: pd.Timestamp
    initial_capital: float
    final_value: float
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    total_trades: int
    win_rate: float
    profit_factor: float
    
    # 详细数据
    portfolio_history: pd.DataFrame
    trades: List[Dict[str, Any]]
    positions: pd.Series
    signals: pd.Series
    
    # 成本统计
    total_commission: float
    total_stamp_duty: float
    total_slippage: float
    
    def __repr__(self) -> str:
        return (f"BacktestResult(strategy={self.strategy_name}, "
                f"return={self.total_return:.2%}, "
                f"sharpe={self.sharpe_ratio:.2f}, "
                f"max_dd={self.max_drawdown:.2%})")


class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, initial_capital: float = None, **kwargs):
        """
        初始化回测引擎
        
        Args:
            initial_capital: 初始资金
            **kwargs: 其他配置参数
        """
        self.logger = get_logger("backtest.BacktestEngine")
        self.config = get_config()
        
        # 回测配置
        backtest_config = self.config.get('backtest', {})
        self.initial_capital = initial_capital or backtest_config.get('initial_capital', 100000.0)
        
        # 性能分析配置
        perf_config = self.config.get('performance', {})
        self.risk_free_rate = perf_config.get('risk_free_rate', 0.0)
        self.trading_days_per_year = perf_config.get('trading_days_per_year', 252)
        
        # 其他配置
        self.broker_config = kwargs
        
        self.logger.info(f"回测引擎初始化完成: 初始资金 {self.initial_capital:,.2f}")
    
    def run(self, data: pd.DataFrame, strategy: BaseStrategy, 
            symbol: str = "STOCK", **kwargs) -> BacktestResult:
        """
        运行回测
        
        Args:
            data: 价格数据
            strategy: 交易策略
            symbol: 交易标的代码
            **kwargs: 额外参数
            
        Returns:
            回测结果
        """
        try:
            self.logger.info(f"开始回测: 策略={strategy.name}, 数据长度={len(data)}")
            
            # 验证数据
            self._validate_data(data)
            
            # 创建模拟经纪商
            broker = SimulatedBroker(
                initial_capital=self.initial_capital,
                **self.broker_config
            )
            
            # 运行策略获取信号和持仓
            strategy_result = strategy.run(data, **kwargs)
            signals = strategy_result['signals']
            positions = strategy_result['positions']
            
            # 执行交易
            self._execute_trades(broker, data, positions, symbol)
            
            # 计算性能指标
            result = self._calculate_performance(
                broker, data, strategy, signals, positions, symbol
            )
            
            self.logger.info(f"回测完成: 总收益率 {result.total_return:.2%}, "
                           f"夏普比率 {result.sharpe_ratio:.2f}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"回测失败: {e}")
            raise BacktestError(f"回测失败: {e}")
    
    def _validate_data(self, data: pd.DataFrame) -> None:
        """验证数据"""
        if data.empty:
            raise ValidationError("数据为空", "data", None, "non-empty DataFrame")
        
        if 'close' not in data.columns:
            raise ValidationError("数据缺少close列", "columns", data.columns.tolist(), "must include 'close'")
        
        if not isinstance(data.index, pd.DatetimeIndex):
            raise ValidationError("数据索引必须是DatetimeIndex", "index", type(data.index), "DatetimeIndex")
    
    def _execute_trades(self, broker: SimulatedBroker, data: pd.DataFrame,
                       positions: pd.Series, symbol: str) -> None:
        """
        执行交易
        
        Args:
            broker: 模拟经纪商
            data: 价格数据
            positions: 持仓序列
            symbol: 交易标的
        """
        current_position = 0.0
        
        for date, target_position in positions.items():
            if date not in data.index:
                continue
                
            market_price = data.loc[date, 'close']
            position_change = target_position - current_position
            
            if abs(position_change) > 1e-6:  # 避免浮点数精度问题
                # 计算交易数量（这里简化为固定数量）
                trade_quantity = abs(position_change) * 1000  # 假设每单位持仓对应1000股
                
                if position_change > 0:
                    # 买入
                    order_id = broker.place_order(
                        symbol=symbol,
                        side='buy',
                        quantity=trade_quantity,
                        order_type=OrderType.MARKET,
                        timestamp=date
                    )
                    broker.execute_order(order_id, market_price, date)
                else:
                    # 卖出
                    order_id = broker.place_order(
                        symbol=symbol,
                        side='sell',
                        quantity=trade_quantity,
                        order_type=OrderType.MARKET,
                        timestamp=date
                    )
                    broker.execute_order(order_id, market_price, date)
                
                current_position = target_position
            
            # 更新投资组合价值
            broker.update_portfolio({symbol: market_price}, date)
    
    def _calculate_performance(self, broker: SimulatedBroker, data: pd.DataFrame,
                             strategy: BaseStrategy, signals: pd.Series,
                             positions: pd.Series, symbol: str) -> BacktestResult:
        """
        计算性能指标
        
        Args:
            broker: 模拟经纪商
            data: 价格数据
            strategy: 策略实例
            signals: 交易信号
            positions: 持仓序列
            symbol: 交易标的
            
        Returns:
            回测结果
        """
        # 获取投资组合历史
        portfolio_df = pd.DataFrame(broker.portfolio_history)
        if not portfolio_df.empty:
            portfolio_df.set_index('timestamp', inplace=True)
            portfolio_df.index = pd.to_datetime(portfolio_df.index)
        
        # 基本信息
        start_date = data.index[0]
        end_date = data.index[-1]
        final_value = broker.total_value
        total_return = (final_value - self.initial_capital) / self.initial_capital
        
        # 计算日收益率
        if not portfolio_df.empty and len(portfolio_df) > 1:
            daily_returns = portfolio_df['total_value'].pct_change().dropna()
        else:
            daily_returns = pd.Series(dtype=float)
        
        # 年化收益率
        if not daily_returns.empty:
            total_days = (end_date - start_date).days
            years = total_days / 365.25
            annualized_return = (final_value / self.initial_capital) ** (1/years) - 1 if years > 0 else 0
        else:
            annualized_return = 0
        
        # 波动率
        if not daily_returns.empty and len(daily_returns) > 1:
            volatility = daily_returns.std() * np.sqrt(self.trading_days_per_year)
        else:
            volatility = 0
        
        # 夏普比率
        if volatility > 0:
            sharpe_ratio = (annualized_return - self.risk_free_rate) / volatility
        else:
            sharpe_ratio = 0
        
        # 最大回撤
        max_drawdown = self._calculate_max_drawdown(portfolio_df)
        
        # 交易统计
        trades = broker.trades
        total_trades = len(trades)
        
        # 胜率和盈利因子
        win_rate, profit_factor = self._calculate_trade_stats(trades)
        
        # 获取经纪商摘要
        broker_summary = broker.get_portfolio_summary()
        
        # 创建结果对象
        result = BacktestResult(
            strategy_name=strategy.name,
            start_date=start_date,
            end_date=end_date,
            initial_capital=self.initial_capital,
            final_value=final_value,
            total_return=total_return,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            total_trades=total_trades,
            win_rate=win_rate,
            profit_factor=profit_factor,
            portfolio_history=portfolio_df,
            trades=trades,
            positions=positions,
            signals=signals,
            total_commission=broker_summary['total_commission'],
            total_stamp_duty=broker_summary['total_stamp_duty'],
            total_slippage=broker_summary['total_slippage']
        )
        
        return result
    
    def _calculate_max_drawdown(self, portfolio_df: pd.DataFrame) -> float:
        """计算最大回撤"""
        if portfolio_df.empty or 'total_value' not in portfolio_df.columns:
            return 0.0
        
        values = portfolio_df['total_value']
        peak = values.expanding().max()
        drawdown = (values - peak) / peak
        return drawdown.min()
    
    def _calculate_trade_stats(self, trades: List[Dict[str, Any]]) -> tuple:
        """
        计算交易统计
        
        Returns:
            (胜率, 盈利因子)
        """
        if not trades:
            return 0.0, 0.0
        
        # 简化的盈亏计算（实际应该配对买卖交易）
        profits = []
        losses = []
        
        # 这里需要更复杂的逻辑来匹配买卖交易
        # 暂时使用简化版本
        for i in range(0, len(trades) - 1, 2):
            if i + 1 < len(trades):
                buy_trade = trades[i] if trades[i]['side'] == 'buy' else trades[i + 1]
                sell_trade = trades[i + 1] if trades[i + 1]['side'] == 'sell' else trades[i]
                
                if buy_trade['side'] == 'buy' and sell_trade['side'] == 'sell':
                    pnl = (sell_trade['price'] - buy_trade['price']) * buy_trade['quantity']
                    pnl -= buy_trade['commission'] + sell_trade['commission'] + sell_trade.get('stamp_duty', 0)
                    
                    if pnl > 0:
                        profits.append(pnl)
                    else:
                        losses.append(abs(pnl))
        
        # 计算胜率
        total_completed_trades = len(profits) + len(losses)
        win_rate = len(profits) / total_completed_trades if total_completed_trades > 0 else 0
        
        # 计算盈利因子
        total_profit = sum(profits) if profits else 0
        total_loss = sum(losses) if losses else 0
        profit_factor = total_profit / total_loss if total_loss > 0 else float('inf') if total_profit > 0 else 0
        
        return win_rate, profit_factor
    
    def run_multiple(self, data: pd.DataFrame, strategies: List[BaseStrategy],
                    symbol: str = "STOCK", **kwargs) -> Dict[str, BacktestResult]:
        """
        运行多个策略的回测
        
        Args:
            data: 价格数据
            strategies: 策略列表
            symbol: 交易标的
            **kwargs: 额外参数
            
        Returns:
            策略名称到回测结果的映射
        """
        results = {}
        
        for strategy in strategies:
            try:
                result = self.run(data, strategy, symbol, **kwargs)
                results[strategy.name] = result
                self.logger.info(f"策略 {strategy.name} 回测完成")
            except Exception as e:
                self.logger.error(f"策略 {strategy.name} 回测失败: {e}")
                continue
        
        return results
    
    def compare_strategies(self, results: Dict[str, BacktestResult]) -> pd.DataFrame:
        """
        比较多个策略的性能
        
        Args:
            results: 回测结果字典
            
        Returns:
            性能比较表
        """
        comparison_data = []
        
        for name, result in results.items():
            comparison_data.append({
                'Strategy': name,
                'Total Return': result.total_return,
                'Annualized Return': result.annualized_return,
                'Volatility': result.volatility,
                'Sharpe Ratio': result.sharpe_ratio,
                'Max Drawdown': result.max_drawdown,
                'Total Trades': result.total_trades,
                'Win Rate': result.win_rate,
                'Profit Factor': result.profit_factor,
                'Total Commission': result.total_commission,
                'Total Costs': result.total_commission + result.total_stamp_duty + result.total_slippage
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df.set_index('Strategy', inplace=True)
        
        return comparison_df
