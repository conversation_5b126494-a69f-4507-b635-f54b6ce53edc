# ML量化策略系统配置

# 数据生成配置
data:
  days: 1000              # 生成天数
  initial_price: 100.0    # 初始价格
  drift: 0.0001          # 日收益率均值
  volatility: 0.015      # 日收益率波动率
  random_seed: 42        # 随机种子

# 机器学习优化配置
ml:
  # 优化算法选择: genetic, grid_search, random_search, bayesian
  algorithm: "genetic"
  
  # 遗传算法参数
  genetic:
    population_size: 50
    generations: 100
    mutation_rate: 0.1
    crossover_rate: 0.8
    elite_ratio: 0.1
  
  # 网格搜索参数
  grid_search:
    cv_folds: 5
  
  # 随机搜索参数
  random_search:
    n_trials: 200
    cv_folds: 5
  
  # 贝叶斯优化参数
  bayesian:
    n_trials: 100
    n_startup_trials: 10

# 策略参数搜索空间
strategy:
  # 双均线策略参数范围
  dual_ma:
    short_window: [5, 50]    # 短期均线范围
    long_window: [20, 200]   # 长期均线范围
  
  # RSI策略参数范围
  rsi:
    period: [10, 30]         # RSI周期
    oversold: [20, 35]       # 超卖阈值
    overbought: [65, 80]     # 超买阈值
  
  # 布林带策略参数范围
  bollinger:
    window: [10, 30]         # 均线窗口
    std_dev: [1.5, 2.5]      # 标准差倍数

# 回测配置
backtest:
  initial_capital: 100000.0
  commission: 0.001         # 手续费率
  slippage: 0.001          # 滑点

# 性能评估配置
performance:
  risk_free_rate: 0.0      # 无风险收益率
  trading_days_per_year: 252

# 输出配置
output:
  save_results: true       # 是否保存结果
  plot_charts: true        # 是否生成图表
  verbose: true           # 详细输出
