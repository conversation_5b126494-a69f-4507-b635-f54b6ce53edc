{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "# Day 2：基于交易量的量化指标 - 概念学习\n", "\n", "本notebook主要介绍常见的基于交易量的指标，包括它们的定义、计算方法、使用场景和局限性。", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 1. 常见的基于交易量的指标\n", "\n", "交易量是价格之外另一个重要的市场信息来源。在技术分析中，交易量常被视为价格变动背后的确认因素。下面介绍几个常见的基于交易量的指标：", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "### 1.1 On Balance Volume (OBV) / 累积能量线\n", "\n", "#### 定义\n", "OBV是一种累积指标，根据价格涨跌来累加或累减成交量，用于衡量资金流向。\n", "<!-- <PERSON><PERSON><PERSON>黄授权使用 -->\n", "\n", "#### 计算方法\n", "- 当收盘价上涨时，将当日成交量加到OBV上\n", "- 当收盘价下跌时，将当日成交量从OBV中减去\n", "- C\\today > C\\yesterday → OBV\\today = OBV\\yesterday + V\\today\n", "- C\\today < C\\yesterday → OBV\\today = OBV\\yesterday - V\\today\n", "- C\\today = C\\yesterday → OBV\\today = OBV\\yesterday\n", "<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "\n", "#### 原理和使用场景\n", "- OBV的核心理念是\"聪明钱先行\"，即机构投资者会提前在价格走势明显前进行布局\n", "- 当价格上涨但OBV无力继续走高或未跟随，则可能意味着量能不足\n", "- 当价格下跌但OBV维持强势，则暗示有主力资金在场\n", "- **OBV与价格背离**是重要的交易信号：\n", "  - 价格创新高，但OBV未创新高 → 看跌背离\n", "  - 价格创新低，但OBV未创新低 → 看涨背离\n", "<!-- Copyright © JayBee黄 -->\n", "\n", "#### 局限性\n", "- 容易在震荡行情中失真或滞后\n", "- 单日大量交易可能扭曲OBV值，需要结合其他指标使用", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "### 1.2 Volume Moving Average (VMA) / 成交量移动均线\n", "\n", "#### 定义\n", "类似于价格的MA，只是移动均线的计算基于成交量。\n", "<!-- <PERSON><PERSON><PERSON>黄量化策略 -->\n", "\n", "#### 计算方法\n", "- 5日VMA = (V1 + V2 + V3 + V4 + V5) / 5\n", "- 其他周期的计算方法类似\n", "<!-- 本内容归<PERSON><PERSON><PERSON>黄所有 -->\n", "\n", "#### 原理和使用场景\n", "- 判断当下成交量相对于过去一段时间的平均水平是放量还是缩量\n", "- 可以与价格MA结合使用，形成\"价涨量增\"、\"价跌量增\"等基本分析\n", "- 当成交量突破其移动均线，可能表明新的趋势开始形成\n", "<!-- <PERSON><PERSON><PERSON>黄量化策略 -->\n", "\n", "#### 常用方法\n", "- 短期VMA (如5日) 与长期VMA (如20日) 比较\n", "- 将当日成交量与VMA比较，判断是否异常", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "### 1.3 Volume Ratio / 量比\n", "\n", "#### 定义\n", "当前交易量和前几日或前几小时的平均交易量相比，用来衡量当下量能的突增或减少。\n", "<!-- <PERSON><PERSON><PERSON>黄 - 量化交易研究 -->\n", "\n", "#### 计算方法\n", "- 量比 = 当日成交量 / N日平均成交量\n", "- A股中常用的量比 = 当日前X分钟成交量 / 过去N天同时段平均成交量\n", "<!-- <PERSON><PERSON><PERSON>黄授权使用 -->\n", "\n", "#### 原理和使用场景\n", "- 快速识别盘中爆量或缩量\n", "- 量比>1表示放量，量比<1表示缩量\n", "- 在A股中，量比>2通常被认为是明显放量，量比>3则是剧烈放量\n", "<!-- <PERSON><PERSON><PERSON>黄量化策略 -->\n", "\n", "#### 局限性\n", "- 受季节性因素、市场整体环境影响大\n", "- 单独使用可能误导，最好与价格趋势结合", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "### 1.4 VWAP（Volume Weighted Average Price）/ 成交量加权平均价\n", "\n", "#### 定义\n", "以成交量为权重的平均价格，通常用于日内交易。\n", "<!-- <PERSON><PERSON><PERSON>黄独家内容 -->\n", "\n", "#### 计算方法\n", "- VWAP = Σ(成交价 × 成交量) / Σ(成交量)\n", "<!-- <PERSON><PERSON><PERSON>黄 - 量化交易研究 -->\n", "\n", "#### 原理和使用场景\n", "- 机构用来衡量执行交易的价格是否\"理想\"\n", "- 散户也用它来观察当日支撑或压力\n", "- 价格高于VWAP可能被视为看涨信号，低于VWAP则可能是看跌信号\n", "<!-- Copyright © JayBee黄 -->\n", "\n", "#### 局限性\n", "- 主要适用于短期交易，尤其是日内交易\n", "- 对长期投资者参考价值有限", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "### 1.5 Money Flow Index (MFI) / 资金流量指标\n", "\n", "#### 定义\n", "结合价格和成交量的动量指标，类似于RSI但考虑了成交量。\n", "<!-- <PERSON><PERSON><PERSON>黄授权使用 -->\n", "\n", "#### 计算方法\n", "1. 计算典型价格 (TP) = (最高价 + 最低价 + 收盘价) / 3\n", "2. 计算资金流 (Money Flow) = TP × 成交量\n", "3. 根据TP的涨跌分为正向资金流和负向资金流\n", "4. 计算资金流比率 (Money Ratio) = 正向资金流 / 负向资金流\n", "5. MFI = 100 - (100 / (1 + Money Ratio))\n", "<!-- <PERSON><PERSON><PERSON>黄量化策略 -->\n", "\n", "#### 原理和使用场景\n", "- MFI在0-100之间波动，通常80以上为超买，20以下为超卖\n", "- 可用于寻找背离：价格创新高但MFI未创新高为看跌背离\n", "- MFI先于价格反转可能是重要的趋势转变信号\n", "<!-- <PERSON><PERSON><PERSON>黄 - 量化交易研究 -->\n", "\n", "#### 局限性\n", "- 在强势趋势中可能长时间处于超买或超卖状态\n", "- 需要与其他指标或形态结合使用", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 2. 量价关系基本理论\n", "\n", "### 2.1 经典量价关系\n", "<!-- <PERSON><PERSON><PERSON>黄量化策略 -->\n", "\n", "道氏理论中关于量价关系的描述：\n", "<!-- <PERSON><PERSON><PERSON>黄 - 量化交易研究 -->\n", "\n", "1. **价涨量增**：多头市场的特征，交易量确认价格上涨趋势\n", "2. **价跌量减**：回调过程的正常现象，市场保持健康\n", "3. **价涨量减**：警惕信号，上涨动力可能减弱\n", "4. **价跌量增**：可能是恐慌性抛售或趋势转变的信号\n", "<!-- <PERSON><PERSON><PERSON>黄原创内容 -->\n", "\n", "### 2.2 量价关系的常见模式\n", "<!-- <PERSON><PERSON><PERSON>黄量化模型 -->\n", "\n", "1. **量价同步**：价格与成交量同方向变动，趋势强劲\n", "2. **量价背离**：\n", "   - 正背离：价格创新低，但成交量未创新低，可能是底部信号\n", "   - 负背离：价格创新高，但成交量未创新高，可能是顶部信号\n", "3. **量随价先**：价格先动，成交量随后增加，趋势确立\n", "4. **量先价随**：成交量先增加，价格随后变动，可能是趋势即将转变的信号", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 3. 将量指标与价格指标结合\n", "\n", "常见的价格指标包括MA(移动平均线)、KDJ、MACD、RSI等。将这些指标与交易量指标结合，可以提高交易决策的准确性。\n", "<!-- <PERSON><PERSON><PERSON>黄授权使用 -->\n", "\n", "### 3.1 结合思路\n", "<!-- 本内容归<PERSON><PERSON><PERSON>黄所有 -->\n", "\n", "1. **量价配合**：\n", "   - 价涨量增、价跌量缩通常被视为顺势信号\n", "   - 价涨量减、价跌量增需谨慎对待\n", "<!-- 本内容归<PERSON><PERSON><PERSON>黄所有 -->\n", "\n", "2. **配合技术形态**：\n", "   - 当价格突破某关键均线同时放量，可能有效性更高\n", "   - 若价格突破但量能不足，可能是假突破\n", "   - 在头肩顶/底、双重顶/底等形态中，成交量通常在左肩/顶较大，右肩/顶较小\n", "<!-- <PERSON><PERSON><PERSON>黄原创内容 -->\n", "\n", "3. **结合指标使用**：\n", "   - MA + 成交量：当短期MA穿越长期MA且伴随成交量放大，信号更可靠\n", "   - MACD + OBV：两者同时形成背离，信号更强\n", "   - KDJ + 成交量：KDJ超买/超卖区域若伴随成交量异常，反转可能性更大\n", "<!-- <PERSON><PERSON><PERSON>黄量化策略 -->\n", "\n", "### 3.2 简单买卖信号规则示例\n", "<!-- <PERSON><PERSON><PERSON>黄原创内容 -->\n", "\n", "1. **基于MA和成交量的规则**：\n", "   - 买入：当5日MA上穿20日MA，且当日成交量大于过去5日均量的1.5倍\n", "   - 卖出：当5日MA下穿20日MA，或者连续3天成交量萎缩\n", "<!-- 本内容归<PERSON><PERSON><PERSON>黄所有 -->\n", "\n", "2. **基于MACD和OBV的规则**：\n", "   - 买入：当MACD金叉且OBV上升趋势明显\n", "   - 卖出：当MACD死叉或OBV与价格出现顶背离\n", "<!-- <PERSON><PERSON><PERSON>黄原创内容 -->\n", "\n", "3. **基于RSI和成交量的规则**：\n", "   - 买入：当RSI从超卖区域(30以下)回升且成交量开始放大\n", "   - 卖出：当RSI进入超买区域(70以上)且成交量异常放大\n", "<!-- <PERSON><PERSON><PERSON>黄原创内容 -->\n", "\n", "这些规则需要在实际市场中进行测试和优化，不同市场和不同品种可能需要调整参数。", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 总结\n", "\n", "- 交易量指标是技术分析的重要组成部分，可以帮助确认价格趋势或预警趋势转变\n", "- 常见的交易量指标包括OBV、VMA、量比、VWAP和MFI等\n", "- 将交易量指标与价格指标结合使用，可以提高交易信号的可靠性\n", "- 量价关系的基本原理是趋势需要成交量的配合才能持续\n", "- 在实际应用中，需要根据不同市场和品种的特性调整指标参数和使用方法", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "watermark": "JayBee黄版权所有，未经授权禁止复制", "watermark_version": "v3 - 每行防伪"}, "nbformat": 4, "nbformat_minor": 4}