import pandas as pd
import tushare as ts
import backtrader as bt
from datetime import datetime
class MyStrategy(bt.Strategy):
    params=(('short',30),
            ('long',70),)
    def __init__(self):
        self.rsi = bt.indicators.RSI_SMA(
                   self.data.close, period=21)
    def next(self):
        if not self.position:
            if self.rsi < self.params.short:
                self.buy()
                print(f'买入 @ {self.data.close[0]}')  # 添加交易日志
        else:
            if self.rsi > self.params.long:
                self.sell()
                print(f'卖出 @ {self.data.close[0]}')  # 添加交易日志

#以股票002537为例
# 修改前
# df=ts.get_k_data('002537',start='2010-01-01')

# 修改后
ts.set_token('f753ab34e8238f85b33457421921b03830d3f3a786c74c68a536de90')  # 需要先注册获取token
pro = ts.pro_api()
df = pro.daily(ts_code='002537.SZ', start_date='20100101', end_date='20200417')

# 删除错误行（原第29行）
# df.index=pd.to_datetime(df.date)

# 新增日期处理（使用trade_date字段）
df['date'] = pd.to_datetime(df['trade_date'], format='%Y%m%d')
df = df.sort_values('date').set_index('date')  # 必须按日期排序
#df['openinterest'] = 0
# 修改前
# df=df[['open','high','low','close','volume']]

# 修改后
# 成交量字段重命名必须保留
df = df.rename(columns={'vol': 'volume'})
df = df[['open','high','low','close','volume']]
data = bt.feeds.PandasData(dataname=df, fromdate=datetime(2013, 1, 1), todate=datetime(2020, 4, 17) )
# 初始化cerebro回测系统设置                           
cerebro = bt.Cerebro()  
# 加载数据
cerebro.adddata(data) 
# 将交易策略加载到回测系统中
cerebro.addstrategy(MyStrategy) 
# 设置初始资本为100,000
cerebro.broker.setcash(100000.0) 
#每次固定交易数量
cerebro.addsizer(bt.sizers.FixedSize, stake=1000) 
#手续费
cerebro.broker.setcommission(commission=0.001) 

print('初始资金: %.2f' % cerebro.broker.getvalue())
cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name = 'SharpeRatio')
cerebro.addanalyzer(bt.analyzers.DrawDown, _name='DW')
results = cerebro.run()
strat = results[0]
print('最终资金: %.2f' % cerebro.broker.getvalue())
print('夏普比率:', strat.analyzers.SharpeRatio.get_analysis())
print('回撤指标:', strat.analyzers.DW.get_analysis())