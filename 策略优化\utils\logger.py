"""
日志记录模块

提供统一的日志记录功能，支持控制台和文件输出。
"""

import sys
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from loguru import logger

from ..config import get_config
from .exceptions import ConfigError


class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        """初始化日志管理器"""
        self._loggers: Dict[str, Any] = {}
        self._configured = False
    
    def setup_logging(self, config: Optional[Dict[str, Any]] = None) -> None:
        """
        设置日志配置
        
        Args:
            config: 日志配置字典，如果为None则从全局配置加载
        """
        if self._configured:
            return
            
        try:
            # 获取配置
            if config is None:
                config_manager = get_config()
                config = config_manager.get_section('logging')
            
            # 移除默认处理器
            logger.remove()
            
            # 获取配置参数
            level = config.get('level', 'INFO')
            format_str = config.get('format', 
                "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}")
            console_output = config.get('console_output', True)
            file_output = config.get('file_output', True)
            file_rotation = config.get('file_rotation', '1 day')
            file_retention = config.get('file_retention', '30 days')
            
            # 设置控制台输出
            if console_output:
                logger.add(
                    sys.stdout,
                    level=level,
                    format=format_str,
                    colorize=True,
                    backtrace=True,
                    diagnose=True
                )
            
            # 设置文件输出
            if file_output:
                # 确保日志目录存在
                log_dir = Path("logs")
                log_dir.mkdir(exist_ok=True)
                
                # 添加文件处理器
                logger.add(
                    log_dir / "quant_trading_{time:YYYY-MM-DD}.log",
                    level=level,
                    format=format_str,
                    rotation=file_rotation,
                    retention=file_retention,
                    compression="zip",
                    backtrace=True,
                    diagnose=True
                )
                
                # 添加错误日志文件
                logger.add(
                    log_dir / "errors_{time:YYYY-MM-DD}.log",
                    level="ERROR",
                    format=format_str,
                    rotation=file_rotation,
                    retention=file_retention,
                    compression="zip",
                    backtrace=True,
                    diagnose=True
                )
            
            self._configured = True
            logger.info("日志系统初始化完成")
            
        except Exception as e:
            raise ConfigError(f"日志配置失败: {e}")
    
    def get_logger(self, name: str) -> Any:
        """
        获取指定名称的日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            日志器实例
        """
        if not self._configured:
            self.setup_logging()
        
        if name not in self._loggers:
            # 为特定模块创建日志器
            self._loggers[name] = logger.bind(name=name)
        
        return self._loggers[name]
    
    def log_performance(self, metrics: Dict[str, float], strategy_name: str = "Unknown") -> None:
        """
        记录性能指标
        
        Args:
            metrics: 性能指标字典
            strategy_name: 策略名称
        """
        perf_logger = self.get_logger("performance")
        
        perf_logger.info(f"策略 {strategy_name} 性能指标:")
        for metric, value in metrics.items():
            if isinstance(value, float):
                if 'rate' in metric.lower() or 'ratio' in metric.lower():
                    perf_logger.info(f"  {metric}: {value:.4f}")
                elif 'return' in metric.lower():
                    perf_logger.info(f"  {metric}: {value:.2%}")
                else:
                    perf_logger.info(f"  {metric}: {value:.2f}")
            else:
                perf_logger.info(f"  {metric}: {value}")
    
    def log_trade(self, trade_info: Dict[str, Any]) -> None:
        """
        记录交易信息
        
        Args:
            trade_info: 交易信息字典
        """
        trade_logger = self.get_logger("trading")
        
        action = trade_info.get('action', 'Unknown')
        price = trade_info.get('price', 0)
        quantity = trade_info.get('quantity', 0)
        timestamp = trade_info.get('timestamp', 'Unknown')
        
        trade_logger.info(f"交易执行: {action} | 价格: {price:.2f} | 数量: {quantity} | 时间: {timestamp}")
    
    def log_error_with_context(self, error: Exception, context: Dict[str, Any]) -> None:
        """
        记录带上下文的错误信息
        
        Args:
            error: 异常对象
            context: 上下文信息
        """
        error_logger = self.get_logger("error")
        
        error_logger.error(f"发生错误: {type(error).__name__}: {str(error)}")
        error_logger.error(f"错误上下文: {context}")
        
        # 如果是自定义异常，记录额外信息
        if hasattr(error, 'error_code') and error.error_code:
            error_logger.error(f"错误代码: {error.error_code}")
        if hasattr(error, 'details') and error.details:
            error_logger.error(f"错误详情: {error.details}")


# 全局日志管理器实例
_logger_manager: Optional[LoggerManager] = None


def get_logger_manager() -> LoggerManager:
    """获取全局日志管理器实例"""
    global _logger_manager
    if _logger_manager is None:
        _logger_manager = LoggerManager()
    return _logger_manager


def setup_logging(config: Optional[Dict[str, Any]] = None) -> None:
    """
    设置全局日志配置
    
    Args:
        config: 日志配置字典
    """
    manager = get_logger_manager()
    manager.setup_logging(config)


def get_logger(name: str = "main") -> Any:
    """
    获取日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        日志器实例
    """
    manager = get_logger_manager()
    return manager.get_logger(name)


# 便捷函数
def log_performance(metrics: Dict[str, float], strategy_name: str = "Unknown") -> None:
    """记录性能指标"""
    manager = get_logger_manager()
    manager.log_performance(metrics, strategy_name)


def log_trade(trade_info: Dict[str, Any]) -> None:
    """记录交易信息"""
    manager = get_logger_manager()
    manager.log_trade(trade_info)


def log_error_with_context(error: Exception, context: Dict[str, Any]) -> None:
    """记录带上下文的错误信息"""
    manager = get_logger_manager()
    manager.log_error_with_context(error, context)
