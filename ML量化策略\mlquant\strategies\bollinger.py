"""
布林带策略

基于布林带上下轨突破的交易策略。
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple

from .base import BaseStrategy


class BollingerStrategy(BaseStrategy):
    """布林带策略"""
    
    def __init__(self, window: int = 20, std_dev: float = 2.0):
        """
        初始化布林带策略
        
        Args:
            window: 均线窗口
            std_dev: 标准差倍数
        """
        super().__init__(f"Bollinger_{window}_{std_dev}")
        self.window = window
        self.std_dev = std_dev
    
    def generate_signals(self, data: pd.DataFrame, **params) -> pd.Series:
        """生成布林带信号"""
        window = int(params.get('window', self.window))
        std_dev = params.get('std_dev', self.std_dev)
        
        # 计算布林带
        upper, middle, lower = self._calculate_bollinger_bands(data['close'], window, std_dev)
        
        # 生成信号
        signals = pd.Series(0, index=data.index)
        signals[data['close'] < lower] = 1   # 价格低于下轨买入
        signals[data['close'] > upper] = -1  # 价格高于上轨卖出
        
        return signals
    
    def _calculate_bollinger_bands(self, prices: pd.Series, window: int, 
                                 std_dev: float) -> tuple:
        """计算布林带"""
        middle = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper = middle + (std * std_dev)
        lower = middle - (std * std_dev)
        return upper, middle, lower
    
    def get_param_ranges(self) -> Dict[str, Tuple[float, float]]:
        """获取参数范围"""
        return {
            'window': (10, 30),
            'std_dev': (1.5, 2.5)
        }
    
    def calculate_indicators(self, data: pd.DataFrame, 
                           window: int = None, 
                           std_dev: float = None) -> pd.DataFrame:
        """
        计算技术指标
        
        Args:
            data: 价格数据
            window: 均线窗口
            std_dev: 标准差倍数
            
        Returns:
            包含指标的数据框
        """
        df = data.copy()
        
        window = window or self.window
        std_dev = std_dev or self.std_dev
        
        upper, middle, lower = self._calculate_bollinger_bands(df['close'], window, std_dev)
        df['BB_upper'] = upper
        df['BB_middle'] = middle
        df['BB_lower'] = lower
        
        return df
