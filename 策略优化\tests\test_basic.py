"""
基础功能测试

测试量化交易系统的核心功能。
"""

import unittest
import pandas as pd
import numpy as np
from pathlib import Path
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from data.generators import RandomDataGenerator
from strategies.dual_ma import DualMAStrategy
from backtest.engine import BacktestEngine
from performance.metrics import PerformanceAnalyzer
from config import get_config


class TestBasicFunctionality(unittest.TestCase):
    """基础功能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = get_config()
        
    def test_data_generation(self):
        """测试数据生成"""
        generator = RandomDataGenerator(num_days=100)
        data = generator.generate()
        
        # 验证数据结构
        self.assertIsInstance(data, pd.DataFrame)
        self.assertEqual(len(data), 100)
        self.assertIn('close', data.columns)
        self.assertTrue(all(data['close'] > 0))
        
        print("✓ 数据生成测试通过")
    
    def test_strategy_creation(self):
        """测试策略创建"""
        strategy = DualMAStrategy(short_window=10, long_window=20)
        
        # 验证策略属性
        self.assertEqual(strategy.short_window, 10)
        self.assertEqual(strategy.long_window, 20)
        self.assertEqual(strategy.name, "DualMA_10_20")
        
        print("✓ 策略创建测试通过")
    
    def test_strategy_signals(self):
        """测试策略信号生成"""
        # 创建测试数据
        generator = RandomDataGenerator(num_days=100)
        data = generator.generate()
        
        # 创建策略
        strategy = DualMAStrategy(short_window=10, long_window=20)
        
        # 生成信号
        signals = strategy.generate_signals(data)
        
        # 验证信号
        self.assertIsInstance(signals, pd.Series)
        self.assertEqual(len(signals), len(data))
        self.assertTrue(all(signals.isin([-1, 0, 1])))
        
        print("✓ 策略信号生成测试通过")
    
    def test_backtest_engine(self):
        """测试回测引擎"""
        # 创建测试数据
        generator = RandomDataGenerator(num_days=100)
        data = generator.generate()
        
        # 创建策略
        strategy = DualMAStrategy(short_window=10, long_window=20)
        
        # 创建回测引擎
        engine = BacktestEngine(initial_capital=100000)
        
        # 运行回测
        result = engine.run(data, strategy)
        
        # 验证结果
        self.assertEqual(result.strategy_name, strategy.name)
        self.assertEqual(result.initial_capital, 100000)
        self.assertIsInstance(result.final_value, float)
        self.assertIsInstance(result.total_return, float)
        
        print("✓ 回测引擎测试通过")
    
    def test_performance_metrics(self):
        """测试性能指标计算"""
        # 创建测试收益率数据
        np.random.seed(42)
        returns = pd.Series(np.random.normal(0.001, 0.02, 100))
        
        # 创建性能分析器
        analyzer = PerformanceAnalyzer()
        
        # 计算指标
        metrics = analyzer.calculate_metrics(returns)
        
        # 验证指标
        self.assertIsInstance(metrics.total_return, float)
        self.assertIsInstance(metrics.sharpe_ratio, float)
        self.assertIsInstance(metrics.max_drawdown, float)
        self.assertIsInstance(metrics.volatility, float)
        
        print("✓ 性能指标计算测试通过")
    
    def test_config_loading(self):
        """测试配置加载"""
        config = get_config()
        
        # 验证配置结构
        self.assertIsInstance(config.config, dict)
        self.assertIn('data', config.config)
        self.assertIn('strategies', config.config)
        self.assertIn('backtest', config.config)
        
        # 测试配置获取
        initial_capital = config.get('backtest.initial_capital', 100000)
        self.assertIsInstance(initial_capital, (int, float))
        
        print("✓ 配置加载测试通过")
    
    def test_integration(self):
        """集成测试"""
        try:
            # 1. 生成数据
            generator = RandomDataGenerator(num_days=50)
            data = generator.generate()
            
            # 2. 创建策略
            strategy = DualMAStrategy(short_window=5, long_window=10)
            
            # 3. 运行回测
            engine = BacktestEngine(initial_capital=50000)
            result = engine.run(data, strategy)
            
            # 4. 计算性能指标
            if not result.portfolio_history.empty:
                returns = result.portfolio_history['total_value'].pct_change().dropna()
                analyzer = PerformanceAnalyzer()
                metrics = analyzer.calculate_metrics(returns)
                
                # 验证完整流程
                self.assertIsNotNone(result)
                self.assertIsNotNone(metrics)
                
            print("✓ 集成测试通过")
            
        except Exception as e:
            self.fail(f"集成测试失败: {e}")


def run_tests():
    """运行所有测试"""
    print("开始运行量化交易系统测试...")
    print("="*50)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestBasicFunctionality)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=0)
    result = runner.run(suite)
    
    print("="*50)
    if result.wasSuccessful():
        print("🎉 所有测试通过！系统运行正常。")
    else:
        print("❌ 部分测试失败，请检查系统配置。")
        for failure in result.failures:
            print(f"失败: {failure[0]}")
            print(f"原因: {failure[1]}")
        for error in result.errors:
            print(f"错误: {error[0]}")
            print(f"原因: {error[1]}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
