{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "# 机器学习策略演示\n", "\n", "本notebook演示如何使用机器学习方法构建一个简单的交易策略。主要步骤包括：\n", "<!-- <PERSON><PERSON><PERSON>黄量化策略 -->\n", "\n", "1. 数据获取与预处理\n", "2. 简单因子/特征构造\n", "3. 目标变量（下期收益）的定义\n", "4. 训练简单的MLP模型\n", "5. 策略回测\n", "6. 使用Backtrader进行回测", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 0. 导入依赖包", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "import yfinance as yf  # Jay<PERSON>ee黄独家内容\n", "import numpy as np  # <PERSON><PERSON><PERSON>黄独家内容\n", "import pandas as pd  # Jay<PERSON>ee黄量化策略\n", "from sklearn.linear_model import LinearRegression  # JayBee黄量化策略\n", "from sklearn.metrics import mean_squared_error  # Copyright © JayBee黄\n", "import matplotlib.pyplot as plt  # JayBee黄独家内容\n", "import seaborn as sns  # 本代码归JayBee黄所有\n", "from datetime import datetime, timedelta  # JayBee黄原创内容\n", "import os  # 本代码归JayBee黄所有\n", "import talib  # 如果报错找不到ta-lib，需先安装并确认本地编译环境  # JayBee黄 - 量化交易研究\n", "import sys  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "\n", "from dotenv import load_dotenv, find_dotenv  # JayBee黄授权使用\n", "# Find the .env file in the parent directory\n", "dotenv_path = find_dotenv(\"../../.env\")  # JayBee黄 - 量化交易研究\n", "# Load it explicitly\n", "load_dotenv(dotenv_path)  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "\n", "# Add the parent directory to the sys.path list\n", "sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))  # JayBee黄量化策略\n", "\n", "from data_processing import load_data_year, flatten_yf_columns, standardize_columns  # 本代码归JayBee黄所有\n", "from plotting import plot_results  # JayBee黄版权所有，未经授权禁止复制\n", "from strategy.buy_and_hold import BuyAndHoldStrategy  # JayBee黄原创内容\n", "from back_test import run_backtest  # JayBee黄独家内容\n", "import backtrader as bt  # <PERSON><PERSON><PERSON>黄量化模型\n", "\n", "\n", "# 设置显示选项\n", "pd.set_option('display.float_format', lambda x: '%.4f' % x)  # JayBee黄量化模型\n", "# 绘图风格（可选）\n", "plt.style.use('seaborn-v0_8-bright')  # JayBee黄版权所有，未经授权禁止复制\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['PingFang HK']  # 版权所有: Jay<PERSON>ee黄\n", "plt.rcParams['axes.unicode_minus'] = False  # 本代码归JayBee黄所有", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 1. 数据获取与预处理\n", "\n", "我们获取TSLA过去5年的日线数据。", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["获取数据时间范围：2020-03-13 到 2025-03-12\n", "YF.download() has changed argument auto_adjust default to True\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[*********************100%***********************]  1 of 1 completed\n"]}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 设定时间范围（从现在往前推5年）\n", "end_date = datetime.now()  # JayBee黄量化模型\n", "start_date = end_date - <PERSON><PERSON><PERSON>(days=5*365)  # <PERSON><PERSON>ee黄量化策略\n", "\n", "print(f\"获取数据时间范围：{start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}\")  # 本代码归JayBee黄所有\n", "\n", "# 下载特斯拉数据\n", "ticker = 'TSLA'  # <PERSON><PERSON><PERSON>黄授权使用\n", "data = yf.download(ticker, start=start_date, end=end_date)  # 本代码归JayBee黄所有", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 1.2 数据预处理", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "data = flatten_yf_columns(data)  # JayBee黄 - 量化交易研究\n", "data = standardize_columns(data)  # JayBee黄原创内容", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "DatetimeIndex: 1255 entries, 2020-03-13 to 2025-03-11\n", "Data columns (total 5 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   close   1255 non-null   float64\n", " 1   high    1255 non-null   float64\n", " 2   low     1255 non-null   float64\n", " 3   open    1255 non-null   float64\n", " 4   volume  1255 non-null   int64  \n", "dtypes: float64(4), int64(1)\n", "memory usage: 58.8 KB\n", "None\n", "             close    high     low    open     volume\n", "Date                                                 \n", "2020-03-13 36.4413 40.5047 33.4667 39.6667  339604500\n", "2020-03-16 29.6713 32.9913 29.4780 31.3000  307342500\n", "2020-03-17 28.6800 31.4567 26.4000 29.3340  359919000\n", "2020-03-18 24.0813 26.9907 23.3673 25.9333  356793000\n", "2020-03-19 28.5093 30.1333 23.8973 24.9800  452932500\n", "2020-03-20 28.5020 31.8000 28.3860 29.2133  424282500\n", "2020-03-23 28.9527 29.4667 27.3667 28.9067  246817500\n", "2020-03-24 33.6667 34.2460 31.6000 31.8200  343428000\n", "2020-03-25 35.9500 37.1333 34.0740 36.3500  318340500\n", "2020-03-26 35.2107 37.3333 34.1500 36.4927  260710500\n", "              close     high      low     open     volume\n", "Date                                                     \n", "2025-02-26 290.8000 309.0000 288.0400 303.7100  100118300\n", "2025-02-27 281.9500 297.2300 280.8800 291.1600  101748200\n", "2025-02-28 292.9800 293.8800 273.6000 279.5000  115697000\n", "2025-03-03 284.6500 303.9400 277.3000 300.3400  115551400\n", "2025-03-04 272.0400 284.3500 261.8400 270.9300  126706600\n", "2025-03-05 279.1000 279.5500 267.7100 272.9200   94042900\n", "2025-03-06 263.4500 272.6500 260.0200 272.0600   98451600\n", "2025-03-07 262.6700 266.2500 250.7300 259.3200  102369600\n", "2025-03-10 222.1500 253.3700 220.0000 252.5400  189076900\n", "2025-03-11 230.5800 237.0600 217.0200 225.3100  174896400\n", "2020-03-13 00:00:00\n", "2025-03-11 00:00:00\n"]}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "print(data.info())              # 看看总共有多少行、列，各字段数据类型  # JayBee黄版权所有，未经授权禁止复制\n", "print(data.head(10))           # 查看前10行，确认最早日期  # 本代码归JayBee黄所有\n", "print(data.tail(10))           # 查看后10行，确认最晚日期  # 本代码归JayBee黄所有\n", "print(data.index.min())  # DataFrame中最早的日期  # 版权所有: JayBee黄\n", "print(data.index.max())  # DataFrame中最晚的日期  # JayBee黄授权使用\n", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 2. 加入更多技术指标\n", "\n", "构建两个简单的因子：\n", "1. 动量因子：过去5日涨跌幅\n", "2. 成交量比值：最近5日均量vs最近10日均量\n", "3. 先举几个常用指标的例子：RSI, MACD, 布林带。", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              close  momentum_5  vol_ratio  RSI_14  BB_upper  BB_lower\n", "Date                                                                  \n", "2025-03-05 279.1000     -0.0402     0.1253 29.9017  397.3141  261.0209\n", "2025-03-06 263.4500     -0.0656     0.0620 26.9042  393.4019  253.4611\n", "2025-03-07 262.6700     -0.1035     0.0087 26.7602  388.5007  247.1973\n", "2025-03-10 222.1500     -0.2196     0.0368 20.5942  389.9048  231.8462\n", "2025-03-11 230.5800     -0.1524     0.0812 24.4922  388.9695  220.7665\n"]}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 复制数据\n", "df = data.copy()  # 本代码归JayBee黄所有\n", "\n", "# 动量因子: 过去5日涨跌幅\n", "df['momentum_5'] = df['close'] / df['close'].shift(5) - 1  # Jay<PERSON>ee黄量化模型\n", "\n", "# 成交量因子: (最近5日平均成交量) / (最近10日平均成交量) - 1\n", "df['vol_ratio'] = (df['volume'].rolling(5).mean()) / (df['volume'].rolling(10).mean()) - 1  # 本代码归JayBee黄所有\n", "\n", "# 计算RSI (默认周期14)\n", "df['RSI_14'] = talib.RSI(df['close'], timeperiod=14)  # JayBee黄独家内容\n", "\n", "# 布林带\n", "upper, middle, lower = talib.BBANDS(  # 本代码归JayBee黄所有\n", "    df['close'],  # <PERSON><PERSON><PERSON>黄量化策略\n", "    timeperiod=20,  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "    nbdevup=2,  # <PERSON><PERSON><PERSON>黄原创内容\n", "    nbdevdn=2,  # <PERSON><PERSON><PERSON>黄独家内容\n", "    matype=0  # <PERSON><PERSON><PERSON>黄授权使用\n", ")  # Jay<PERSON>ee黄量化策略\n", "df['BB_upper'] = upper  # JayBee黄 - 量化交易研究\n", "df['BB_middle'] = middle  # <PERSON><PERSON><PERSON>黄原创内容\n", "df['BB_lower'] = lower  # JayBee黄原创内容\n", "\n", "# 也可以增加其他指标，比如ATR, CCI等，根据需要添加\n", "df.dropna(inplace=True)  # 丢掉因子无法计算的前几行  # JayBee黄 - 量化交易研究\n", "\n", "factors = ['momentum_5', 'vol_ratio' ,'RSI_14','BB_upper','BB_lower']  # Jay<PERSON>ee黄量化策略\n", "# 看看加上技术指标后的DataFrame\n", "print(df[['close'] + factors].tail(5))  # <PERSON><PERSON>ee黄授权使用\n", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 3. 目标变量的定义\n", "\n", "定义下期1日收益率作为目标变量。", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["添加目标变量后的数据预览：\n", "             close  momentum_5  vol_ratio  RSI_14  BB_upper  BB_lower\n", "Date                                                                 \n", "2020-04-09 38.2000      0.2608     0.0272 54.1876   39.7858   25.4478\n", "2020-04-13 43.3967      0.3561    -0.0236 61.5239   41.4042   24.5249\n", "2020-04-14 47.3260      0.3751     0.0475 65.9624   44.2006   23.4940\n", "2020-04-15 48.6553      0.3380     0.0749 67.3352   46.7505   22.9416\n", "2020-04-16 49.6807      0.3578     0.1159 68.3941   48.6161   23.6360\n", "2020-04-17 50.2593      0.3157     0.1495 69.0048   50.6157   23.8114\n", "2020-04-20 49.7573      0.1466     0.1144 67.7812   52.1107   24.4419\n", "2020-04-21 45.7813     -0.0326    -0.0262 58.8769   52.6247   25.6108\n", "2020-04-22 48.8073      0.0031    -0.1078 62.8743   53.7665   25.9830\n", "2020-04-23 47.0420     -0.0531    -0.1902 59.2555   54.5341   26.3247\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["目标变量的均值=0.0022512106832757645\n", "目标变量的方差=0.039952436916353626\n"]}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 计算下期收益率\n", "df['future_ret_1d'] = df['close'].pct_change().shift(-1)  # JayBee黄授权使用\n", "\n", "# 去掉NaN值\n", "df.dropna(inplace=True)  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "\n", "print(\"添加目标变量后的数据预览：\")  # JayBee黄授权使用\n", "print(df[['close']+factors].head(10))  # Jay<PERSON>ee黄 - 量化交易研究\n", "\n", "# 绘制目标变量分布\n", "plt.figure(figsize=(10, 5))  # 本代码归JayBee黄所有\n", "sns.histplot(df['future_ret_1d'], bins=50)  # JayBee黄 - 量化交易研究\n", "plt.title('下期收益率分布')  # Jay<PERSON>ee黄 - 量化交易研究\n", "plt.xlabel('收益率')  # <PERSON><PERSON>ee黄独家内容\n", "plt.show()  # <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制\n", "\n", "# 计算因子与目标变量的相关性\n", "corr = df[['close']+factors].corr()  # JayBee黄量化模型\n", "\n", "plt.figure(figsize=(8, 6))  # 本代码归JayBee黄所有\n", "sns.heatmap(corr, annot=True, cmap='coolwarm', center=0)  # Copyright © JayBee黄\n", "plt.title('因子与目标变量相关性')  # JayBee黄版权所有，未经授权禁止复制\n", "plt.show()  # <PERSON><PERSON><PERSON>黄原创内容\n", "\n", "print(f\"目标变量的均值={np.mean(df['future_ret_1d'])}\")  # JayBee黄量化策略\n", "print(f\"目标变量的方差={np.std(df['future_ret_1d'])}\")  # JayBee黄量化模型", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 4. 划分训练集与测试集\n", "\n", "按照时间顺序，使用前80%的数据作为训练集，后20%作为测试集。", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集范围: 2020-04-09 00:00:00 → 2023-03-20 00:00:00\n", "验证集范围: 2023-03-21 00:00:00 → 2024-03-13 00:00:00\n", "测试集范围: 2024-03-14 00:00:00 → 2025-03-10 00:00:00\n", "\n", "训练集样本数: 741\n", "验证集样本数: 247\n", "测试集样本数: 247\n"]}, {"data": {"image/png": "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******************************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****************************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***************************************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", "text/plain": ["<Figure size 1500x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 计算分割点\n", "train_idx = int(len(df) * 0.6)  # JayBee黄量化模型\n", "valid_idx = int(len(df) * 0.8)  # JayBee黄独家内容\n", "\n", "split_date_1 = df.index[train_idx]  # JayBee黄原创内容\n", "split_date_2 = df.index[valid_idx]  # Copyright © JayBee黄\n", "\n", "train_data = df.iloc[:train_idx].copy()  # 本代码归JayBee黄所有\n", "valid_data = df.iloc[train_idx:valid_idx].copy()  # JayBee黄 - 量化交易研究\n", "test_data = df.iloc[valid_idx:].copy()  # 本代码归JayBee黄所有\n", "\n", "print(\"训练集范围:\", train_data.index.min(), \"→\", train_data.index.max())  # JayBee黄独家内容\n", "print(\"验证集范围:\", valid_data.index.min(), \"→\", valid_data.index.max())  # JayBee黄量化模型\n", "print(\"测试集范围:\", test_data.index.min(), \"→\", test_data.index.max())  # 本代码归JayBee黄所有\n", "print(\"\\n训练集样本数:\", len(train_data))  # Jay<PERSON>ee黄授权使用\n", "print(\"验证集样本数:\", len(valid_data))  # Jay<PERSON>ee黄量化策略\n", "print(\"测试集样本数:\", len(test_data))  # JayBee黄 - 量化交易研究\n", "\n", "# 可视化训练集和测试集的划分\n", "plt.figure(figsize=(15, 6))  # <PERSON><PERSON><PERSON>黄原创内容\n", "plt.plot(train_data.index, train_data['future_ret_1d'], label='训练集', color='blue')  # JayBee黄授权使用\n", "plt.plot(valid_data.index, valid_data['future_ret_1d'], label='验证集', color='green')  # JayBee黄授权使用\n", "plt.plot(test_data.index, test_data['future_ret_1d'], label='测试集', color='red')  # JayBee黄授权使用\n", "plt.axvline(split_date_1, color='black', linestyle='--', label='划分点')  # Copyright © JayBee黄\n", "plt.axvline(split_date_2, color='black', linestyle='--', label='划分点')  # JayBee黄 - 量化交易研究\n", "plt.title('训练集、验证集、测试集划分')  # Copyright © JayBee黄\n", "plt.xlabel('日期')  # Copyright © JayBee黄\n", "plt.ylabel('收益率')  # <PERSON><PERSON>ee黄量化策略\n", "plt.legend()  # <PERSON><PERSON><PERSON>黄授权使用\n", "plt.grid(True)  # Copyright © JayBee黄\n", "plt.show()  # <PERSON><PERSON><PERSON>黄量化策略", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 5. Buy&Hold 策略", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["初始资金: 100000.00\n", "2024-03-14 00:00:00 [买入] 执行买入并持有策略: 价格=162.50, 数量=584\n", "2024-03-15 00:00:00 [成交] 买单执行: 价格=163.21, 数量=584\n", "2025-03-10 00:00:00 [回测结束] Buy & Hold 策略最终市值: 134325.64\n", "2025-03-10 00:00:00 [回测结束] 总收益率: 34.33%\n", "回测结束资金: 134325.64\n", "=== 回测分析报告 ===\n", "夏普比率: 0.7685\n", "最大回撤比例: 52.84%\n", "最大回撤金额(自定义): 150502.64\n", "累计收益率: 29.51%\n", "年化收益率: 35.13%\n", "=== 交易详情 ===\n", "总交易笔数: 1\n", "胜率: 0 / 1\n"]}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 若想看最优参数的详细回测日志，可再手动调用:\n", "bh_result, bh_cerebro = run_backtest(  # JayBee黄版权所有，未经授权禁止复制\n", "    ticker=ticker,  # <PERSON><PERSON><PERSON>黄量化策略\n", "    df=test_data,  # JayBee黄版权所有，未经授权禁止复制\n", "    start_date=start_date,  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    end_date=end_date,  # <PERSON><PERSON>ee黄版权所有，未经授权禁止复制\n", "    strategy=BuyAndHoldStrategy,  # JayBee黄版权所有，未经授权禁止复制\n", "    initial_cash=100000,  # <PERSON><PERSON><PERSON>黄独家内容\n", "    print_log=True,  # 这次打开日志  # Copyright © JayBee黄\n", "    timeframe=bt.TimeFrame.Days,  # 本代码归JayBee黄所有\n", "    compression=1  # 版权所有: <PERSON><PERSON><PERSON>黄\n", ")  # Copyright © Jay<PERSON><PERSON>黄", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/lb/hgfyjd8d6jdgjkfcksmwvbd80000gq/T/ipykernel_21644/1425693070.py:5: FutureWarning: In the future `np.object` will be defined as the corresponding NumPy scalar.\n", "  if not hasattr(np, 'object'):\n"]}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "\n", "# ===== 猴子补丁：为 numpy 添加 bool8 和 object 属性 =====\n", "import numpy as np  # <PERSON><PERSON>ee黄版权所有，未经授权禁止复制\n", "if not hasattr(np, 'bool8'):  # 本代码归Jay<PERSON>ee黄所有\n", "    np.bool8 = np.bool_  # 使用 numpy 自带的 bool_ 类型  # JayBee黄量化策略\n", "if not hasattr(np, 'object'):  # <PERSON><PERSON>ee黄授权使用\n", "    np.object = object  # 兼容 backtrader_plotting 的引用  # JayBee黄 - 量化交易研究", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["\n", "    <div class=\"bk-root\">\n", "        <a href=\"https://bokeh.org\" target=\"_blank\" class=\"bk-logo bk-logo-small bk-logo-notebook\"></a>\n", "        <span id=\"1002\">Loading BokehJS ...</span>\n", "    </div>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/javascript": ["\n", "(function(root) {\n", "  function now() {\n", "    return new Date();\n", "  }\n", "\n", "  var force = true;\n", "\n", "  if (typeof root._bokeh_onload_callbacks === \"undefined\" || force === true) {\n", "    root._bokeh_onload_callbacks = [];\n", "    root._bokeh_is_loading = undefined;\n", "  }\n", "\n", "  var JS_MIME_TYPE = 'application/javascript';\n", "  var HTML_MIME_TYPE = 'text/html';\n", "  var EXEC_MIME_TYPE = 'application/vnd.bokehjs_exec.v0+json';\n", "  var CLASS_NAME = 'output_bokeh rendered_html';\n", "\n", "  /**\n", "   * Render data to the DOM node\n", "   */\n", "  function render(props, node) {\n", "    var script = document.createElement(\"script\");\n", "    node.appendChild(script);\n", "  }\n", "\n", "  /**\n", "   * Handle when an output is cleared or removed\n", "   */\n", "  function handleClearOutput(event, handle) {\n", "    var cell = handle.cell;\n", "\n", "    var id = cell.output_area._bokeh_element_id;\n", "    var server_id = cell.output_area._bokeh_server_id;\n", "    // Clean up Bokeh references\n", "    if (id != null && id in Bokeh.index) {\n", "      Bokeh.index[id].model.document.clear();\n", "      delete Bokeh.index[id];\n", "    }\n", "\n", "    if (server_id !== undefined) {\n", "      // Clean up Bokeh references\n", "      var cmd = \"from bokeh.io.state import curstate; print(curstate().uuid_to_server['\" + server_id + \"'].get_sessions()[0].document.roots[0]._id)\";\n", "      cell.notebook.kernel.execute(cmd, {\n", "        iopub: {\n", "          output: function(msg) {\n", "            var id = msg.content.text.trim();\n", "            if (id in Bokeh.index) {\n", "              Bokeh.index[id].model.document.clear();\n", "              delete Bokeh.index[id];\n", "            }\n", "          }\n", "        }\n", "      });\n", "      // Destroy server and session\n", "      var cmd = \"import bokeh.io.notebook as ion; ion.destroy_server('\" + server_id + \"')\";\n", "      cell.notebook.kernel.execute(cmd);\n", "    }\n", "  }\n", "\n", "  /**\n", "   * Handle when a new output is added\n", "   */\n", "  function handleAddOutput(event, handle) {\n", "    var output_area = handle.output_area;\n", "    var output = handle.output;\n", "\n", "    // limit handleAddOutput to display_data with EXEC_MIME_TYPE content only\n", "    if ((output.output_type != \"display_data\") || (!Object.prototype.hasOwnProperty.call(output.data, EXEC_MIME_TYPE))) {\n", "      return\n", "    }\n", "\n", "    var toinsert = output_area.element.find(\".\" + CLASS_NAME.split(' ')[0]);\n", "\n", "    if (output.metadata[EXEC_MIME_TYPE][\"id\"] !== undefined) {\n", "      toinsert[toinsert.length - 1].firstChild.textContent = output.data[JS_MIME_TYPE];\n", "      // store reference to embed id on output_area\n", "      output_area._bokeh_element_id = output.metadata[EXEC_MIME_TYPE][\"id\"];\n", "    }\n", "    if (output.metadata[EXEC_MIME_TYPE][\"server_id\"] !== undefined) {\n", "      var bk_div = document.createElement(\"div\");\n", "      bk_div.innerHTML = output.data[HTML_MIME_TYPE];\n", "      var script_attrs = bk_div.children[0].attributes;\n", "      for (var i = 0; i < script_attrs.length; i++) {\n", "        toinsert[toinsert.length - 1].firstChild.setAttribute(script_attrs[i].name, script_attrs[i].value);\n", "        toinsert[toinsert.length - 1].firstChild.textContent = bk_div.children[0].textContent\n", "      }\n", "      // store reference to server id on output_area\n", "      output_area._bokeh_server_id = output.metadata[EXEC_MIME_TYPE][\"server_id\"];\n", "    }\n", "  }\n", "\n", "  function register_renderer(events, OutputArea) {\n", "\n", "    function append_mime(data, metadata, element) {\n", "      // create a DOM node to render to\n", "      var toinsert = this.create_output_subarea(\n", "        metadata,\n", "        CLASS_NAME,\n", "        EXEC_MIME_TYPE\n", "      );\n", "      this.keyboard_manager.register_events(toinsert);\n", "      // Render to node\n", "      var props = {data: data, metadata: metadata[EXEC_MIME_TYPE]};\n", "      render(props, toinsert[toinsert.length - 1]);\n", "      element.append(toinsert);\n", "      return toinsert\n", "    }\n", "\n", "    /* Handle when an output is cleared or removed */\n", "    events.on('clear_output.CodeCell', handleClearOutput);\n", "    events.on('delete.Cell', handleClearOutput);\n", "\n", "    /* Handle when a new output is added */\n", "    events.on('output_added.OutputArea', handleAddOutput);\n", "\n", "    /**\n", "     * Register the mime type and append_mime function with output_area\n", "     */\n", "    OutputArea.prototype.register_mime_type(EXEC_MIME_TYPE, append_mime, {\n", "      /* Is output safe? */\n", "      safe: true,\n", "      /* Index of renderer in `output_area.display_order` */\n", "      index: 0\n", "    });\n", "  }\n", "\n", "  // register the mime type if in Jupyter Notebook environment and previously unregistered\n", "  if (root.<PERSON>pyter !== undefined) {\n", "    var events = require('base/js/events');\n", "    var OutputArea = require('notebook/js/outputarea').OutputArea;\n", "\n", "    if (OutputArea.prototype.mime_types().indexOf(EXEC_MIME_TYPE) == -1) {\n", "      register_renderer(events, OutputArea);\n", "    }\n", "  }\n", "\n", "  \n", "  if (typeof (root._bokeh_timeout) === \"undefined\" || force === true) {\n", "    root._bokeh_timeout = Date.now() + 5000;\n", "    root._bokeh_failed_load = false;\n", "  }\n", "\n", "  var NB_LOAD_WARNING = {'data': {'text/html':\n", "     \"<div style='background-color: #fdd'>\\n\"+\n", "     \"<p>\\n\"+\n", "     \"BokehJS does not appear to have successfully loaded. If loading BokehJS from CDN, this \\n\"+\n", "     \"may be due to a slow or bad network connection. Possible fixes:\\n\"+\n", "     \"</p>\\n\"+\n", "     \"<ul>\\n\"+\n", "     \"<li>re-rerun `output_notebook()` to attempt to load from CDN again, or</li>\\n\"+\n", "     \"<li>use INLINE resources instead, as so:</li>\\n\"+\n", "     \"</ul>\\n\"+\n", "     \"<code>\\n\"+\n", "     \"from bokeh.resources import INLINE\\n\"+\n", "     \"output_notebook(resources=INLINE)\\n\"+\n", "     \"</code>\\n\"+\n", "     \"</div>\"}};\n", "\n", "  function display_loaded() {\n", "    var el = document.getElementById(\"1002\");\n", "    if (el != null) {\n", "      el.textContent = \"BokehJS is loading...\";\n", "    }\n", "    if (root.Bokeh !== undefined) {\n", "      if (el != null) {\n", "        el.textContent = \"BokehJS \" + root.Bokeh.version + \" successfully loaded.\";\n", "      }\n", "    } else if (Date.now() < root._bokeh_timeout) {\n", "      setTimeout(display_loaded, 100)\n", "    }\n", "  }\n", "\n", "\n", "  function run_callbacks() {\n", "    try {\n", "      root._bokeh_onload_callbacks.forEach(function(callback) {\n", "        if (callback != null)\n", "          callback();\n", "      });\n", "    } finally {\n", "      delete root._bokeh_onload_callbacks\n", "    }\n", "    console.debug(\"<PERSON>keh: all callbacks have finished\");\n", "  }\n", "\n", "  function load_libs(css_urls, js_urls, callback) {\n", "    if (css_urls == null) css_urls = [];\n", "    if (js_urls == null) js_urls = [];\n", "\n", "    root._bokeh_onload_callbacks.push(callback);\n", "    if (root._bokeh_is_loading > 0) {\n", "      console.debug(\"Bokeh: BokehJS is being loaded, scheduling callback at\", now());\n", "      return null;\n", "    }\n", "    if (js_urls == null || js_urls.length === 0) {\n", "      run_callbacks();\n", "      return null;\n", "    }\n", "    console.debug(\"Bokeh: BokehJS not loaded, scheduling load and callback at\", now());\n", "    root._bokeh_is_loading = css_urls.length + js_urls.length;\n", "\n", "    function on_load() {\n", "      root._bokeh_is_loading--;\n", "      if (root._bokeh_is_loading === 0) {\n", "        console.debug(\"Bokeh: all BokehJS libraries/stylesheets loaded\");\n", "        run_callbacks()\n", "      }\n", "    }\n", "\n", "    function on_error(url) {\n", "      console.error(\"failed to load \" + url);\n", "    }\n", "\n", "    for (let i = 0; i < css_urls.length; i++) {\n", "      const url = css_urls[i];\n", "      const element = document.createElement(\"link\");\n", "      element.onload = on_load;\n", "      element.onerror = on_error.bind(null, url);\n", "      element.rel = \"stylesheet\";\n", "      element.type = \"text/css\";\n", "      element.href = url;\n", "      console.debug(\"Bokeh: injecting link tag for BokehJS stylesheet: \", url);\n", "      document.body.appendChild(element);\n", "    }\n", "\n", "    const hashes = {\"https://cdn.bokeh.org/bokeh/release/bokeh-2.3.3.min.js\": \"dM3QQsP+wXdHg42wTqW85BjZQdLNNIXqlPw/BgKoExPmTG7ZLML4EGqLMfqHT6ON\", \"https://cdn.bokeh.org/bokeh/release/bokeh-tables-2.3.3.min.js\": \"8x57I4YuIfu8XyZfFo0XVr2WAT8EK4rh/uDe3wF7YuW2FNUSNEpJbsPaB1nJ2fz2\", \"https://cdn.bokeh.org/bokeh/release/bokeh-widgets-2.3.3.min.js\": \"3QTqdz9LyAm2i0sG5XTePsHec3UHWwVsrOL68SYRoAXsafvfAyqtQ+h440+qIBhS\"};\n", "\n", "    for (let i = 0; i < js_urls.length; i++) {\n", "      const url = js_urls[i];\n", "      const element = document.createElement('script');\n", "      element.onload = on_load;\n", "      element.onerror = on_error.bind(null, url);\n", "      element.async = false;\n", "      element.src = url;\n", "      if (url in hashes) {\n", "        element.crossOrigin = \"anonymous\";\n", "        element.integrity = \"sha384-\" + hashes[url];\n", "      }\n", "      console.debug(\"Bokeh: injecting script tag for BokehJS library: \", url);\n", "      document.head.appendChild(element);\n", "    }\n", "  };\n", "\n", "  function inject_raw_css(css) {\n", "    const element = document.createElement(\"style\");\n", "    element.appendChild(document.createTextNode(css));\n", "    document.body.appendChild(element);\n", "  }\n", "\n", "  \n", "  var js_urls = [\"https://cdn.bokeh.org/bokeh/release/bokeh-2.3.3.min.js\", \"https://cdn.bokeh.org/bokeh/release/bokeh-widgets-2.3.3.min.js\", \"https://cdn.bokeh.org/bokeh/release/bokeh-tables-2.3.3.min.js\"];\n", "  var css_urls = [];\n", "  \n", "\n", "  var inline_js = [\n", "    function(Bokeh) {\n", "      Bokeh.set_log_level(\"info\");\n", "    },\n", "    function(Bokeh) {\n", "    \n", "    \n", "    }\n", "  ];\n", "\n", "  function run_inline_js() {\n", "    \n", "    if (root.Bokeh !== undefined || force === true) {\n", "      \n", "    for (var i = 0; i < inline_js.length; i++) {\n", "      inline_js[i].call(root, root.Bokeh);\n", "    }\n", "    if (force === true) {\n", "        display_loaded();\n", "      }} else if (Date.now() < root._bokeh_timeout) {\n", "      setTimeout(run_inline_js, 100);\n", "    } else if (!root._bokeh_failed_load) {\n", "      console.log(\"Bokeh: BokehJS failed to load within specified timeout.\");\n", "      root._bokeh_failed_load = true;\n", "    } else if (force !== true) {\n", "      var cell = $(document.getElementById(\"1002\")).parents('.cell').data().cell;\n", "      cell.output_area.append_execute_result(NB_LOAD_WARNING)\n", "    }\n", "\n", "  }\n", "\n", "  if (root._bokeh_is_loading === 0) {\n", "    console.debug(\"Bokeh: BokehJS loaded, going straight to plotting\");\n", "    run_inline_js();\n", "  } else {\n", "    load_libs(css_urls, js_urls, function() {\n", "      console.debug(\"Bokeh: BokehJS plotting callback run at\", now());\n", "      run_inline_js();\n", "    });\n", "  }\n", "}(window));"], "application/vnd.bokehjs_load.v0+json": "\n(function(root) {\n  function now() {\n    return new Date();\n  }\n\n  var force = true;\n\n  if (typeof root._bokeh_onload_callbacks === \"undefined\" || force === true) {\n    root._bokeh_onload_callbacks = [];\n    root._bokeh_is_loading = undefined;\n  }\n\n  \n\n  \n  if (typeof (root._bokeh_timeout) === \"undefined\" || force === true) {\n    root._bokeh_timeout = Date.now() + 5000;\n    root._bokeh_failed_load = false;\n  }\n\n  var NB_LOAD_WARNING = {'data': {'text/html':\n     \"<div style='background-color: #fdd'>\\n\"+\n     \"<p>\\n\"+\n     \"BokehJS does not appear to have successfully loaded. If loading BokehJS from CDN, this \\n\"+\n     \"may be due to a slow or bad network connection. Possible fixes:\\n\"+\n     \"</p>\\n\"+\n     \"<ul>\\n\"+\n     \"<li>re-rerun `output_notebook()` to attempt to load from CDN again, or</li>\\n\"+\n     \"<li>use INLINE resources instead, as so:</li>\\n\"+\n     \"</ul>\\n\"+\n     \"<code>\\n\"+\n     \"from bokeh.resources import INLINE\\n\"+\n     \"output_notebook(resources=INLINE)\\n\"+\n     \"</code>\\n\"+\n     \"</div>\"}};\n\n  function display_loaded() {\n    var el = document.getElementById(\"1002\");\n    if (el != null) {\n      el.textContent = \"BokehJS is loading...\";\n    }\n    if (root.Bokeh !== undefined) {\n      if (el != null) {\n        el.textContent = \"BokehJS \" + root.Bokeh.version + \" successfully loaded.\";\n      }\n    } else if (Date.now() < root._bokeh_timeout) {\n      setTimeout(display_loaded, 100)\n    }\n  }\n\n\n  function run_callbacks() {\n    try {\n      root._bokeh_onload_callbacks.forEach(function(callback) {\n        if (callback != null)\n          callback();\n      });\n    } finally {\n      delete root._bokeh_onload_callbacks\n    }\n    console.debug(\"Bokeh: all callbacks have finished\");\n  }\n\n  function load_libs(css_urls, js_urls, callback) {\n    if (css_urls == null) css_urls = [];\n    if (js_urls == null) js_urls = [];\n\n    root._bokeh_onload_callbacks.push(callback);\n    if (root._bokeh_is_loading > 0) {\n      console.debug(\"Bokeh: BokehJS is being loaded, scheduling callback at\", now());\n      return null;\n    }\n    if (js_urls == null || js_urls.length === 0) {\n      run_callbacks();\n      return null;\n    }\n    console.debug(\"Bokeh: BokehJS not loaded, scheduling load and callback at\", now());\n    root._bokeh_is_loading = css_urls.length + js_urls.length;\n\n    function on_load() {\n      root._bokeh_is_loading--;\n      if (root._bokeh_is_loading === 0) {\n        console.debug(\"Bokeh: all BokehJS libraries/stylesheets loaded\");\n        run_callbacks()\n      }\n    }\n\n    function on_error(url) {\n      console.error(\"failed to load \" + url);\n    }\n\n    for (let i = 0; i < css_urls.length; i++) {\n      const url = css_urls[i];\n      const element = document.createElement(\"link\");\n      element.onload = on_load;\n      element.onerror = on_error.bind(null, url);\n      element.rel = \"stylesheet\";\n      element.type = \"text/css\";\n      element.href = url;\n      console.debug(\"Bokeh: injecting link tag for BokehJS stylesheet: \", url);\n      document.body.appendChild(element);\n    }\n\n    const hashes = {\"https://cdn.bokeh.org/bokeh/release/bokeh-2.3.3.min.js\": \"dM3QQsP+wXdHg42wTqW85BjZQdLNNIXqlPw/BgKoExPmTG7ZLML4EGqLMfqHT6ON\", \"https://cdn.bokeh.org/bokeh/release/bokeh-tables-2.3.3.min.js\": \"8x57I4YuIfu8XyZfFo0XVr2WAT8EK4rh/uDe3wF7YuW2FNUSNEpJbsPaB1nJ2fz2\", \"https://cdn.bokeh.org/bokeh/release/bokeh-widgets-2.3.3.min.js\": \"3QTqdz9LyAm2i0sG5XTePsHec3UHWwVsrOL68SYRoAXsafvfAyqtQ+h440+qIBhS\"};\n\n    for (let i = 0; i < js_urls.length; i++) {\n      const url = js_urls[i];\n      const element = document.createElement('script');\n      element.onload = on_load;\n      element.onerror = on_error.bind(null, url);\n      element.async = false;\n      element.src = url;\n      if (url in hashes) {\n        element.crossOrigin = \"anonymous\";\n        element.integrity = \"sha384-\" + hashes[url];\n      }\n      console.debug(\"Bokeh: injecting script tag for BokehJS library: \", url);\n      document.head.appendChild(element);\n    }\n  };\n\n  function inject_raw_css(css) {\n    const element = document.createElement(\"style\");\n    element.appendChild(document.createTextNode(css));\n    document.body.appendChild(element);\n  }\n\n  \n  var js_urls = [\"https://cdn.bokeh.org/bokeh/release/bokeh-2.3.3.min.js\", \"https://cdn.bokeh.org/bokeh/release/bokeh-widgets-2.3.3.min.js\", \"https://cdn.bokeh.org/bokeh/release/bokeh-tables-2.3.3.min.js\"];\n  var css_urls = [];\n  \n\n  var inline_js = [\n    function(Bokeh) {\n      Bokeh.set_log_level(\"info\");\n    },\n    function(Bokeh) {\n    \n    \n    }\n  ];\n\n  function run_inline_js() {\n    \n    if (root.Bokeh !== undefined || force === true) {\n      \n    for (var i = 0; i < inline_js.length; i++) {\n      inline_js[i].call(root, root.Bokeh);\n    }\n    if (force === true) {\n        display_loaded();\n      }} else if (Date.now() < root._bokeh_timeout) {\n      setTimeout(run_inline_js, 100);\n    } else if (!root._bokeh_failed_load) {\n      console.log(\"Bokeh: BokehJS failed to load within specified timeout.\");\n      root._bokeh_failed_load = true;\n    } else if (force !== true) {\n      var cell = $(document.getElementById(\"1002\")).parents('.cell').data().cell;\n      cell.output_area.append_execute_result(NB_LOAD_WARNING)\n    }\n\n  }\n\n  if (root._bokeh_is_loading === 0) {\n    console.debug(\"Bokeh: BokehJS loaded, going straight to plotting\");\n    run_inline_js();\n  } else {\n    load_libs(css_urls, js_urls, function() {\n      console.debug(\"Bokeh: BokehJS plotting callback run at\", now());\n      run_inline_js();\n    });\n  }\n}(window));"}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style>\n", "    body {\n", "        background-color: white;\n", "        color: #222222;\n", "    }\n", "\n", "    table.metaDataTable {\n", "      border: 1px solid #1C6EA4;\n", "      background-color: #eeeeee;\n", "      width: 100%;\n", "      text-align: left;\n", "      border-collapse: collapse;\n", "    }\n", "    table.metaDataTable td, table.metaDataTable th {\n", "      border: 1px solid #eeeeee;\n", "      padding: 3px 2px;\n", "    }\n", "    table.metaDataTable tbody td {\n", "      font-size: 13px;\n", "    }\n", "    table.metaDataTable tr:nth-child(even) {\n", "      background: #fefefefe;\n", "    }\n", "    table.metaDataTable tfoot td {\n", "      font-size: 14px;\n", "    }\n", "    table.metaDataTable tfoot .links {\n", "      text-align: right;\n", "    }\n", "    table.metaDataTable tfoot .links a{\n", "      display: inline-block;\n", "      background: #1C6EA4;\n", "      color: #FFFFFF;\n", "      padding: 2px 8px;\n", "      border-radius: 5px;\n", "    }\n", "\n", "    pre {\n", "        background-color: #222222;\n", "        color: lightgrey;\n", "    }\n", "\n", "    #headline {\n", "        font-size: 200%;\n", "        font-family: \"Segoe UI\", \"Arial Black\", Gadget, sans-serif;\n", "        color: #333333;\n", "    }\n", "\n", "    .bk-root {\n", "        font-family: \"Segoe UI\", \"Arial Black\", Gadget, sans-serif;\n", "    }\n", "    .bk-toolbar {position: fixed;}\n", "\n", "    .slick-header-column {\n", "        background-color: #cccccc !important;\n", "        background-image: none !important;\n", "        font-size: 130%;\n", "    }\n", "\n", "    .slick-row.even {\n", "        background-color: #fefefefe !important;\n", "    }\n", "\n", "    .slick-row.odd {\n", "        background-color: #eeeeee !important;\n", "    }\n", "\n", "    .bk-root .bk-bs-nav-tabs>li.bk-bs-active>span {\n", "        background-color: #dddddd !important;\n", "        color: #111111 !important;\n", "        border-color: #dddddd !important;\n", "    }\n", "\n", "    .bk-root .bk-bs-nav>li>span:hover {\n", "        background-color: #dddddd !important;\n", "        border-color: #dddddd !important;\n", "        color: #111111 !important;\n", "    }\n", "\n", "    .bk-tooltip {\n", "        border-radius: 3px;\n", "        background-color: #f5f5f5 !important;\n", "        border-color: #f5f5f5 !important;\n", "    }\n", "\n", "    .bk-tooltip-row-label {\n", "        color: #848EFF !important;\n", "    }\n", "\n", "    .bk-tooltip-row-value {\n", "        color: #aaaaaa !important;\n", "    }\n", "</style>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "\n", "\n", "\n", "\n", "\n", "  <div class=\"bk-root\" id=\"26a3a721-c2f5-4a53-b544-259bd71bac30\" data-root-id=\"1418\"></div>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/javascript": ["(function(root) {\n", "  function embed_document(root) {\n", "    \n", "  var docs_json = {\"858237be-5877-45f3-bf1f-8e5c2dc1ff72\":{\"defs\":[],\"roots\":{\"references\":[{\"attributes\":{\"tabs\":[{\"id\":\"1318\"},{\"id\":\"1415\"},{\"id\":\"1417\"}]},\"id\":\"1418\",\"type\":\"Tabs\"},{\"attributes\":{},\"id\":\"1181\",\"type\":\"AllLabels\"},{\"attributes\":{},\"id\":\"1054\",\"type\":\"UnionRenderers\"},{\"attributes\":{\"fill_alpha\":{\"value\":0.5},\"fill_color\":{\"field\":\"6102424592colors_volume\"},\"line_alpha\":{\"value\":0.5},\"top\":{\"field\":\"6102424592volume\"},\"width\":{\"value\":0.5},\"x\":{\"field\":\"index\"}},\"id\":\"1079\",\"type\":\"VBar\"},{\"attributes\":{\"axis_label_text_color\":\"#aaaaaa\",\"axis_line_color\":\"#aaaaaa\",\"formatter\":{\"id\":\"1074\"},\"major_label_policy\":{\"id\":\"1089\"},\"major_label_text_color\":\"#aaaaaa\",\"major_tick_line_color\":\"#aaaaaa\",\"minor_tick_line_color\":\"#aaaaaa\",\"ticker\":{\"id\":\"1088\"},\"y_range_name\":\"axvol\"},\"id\":\"1076\",\"type\":\"LinearAxis\"},{\"attributes\":{\"child\":{\"id\":\"1414\"},\"title\":\"Analyzers\"},\"id\":\"1415\",\"type\":\"Panel\"},{\"attributes\":{},\"id\":\"1055\",\"type\":\"Selection\"},{\"attributes\":{},\"id\":\"1404\",\"type\":\"StringEditor\"},{\"attributes\":{\"callback\":null,\"formatters\":{\"@datetime\":\"datetime\"},\"mode\":\"vline\",\"renderers\":[{\"id\":\"1062\"}],\"tooltips\":[[\"Time\",\"@datetime{%F %R}\"],[\"Open\",\"@6102424592open{0,0.000}\"],[\"High\",\"@6102424592high{0,0.000}\"],[\"Low\",\"@6102424592low{0,0.000}\"],[\"Close\",\"@6102424592close{0,0.000}\"],[\"Volume\",\"@6102424592volume{(0,0.000)}\"],[\"BuySell (True, 0.015) - buy\",\"@6102400256{0,0.000}\"],[\"BuySell (True, 0.015) - sell\",\"@6102399824{0,0.000}\"]]},\"id\":\"1038\",\"type\":\"HoverTool\"},{\"attributes\":{},\"id\":\"1405\",\"type\":\"UnionRenderers\"},{\"attributes\":{},\"id\":\"1184\",\"type\":\"AllLabels\"},{\"attributes\":{\"background_fill_color\":\"#f5f5f5\",\"click_policy\":\"hide\",\"items\":[{\"id\":\"1058\"},{\"id\":\"1095\"},{\"id\":\"1116\"},{\"id\":\"1134\"}],\"label_text_color\":\"#333333\",\"location\":\"top_left\",\"orientation\":\"horizontal\"},\"id\":\"1057\",\"type\":\"Legend\"},{\"attributes\":{},\"id\":\"1406\",\"type\":\"Selection\"},{\"attributes\":{\"bottom_units\":\"screen\",\"fill_alpha\":0.5,\"fill_color\":\"lightgrey\",\"left_units\":\"screen\",\"level\":\"overlay\",\"line_alpha\":1.0,\"line_color\":\"black\",\"line_dash\":[4,4],\"line_width\":2,\"right_units\":\"screen\",\"syncable\":false,\"top_units\":\"screen\"},\"id\":\"1025\",\"type\":\"BoxAnnotation\"},{\"attributes\":{\"text\":\"<h1>Strategy: BuyAndHoldStrategy</h1>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>target_percent</td>\\n  <td>0.95</td>\\n</tr>\\n<tr>\\n  <td>use<em>risk</em>sizing</td>\\n  <td>False</td>\\n</tr>\\n<tr>\\n  <td>risk<em>per</em>trade</td>\\n  <td>0.01</td>\\n</tr>\\n<tr>\\n  <td>atr_period</td>\\n  <td>14</td>\\n</tr>\\n<tr>\\n  <td>atr<em>risk</em>factor</td>\\n  <td>2.00</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h2>Indicators:</h2>\\n\\n<hr />\\n\\n<h1>Data Feeds</h1>\\n\\n<h2>PandasData</h2>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Property</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>DataName:</td>\\n  <td>close     high      low     open     volume  momentum_5  \\\\</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<p>Date<br />\\n2024-03-14 162.5000 171.1700 160.5100 167.7700  126325700     -0.0904<br />\\n2024-03-15 163.5700 165.1800 160.7600 163.1600   96971900     -0.0671<br />\\n2024-03-18 173.8000 174.7200 165.9000 170.0200  108214400     -0.0223<br />\\n2024-03-19 171.3200 172.8200 167.4200 172.3600   77271400     -0.0350<br />\\n2024-03-20 175.6600 176.2500 170.8200 173.0000   83846700      0.0365<br />\\n...             ...      ...      ...      ...        ...         ...<br />\\n2025-03-04 272.0400 284.3500 261.8400 270.9300  126706600     -0.1016<br />\\n2025-03-05 279.1000 279.5500 267.7100 272.9200   94042900     -0.0402<br />\\n2025-03-06 263.4500 272.6500 260.0200 272.0600   98451600     -0.0656<br />\\n2025-03-07 262.6700 266.2500 250.7300 259.3200  102369600     -0.1035<br />\\n2025-03-10 222.1500 253.3700 220.0000 252.5400  189076900     -0.2196   </p>\\n\\n<pre><code>        vol_ratio  RSI_14  BB_upper  BB_middle  BB_lower  future_ret_1d\\n</code></pre>\\n\\n<p>Date<br />\\n2024-03-14    -0.0532 27.6926  212.9732   188.5350  164.0968         0.0066<br />\\n2024-03-15    -0.0444 29.0287  212.7655   186.6910  160.6165         0.0625<br />\\n2024-03-18     0.0244 40.3732  211.2894   185.3835  159.4776        -0.0143<br />\\n2024-03-19     0.0480 38.7558  210.5599   184.2615  157.9631         0.0253<br />\\n2024-03-20     0.0270 43.0550  209.3955   183.3060  157.2165        -0.0162<br />\\n...               ...     ...       ...        ...       ...            ...<br />\\n2025-03-04     0.1697 26.4700  404.1738   334.8230  265.4722         0.0260<br />\\n2025-03-05     0.1253 29.9017  397.3141   329.1675  261.0209        -0.0561<br />\\n2025-03-06     0.0620 26.9042  393.4019   323.4315  253.4611        -0.0030<br />\\n2025-03-07     0.0087 26.7602  388.5007   317.8490  247.1973        -0.1543<br />\\n2025-03-10     0.0368 20.5942  389.9048   310.8755  231.8462         0.0379  </p>\\n\\n<p>[247 rows x 12 columns]|\\n|Timezone:|None|\\n|Number of bars:|247|\\n|Bar Length:|1 Day|\\n|Time From:|2020-03-13 14:34:40.290751|\\n|Time To:|2025-03-12 14:34:40.290751|</p>\\n\\n<hr />\\n\\n<h1>Observers</h1>\\n\\n<h2>Broker</h2>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>fund</td>\\n  <td>None</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h2>Trades</h2>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>pnlcomm</td>\\n  <td>True</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<hr />\\n\\n<h1>Analyzers</h1>\\n\\n<h2>SharpeRatio</h2>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>timeframe</td>\\n  <td>Day</td>\\n</tr>\\n<tr>\\n  <td>compression</td>\\n  <td>1</td>\\n</tr>\\n<tr>\\n  <td>riskfreerate</td>\\n  <td>0.01</td>\\n</tr>\\n<tr>\\n  <td>factor</td>\\n  <td>252</td>\\n</tr>\\n<tr>\\n  <td>convertrate</td>\\n  <td>True</td>\\n</tr>\\n<tr>\\n  <td>annualize</td>\\n  <td>True</td>\\n</tr>\\n<tr>\\n  <td>stddev_sample</td>\\n  <td>False</td>\\n</tr>\\n<tr>\\n  <td>daysfactor</td>\\n  <td>None</td>\\n</tr>\\n<tr>\\n  <td>legacyannual</td>\\n  <td>False</td>\\n</tr>\\n<tr>\\n  <td>fund</td>\\n  <td>None</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h2>DrawDown</h2>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>fund</td>\\n  <td>None</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h2>Returns</h2>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>timeframe</td>\\n  <td>None</td>\\n</tr>\\n<tr>\\n  <td>compression</td>\\n  <td>None</td>\\n</tr>\\n<tr>\\n  <td>_doprenext</td>\\n  <td>True</td>\\n</tr>\\n<tr>\\n  <td>tann</td>\\n  <td>None</td>\\n</tr>\\n<tr>\\n  <td>fund</td>\\n  <td>None</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h2>TradeAnalyzer</h2>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td></td>\\n  <td></td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h2>MoneyDrawDownAnalyzer</h2>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td></td>\\n  <td></td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<hr />\\n\"},\"id\":\"1416\",\"type\":\"Div\"},{\"attributes\":{\"label\":{\"value\":\"TSLA\"},\"renderers\":[{\"id\":\"1045\"},{\"id\":\"1062\"}]},\"id\":\"1058\",\"type\":\"LegendItem\"},{\"attributes\":{\"child\":{\"id\":\"1416\"},\"title\":\"Meta\"},\"id\":\"1417\",\"type\":\"Panel\"},{\"attributes\":{\"fill_alpha\":{\"value\":0.1},\"fill_color\":{\"field\":\"6102424592colors_volume\"},\"line_alpha\":{\"value\":0.1},\"top\":{\"field\":\"6102424592volume\"},\"width\":{\"value\":0.5},\"x\":{\"field\":\"index\"}},\"id\":\"1080\",\"type\":\"VBar\"},{\"attributes\":{\"line_color\":{\"field\":\"6102424592colors_wicks\"},\"x0\":{\"field\":\"index\"},\"x1\":{\"field\":\"index\"},\"y0\":{\"field\":\"6102424592high\"},\"y1\":{\"field\":\"6102424592low\"}},\"id\":\"1043\",\"type\":\"Segment\"},{\"attributes\":{},\"id\":\"1391\",\"type\":\"Selection\"},{\"attributes\":{},\"id\":\"1251\",\"type\":\"AllLabels\"},{\"attributes\":{\"source\":{\"id\":\"1003\"}},\"id\":\"1046\",\"type\":\"CDSView\"},{\"attributes\":{\"line_alpha\":{\"value\":0.1},\"line_color\":{\"field\":\"6102424592colors_wicks\"},\"x0\":{\"field\":\"index\"},\"x1\":{\"field\":\"index\"},\"y0\":{\"field\":\"6102424592high\"},\"y1\":{\"field\":\"6102424592low\"}},\"id\":\"1044\",\"type\":\"Segment\"},{\"attributes\":{\"fill_alpha\":{\"value\":0.1},\"fill_color\":{\"value\":\"#00ff00\"},\"line_alpha\":{\"value\":0.1},\"line_color\":{\"value\":\"#00ff00\"},\"marker\":{\"value\":\"triangle\"},\"size\":{\"value\":8.0},\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6102400256\"}},\"id\":\"1101\",\"type\":\"Scatter\"},{\"attributes\":{},\"id\":\"1254\",\"type\":\"AllLabels\"},{\"attributes\":{\"data_source\":{\"id\":\"1003\"},\"glyph\":{\"id\":\"1043\"},\"hover_glyph\":null,\"muted_glyph\":null,\"nonselection_glyph\":{\"id\":\"1044\"},\"view\":{\"id\":\"1046\"}},\"id\":\"1045\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"callback\":null,\"formatters\":{\"@datetime\":\"datetime\"},\"mode\":\"vline\",\"renderers\":[{\"id\":\"1176\"}],\"tooltips\":[[\"Time\",\"@datetime{%F %R}\"],[\"Broker (None) - cash\",\"@6102423728{0,0.000}\"],[\"Broker (None) - value\",\"@6102425360{0,0.000}\"]]},\"id\":\"1169\",\"type\":\"HoverTool\"},{\"attributes\":{\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6102423728\"}},\"id\":\"1174\",\"type\":\"Line\"},{\"attributes\":{\"label\":{\"value\":\"BuySell (True, 0.015) buy\"},\"renderers\":[{\"id\":\"1102\"}]},\"id\":\"1116\",\"type\":\"LegendItem\"},{\"attributes\":{\"text\":\"Broker (None)@(TSLA)\",\"text_color\":\"#333333\"},\"id\":\"1162\",\"type\":\"Title\"},{\"attributes\":{\"angle\":{\"units\":\"deg\",\"value\":180},\"fill_alpha\":{\"value\":0.1},\"fill_color\":{\"value\":\"#ff0000\"},\"line_alpha\":{\"value\":0.1},\"line_color\":{\"value\":\"#ff0000\"},\"marker\":{\"value\":\"triangle\"},\"size\":{\"value\":8.0},\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6102399824\"}},\"id\":\"1119\",\"type\":\"Scatter\"},{\"attributes\":{\"data_source\":{\"id\":\"1003\"},\"glyph\":{\"id\":\"1100\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"buy\",\"nonselection_glyph\":{\"id\":\"1101\"},\"view\":{\"id\":\"1103\"}},\"id\":\"1102\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"source\":{\"id\":\"1003\"}},\"id\":\"1103\",\"type\":\"CDSView\"},{\"attributes\":{},\"id\":\"1394\",\"type\":\"StringEditor\"},{\"attributes\":{},\"id\":\"1388\",\"type\":\"StringEditor\"},{\"attributes\":{},\"id\":\"1395\",\"type\":\"UnionRenderers\"},{\"attributes\":{\"bottom\":{\"field\":\"6102424592close\"},\"fill_color\":{\"field\":\"6102424592colors_bars\"},\"line_color\":{\"field\":\"6102424592colors_outline\"},\"top\":{\"field\":\"6102424592open\"},\"width\":{\"value\":0.5},\"x\":{\"field\":\"index\"}},\"id\":\"1060\",\"type\":\"VBar\"},{\"attributes\":{},\"id\":\"1392\",\"type\":\"StringEditor\"},{\"attributes\":{\"data_source\":{\"id\":\"1003\"},\"glyph\":{\"id\":\"1060\"},\"hover_glyph\":null,\"muted_glyph\":null,\"nonselection_glyph\":{\"id\":\"1061\"},\"view\":{\"id\":\"1063\"}},\"id\":\"1062\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"data\":{\"6102398864\":{\"__ndarray__\":\"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8=\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6102399632\":{\"__ndarray__\":\"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8=\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6102399824\":{\"__ndarray__\":\"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8=\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6102400256\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6102423728\":{\"__ndarray__\":\"AAAAAABq+EA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA2rWkQC+6xQDataRAL7rFANq1pEAvusUA=\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6102424592close\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6102424592colors_bars\":[\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\"],\"6102424592colors_outline\":[\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\"],\"6102424592colors_volume\":[\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\"],\"6102424592colors_wicks\":[\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\"],\"6102424592high\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6102424592low\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6102424592open\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6102424592volume\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6102425360\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"datetime\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"index\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246]},\"selected\":{\"id\":\"1055\"},\"selection_policy\":{\"id\":\"1054\"}},\"id\":\"1003\",\"type\":\"ColumnDataSource\"},{\"attributes\":{\"bottom\":{\"field\":\"6102424592close\"},\"fill_alpha\":{\"value\":0.1},\"fill_color\":{\"field\":\"6102424592colors_bars\"},\"line_alpha\":{\"value\":0.1},\"line_color\":{\"field\":\"6102424592colors_outline\"},\"top\":{\"field\":\"6102424592open\"},\"width\":{\"value\":0.5},\"x\":{\"field\":\"index\"}},\"id\":\"1061\",\"type\":\"VBar\"},{\"attributes\":{\"source\":{\"id\":\"1003\"}},\"id\":\"1063\",\"type\":\"CDSView\"},{\"attributes\":{\"line_color\":\"#0000ff\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6102425360\"}},\"id\":\"1191\",\"type\":\"Line\"},{\"attributes\":{\"format\":\"0,0.000\"},\"id\":\"1074\",\"type\":\"NumeralTickFormatter\"},{\"attributes\":{\"range_padding\":3.0303030303030303,\"renderers\":[{\"id\":\"1081\"}],\"start\":0},\"id\":\"1075\",\"type\":\"DataRange1d\"},{\"attributes\":{\"angle\":{\"units\":\"deg\",\"value\":180},\"fill_color\":{\"value\":\"#ff0000\"},\"line_color\":{\"value\":\"#ff0000\"},\"marker\":{\"value\":\"triangle\"},\"size\":{\"value\":8.0},\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6102399824\"}},\"id\":\"1118\",\"type\":\"Scatter\"},{\"attributes\":{},\"id\":\"1396\",\"type\":\"Selection\"},{\"attributes\":{\"data_source\":{\"id\":\"1003\"},\"glyph\":{\"id\":\"1191\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"value\",\"nonselection_glyph\":{\"id\":\"1192\"},\"view\":{\"id\":\"1194\"}},\"id\":\"1193\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"label\":{\"value\":\"BuySell (True, 0.015) sell\"},\"renderers\":[{\"id\":\"1120\"}]},\"id\":\"1134\",\"type\":\"LegendItem\"},{\"attributes\":{},\"id\":\"1393\",\"type\":\"StringEditor\"},{\"attributes\":{},\"id\":\"1407\",\"type\":\"StringEditor\"},{\"attributes\":{\"source\":{\"id\":\"1003\"}},\"id\":\"1121\",\"type\":\"CDSView\"},{\"attributes\":{\"line_alpha\":0.1,\"line_color\":\"#0000ff\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6102425360\"}},\"id\":\"1192\",\"type\":\"Line\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"1165\"},\"major_label_policy\":{\"id\":\"1184\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"1145\"}},\"id\":\"1144\",\"type\":\"LinearAxis\"},{\"attributes\":{\"text\":\"Trades - Net Profit/Loss (True)@(TSLA)\",\"text_color\":\"#333333\"},\"id\":\"1232\",\"type\":\"Title\"},{\"attributes\":{\"data_source\":{\"id\":\"1003\"},\"glyph\":{\"id\":\"1118\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"sell\",\"nonselection_glyph\":{\"id\":\"1119\"},\"view\":{\"id\":\"1121\"}},\"id\":\"1120\",\"type\":\"GlyphRenderer\"},{\"attributes\":{},\"id\":\"1408\",\"type\":\"StringEditor\"},{\"attributes\":{\"source\":{\"id\":\"1003\"}},\"id\":\"1194\",\"type\":\"CDSView\"},{\"attributes\":{},\"id\":\"1409\",\"type\":\"UnionRenderers\"},{\"attributes\":{},\"id\":\"1050\",\"type\":\"AllLabels\"},{\"attributes\":{},\"id\":\"1410\",\"type\":\"Selection\"},{\"attributes\":{\"sizing_mode\":\"stretch_width\",\"style\":{\"font-size\":\"large\"},\"text\":\"Sharpe-Ratio (timeframe: Day/factor: 252/annualize: True)\"},\"id\":\"1329\",\"type\":\"Paragraph\"},{\"attributes\":{},\"id\":\"1053\",\"type\":\"AllLabels\"},{\"attributes\":{\"fill_color\":{\"value\":\"#ff0000\"},\"line_color\":{\"value\":\"#ff0000\"},\"size\":{\"value\":8.0},\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6102398864\"}},\"id\":\"1261\",\"type\":\"Circle\"},{\"attributes\":{\"children\":[[{\"id\":\"1004\"},0,0],[{\"id\":\"1135\"},1,0],[{\"id\":\"1205\"},2,0]]},\"id\":\"1314\",\"type\":\"GridBox\"},{\"attributes\":{\"source\":{\"id\":\"1003\"}},\"id\":\"1082\",\"type\":\"CDSView\"},{\"attributes\":{},\"id\":\"1389\",\"type\":\"StringEditor\"},{\"attributes\":{\"fill_color\":{\"value\":\"#00ff00\"},\"line_color\":{\"value\":\"#00ff00\"},\"marker\":{\"value\":\"triangle\"},\"size\":{\"value\":8.0},\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6102400256\"}},\"id\":\"1100\",\"type\":\"Scatter\"},{\"attributes\":{\"toolbar\":{\"id\":\"1315\"}},\"id\":\"1316\",\"type\":\"ToolbarBox\"},{\"attributes\":{\"label\":{\"value\":\"Volume\"},\"renderers\":[{\"id\":\"1081\"}]},\"id\":\"1095\",\"type\":\"LegendItem\"},{\"attributes\":{\"fill_alpha\":{\"value\":0.1},\"fill_color\":{\"value\":\"#ff0000\"},\"line_alpha\":{\"value\":0.1},\"line_color\":{\"value\":\"#ff0000\"},\"size\":{\"value\":8.0},\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6102398864\"}},\"id\":\"1262\",\"type\":\"Circle\"},{\"attributes\":{\"line_color\":\"#a8a8a8\",\"line_dash\":[6],\"location\":0.0},\"id\":\"1275\",\"type\":\"Span\"},{\"attributes\":{\"aspect_ratio\":3.0,\"below\":[{\"id\":\"1144\"}],\"center\":[{\"id\":\"1147\"},{\"id\":\"1151\"},{\"id\":\"1188\"}],\"left\":[{\"id\":\"1148\"}],\"output_backend\":\"webgl\",\"renderers\":[{\"id\":\"1176\"},{\"id\":\"1193\"}],\"sizing_mode\":\"scale_width\",\"title\":{\"id\":\"1162\"},\"toolbar\":{\"id\":\"1157\"},\"toolbar_location\":null,\"x_range\":{\"id\":\"1005\"},\"x_scale\":{\"id\":\"1140\"},\"y_range\":{\"id\":\"1138\"},\"y_scale\":{\"id\":\"1142\"}},\"id\":\"1135\",\"subtype\":\"Figure\",\"type\":\"Plot\"},{\"attributes\":{\"data_source\":{\"id\":\"1003\"},\"glyph\":{\"id\":\"1079\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"Volume\",\"nonselection_glyph\":{\"id\":\"1080\"},\"view\":{\"id\":\"1082\"},\"y_range_name\":\"axvol\"},\"id\":\"1081\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"data\":{\"col0\":[\"Sharpe-Ratio\"],\"col1\":[0.768511093007662]},\"selected\":{\"id\":\"1391\"},\"selection_policy\":{\"id\":\"1390\"}},\"id\":\"1319\",\"type\":\"ColumnDataSource\"},{\"attributes\":{},\"id\":\"1140\",\"type\":\"LinearScale\"},{\"attributes\":{},\"id\":\"1089\",\"type\":\"AllLabels\"},{\"attributes\":{},\"id\":\"1390\",\"type\":\"UnionRenderers\"},{\"attributes\":{\"range_padding\":0.5},\"id\":\"1138\",\"type\":\"DataRange1d\"},{\"attributes\":{\"data_source\":{\"id\":\"1003\"},\"glyph\":{\"id\":\"1261\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"pnlminus\",\"nonselection_glyph\":{\"id\":\"1262\"},\"view\":{\"id\":\"1264\"}},\"id\":\"1263\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"children\":[{\"id\":\"1413\"},{\"id\":\"1411\"}],\"sizing_mode\":\"stretch_width\"},\"id\":\"1414\",\"type\":\"Column\"},{\"attributes\":{\"aspect_ratio\":3.0,\"below\":[{\"id\":\"1214\"}],\"center\":[{\"id\":\"1217\"},{\"id\":\"1221\"},{\"id\":\"1258\"}],\"left\":[{\"id\":\"1218\"}],\"output_backend\":\"webgl\",\"renderers\":[{\"id\":\"1246\"},{\"id\":\"1263\"},{\"id\":\"1275\"}],\"sizing_mode\":\"scale_width\",\"title\":{\"id\":\"1232\"},\"toolbar\":{\"id\":\"1227\"},\"toolbar_location\":null,\"x_range\":{\"id\":\"1005\"},\"x_scale\":{\"id\":\"1210\"},\"y_range\":{\"id\":\"1208\"},\"y_scale\":{\"id\":\"1212\"}},\"id\":\"1205\",\"subtype\":\"Figure\",\"type\":\"Plot\"},{\"attributes\":{},\"id\":\"1088\",\"type\":\"BasicTicker\"},{\"attributes\":{\"toolbar\":{\"id\":\"1412\"},\"toolbar_location\":\"above\"},\"id\":\"1413\",\"type\":\"ToolbarBox\"},{\"attributes\":{\"source\":{\"id\":\"1003\"}},\"id\":\"1264\",\"type\":\"CDSView\"},{\"attributes\":{},\"id\":\"1142\",\"type\":\"LinearScale\"},{\"attributes\":{\"range_padding\":0.5},\"id\":\"1208\",\"type\":\"DataRange1d\"},{\"attributes\":{\"logo\":null},\"id\":\"1412\",\"type\":\"ProxyToolbar\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"1235\"},\"major_label_policy\":{\"id\":\"1254\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"1215\"}},\"id\":\"1214\",\"type\":\"LinearAxis\"},{\"attributes\":{},\"id\":\"1145\",\"type\":\"BasicTicker\"},{\"attributes\":{\"axis\":{\"id\":\"1144\"},\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"1147\",\"type\":\"Grid\"},{\"attributes\":{},\"id\":\"1210\",\"type\":\"LinearScale\"},{\"attributes\":{\"text\":\"TSLA | BuySell (True, 0.015)@(TSLA)\",\"text_color\":\"#333333\"},\"id\":\"1031\",\"type\":\"Title\"},{\"attributes\":{},\"id\":\"1212\",\"type\":\"LinearScale\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"1163\"},\"major_label_policy\":{\"id\":\"1181\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"1149\"}},\"id\":\"1148\",\"type\":\"LinearAxis\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"1233\"},\"major_label_policy\":{\"id\":\"1251\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"1219\"}},\"id\":\"1218\",\"type\":\"LinearAxis\"},{\"attributes\":{},\"id\":\"1215\",\"type\":\"BasicTicker\"},{\"attributes\":{\"axis\":{\"id\":\"1214\"},\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"1217\",\"type\":\"Grid\"},{\"attributes\":{\"axis\":{\"id\":\"1148\"},\"dimension\":1,\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"1151\",\"type\":\"Grid\"},{\"attributes\":{},\"id\":\"1149\",\"type\":\"BasicTicker\"},{\"attributes\":{\"use_scientific\":false},\"id\":\"1163\",\"type\":\"BasicTickFormatter\"},{\"attributes\":{\"axis\":{\"id\":\"1218\"},\"dimension\":1,\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"1221\",\"type\":\"Grid\"},{\"attributes\":{},\"id\":\"1153\",\"type\":\"WheelZoomTool\"},{\"attributes\":{},\"id\":\"1219\",\"type\":\"BasicTicker\"},{\"attributes\":{},\"id\":\"1152\",\"type\":\"PanTool\"},{\"attributes\":{\"use_scientific\":false},\"id\":\"1233\",\"type\":\"BasicTickFormatter\"},{\"attributes\":{\"overlay\":{\"id\":\"1156\"}},\"id\":\"1154\",\"type\":\"BoxZoomTool\"},{\"attributes\":{},\"id\":\"1155\",\"type\":\"ResetTool\"},{\"attributes\":{},\"id\":\"1223\",\"type\":\"WheelZoomTool\"},{\"attributes\":{},\"id\":\"1222\",\"type\":\"PanTool\"},{\"attributes\":{\"overlay\":{\"id\":\"1226\"}},\"id\":\"1224\",\"type\":\"BoxZoomTool\"},{\"attributes\":{},\"id\":\"1225\",\"type\":\"ResetTool\"},{\"attributes\":{\"background_fill_color\":\"#f5f5f5\",\"click_policy\":\"hide\",\"items\":[{\"id\":\"1189\"}],\"label_text_color\":\"#333333\",\"location\":\"top_left\",\"orientation\":\"horizontal\"},\"id\":\"1188\",\"type\":\"Legend\"},{\"attributes\":{\"editor\":{\"id\":\"1404\"},\"field\":\"col3\",\"formatter\":{\"id\":\"1368\"},\"title\":\"Closed\"},\"id\":\"1369\",\"type\":\"TableColumn\"},{\"attributes\":{\"sizing_mode\":\"stretch_width\",\"style\":{\"font-size\":\"large\"},\"text\":\"Drawdown\"},\"id\":\"1344\",\"type\":\"Paragraph\"},{\"attributes\":{},\"id\":\"1320\",\"type\":\"StringFormatter\"},{\"attributes\":{\"children\":[{\"id\":\"1329\"},{\"id\":\"1326\"}],\"sizing_mode\":\"stretch_width\"},\"id\":\"1330\",\"type\":\"Column\"},{\"attributes\":{},\"id\":\"1332\",\"type\":\"StringFormatter\"},{\"attributes\":{\"source\":{\"id\":\"1376\"}},\"id\":\"1385\",\"type\":\"CDSView\"},{\"attributes\":{\"data_source\":{\"id\":\"1003\"},\"glyph\":{\"id\":\"1174\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"cash\",\"nonselection_glyph\":{\"id\":\"1175\"},\"view\":{\"id\":\"1177\"}},\"id\":\"1176\",\"type\":\"GlyphRenderer\"},{\"attributes\":{},\"id\":\"1380\",\"type\":\"StringFormatter\"},{\"attributes\":{\"fill_color\":{\"value\":\"#0000ff\"},\"line_color\":{\"value\":\"#0000ff\"},\"size\":{\"value\":8.0},\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6102399632\"}},\"id\":\"1244\",\"type\":\"Circle\"},{\"attributes\":{\"children\":[{\"id\":\"1344\"},{\"id\":\"1341\"}],\"sizing_mode\":\"stretch_width\"},\"id\":\"1345\",\"type\":\"Column\"},{\"attributes\":{\"columns\":[{\"id\":\"1321\"},{\"id\":\"1324\"}],\"height\":75,\"index_position\":null,\"sizing_mode\":\"stretch_width\",\"source\":{\"id\":\"1319\"},\"view\":{\"id\":\"1328\"}},\"id\":\"1326\",\"type\":\"DataTable\"},{\"attributes\":{\"label\":{\"value\":\"Broker (None)\"},\"renderers\":[{\"id\":\"1176\"},{\"id\":\"1193\"}]},\"id\":\"1189\",\"type\":\"LegendItem\"},{\"attributes\":{},\"id\":\"1377\",\"type\":\"StringFormatter\"},{\"attributes\":{\"line_alpha\":0.1,\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6102423728\"}},\"id\":\"1175\",\"type\":\"Line\"},{\"attributes\":{\"editor\":{\"id\":\"1388\"},\"field\":\"col0\",\"formatter\":{\"id\":\"1320\"},\"title\":\"Name\"},\"id\":\"1321\",\"type\":\"TableColumn\"},{\"attributes\":{\"line_color\":\"#000000\"},\"id\":\"1167\",\"type\":\"CrosshairTool\"},{\"attributes\":{\"children\":[{\"id\":\"1314\"},{\"id\":\"1316\"}],\"sizing_mode\":\"scale_width\"},\"id\":\"1317\",\"type\":\"Row\"},{\"attributes\":{\"children\":[[{\"id\":\"1330\"},0,0],[{\"id\":\"1345\"},0,1],[{\"id\":\"1357\"},1,0],[{\"id\":\"1375\"},1,1],[{\"id\":\"1387\"},2,0]]},\"id\":\"1411\",\"type\":\"GridBox\"},{\"attributes\":{\"aspect_ratio\":3.0,\"below\":[{\"id\":\"1013\"}],\"center\":[{\"id\":\"1016\"},{\"id\":\"1020\"},{\"id\":\"1057\"}],\"extra_y_ranges\":{\"axvol\":{\"id\":\"1075\"}},\"left\":[{\"id\":\"1017\"},{\"id\":\"1076\"}],\"output_backend\":\"webgl\",\"renderers\":[{\"id\":\"1045\"},{\"id\":\"1062\"},{\"id\":\"1081\"},{\"id\":\"1102\"},{\"id\":\"1120\"}],\"sizing_mode\":\"scale_width\",\"title\":{\"id\":\"1031\"},\"toolbar\":{\"id\":\"1026\"},\"toolbar_location\":null,\"x_range\":{\"id\":\"1005\"},\"x_scale\":{\"id\":\"1009\"},\"y_range\":{\"id\":\"1007\"},\"y_scale\":{\"id\":\"1011\"}},\"id\":\"1004\",\"subtype\":\"Figure\",\"type\":\"Plot\"},{\"attributes\":{\"logo\":null,\"toolbars\":[{\"id\":\"1026\"},{\"id\":\"1157\"},{\"id\":\"1227\"}],\"tools\":[{\"id\":\"1021\"},{\"id\":\"1022\"},{\"id\":\"1023\"},{\"id\":\"1024\"},{\"id\":\"1036\"},{\"id\":\"1038\"},{\"id\":\"1152\"},{\"id\":\"1153\"},{\"id\":\"1154\"},{\"id\":\"1155\"},{\"id\":\"1167\"},{\"id\":\"1169\"},{\"id\":\"1222\"},{\"id\":\"1223\"},{\"id\":\"1224\"},{\"id\":\"1225\"},{\"id\":\"1237\"},{\"id\":\"1239\"}]},\"id\":\"1315\",\"type\":\"ProxyToolbar\"},{\"attributes\":{\"editor\":{\"id\":\"1408\"},\"field\":\"col1\",\"formatter\":{\"id\":\"1380\"},\"title\":\"Value\"},\"id\":\"1381\",\"type\":\"TableColumn\"},{\"attributes\":{\"source\":{\"id\":\"1003\"}},\"id\":\"1177\",\"type\":\"CDSView\"},{\"attributes\":{\"fill_alpha\":{\"value\":0.1},\"fill_color\":{\"value\":\"#0000ff\"},\"line_alpha\":{\"value\":0.1},\"line_color\":{\"value\":\"#0000ff\"},\"size\":{\"value\":8.0},\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6102399632\"}},\"id\":\"1245\",\"type\":\"Circle\"},{\"attributes\":{\"data\":{\"col0\":[\"max_drawdown_money\"],\"col1\":[150502.63500976562]},\"selected\":{\"id\":\"1410\"},\"selection_policy\":{\"id\":\"1409\"}},\"id\":\"1376\",\"type\":\"ColumnDataSource\"},{\"attributes\":{\"bottom_units\":\"screen\",\"fill_alpha\":0.5,\"fill_color\":\"lightgrey\",\"left_units\":\"screen\",\"level\":\"overlay\",\"line_alpha\":1.0,\"line_color\":\"black\",\"line_dash\":[4,4],\"line_width\":2,\"right_units\":\"screen\",\"syncable\":false,\"top_units\":\"screen\"},\"id\":\"1156\",\"type\":\"BoxAnnotation\"},{\"attributes\":{},\"id\":\"1397\",\"type\":\"StringEditor\"},{\"attributes\":{\"columns\":[{\"id\":\"1378\"},{\"id\":\"1381\"}],\"height\":75,\"index_position\":null,\"sizing_mode\":\"stretch_width\",\"source\":{\"id\":\"1376\"},\"view\":{\"id\":\"1385\"}},\"id\":\"1383\",\"type\":\"DataTable\"},{\"attributes\":{\"bottom_units\":\"screen\",\"fill_alpha\":0.5,\"fill_color\":\"lightgrey\",\"left_units\":\"screen\",\"level\":\"overlay\",\"line_alpha\":1.0,\"line_color\":\"black\",\"line_dash\":[4,4],\"line_width\":2,\"right_units\":\"screen\",\"syncable\":false,\"top_units\":\"screen\"},\"id\":\"1226\",\"type\":\"BoxAnnotation\"},{\"attributes\":{},\"id\":\"1398\",\"type\":\"StringEditor\"},{\"attributes\":{\"callback\":null,\"formatters\":{\"@datetime\":\"datetime\"},\"mode\":\"vline\",\"renderers\":[{\"id\":\"1246\"},{\"id\":\"1263\"}],\"tooltips\":[[\"Time\",\"@datetime{%F %R}\"],[\"Trades - Net Profit/Loss (True) - pnlplus\",\"@6102399632{0,0.000}\"],[\"Trades - Net Profit/Loss (True) - pnlminus\",\"@6102398864{0,0.000}\"]]},\"id\":\"1239\",\"type\":\"HoverTool\"},{\"attributes\":{\"editor\":{\"id\":\"1403\"},\"field\":\"col2\",\"formatter\":{\"id\":\"1365\"},\"title\":\"Open\"},\"id\":\"1366\",\"type\":\"TableColumn\"},{\"attributes\":{},\"id\":\"1399\",\"type\":\"UnionRenderers\"},{\"attributes\":{\"line_color\":\"#000000\"},\"id\":\"1237\",\"type\":\"CrosshairTool\"},{\"attributes\":{\"columns\":[{\"id\":\"1360\"},{\"id\":\"1363\"},{\"id\":\"1366\"},{\"id\":\"1369\"}],\"height\":75,\"index_position\":null,\"sizing_mode\":\"stretch_width\",\"source\":{\"id\":\"1358\"},\"view\":{\"id\":\"1373\"}},\"id\":\"1371\",\"type\":\"DataTable\"},{\"attributes\":{\"args\":{\"axis\":{\"id\":\"1144\"},\"formatter\":{\"id\":\"1164\"},\"source\":{\"id\":\"1003\"}},\"code\":\"\\n                // We override this axis' formatter's `doFormat` method\\n                // with one that maps index ticks to dates. Some of those dates\\n                // are undefined (e.g. those whose ticks fall out of defined data\\n                // range) and we must filter out and account for those, otherwise\\n                // the formatter computes invalid visible span and returns some\\n                // labels as 'ERR'.\\n                // Note, after this assignment statement, on next plot redrawing,\\n                // our override `doFormat` will be called directly\\n                // -- FunctionTickFormatter.doFormat(), i.e. _this_ code, no longer\\n                // executes.\\n                axis.formatter.doFormat = function (ticks) {\\n                    const dates = ticks.map(i => source.data.datetime[source.data.index.indexOf(i)]),\\n                          valid = t => t !== undefined,\\n                          labels = formatter.doFormat(dates.filter(valid));\\n                    let i = 0;\\n                    return dates.map(t => valid(t) ? labels[i++] : '');\\n                };\\n\\n                // we do this manually only for the first time we are called\\n                const labels = axis.formatter.doFormat(ticks);\\n                return labels[index];\\n            \"},\"id\":\"1165\",\"type\":\"FuncTickFormatter\"},{\"attributes\":{\"editor\":{\"id\":\"1402\"},\"field\":\"col1\",\"formatter\":{\"id\":\"1362\"},\"title\":\"Total\"},\"id\":\"1363\",\"type\":\"TableColumn\"},{\"attributes\":{},\"id\":\"1362\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"source\":{\"id\":\"1319\"}},\"id\":\"1328\",\"type\":\"CDSView\"},{\"attributes\":{\"source\":{\"id\":\"1358\"}},\"id\":\"1373\",\"type\":\"CDSView\"},{\"attributes\":{},\"id\":\"1400\",\"type\":\"Selection\"},{\"attributes\":{},\"id\":\"1365\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"label\":{\"value\":\"Trades - Net Profit/Loss (True)\"},\"renderers\":[{\"id\":\"1246\"},{\"id\":\"1263\"}]},\"id\":\"1259\",\"type\":\"LegendItem\"},{\"attributes\":{\"data\":{\"col0\":[\"Number of Trades\"],\"col1\":[1],\"col2\":[1],\"col3\":[0]},\"selected\":{\"id\":\"1406\"},\"selection_policy\":{\"id\":\"1405\"}},\"id\":\"1358\",\"type\":\"ColumnDataSource\"},{\"attributes\":{\"children\":[{\"id\":\"1374\"},{\"id\":\"1371\"}],\"sizing_mode\":\"stretch_width\"},\"id\":\"1375\",\"type\":\"Column\"},{\"attributes\":{\"days\":[\"%d %b %R\"],\"hourmin\":[\"%H:%M:%S\"],\"hours\":[\"%d %b %R\"],\"minsec\":[\"%H:%M:%S\"],\"minutes\":[\"%H:%M\"],\"months\":[\"%d/%m/%y\"],\"seconds\":[\"%H:%M:%S\"],\"years\":[\"%Y %b\"]},\"id\":\"1164\",\"type\":\"DatetimeTickFormatter\"},{\"attributes\":{\"editor\":{\"id\":\"1401\"},\"field\":\"col0\",\"formatter\":{\"id\":\"1359\"},\"title\":\"\"},\"id\":\"1360\",\"type\":\"TableColumn\"},{\"attributes\":{\"line_color\":\"#000000\"},\"id\":\"1036\",\"type\":\"CrosshairTool\"},{\"attributes\":{},\"id\":\"1368\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"1034\"},\"major_label_policy\":{\"id\":\"1053\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"1014\"}},\"id\":\"1013\",\"type\":\"LinearAxis\"},{\"attributes\":{\"args\":{\"axis\":{\"id\":\"1214\"},\"formatter\":{\"id\":\"1234\"},\"source\":{\"id\":\"1003\"}},\"code\":\"\\n                // We override this axis' formatter's `doFormat` method\\n                // with one that maps index ticks to dates. Some of those dates\\n                // are undefined (e.g. those whose ticks fall out of defined data\\n                // range) and we must filter out and account for those, otherwise\\n                // the formatter computes invalid visible span and returns some\\n                // labels as 'ERR'.\\n                // Note, after this assignment statement, on next plot redrawing,\\n                // our override `doFormat` will be called directly\\n                // -- FunctionTickFormatter.doFormat(), i.e. _this_ code, no longer\\n                // executes.\\n                axis.formatter.doFormat = function (ticks) {\\n                    const dates = ticks.map(i => source.data.datetime[source.data.index.indexOf(i)]),\\n                          valid = t => t !== undefined,\\n                          labels = formatter.doFormat(dates.filter(valid));\\n                    let i = 0;\\n                    return dates.map(t => valid(t) ? labels[i++] : '');\\n                };\\n\\n                // we do this manually only for the first time we are called\\n                const labels = axis.formatter.doFormat(ticks);\\n                return labels[index];\\n            \"},\"id\":\"1235\",\"type\":\"FuncTickFormatter\"},{\"attributes\":{},\"id\":\"1005\",\"type\":\"DataRange1d\"},{\"attributes\":{\"active_multi\":null,\"tools\":[{\"id\":\"1152\"},{\"id\":\"1153\"},{\"id\":\"1154\"},{\"id\":\"1155\"},{\"id\":\"1167\"},{\"id\":\"1169\"}]},\"id\":\"1157\",\"type\":\"Toolbar\"},{\"attributes\":{\"child\":{\"id\":\"1317\"},\"title\":\"Plots\"},\"id\":\"1318\",\"type\":\"Panel\"},{\"attributes\":{\"use_scientific\":false},\"id\":\"1032\",\"type\":\"BasicTickFormatter\"},{\"attributes\":{},\"id\":\"1009\",\"type\":\"LinearScale\"},{\"attributes\":{\"editor\":{\"id\":\"1398\"},\"field\":\"col1\",\"formatter\":{\"id\":\"1350\"},\"title\":\"Value\"},\"id\":\"1351\",\"type\":\"TableColumn\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"1032\"},\"major_label_policy\":{\"id\":\"1050\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"1018\"}},\"id\":\"1017\",\"type\":\"LinearAxis\"},{\"attributes\":{\"days\":[\"%d %b %R\"],\"hourmin\":[\"%H:%M:%S\"],\"hours\":[\"%d %b %R\"],\"minsec\":[\"%H:%M:%S\"],\"minutes\":[\"%H:%M\"],\"months\":[\"%d/%m/%y\"],\"seconds\":[\"%H:%M:%S\"],\"years\":[\"%Y %b\"]},\"id\":\"1234\",\"type\":\"DatetimeTickFormatter\"},{\"attributes\":{\"range_padding\":0.5,\"renderers\":[{\"id\":\"1062\"},{\"id\":\"1102\"},{\"id\":\"1120\"}]},\"id\":\"1007\",\"type\":\"DataRange1d\"},{\"attributes\":{\"sizing_mode\":\"stretch_width\",\"style\":{\"font-size\":\"large\"},\"text\":\"Transaction Analyzer\"},\"id\":\"1374\",\"type\":\"Paragraph\"},{\"attributes\":{},\"id\":\"1011\",\"type\":\"LinearScale\"},{\"attributes\":{\"columns\":[{\"id\":\"1348\"},{\"id\":\"1351\"}],\"height\":150,\"index_position\":null,\"sizing_mode\":\"stretch_width\",\"source\":{\"id\":\"1346\"},\"view\":{\"id\":\"1355\"}},\"id\":\"1353\",\"type\":\"DataTable\"},{\"attributes\":{},\"id\":\"1021\",\"type\":\"PanTool\"},{\"attributes\":{\"editor\":{\"id\":\"1407\"},\"field\":\"col0\",\"formatter\":{\"id\":\"1377\"},\"title\":\"Performance\"},\"id\":\"1378\",\"type\":\"TableColumn\"},{\"attributes\":{\"active_multi\":null,\"tools\":[{\"id\":\"1222\"},{\"id\":\"1223\"},{\"id\":\"1224\"},{\"id\":\"1225\"},{\"id\":\"1237\"},{\"id\":\"1239\"}]},\"id\":\"1227\",\"type\":\"Toolbar\"},{\"attributes\":{},\"id\":\"1014\",\"type\":\"BasicTicker\"},{\"attributes\":{\"source\":{\"id\":\"1346\"}},\"id\":\"1355\",\"type\":\"CDSView\"},{\"attributes\":{\"axis\":{\"id\":\"1013\"},\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"1016\",\"type\":\"Grid\"},{\"attributes\":{},\"id\":\"1350\",\"type\":\"StringFormatter\"},{\"attributes\":{},\"id\":\"1347\",\"type\":\"StringFormatter\"},{\"attributes\":{},\"id\":\"1359\",\"type\":\"StringFormatter\"},{\"attributes\":{\"sizing_mode\":\"stretch_width\",\"style\":{\"font-size\":\"large\"},\"text\":\"Returns\"},\"id\":\"1356\",\"type\":\"Paragraph\"},{\"attributes\":{\"children\":[{\"id\":\"1356\"},{\"id\":\"1353\"}],\"sizing_mode\":\"stretch_width\"},\"id\":\"1357\",\"type\":\"Column\"},{\"attributes\":{\"axis\":{\"id\":\"1017\"},\"dimension\":1,\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"1020\",\"type\":\"Grid\"},{\"attributes\":{\"sizing_mode\":\"stretch_width\",\"style\":{\"font-size\":\"large\"},\"text\":\"MoneyDrawDownAnalyzer\"},\"id\":\"1386\",\"type\":\"Paragraph\"},{\"attributes\":{},\"id\":\"1018\",\"type\":\"BasicTicker\"},{\"attributes\":{\"editor\":{\"id\":\"1397\"},\"field\":\"col0\",\"formatter\":{\"id\":\"1347\"},\"title\":\"Performance\"},\"id\":\"1348\",\"type\":\"TableColumn\"},{\"attributes\":{\"data_source\":{\"id\":\"1003\"},\"glyph\":{\"id\":\"1244\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"pnlplus\",\"nonselection_glyph\":{\"id\":\"1245\"},\"view\":{\"id\":\"1247\"}},\"id\":\"1246\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"source\":{\"id\":\"1003\"}},\"id\":\"1247\",\"type\":\"CDSView\"},{\"attributes\":{\"data\":{\"col0\":[\"rtot\",\"ravg\",\"rnorm\",\"rnorm100\"],\"col1\":[0.2950968126086535,0.0011947239376868563,0.3513045136673431,35.13045136673431]},\"selected\":{\"id\":\"1400\"},\"selection_policy\":{\"id\":\"1399\"}},\"id\":\"1346\",\"type\":\"ColumnDataSource\"},{\"attributes\":{\"children\":[{\"id\":\"1386\"},{\"id\":\"1383\"}],\"sizing_mode\":\"stretch_width\"},\"id\":\"1387\",\"type\":\"Column\"},{\"attributes\":{\"background_fill_color\":\"#f5f5f5\",\"click_policy\":\"hide\",\"items\":[{\"id\":\"1259\"}],\"label_text_color\":\"#333333\",\"location\":\"top_left\",\"orientation\":\"horizontal\"},\"id\":\"1258\",\"type\":\"Legend\"},{\"attributes\":{},\"id\":\"1022\",\"type\":\"WheelZoomTool\"},{\"attributes\":{\"editor\":{\"id\":\"1394\"},\"field\":\"col2\",\"formatter\":{\"id\":\"1338\"},\"title\":\"Maximum\"},\"id\":\"1339\",\"type\":\"TableColumn\"},{\"attributes\":{\"overlay\":{\"id\":\"1025\"}},\"id\":\"1023\",\"type\":\"BoxZoomTool\"},{\"attributes\":{},\"id\":\"1403\",\"type\":\"StringEditor\"},{\"attributes\":{},\"id\":\"1024\",\"type\":\"ResetTool\"},{\"attributes\":{\"format\":\"0,0.000\"},\"id\":\"1338\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"editor\":{\"id\":\"1393\"},\"field\":\"col1\",\"formatter\":{\"id\":\"1335\"},\"title\":\"Value\"},\"id\":\"1336\",\"type\":\"TableColumn\"},{\"attributes\":{\"days\":[\"%d %b %R\"],\"hourmin\":[\"%H:%M:%S\"],\"hours\":[\"%d %b %R\"],\"minsec\":[\"%H:%M:%S\"],\"minutes\":[\"%H:%M\"],\"months\":[\"%d/%m/%y\"],\"seconds\":[\"%H:%M:%S\"],\"years\":[\"%Y %b\"]},\"id\":\"1033\",\"type\":\"DatetimeTickFormatter\"},{\"attributes\":{},\"id\":\"1401\",\"type\":\"StringEditor\"},{\"attributes\":{\"active_multi\":null,\"tools\":[{\"id\":\"1021\"},{\"id\":\"1022\"},{\"id\":\"1023\"},{\"id\":\"1024\"},{\"id\":\"1036\"},{\"id\":\"1038\"}]},\"id\":\"1026\",\"type\":\"Toolbar\"},{\"attributes\":{\"format\":\"0,0.000\"},\"id\":\"1323\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"args\":{\"axis\":{\"id\":\"1013\"},\"formatter\":{\"id\":\"1033\"},\"source\":{\"id\":\"1003\"}},\"code\":\"\\n                // We override this axis' formatter's `doFormat` method\\n                // with one that maps index ticks to dates. Some of those dates\\n                // are undefined (e.g. those whose ticks fall out of defined data\\n                // range) and we must filter out and account for those, otherwise\\n                // the formatter computes invalid visible span and returns some\\n                // labels as 'ERR'.\\n                // Note, after this assignment statement, on next plot redrawing,\\n                // our override `doFormat` will be called directly\\n                // -- FunctionTickFormatter.doFormat(), i.e. _this_ code, no longer\\n                // executes.\\n                axis.formatter.doFormat = function (ticks) {\\n                    const dates = ticks.map(i => source.data.datetime[source.data.index.indexOf(i)]),\\n                          valid = t => t !== undefined,\\n                          labels = formatter.doFormat(dates.filter(valid));\\n                    let i = 0;\\n                    return dates.map(t => valid(t) ? labels[i++] : '');\\n                };\\n\\n                // we do this manually only for the first time we are called\\n                const labels = axis.formatter.doFormat(ticks);\\n                return labels[index];\\n            \"},\"id\":\"1034\",\"type\":\"FuncTickFormatter\"},{\"attributes\":{\"format\":\"0,0.000\"},\"id\":\"1335\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"source\":{\"id\":\"1331\"}},\"id\":\"1343\",\"type\":\"CDSView\"},{\"attributes\":{\"data\":{\"col0\":[\"Length\",\"Moneydown\",\"Drawdown\"],\"col1\":[54,150502.63500976562,52.83978045615101],\"col2\":[75,150502.63500976562,52.83978045615101]},\"selected\":{\"id\":\"1396\"},\"selection_policy\":{\"id\":\"1395\"}},\"id\":\"1331\",\"type\":\"ColumnDataSource\"},{\"attributes\":{},\"id\":\"1402\",\"type\":\"StringEditor\"},{\"attributes\":{\"editor\":{\"id\":\"1389\"},\"field\":\"col1\",\"formatter\":{\"id\":\"1323\"},\"title\":\"Value\"},\"id\":\"1324\",\"type\":\"TableColumn\"},{\"attributes\":{\"columns\":[{\"id\":\"1333\"},{\"id\":\"1336\"},{\"id\":\"1339\"}],\"height\":125,\"index_position\":null,\"sizing_mode\":\"stretch_width\",\"source\":{\"id\":\"1331\"},\"view\":{\"id\":\"1343\"}},\"id\":\"1341\",\"type\":\"DataTable\"},{\"attributes\":{\"editor\":{\"id\":\"1392\"},\"field\":\"col0\",\"formatter\":{\"id\":\"1332\"},\"title\":\"Feature\"},\"id\":\"1333\",\"type\":\"TableColumn\"}],\"root_ids\":[\"1418\"]},\"title\":\"Bokeh Application\",\"version\":\"2.3.3\"}};\n", "  var render_items = [{\"docid\":\"858237be-5877-45f3-bf1f-8e5c2dc1ff72\",\"root_ids\":[\"1418\"],\"roots\":{\"1418\":\"26a3a721-c2f5-4a53-b544-259bd71bac30\"}}];\n", "  root.Bokeh.embed.embed_items_notebook(docs_json, render_items);\n", "\n", "  }\n", "  if (root.Bokeh !== undefined) {\n", "    embed_document(root);\n", "  } else {\n", "    var attempts = 0;\n", "    var timer = setInterval(function(root) {\n", "      if (root.Bokeh !== undefined) {\n", "        clearInterval(timer);\n", "        embed_document(root);\n", "      } else {\n", "        attempts++;\n", "        if (attempts > 100) {\n", "          clearInterval(timer);\n", "          console.log(\"Bokeh: ERROR: Unable to run BokehJS code because BokehJS library is missing\");\n", "        }\n", "      }\n", "    }, 10, root)\n", "  }\n", "})(window);"], "application/vnd.bokehjs_exec.v0+json": ""}, "metadata": {"application/vnd.bokehjs_exec.v0+json": {"id": "1418"}}, "output_type": "display_data"}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "plot_results(bh_cerebro)  # JayBee黄版权所有，未经授权禁止复制", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 6. 训练MLP", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "features = factors  # JayBee黄量化模型\n", "X_train = train_data[features].values  # JayBee黄量化模型\n", "y_train = train_data['future_ret_1d'].values  # JayBee黄授权使用\n", "X_valid = valid_data[features].values  # JayBee黄授权使用\n", "y_valid = valid_data['future_ret_1d'].values  # JayBee黄版权所有，未经授权禁止复制\n", "X_test = test_data[features].values  # JayBee黄版权所有，未经授权禁止复制\n", "y_test = test_data['future_ret_1d'].values  # JayBee黄量化策略", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["updated -0.4151653557031676 {'mlp__alpha': 0.0001, 'mlp__hidden_layer_sizes': (64, 64), 'mlp__learning_rate_init': 0.001, 'mlp__solver': 'adam'}\n", "updated -0.1506631889886274 {'mlp__alpha': 0.0001, 'mlp__hidden_layer_sizes': (64, 64), 'mlp__learning_rate_init': 0.01, 'mlp__solver': 'adam'}\n", "updated -0.05583089717663259 {'mlp__alpha': 0.0001, 'mlp__hidden_layer_sizes': (128, 128), 'mlp__learning_rate_init': 0.01, 'mlp__solver': 'adam'}\n", "updated 0.009406988576607156 {'mlp__alpha': 0.0001, 'mlp__hidden_layer_sizes': (256, 256), 'mlp__learning_rate_init': 0.01, 'mlp__solver': 'adam'}\n", "updated 0.012027576448328303 {'mlp__alpha': 0.01, 'mlp__hidden_layer_sizes': (256, 256), 'mlp__learning_rate_init': 0.01, 'mlp__solver': 'adam'}\n", "Best Params: {'mlp__alpha': 0.01, 'mlp__hidden_layer_sizes': (256, 256), 'mlp__learning_rate_init': 0.01, 'mlp__solver': 'adam'}\n", "==== MLP - 训练集 ====\n", "MSE: 0.0016249659482098556\n", "R2:  0.07700573172005343\n", "==== MLP - 测试集 ====\n", "MSE: 0.001765236087664075\n", "R2:  0.010958584235108426\n"]}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "import copy  # <PERSON><PERSON>ee黄量化策略\n", "import numpy as np  # <PERSON><PERSON><PERSON>黄独家内容\n", "from sklearn.preprocessing import StandardScaler  # JayBee黄版权所有，未经授权禁止复制\n", "from sklearn.neural_network import MLPRegressor  # JayBee黄版权所有，未经授权禁止复制\n", "from sklearn.metrics import mean_squared_error, r2_score  # JayBee黄量化策略\n", "from sklearn.model_selection import ParameterGrid  # JayBee黄原创内容\n", "from sklearn.pipeline import Pipeline  # JayBee黄量化策略\n", "\n", "# 假设 X_train, y_train, X_valid, y_valid, X_test, y_test 已经定义\n", "\n", "######################################\n", "# 1. 建立 Pipeline（先缩放，再 MLP 回归）\n", "######################################\n", "pipeline = Pipeline([  # Jay<PERSON>ee黄原创内容\n", "    ('scaler', StandardScaler()),  # JayBee黄版权所有，未经授权禁止复制\n", "    ('mlp', MLPRegressor(random_state=42, max_iter=1000))  # Jay<PERSON>ee黄量化策略\n", "])  # <PERSON><PERSON><PERSON>黄独家内容\n", "\n", "######################################\n", "# 2. 定义 MLP 的超参数搜索范围\n", "######################################\n", "param_grid_mlp = {  # Copyright © JayBee黄\n", "    'mlp__hidden_layer_sizes': [(64, 64), (128, 128), (256, 256)],  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    'mlp__alpha': [1e-4, 1e-3, 1e-2],  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    'mlp__learning_rate_init': [1e-3, 1e-2],  # <PERSON><PERSON><PERSON>黄量化策略\n", "    'mlp__solver': ['adam', 'sgd']  # Copyright © JayBee黄\n", "}  # 本代码归<PERSON><PERSON><PERSON>黄所有\n", "\n", "######################################\n", "# 3. 遍历所有参数组合，寻找最优 MLP 模型（在验证集上评估）\n", "######################################\n", "best_score = float('-inf')  # 本代码归JayBee黄所有\n", "best_params = None  # <PERSON><PERSON>ee黄量化模型\n", "best_pipeline = None  # JayBee黄量化策略\n", "\n", "for params in ParameterGrid(param_grid_mlp):  # <PERSON><PERSON>ee黄原创内容\n", "    # 设置 Pipeline 的参数\n", "    pipeline.set_params(**params)  # JayBee黄 - 量化交易研究\n", "    pipeline.fit(X_train, y_train)  # JayBee黄原创内容\n", "    \n", "    # 在验证集上进行预测和评估\n", "    valid_pred = pipeline.predict(X_valid)  # JayBee黄独家内容\n", "    valid_r2 = r2_score(y_valid, valid_pred)  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    \n", "    if valid_r2 > best_score:  # JayBee黄 - 量化交易研究\n", "        best_score = valid_r2  # <PERSON><PERSON>ee黄独家内容\n", "        best_params = params  # JayBee黄授权使用\n", "        # 复制当前 pipeline，保存最佳模型\n", "        best_pipeline = copy.deepcopy(pipeline)  # JayBee黄量化模型\n", "        print('updated', best_score, best_params)  # 本代码归JayBee黄所有\n", "\n", "print(\"Best Params:\", best_params)  # 本代码归JayBee黄所有\n", "\n", "######################################\n", "# 4. 使用最优模型在训练集和测试集上评估\n", "######################################\n", "y_pred_train_mlp = best_pipeline.predict(X_train)  # JayBee黄原创内容\n", "y_pred_test_mlp  = best_pipeline.predict(X_test)  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "\n", "train_mse_mlp = mean_squared_error(y_train, y_pred_train_mlp)  # JayBee黄版权所有，未经授权禁止复制\n", "test_mse_mlp  = mean_squared_error(y_test, y_pred_test_mlp)  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "train_r2_mlp  = r2_score(y_train, y_pred_train_mlp)  # JayBee黄独家内容\n", "test_r2_mlp   = r2_score(y_test, y_pred_test_mlp)  # JayBee黄独家内容\n", "\n", "print(\"==== MLP - 训练集 ====\")  # JayBee黄独家内容\n", "print(\"MSE:\", train_mse_mlp)  # JayBee黄量化策略\n", "print(\"R2: \", train_r2_mlp)  # JayBee黄授权使用\n", "\n", "print(\"==== MLP - 测试集 ====\")  # JayBee黄版权所有，未经授权禁止复制\n", "print(\"MSE:\", test_mse_mlp)  # JayBee黄版权所有，未经授权禁止复制\n", "print(\"R2: \", test_r2_mlp)  # Copyright © JayBee黄", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>close</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>open</th>\n", "      <th>volume</th>\n", "      <th>momentum_5</th>\n", "      <th>vol_ratio</th>\n", "      <th>RSI_14</th>\n", "      <th>BB_upper</th>\n", "      <th>BB_middle</th>\n", "      <th>BB_lower</th>\n", "      <th>future_ret_1d</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2020-04-09</th>\n", "      <td>38.2000</td>\n", "      <td>38.3453</td>\n", "      <td>37.1407</td>\n", "      <td>37.4727</td>\n", "      <td>204750000</td>\n", "      <td>0.2608</td>\n", "      <td>0.0272</td>\n", "      <td>54.1876</td>\n", "      <td>39.7858</td>\n", "      <td>32.6168</td>\n", "      <td>25.4478</td>\n", "      <td>0.1360</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-04-13</th>\n", "      <td>43.3967</td>\n", "      <td>43.4667</td>\n", "      <td>38.7020</td>\n", "      <td>39.3440</td>\n", "      <td>337131000</td>\n", "      <td>0.3561</td>\n", "      <td>-0.0236</td>\n", "      <td>61.5239</td>\n", "      <td>41.4042</td>\n", "      <td>32.9646</td>\n", "      <td>24.5249</td>\n", "      <td>0.0905</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-04-14</th>\n", "      <td>47.3260</td>\n", "      <td>49.4587</td>\n", "      <td>46.1620</td>\n", "      <td>46.5980</td>\n", "      <td>458647500</td>\n", "      <td>0.3751</td>\n", "      <td>0.0475</td>\n", "      <td>65.9624</td>\n", "      <td>44.2006</td>\n", "      <td>33.8473</td>\n", "      <td>23.4940</td>\n", "      <td>0.0281</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-04-15</th>\n", "      <td>48.6553</td>\n", "      <td>50.2087</td>\n", "      <td>47.3333</td>\n", "      <td>49.4667</td>\n", "      <td>353655000</td>\n", "      <td>0.3380</td>\n", "      <td>0.0749</td>\n", "      <td>67.3352</td>\n", "      <td>46.7505</td>\n", "      <td>34.8461</td>\n", "      <td>22.9416</td>\n", "      <td>0.0211</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-04-16</th>\n", "      <td>49.6807</td>\n", "      <td>50.6300</td>\n", "      <td>47.1147</td>\n", "      <td>47.7960</td>\n", "      <td>309868500</td>\n", "      <td>0.3578</td>\n", "      <td>0.1159</td>\n", "      <td>68.3941</td>\n", "      <td>48.6161</td>\n", "      <td>36.1260</td>\n", "      <td>23.6360</td>\n", "      <td>0.0116</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-03-14</th>\n", "      <td>183.2600</td>\n", "      <td>183.8000</td>\n", "      <td>177.1400</td>\n", "      <td>177.3100</td>\n", "      <td>143717900</td>\n", "      <td>-0.0237</td>\n", "      <td>0.0352</td>\n", "      <td>48.3179</td>\n", "      <td>219.6131</td>\n", "      <td>195.1720</td>\n", "      <td>170.7309</td>\n", "      <td>-0.0153</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-03-15</th>\n", "      <td>180.4500</td>\n", "      <td>182.3400</td>\n", "      <td>176.0300</td>\n", "      <td>180.8000</td>\n", "      <td>145995600</td>\n", "      <td>-0.0085</td>\n", "      <td>0.0349</td>\n", "      <td>46.6146</td>\n", "      <td>218.0792</td>\n", "      <td>193.7320</td>\n", "      <td>169.3848</td>\n", "      <td>0.0204</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-03-16</th>\n", "      <td>184.1300</td>\n", "      <td>185.8100</td>\n", "      <td>178.8400</td>\n", "      <td>180.3700</td>\n", "      <td>121136800</td>\n", "      <td>0.0648</td>\n", "      <td>0.0117</td>\n", "      <td>49.1431</td>\n", "      <td>214.9871</td>\n", "      <td>192.2265</td>\n", "      <td>169.4659</td>\n", "      <td>-0.0217</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-03-17</th>\n", "      <td>180.1300</td>\n", "      <td>186.2200</td>\n", "      <td>177.3300</td>\n", "      <td>184.5200</td>\n", "      <td>133197100</td>\n", "      <td>0.0386</td>\n", "      <td>-0.0518</td>\n", "      <td>46.5616</td>\n", "      <td>214.0056</td>\n", "      <td>191.1310</td>\n", "      <td>168.2564</td>\n", "      <td>0.0173</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-03-20</th>\n", "      <td>183.2500</td>\n", "      <td>186.4400</td>\n", "      <td>176.3500</td>\n", "      <td>178.0800</td>\n", "      <td>129684400</td>\n", "      <td>0.0503</td>\n", "      <td>-0.1035</td>\n", "      <td>48.8200</td>\n", "      <td>211.5659</td>\n", "      <td>189.8780</td>\n", "      <td>168.1901</td>\n", "      <td>0.0782</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>741 rows × 12 columns</p>\n", "</div>"], "text/plain": ["              close     high      low     open     volume  momentum_5  \\\n", "Date                                                                    \n", "2020-04-09  38.2000  38.3453  37.1407  37.4727  204750000      0.2608   \n", "2020-04-13  43.3967  43.4667  38.7020  39.3440  337131000      0.3561   \n", "2020-04-14  47.3260  49.4587  46.1620  46.5980  458647500      0.3751   \n", "2020-04-15  48.6553  50.2087  47.3333  49.4667  353655000      0.3380   \n", "2020-04-16  49.6807  50.6300  47.1147  47.7960  309868500      0.3578   \n", "...             ...      ...      ...      ...        ...         ...   \n", "2023-03-14 183.2600 183.8000 177.1400 177.3100  143717900     -0.0237   \n", "2023-03-15 180.4500 182.3400 176.0300 180.8000  145995600     -0.0085   \n", "2023-03-16 184.1300 185.8100 178.8400 180.3700  121136800      0.0648   \n", "2023-03-17 180.1300 186.2200 177.3300 184.5200  133197100      0.0386   \n", "2023-03-20 183.2500 186.4400 176.3500 178.0800  129684400      0.0503   \n", "\n", "            vol_ratio  RSI_14  BB_upper  BB_middle  BB_lower  future_ret_1d  \n", "Date                                                                         \n", "2020-04-09     0.0272 54.1876   39.7858    32.6168   25.4478         0.1360  \n", "2020-04-13    -0.0236 61.5239   41.4042    32.9646   24.5249         0.0905  \n", "2020-04-14     0.0475 65.9624   44.2006    33.8473   23.4940         0.0281  \n", "2020-04-15     0.0749 67.3352   46.7505    34.8461   22.9416         0.0211  \n", "2020-04-16     0.1159 68.3941   48.6161    36.1260   23.6360         0.0116  \n", "...               ...     ...       ...        ...       ...            ...  \n", "2023-03-14     0.0352 48.3179  219.6131   195.1720  170.7309        -0.0153  \n", "2023-03-15     0.0349 46.6146  218.0792   193.7320  169.3848         0.0204  \n", "2023-03-16     0.0117 49.1431  214.9871   192.2265  169.4659        -0.0217  \n", "2023-03-17    -0.0518 46.5616  214.0056   191.1310  168.2564         0.0173  \n", "2023-03-20    -0.1035 48.8200  211.5659   189.8780  168.1901         0.0782  \n", "\n", "[741 rows x 12 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "train_data  # 版权所有: <PERSON><PERSON><PERSON>黄", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 6. 使用Backtrader进行回测", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "import backtrader as bt  # <PERSON><PERSON><PERSON>黄授权使用\n", "\n", "# 自定义成交量指标，把成交量数据单独显示在子图中\n", "class MyVolumeIndicator(bt.Indicator):  # <PERSON><PERSON><PERSON>黄独家内容\n", "    \"\"\"  # JayBee黄 - 量化交易研究\n", "    简单示例，把data的volume包装成一个单独的子图指标。  # JayBee黄量化模型\n", "    \"\"\"  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    lines = ('vol',)  # <PERSON><PERSON><PERSON>黄量化策略\n", "    plotinfo = dict(subplot=True, plotname='Volume')  # 让它单独开子图  # JayBee黄版权所有，未经授权禁止复制\n", "\n", "    def __init__(self):  # 本代码归Jay<PERSON>ee黄所有\n", "        self.lines.vol = self.data.volume  # 本代码归JayBee黄所有\n", "\n", "class MLFactorStrategy(bt.Strategy):  # <PERSON><PERSON>ee黄量化模型\n", "    params = (  # <PERSON><PERSON><PERSON>黄授权使用\n", "        ('model', None),            # 预先训练好的机器学习模型  # <PERSON><PERSON><PERSON>黄原创内容\n", "        ('target_percent', 0.98),   # 目标仓位百分比  # JayBee黄版权所有，未经授权禁止复制\n", "    )  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "\n", "    def __init__(self):  # <PERSON><PERSON><PERSON>黄原创内容\n", "        self.model = self.p.model  # <PERSON><PERSON><PERSON>黄量化策略\n", "        \n", "        # 关闭主图中Data自带的Volume绘制\n", "        self.data.plotinfo.plotvolume = False  # JayBee黄量化模型\n", "\n", "        # 自定义成交量指标以及其SMA指标\n", "        self.myvol = MyVolumeIndicator(self.data)  # JayBee黄版权所有，未经授权禁止复制\n", "        self.vol_5 = bt.indicators.SMA(self.myvol.vol, period=5)  # Copyright © JayBee黄\n", "        self.vol_5.plotinfo.subplot = True  # JayBee黄 - 量化交易研究\n", "        self.vol_10 = bt.indicators.SMA(self.myvol.vol, period=10)  # JayBee黄版权所有，未经授权禁止复制\n", "        self.vol_10.plotinfo.subplot = True  # 本代码归JayBee黄所有\n", "\n", "        # 添加其它因子指标\n", "\n", "        # 价格动量指标：计算5日价格百分比变化\n", "        self.momentum_5 = bt.indicators.PercentChange(self.data.close, period=5)  # Jay<PERSON>ee黄量化模型\n", "        \n", "        # RSI指标，14日周期\n", "        self.rsi_14 = bt.indicators.RSI(self.data.close, period=14)  # Copyright © JayBee黄\n", "        \n", "        # 布林带指标，默认20日均线和2倍标准差，返回上轨、均线和下轨\n", "        self.bb = bt.indicators.BollingerBands(self.data.close)  # JayBee黄独家内容\n", "\n", "        self.last_trade_type = None  # 记录上一次交易类型（buy/sell）  # 版权所有: JayBee黄\n", "        \n", "        self.value_history_dates = []  # <PERSON><PERSON>ee黄原创内容\n", "        self.value_history_values = []  # Copyright © JayBee黄\n", "\n", "    def next(self):  # <PERSON><PERSON><PERSON>黄量化模型\n", "        # 计算各个因子的当前值\n", "        momentum = self.momentum_5[0]  # 本代码归JayBee黄所有\n", "        vol_ratio = (self.vol_5[0] / self.vol_10[0] - 1) if self.vol_10[0] != 0 else 0  # <PERSON><PERSON>ee黄量化模型\n", "        rsi = self.rsi_14[0]  # <PERSON><PERSON><PERSON>黄独家内容\n", "        bb_upper = self.bb.top[0]  # 布林带上轨  # JayBee黄量化策略\n", "        bb_lower = self.bb.bot[0]  # 布林带下轨  # JayBee黄 - 量化交易研究\n", "\n", "        # 构建特征向量：注意顺序需要与模型训练时一致\n", "        X = [[momentum, vol_ratio, rsi, bb_upper, bb_lower]]  # Jay<PERSON>ee黄独家内容\n", "        pred_ret = self.model.predict(X)[0]  # Copyright © JayBee黄\n", "\n", "        # 获取当前持仓状态\n", "        current_position = self.getposition().size  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "\n", "        if pred_ret > 0 and current_position == 0:  # Jay<PERSON>ee黄量化策略\n", "            # 只有当当前没有仓位时，才执行买入\n", "            self.order_target_percent(target=self.p.target_percent)  # JayBee黄 - 量化交易研究\n", "            self.last_trade_type = \"BUY\"  # JayBee黄版权所有，未经授权禁止复制\n", "            print(f\"{self.datas[0].datetime.date(0)} => BUY signal, pred_ret={pred_ret:.6f}\")  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        \n", "        elif pred_ret <= 0 and current_position > 0:  # <PERSON><PERSON><PERSON>黄独家内容\n", "            # 只有当当前有仓位时，才执行卖出\n", "            self.order_target_percent(target=0.0)  # 本代码归JayBee黄所有\n", "            self.last_trade_type = \"SELL\"  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "            print(f\"{self.datas[0].datetime.date(0)} => SELL signal, pred_ret={pred_ret:.6f}\")  # JayBee黄 - 量化交易研究\n", "\n", "        # 只在交易执行时打印仓位信息\n", "        if self.last_trade_type:  # <PERSON><PERSON><PERSON>黄量化模型\n", "            print(f\"Current position size: {self.getposition().size}, Value: {self.broker.getvalue()}\")  # Jay<PERSON>ee黄量化模型\n", "\n", "        dt = self.data.datetime.date(0)  # JayBee黄量化模型\n", "        self.value_history_dates.append(dt)  # JayBee黄独家内容\n", "        self.value_history_values.append(self.broker.getvalue())  # JayBee黄 - 量化交易研究\n", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>close</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>open</th>\n", "      <th>volume</th>\n", "      <th>momentum_5</th>\n", "      <th>vol_ratio</th>\n", "      <th>RSI_14</th>\n", "      <th>BB_upper</th>\n", "      <th>BB_middle</th>\n", "      <th>BB_lower</th>\n", "      <th>future_ret_1d</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-03-14</th>\n", "      <td>162.5000</td>\n", "      <td>171.1700</td>\n", "      <td>160.5100</td>\n", "      <td>167.7700</td>\n", "      <td>126325700</td>\n", "      <td>-0.0904</td>\n", "      <td>-0.0532</td>\n", "      <td>27.6926</td>\n", "      <td>212.9732</td>\n", "      <td>188.5350</td>\n", "      <td>164.0968</td>\n", "      <td>0.0066</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-03-15</th>\n", "      <td>163.5700</td>\n", "      <td>165.1800</td>\n", "      <td>160.7600</td>\n", "      <td>163.1600</td>\n", "      <td>96971900</td>\n", "      <td>-0.0671</td>\n", "      <td>-0.0444</td>\n", "      <td>29.0287</td>\n", "      <td>212.7655</td>\n", "      <td>186.6910</td>\n", "      <td>160.6165</td>\n", "      <td>0.0625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-03-18</th>\n", "      <td>173.8000</td>\n", "      <td>174.7200</td>\n", "      <td>165.9000</td>\n", "      <td>170.0200</td>\n", "      <td>108214400</td>\n", "      <td>-0.0223</td>\n", "      <td>0.0244</td>\n", "      <td>40.3732</td>\n", "      <td>211.2894</td>\n", "      <td>185.3835</td>\n", "      <td>159.4776</td>\n", "      <td>-0.0143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-03-19</th>\n", "      <td>171.3200</td>\n", "      <td>172.8200</td>\n", "      <td>167.4200</td>\n", "      <td>172.3600</td>\n", "      <td>77271400</td>\n", "      <td>-0.0350</td>\n", "      <td>0.0480</td>\n", "      <td>38.7558</td>\n", "      <td>210.5599</td>\n", "      <td>184.2615</td>\n", "      <td>157.9631</td>\n", "      <td>0.0253</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-03-20</th>\n", "      <td>175.6600</td>\n", "      <td>176.2500</td>\n", "      <td>170.8200</td>\n", "      <td>173.0000</td>\n", "      <td>83846700</td>\n", "      <td>0.0365</td>\n", "      <td>0.0270</td>\n", "      <td>43.0550</td>\n", "      <td>209.3955</td>\n", "      <td>183.3060</td>\n", "      <td>157.2165</td>\n", "      <td>-0.0162</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-04</th>\n", "      <td>272.0400</td>\n", "      <td>284.3500</td>\n", "      <td>261.8400</td>\n", "      <td>270.9300</td>\n", "      <td>126706600</td>\n", "      <td>-0.1016</td>\n", "      <td>0.1697</td>\n", "      <td>26.4700</td>\n", "      <td>404.1738</td>\n", "      <td>334.8230</td>\n", "      <td>265.4722</td>\n", "      <td>0.0260</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-05</th>\n", "      <td>279.1000</td>\n", "      <td>279.5500</td>\n", "      <td>267.7100</td>\n", "      <td>272.9200</td>\n", "      <td>94042900</td>\n", "      <td>-0.0402</td>\n", "      <td>0.1253</td>\n", "      <td>29.9017</td>\n", "      <td>397.3141</td>\n", "      <td>329.1675</td>\n", "      <td>261.0209</td>\n", "      <td>-0.0561</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-06</th>\n", "      <td>263.4500</td>\n", "      <td>272.6500</td>\n", "      <td>260.0200</td>\n", "      <td>272.0600</td>\n", "      <td>98451600</td>\n", "      <td>-0.0656</td>\n", "      <td>0.0620</td>\n", "      <td>26.9042</td>\n", "      <td>393.4019</td>\n", "      <td>323.4315</td>\n", "      <td>253.4611</td>\n", "      <td>-0.0030</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-07</th>\n", "      <td>262.6700</td>\n", "      <td>266.2500</td>\n", "      <td>250.7300</td>\n", "      <td>259.3200</td>\n", "      <td>102369600</td>\n", "      <td>-0.1035</td>\n", "      <td>0.0087</td>\n", "      <td>26.7602</td>\n", "      <td>388.5007</td>\n", "      <td>317.8490</td>\n", "      <td>247.1973</td>\n", "      <td>-0.1543</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-10</th>\n", "      <td>222.1500</td>\n", "      <td>253.3700</td>\n", "      <td>220.0000</td>\n", "      <td>252.5400</td>\n", "      <td>189076900</td>\n", "      <td>-0.2196</td>\n", "      <td>0.0368</td>\n", "      <td>20.5942</td>\n", "      <td>389.9048</td>\n", "      <td>310.8755</td>\n", "      <td>231.8462</td>\n", "      <td>0.0379</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>247 rows × 12 columns</p>\n", "</div>"], "text/plain": ["              close     high      low     open     volume  momentum_5  \\\n", "Date                                                                    \n", "2024-03-14 162.5000 171.1700 160.5100 167.7700  126325700     -0.0904   \n", "2024-03-15 163.5700 165.1800 160.7600 163.1600   96971900     -0.0671   \n", "2024-03-18 173.8000 174.7200 165.9000 170.0200  108214400     -0.0223   \n", "2024-03-19 171.3200 172.8200 167.4200 172.3600   77271400     -0.0350   \n", "2024-03-20 175.6600 176.2500 170.8200 173.0000   83846700      0.0365   \n", "...             ...      ...      ...      ...        ...         ...   \n", "2025-03-04 272.0400 284.3500 261.8400 270.9300  126706600     -0.1016   \n", "2025-03-05 279.1000 279.5500 267.7100 272.9200   94042900     -0.0402   \n", "2025-03-06 263.4500 272.6500 260.0200 272.0600   98451600     -0.0656   \n", "2025-03-07 262.6700 266.2500 250.7300 259.3200  102369600     -0.1035   \n", "2025-03-10 222.1500 253.3700 220.0000 252.5400  189076900     -0.2196   \n", "\n", "            vol_ratio  RSI_14  BB_upper  BB_middle  BB_lower  future_ret_1d  \n", "Date                                                                         \n", "2024-03-14    -0.0532 27.6926  212.9732   188.5350  164.0968         0.0066  \n", "2024-03-15    -0.0444 29.0287  212.7655   186.6910  160.6165         0.0625  \n", "2024-03-18     0.0244 40.3732  211.2894   185.3835  159.4776        -0.0143  \n", "2024-03-19     0.0480 38.7558  210.5599   184.2615  157.9631         0.0253  \n", "2024-03-20     0.0270 43.0550  209.3955   183.3060  157.2165        -0.0162  \n", "...               ...     ...       ...        ...       ...            ...  \n", "2025-03-04     0.1697 26.4700  404.1738   334.8230  265.4722         0.0260  \n", "2025-03-05     0.1253 29.9017  397.3141   329.1675  261.0209        -0.0561  \n", "2025-03-06     0.0620 26.9042  393.4019   323.4315  253.4611        -0.0030  \n", "2025-03-07     0.0087 26.7602  388.5007   317.8490  247.1973        -0.1543  \n", "2025-03-10     0.0368 20.5942  389.9048   310.8755  231.8462         0.0379  \n", "\n", "[247 rows x 12 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "test_data  # JayBee黄版权所有，未经授权禁止复制", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["初始资金: 100000.00\n", "2024-04-11 => BUY signal, pred_ret=0.003732\n", "Current position size: 0, Value: 100000.0\n", "Current position size: 561, Value: 99151.55297853393\n", "Current position size: 561, Value: 93782.77886964721\n", "Current position size: 561, Value: 91331.21160890503\n", "Current position size: 561, Value: 90399.94955446167\n", "Current position size: 561, Value: 87303.22715761108\n", "Current position size: 561, Value: 85687.55297853393\n", "Current position size: 561, Value: 82882.55297853393\n", "Current position size: 561, Value: 84357.97715761108\n", "Current position size: 561, Value: 94147.43400575561\n", "Current position size: 561, Value: 98663.47715761108\n", "Current position size: 561, Value: 97603.1875000183\n", "Current position size: 561, Value: 112054.55297853393\n", "Current position size: 561, Value: 106012.58058168335\n", "Current position size: 561, Value: 104166.89434816284\n", "Current position size: 561, Value: 104178.10818483276\n", "Current position size: 561, Value: 104840.0926361267\n", "Current position size: 561, Value: 106842.85818483276\n", "Current position size: 561, Value: 102943.90989686889\n", "Current position size: 561, Value: 101210.42195131225\n", "Current position size: 561, Value: 99667.67195131225\n", "Current position size: 561, Value: 97704.17195131225\n", "Current position size: 561, Value: 99622.79092409057\n", "Current position size: 561, Value: 102798.05297853393\n", "Current position size: 561, Value: 100800.89434816284\n", "Current position size: 561, Value: 101277.73921205444\n", "Current position size: 561, Value: 102747.56503297729\n", "Current position size: 561, Value: 101339.44955446167\n", "Current position size: 561, Value: 107875.10469057006\n", "Current position size: 561, Value: 104234.21160890503\n", "2024-05-23 => SELL signal, pred_ret=-0.000305\n", "Current position size: 561, Value: 100660.64434816284\n", "2024-05-24 => BUY signal, pred_ret=0.001294\n", "Current position size: 0, Value: 101151.63202410888\n", "Current position size: 553, Value: 101219.95855272826\n", "Current position size: 553, Value: 100910.27990282592\n", "Current position size: 553, Value: 102348.0748399597\n", "Current position size: 553, Value: 101955.4495653015\n", "Current position size: 553, Value: 100965.5748399597\n", "Current position size: 553, Value: 100125.02091539916\n", "Current position size: 553, Value: 100252.20855272826\n", "Current position size: 553, Value: 101878.02990282592\n", "Current position size: 553, Value: 101623.64619005736\n", "Current position size: 553, Value: 99583.0748399597\n", "2024-06-11 => SELL signal, pred_ret=-0.000487\n", "Current position size: 553, Value: 97852.19057787475\n", "2024-06-12 => BUY signal, pred_ret=0.000448\n", "Current position size: 0, Value: 97984.31414523313\n", "2024-06-13 => BUY signal, pred_ret=0.003262\n", "Current position size: 0, Value: 97984.31414523313\n", "Current position size: 526, Value: 93762.712548999\n", "Current position size: 526, Value: 98722.89672258298\n", "Current position size: 526, Value: 97365.81575944822\n", "Current position size: 526, Value: 95635.27929094236\n", "Current position size: 526, Value: 96392.712548999\n", "2024-06-24 => SELL signal, pred_ret=-0.000054\n", "Current position size: 526, Value: 96166.53640153806\n", "2024-06-25 => BUY signal, pred_ret=0.004128\n", "Current position size: 0, Value: 97000.58413116452\n", "2024-06-26 => SELL signal, pred_ret=-0.000974\n", "Current position size: 507, Value: 101864.44393291317\n", "Current position size: 0, Value: 101131.76964108272\n", "2024-06-28 => BUY signal, pred_ret=0.000953\n", "Current position size: 0, Value: 101131.76964108272\n", "Current position size: 500, Value: 105426.2328078918\n", "Current position size: 500, Value: 116126.229756134\n", "Current position size: 500, Value: 123691.23219754024\n", "Current position size: 500, Value: 126256.2346389465\n", "Current position size: 500, Value: 126966.23372341915\n", "Current position size: 500, Value: 131661.22578884882\n", "Current position size: 500, Value: 132126.2373855285\n", "Current position size: 500, Value: 121011.23189236446\n", "Current position size: 500, Value: 124611.23036648556\n", "2024-07-15 => SELL signal, pred_ret=-0.000414\n", "Current position size: 500, Value: 126816.23219754024\n", "2024-07-16 => BUY signal, pred_ret=0.001761\n", "Current position size: 0, Value: 127998.60128323358\n", "Current position size: 488, Value: 125786.60673027947\n", "2024-07-18 => SELL signal, pred_ret=-0.003791\n", "Current position size: 488, Value: 126142.84464531853\n", "2024-07-19 => BUY signal, pred_ret=0.003238\n", "Current position size: 0, Value: 125294.82633718864\n", "2024-07-22 => BUY signal, pred_ret=0.000367\n", "Current position size: 0, Value: 125294.82633718864\n", "Current position size: 488, Value: 121623.284538507\n", "Current position size: 488, Value: 106792.96483635856\n", "Current position size: 488, Value: 108871.8421556945\n", "Current position size: 488, Value: 108652.24364495231\n", "Current position size: 488, Value: 114654.64513421012\n", "Current position size: 488, Value: 110028.399772882\n", "2024-07-31 => SELL signal, pred_ret=-0.004015\n", "Current position size: 488, Value: 114640.00572991325\n", "Current position size: 0, Value: 112367.07502590935\n", "2024-08-02 => BUY signal, pred_ret=0.001776\n", "Current position size: 0, Value: 112367.07502590935\n", "Current position size: 530, Value: 119482.18386618035\n", "Current position size: 530, Value: 120414.9809548034\n", "Current position size: 530, Value: 115708.57836691277\n", "Current position size: 530, Value: 119460.97933737175\n", "Current position size: 530, Value: 120075.78127828972\n", "2024-08-12 => SELL signal, pred_ret=-0.000030\n", "Current position size: 530, Value: 118745.48418966668\n", "Current position size: 0, Value: 119133.21932461539\n", "Current position size: 0, Value: 119133.21932461539\n", "Current position size: 0, Value: 119133.21932461539\n", "Current position size: 0, Value: 119133.21932461539\n", "Current position size: 0, Value: 119133.21932461539\n", "Current position size: 0, Value: 119133.21932461539\n", "Current position size: 0, Value: 119133.21932461539\n", "Current position size: 0, Value: 119133.21932461539\n", "2024-08-23 => BUY signal, pred_ret=0.000709\n", "Current position size: 0, Value: 119133.21932461539\n", "Current position size: 529, Value: 116060.36767625112\n", "Current position size: 529, Value: 113944.36767625112\n", "Current position size: 529, Value: 112114.02412461537\n", "Current position size: 529, Value: 112394.39347886342\n", "Current position size: 529, Value: 116536.46444749135\n", "2024-09-03 => SELL signal, pred_ret=-0.000131\n", "Current position size: 529, Value: 114679.67735337514\n", "2024-09-04 => BUY signal, pred_ret=0.000537\n", "Current position size: 0, Value: 114552.41646525258\n", "Current position size: 511, Value: 117826.11377976979\n", "Current position size: 511, Value: 107892.2725322112\n", "Current position size: 511, Value: 110723.21689866627\n", "Current position size: 511, Value: 115782.11377976979\n", "Current position size: 511, Value: 116783.67721055592\n", "Current position size: 511, Value: 117642.15346788014\n", "2024-09-13 => SELL signal, pred_ret=-0.004807\n", "Current position size: 511, Value: 117887.4312846526\n", "Current position size: 0, Value: 117238.84952332753\n", "2024-09-17 => BUY signal, pred_ret=0.001268\n", "Current position size: 0, Value: 117238.84952332753\n", "Current position size: 504, Value: 115641.09927279041\n", "2024-09-19 => SELL signal, pred_ret=-0.000977\n", "Current position size: 504, Value: 124067.97988802478\n", "2024-09-20 => BUY signal, pred_ret=0.003384\n", "Current position size: 0, Value: 122711.48208204334\n", "Current position size: 504, Value: 126288.54113411852\n", "Current position size: 504, Value: 128440.62328743884\n", "Current position size: 504, Value: 129826.61559700915\n", "Current position size: 504, Value: 128415.4217493529\n", "2024-09-27 => SELL signal, pred_ret=-0.002082\n", "Current position size: 504, Value: 131560.37682747788\n", "Current position size: 0, Value: 130688.97447645251\n", "Current position size: 0, Value: 130688.97447645251\n", "2024-10-02 => BUY signal, pred_ret=0.001403\n", "Current position size: 0, Value: 130688.97447645251\n", "Current position size: 514, Value: 128574.11013701768\n", "Current position size: 514, Value: 133415.98919585557\n", "Current position size: 514, Value: 128661.48919585557\n", "Current position size: 514, Value: 130547.86825469346\n", "2024-10-09 => SELL signal, pred_ret=-0.000855\n", "Current position size: 514, Value: 128774.56982329697\n", "Current position size: 0, Value: 129015.24236106552\n", "2024-10-11 => BUY signal, pred_ret=0.007434\n", "Current position size: 0, Value: 129015.24236106552\n", "2024-10-14 => SELL signal, pred_ret=-0.000656\n", "Current position size: 580, Value: 128295.93725022567\n", "2024-10-15 => BUY signal, pred_ret=0.002822\n", "Current position size: 0, Value: 128632.3551433531\n", "Current position size: 574, Value: 128436.36740129987\n", "2024-10-17 => SELL signal, pred_ret=-0.001323\n", "Current position size: 574, Value: 128183.80599993268\n", "Current position size: 0, Value: 127925.13136018049\n", "2024-10-21 => BUY signal, pred_ret=0.000061\n", "Current position size: 0, Value: 127925.13136018049\n", "Current position size: 572, Value: 128149.72353630353\n", "Current position size: 572, Value: 125678.6793468504\n", "2024-10-24 => SELL signal, pred_ret=-0.001403\n", "Current position size: 572, Value: 152465.44912224103\n", "2024-10-25 => BUY signal, pred_ret=0.014657\n", "Current position size: 0, Value: 149733.5992984129\n", "Current position size: 545, Value: 145477.12737067853\n", "Current position size: 545, Value: 143847.56606086408\n", "Current position size: 545, Value: 142773.91539558087\n", "Current position size: 545, Value: 138577.42537482892\n", "Current position size: 545, Value: 138103.2697199217\n", "Current position size: 545, Value: 134756.9700525633\n", "Current position size: 545, Value: 139443.9733789793\n", "2024-11-06 => SELL signal, pred_ret=-0.006555\n", "Current position size: 545, Value: 159658.0213831297\n", "Current position size: 0, Value: 159669.56222382793\n", "2024-11-08 => BUY signal, pred_ret=0.010878\n", "Current position size: 0, Value: 159669.56222382793\n", "2024-11-11 => BUY signal, pred_ret=0.013514\n", "Current position size: 0, Value: 159669.56222382793\n", "Current position size: 447, Value: 153124.23509819317\n", "Current position size: 447, Value: 153906.48509819317\n", "Current position size: 447, Value: 145386.66618950176\n", "2024-11-15 => SELL signal, pred_ret=-0.000485\n", "Current position size: 447, Value: 149651.05000908184\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "2024-12-06 => BUY signal, pred_ret=0.001616\n", "Current position size: 0, Value: 158420.8904094053\n", "2024-12-09 => BUY signal, pred_ret=0.000935\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 398, Value: 161552.06290064068\n", "Current position size: 398, Value: 171016.50241480084\n", "Current position size: 398, Value: 168361.84921655865\n", "Current position size: 398, Value: 175577.59115991803\n", "Current position size: 398, Value: 186240.00241480084\n", "Current position size: 398, Value: 192942.3209572813\n", "2024-12-18 => SELL signal, pred_ret=-0.009847\n", "Current position size: 398, Value: 177129.7887307188\n", "Current position size: 0, Value: 181606.56038877543\n", "Current position size: 0, Value: 181606.56038877543\n", "2024-12-23 => BUY signal, pred_ret=0.000208\n", "Current position size: 0, Value: 181606.56038877543\n", "2024-12-24 => SELL signal, pred_ret=-0.009002\n", "Current position size: 413, Value: 192300.80505789776\n", "Current position size: 0, Value: 193277.50664298685\n", "Current position size: 0, Value: 193277.50664298685\n", "Current position size: 0, Value: 193277.50664298685\n", "Current position size: 0, Value: 193277.50664298685\n", "2025-01-02 => BUY signal, pred_ret=0.006174\n", "Current position size: 0, Value: 193277.50664298685\n", "2025-01-03 => SELL signal, pred_ret=-0.006172\n", "Current position size: 499, Value: 207513.20890358865\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "2025-02-11 => BUY signal, pred_ret=0.002364\n", "Current position size: 0, Value: 213644.3519205442\n", "2025-02-12 => SELL signal, pred_ret=-0.004850\n", "Current position size: 637, Value: 217587.39295451637\n", "Current position size: 0, Value: 222743.93358381325\n", "Current position size: 0, Value: 222743.93358381325\n", "Current position size: 0, Value: 222743.93358381325\n", "Current position size: 0, Value: 222743.93358381325\n", "Current position size: 0, Value: 222743.93358381325\n", "Current position size: 0, Value: 222743.93358381325\n", "Current position size: 0, Value: 222743.93358381325\n", "Current position size: 0, Value: 222743.93358381325\n", "2025-02-26 => BUY signal, pred_ret=0.009675\n", "Current position size: 0, Value: 222743.93358381325\n", "Current position size: 750, Value: 215580.53248975807\n", "Current position size: 750, Value: 223853.03157423073\n", "Current position size: 750, Value: 217605.51875684792\n", "2025-03-04 => SELL signal, pred_ret=-0.001048\n", "Current position size: 750, Value: 208148.02974317604\n", "Current position size: 0, Value: 208565.8808952146\n", "Current position size: 0, Value: 208565.8808952146\n", "Current position size: 0, Value: 208565.8808952146\n", "2025-03-10 => BUY signal, pred_ret=0.018845\n", "Current position size: 0, Value: 208565.8808952146\n", "回测结束资金: 208565.88\n", "=== 回测分析报告 ===\n", "夏普比率: 15.0580\n", "最大回撤比例: 19.17%\n", "最大回撤金额(自定义): 25333.27\n", "累计收益率: 73.51%\n", "年化收益率: 0.30%\n", "=== 交易详情 ===\n", "总交易笔数: 23\n", "胜率: 13 / 23\n"]}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 若想看最优参数的详细回测日志，可再手动调用:\n", "ml_result, ml_cerebro = run_backtest(  # Jay<PERSON>ee黄原创内容\n", "    ticker=ticker,  # <PERSON><PERSON><PERSON>黄授权使用\n", "    # df=df.iloc[valid_idx:].copy(),\n", "    df=test_data,  # Jay<PERSON>ee黄授权使用\n", "    start_date=start_date,  # 本代码归Jay<PERSON><PERSON>黄所有\n", "    end_date=end_date,  # JayBee黄 - 量化交易研究\n", "    strategy=MLFactorStrategy,  # <PERSON><PERSON>ee黄量化策略\n", "    initial_cash=100000,  # <PERSON><PERSON><PERSON>黄独家内容\n", "    strategy_params={'model': best_pipeline, 'target_percent':0.98},  # <PERSON><PERSON>ee黄独家内容\n", "    print_log=True,  # 这次打开日志  # Copyright © JayBee黄\n", ")  # Jay<PERSON>ee黄量化模型\n", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "\n", "# ===== 猴子补丁：为 numpy 添加 bool8 和 object 属性 =====\n", "import numpy as np  # <PERSON><PERSON><PERSON>黄独家内容\n", "if not hasattr(np, 'bool8'):  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    np.bool8 = np.bool_  # 使用 numpy 自带的 bool_ 类型  # JayBee黄量化模型\n", "if not hasattr(np, 'object'):  # <PERSON><PERSON><PERSON>黄原创内容\n", "    np.object = object  # 兼容 backtrader_plotting 的引用  # 本代码归JayBee黄所有", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"scrolled": true}, "outputs": [{"ename": "NameError", "evalue": "name 'plot_results' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mplot_results\u001b[49m(ml_cerebro)\n", "\u001b[0;31mNameError\u001b[0m: name 'plot_results' is not defined"]}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "plot_results(ml_cerebro)  # 版权所有: <PERSON><PERSON><PERSON>黄", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "# 比较策略和Buy&Hold", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 60, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-04-11 => BUY signal, pred_ret=0.003732\n", "Current position size: 0, Value: 100000.0\n", "Current position size: 561, Value: 99151.55297853393\n", "Current position size: 561, Value: 93782.77886964721\n", "Current position size: 561, Value: 91331.21160890503\n", "Current position size: 561, Value: 90399.94955446167\n", "Current position size: 561, Value: 87303.22715761108\n", "Current position size: 561, Value: 85687.55297853393\n", "Current position size: 561, Value: 82882.55297853393\n", "Current position size: 561, Value: 84357.97715761108\n", "Current position size: 561, Value: 94147.43400575561\n", "Current position size: 561, Value: 98663.47715761108\n", "Current position size: 561, Value: 97603.1875000183\n", "Current position size: 561, Value: 112054.55297853393\n", "Current position size: 561, Value: 106012.58058168335\n", "Current position size: 561, Value: 104166.89434816284\n", "Current position size: 561, Value: 104178.10818483276\n", "Current position size: 561, Value: 104840.0926361267\n", "Current position size: 561, Value: 106842.85818483276\n", "Current position size: 561, Value: 102943.90989686889\n", "Current position size: 561, Value: 101210.42195131225\n", "Current position size: 561, Value: 99667.67195131225\n", "Current position size: 561, Value: 97704.17195131225\n", "Current position size: 561, Value: 99622.79092409057\n", "Current position size: 561, Value: 102798.05297853393\n", "Current position size: 561, Value: 100800.89434816284\n", "Current position size: 561, Value: 101277.73921205444\n", "Current position size: 561, Value: 102747.56503297729\n", "Current position size: 561, Value: 101339.44955446167\n", "Current position size: 561, Value: 107875.10469057006\n", "Current position size: 561, Value: 104234.21160890503\n", "2024-05-23 => SELL signal, pred_ret=-0.000305\n", "Current position size: 561, Value: 100660.64434816284\n", "2024-05-24 => BUY signal, pred_ret=0.001294\n", "Current position size: 0, Value: 101151.63202410888\n", "Current position size: 553, Value: 101219.95855272826\n", "Current position size: 553, Value: 100910.27990282592\n", "Current position size: 553, Value: 102348.0748399597\n", "Current position size: 553, Value: 101955.4495653015\n", "Current position size: 553, Value: 100965.5748399597\n", "Current position size: 553, Value: 100125.02091539916\n", "Current position size: 553, Value: 100252.20855272826\n", "Current position size: 553, Value: 101878.02990282592\n", "Current position size: 553, Value: 101623.64619005736\n", "Current position size: 553, Value: 99583.0748399597\n", "2024-06-11 => SELL signal, pred_ret=-0.000487\n", "Current position size: 553, Value: 97852.19057787475\n", "2024-06-12 => BUY signal, pred_ret=0.000448\n", "Current position size: 0, Value: 97984.31414523313\n", "2024-06-13 => BUY signal, pred_ret=0.003262\n", "Current position size: 0, Value: 97984.31414523313\n", "Current position size: 526, Value: 93762.712548999\n", "Current position size: 526, Value: 98722.89672258298\n", "Current position size: 526, Value: 97365.81575944822\n", "Current position size: 526, Value: 95635.27929094236\n", "Current position size: 526, Value: 96392.712548999\n", "2024-06-24 => SELL signal, pred_ret=-0.000054\n", "Current position size: 526, Value: 96166.53640153806\n", "2024-06-25 => BUY signal, pred_ret=0.004128\n", "Current position size: 0, Value: 97000.58413116452\n", "2024-06-26 => SELL signal, pred_ret=-0.000974\n", "Current position size: 507, Value: 101864.44393291317\n", "Current position size: 0, Value: 101131.76964108272\n", "2024-06-28 => BUY signal, pred_ret=0.000953\n", "Current position size: 0, Value: 101131.76964108272\n", "Current position size: 500, Value: 105426.2328078918\n", "Current position size: 500, Value: 116126.229756134\n", "Current position size: 500, Value: 123691.23219754024\n", "Current position size: 500, Value: 126256.2346389465\n", "Current position size: 500, Value: 126966.23372341915\n", "Current position size: 500, Value: 131661.22578884882\n", "Current position size: 500, Value: 132126.2373855285\n", "Current position size: 500, Value: 121011.23189236446\n", "Current position size: 500, Value: 124611.23036648556\n", "2024-07-15 => SELL signal, pred_ret=-0.000414\n", "Current position size: 500, Value: 126816.23219754024\n", "2024-07-16 => BUY signal, pred_ret=0.001761\n", "Current position size: 0, Value: 127998.60128323358\n", "Current position size: 488, Value: 125786.60673027947\n", "2024-07-18 => SELL signal, pred_ret=-0.003791\n", "Current position size: 488, Value: 126142.84464531853\n", "2024-07-19 => BUY signal, pred_ret=0.003238\n", "Current position size: 0, Value: 125294.82633718864\n", "2024-07-22 => BUY signal, pred_ret=0.000367\n", "Current position size: 0, Value: 125294.82633718864\n", "Current position size: 488, Value: 121623.284538507\n", "Current position size: 488, Value: 106792.96483635856\n", "Current position size: 488, Value: 108871.8421556945\n", "Current position size: 488, Value: 108652.24364495231\n", "Current position size: 488, Value: 114654.64513421012\n", "Current position size: 488, Value: 110028.399772882\n", "2024-07-31 => SELL signal, pred_ret=-0.004015\n", "Current position size: 488, Value: 114640.00572991325\n", "Current position size: 0, Value: 112367.07502590935\n", "2024-08-02 => BUY signal, pred_ret=0.001776\n", "Current position size: 0, Value: 112367.07502590935\n", "Current position size: 530, Value: 119482.18386618035\n", "Current position size: 530, Value: 120414.9809548034\n", "Current position size: 530, Value: 115708.57836691277\n", "Current position size: 530, Value: 119460.97933737175\n", "Current position size: 530, Value: 120075.78127828972\n", "2024-08-12 => SELL signal, pred_ret=-0.000030\n", "Current position size: 530, Value: 118745.48418966668\n", "Current position size: 0, Value: 119133.21932461539\n", "Current position size: 0, Value: 119133.21932461539\n", "Current position size: 0, Value: 119133.21932461539\n", "Current position size: 0, Value: 119133.21932461539\n", "Current position size: 0, Value: 119133.21932461539\n", "Current position size: 0, Value: 119133.21932461539\n", "Current position size: 0, Value: 119133.21932461539\n", "Current position size: 0, Value: 119133.21932461539\n", "2024-08-23 => BUY signal, pred_ret=0.000709\n", "Current position size: 0, Value: 119133.21932461539\n", "Current position size: 529, Value: 116060.36767625112\n", "Current position size: 529, Value: 113944.36767625112\n", "Current position size: 529, Value: 112114.02412461537\n", "Current position size: 529, Value: 112394.39347886342\n", "Current position size: 529, Value: 116536.46444749135\n", "2024-09-03 => SELL signal, pred_ret=-0.000131\n", "Current position size: 529, Value: 114679.67735337514\n", "2024-09-04 => BUY signal, pred_ret=0.000537\n", "Current position size: 0, Value: 114552.41646525258\n", "Current position size: 511, Value: 117826.11377976979\n", "Current position size: 511, Value: 107892.2725322112\n", "Current position size: 511, Value: 110723.21689866627\n", "Current position size: 511, Value: 115782.11377976979\n", "Current position size: 511, Value: 116783.67721055592\n", "Current position size: 511, Value: 117642.15346788014\n", "2024-09-13 => SELL signal, pred_ret=-0.004807\n", "Current position size: 511, Value: 117887.4312846526\n", "Current position size: 0, Value: 117238.84952332753\n", "2024-09-17 => BUY signal, pred_ret=0.001268\n", "Current position size: 0, Value: 117238.84952332753\n", "Current position size: 504, Value: 115641.09927279041\n", "2024-09-19 => SELL signal, pred_ret=-0.000977\n", "Current position size: 504, Value: 124067.97988802478\n", "2024-09-20 => BUY signal, pred_ret=0.003384\n", "Current position size: 0, Value: 122711.48208204334\n", "Current position size: 504, Value: 126288.54113411852\n", "Current position size: 504, Value: 128440.62328743884\n", "Current position size: 504, Value: 129826.61559700915\n", "Current position size: 504, Value: 128415.4217493529\n", "2024-09-27 => SELL signal, pred_ret=-0.002082\n", "Current position size: 504, Value: 131560.37682747788\n", "Current position size: 0, Value: 130688.97447645251\n", "Current position size: 0, Value: 130688.97447645251\n", "2024-10-02 => BUY signal, pred_ret=0.001403\n", "Current position size: 0, Value: 130688.97447645251\n", "Current position size: 514, Value: 128574.11013701768\n", "Current position size: 514, Value: 133415.98919585557\n", "Current position size: 514, Value: 128661.48919585557\n", "Current position size: 514, Value: 130547.86825469346\n", "2024-10-09 => SELL signal, pred_ret=-0.000855\n", "Current position size: 514, Value: 128774.56982329697\n", "Current position size: 0, Value: 129015.24236106552\n", "2024-10-11 => BUY signal, pred_ret=0.007434\n", "Current position size: 0, Value: 129015.24236106552\n", "2024-10-14 => SELL signal, pred_ret=-0.000656\n", "Current position size: 580, Value: 128295.93725022567\n", "2024-10-15 => BUY signal, pred_ret=0.002822\n", "Current position size: 0, Value: 128632.3551433531\n", "Current position size: 574, Value: 128436.36740129987\n", "2024-10-17 => SELL signal, pred_ret=-0.001323\n", "Current position size: 574, Value: 128183.80599993268\n", "Current position size: 0, Value: 127925.13136018049\n", "2024-10-21 => BUY signal, pred_ret=0.000061\n", "Current position size: 0, Value: 127925.13136018049\n", "Current position size: 572, Value: 128149.72353630353\n", "Current position size: 572, Value: 125678.6793468504\n", "2024-10-24 => SELL signal, pred_ret=-0.001403\n", "Current position size: 572, Value: 152465.44912224103\n", "2024-10-25 => BUY signal, pred_ret=0.014657\n", "Current position size: 0, Value: 149733.5992984129\n", "Current position size: 545, Value: 145477.12737067853\n", "Current position size: 545, Value: 143847.56606086408\n", "Current position size: 545, Value: 142773.91539558087\n", "Current position size: 545, Value: 138577.42537482892\n", "Current position size: 545, Value: 138103.2697199217\n", "Current position size: 545, Value: 134756.9700525633\n", "Current position size: 545, Value: 139443.9733789793\n", "2024-11-06 => SELL signal, pred_ret=-0.006555\n", "Current position size: 545, Value: 159658.0213831297\n", "Current position size: 0, Value: 159669.56222382793\n", "2024-11-08 => BUY signal, pred_ret=0.010878\n", "Current position size: 0, Value: 159669.56222382793\n", "2024-11-11 => BUY signal, pred_ret=0.013514\n", "Current position size: 0, Value: 159669.56222382793\n", "Current position size: 447, Value: 153124.23509819317\n", "Current position size: 447, Value: 153906.48509819317\n", "Current position size: 447, Value: 145386.66618950176\n", "2024-11-15 => SELL signal, pred_ret=-0.000485\n", "Current position size: 447, Value: 149651.05000908184\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 0, Value: 158420.8904094053\n", "2024-12-06 => BUY signal, pred_ret=0.001616\n", "Current position size: 0, Value: 158420.8904094053\n", "2024-12-09 => BUY signal, pred_ret=0.000935\n", "Current position size: 0, Value: 158420.8904094053\n", "Current position size: 398, Value: 161552.06290064068\n", "Current position size: 398, Value: 171016.50241480084\n", "Current position size: 398, Value: 168361.84921655865\n", "Current position size: 398, Value: 175577.59115991803\n", "Current position size: 398, Value: 186240.00241480084\n", "Current position size: 398, Value: 192942.3209572813\n", "2024-12-18 => SELL signal, pred_ret=-0.009847\n", "Current position size: 398, Value: 177129.7887307188\n", "Current position size: 0, Value: 181606.56038877543\n", "Current position size: 0, Value: 181606.56038877543\n", "2024-12-23 => BUY signal, pred_ret=0.000208\n", "Current position size: 0, Value: 181606.56038877543\n", "2024-12-24 => SELL signal, pred_ret=-0.009002\n", "Current position size: 413, Value: 192300.80505789776\n", "Current position size: 0, Value: 193277.50664298685\n", "Current position size: 0, Value: 193277.50664298685\n", "Current position size: 0, Value: 193277.50664298685\n", "Current position size: 0, Value: 193277.50664298685\n", "2025-01-02 => BUY signal, pred_ret=0.006174\n", "Current position size: 0, Value: 193277.50664298685\n", "2025-01-03 => SELL signal, pred_ret=-0.006172\n", "Current position size: 499, Value: 207513.20890358865\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "Current position size: 0, Value: 213644.3519205442\n", "2025-02-11 => BUY signal, pred_ret=0.002364\n", "Current position size: 0, Value: 213644.3519205442\n", "2025-02-12 => SELL signal, pred_ret=-0.004850\n", "Current position size: 637, Value: 217587.39295451637\n", "Current position size: 0, Value: 222743.93358381325\n", "Current position size: 0, Value: 222743.93358381325\n", "Current position size: 0, Value: 222743.93358381325\n", "Current position size: 0, Value: 222743.93358381325\n", "Current position size: 0, Value: 222743.93358381325\n", "Current position size: 0, Value: 222743.93358381325\n", "Current position size: 0, Value: 222743.93358381325\n", "Current position size: 0, Value: 222743.93358381325\n", "2025-02-26 => BUY signal, pred_ret=0.009675\n", "Current position size: 0, Value: 222743.93358381325\n", "Current position size: 750, Value: 215580.53248975807\n", "Current position size: 750, Value: 223853.03157423073\n", "Current position size: 750, Value: 217605.51875684792\n", "2025-03-04 => SELL signal, pred_ret=-0.001048\n", "Current position size: 750, Value: 208148.02974317604\n", "Current position size: 0, Value: 208565.8808952146\n", "Current position size: 0, Value: 208565.8808952146\n", "Current position size: 0, Value: 208565.8808952146\n", "2025-03-10 => BUY signal, pred_ret=0.018845\n", "Current position size: 0, Value: 208565.8808952146\n", "2024-03-14 00:00:00 [买入] 执行买入并持有策略: 价格=162.50, 数量=584\n", "2024-03-15 00:00:00 [成交] 买单执行: 价格=163.21, 数量=584\n", "2025-03-10 00:00:00 [回测结束] Buy & Hold 策略最终市值: 134325.64\n", "2025-03-10 00:00:00 [回测结束] 总收益率: 34.33%\n"]}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "results = ml_cerebro.run()  # cerebro.run() 返回一个列表，每个元素是一个策略实例  # 版权所有: Jay<PERSON><PERSON>黄\n", "ml_strategy_instance = results[0]  # 如果你只有一个策略，就取第一个  # JayBee黄原创内容\n", "\n", "results = bh_cerebro.run()  # Copyright © JayBee黄\n", "bh_strategy_instance = results[0]  # Jay<PERSON>ee黄独家内容", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "import matplotlib.pyplot as plt  # Copyright © JayBee黄\n", "plt.figure(figsize=(12, 6))  # <PERSON><PERSON><PERSON>黄独家内容\n", "plt.plot(ml_strategy_instance.value_history_dates, ml_strategy_instance.value_history_values, label='MLP')  # JayBee黄 - 量化交易研究\n", "plt.plot(bh_strategy_instance.value_history_dates, bh_strategy_instance.value_history_values, label='买入并持有')  # JayBee黄 - 量化交易研究\n", "plt.xlabel('时间')  # 版权所有: Jay<PERSON><PERSON>黄\n", "plt.ylabel('资产净值')  # 版权所有: JayBee黄\n", "plt.title('回报曲线对比')  # JayBee黄版权所有，未经授权禁止复制\n", "plt.legend()  # <PERSON><PERSON><PERSON>黄原创内容\n", "plt.show()  # <PERSON><PERSON><PERSON>黄授权使用\n", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# JayBee黄版权所有，未经授权禁止复制\n"]}], "metadata": {"kernelspec": {"display_name": "Python (ta_env)", "language": "python", "name": "ta_arm"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "watermark": "JayBee黄版权所有，未经授权禁止复制", "watermark_version": "v3 - 每行防伪"}, "nbformat": 4, "nbformat_minor": 4}