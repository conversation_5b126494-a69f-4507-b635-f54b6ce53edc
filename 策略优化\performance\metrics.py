"""
性能指标计算模块

实现各种量化交易性能指标的计算。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass

from ..config import get_config
from ..utils.logger import get_logger
from ..utils.exceptions import ValidationError


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    # 收益指标
    total_return: float
    annualized_return: float
    cumulative_return: float
    
    # 风险指标
    volatility: float
    downside_deviation: float
    max_drawdown: float
    max_drawdown_duration: int
    
    # 风险调整收益指标
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    information_ratio: float
    
    # 交易指标
    total_trades: int
    win_rate: float
    profit_factor: float
    avg_win: float
    avg_loss: float
    largest_win: float
    largest_loss: float
    
    # 其他指标
    beta: float
    alpha: float
    tracking_error: float
    var_95: float  # 95% VaR
    cvar_95: float  # 95% CVaR
    
    def to_dict(self) -> Dict[str, float]:
        """转换为字典"""
        return {
            'Total Return': self.total_return,
            'Annualized Return': self.annualized_return,
            'Volatility': self.volatility,
            'Sharpe Ratio': self.sharpe_ratio,
            'Sortino Ratio': self.sortino_ratio,
            'Calmar Ratio': self.calmar_ratio,
            'Max Drawdown': self.max_drawdown,
            'Win Rate': self.win_rate,
            'Profit Factor': self.profit_factor,
            'Total Trades': self.total_trades,
            'VaR 95%': self.var_95,
            'CVaR 95%': self.cvar_95
        }


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self, risk_free_rate: float = 0.0, 
                 trading_days_per_year: int = 252):
        """
        初始化性能分析器
        
        Args:
            risk_free_rate: 无风险收益率
            trading_days_per_year: 年交易日数
        """
        self.logger = get_logger("performance.PerformanceAnalyzer")
        self.config = get_config()
        
        # 从配置获取参数
        perf_config = self.config.get('performance', {})
        self.risk_free_rate = risk_free_rate or perf_config.get('risk_free_rate', 0.0)
        self.trading_days_per_year = trading_days_per_year or perf_config.get('trading_days_per_year', 252)
        
        self.logger.info(f"性能分析器初始化: 无风险利率={self.risk_free_rate:.2%}, "
                        f"年交易日={self.trading_days_per_year}")
    
    def calculate_metrics(self, returns: pd.Series, 
                         benchmark_returns: Optional[pd.Series] = None,
                         trades: Optional[List[Dict[str, Any]]] = None) -> PerformanceMetrics:
        """
        计算完整的性能指标
        
        Args:
            returns: 策略收益率序列
            benchmark_returns: 基准收益率序列
            trades: 交易记录列表
            
        Returns:
            性能指标对象
        """
        try:
            self.logger.info("开始计算性能指标")
            
            # 验证数据
            self._validate_returns(returns)
            
            # 基础收益指标
            total_return = self._calculate_total_return(returns)
            annualized_return = self._calculate_annualized_return(returns)
            cumulative_return = self._calculate_cumulative_return(returns)
            
            # 风险指标
            volatility = self._calculate_volatility(returns)
            downside_deviation = self._calculate_downside_deviation(returns)
            max_drawdown, max_dd_duration = self._calculate_max_drawdown(returns)
            
            # 风险调整收益指标
            sharpe_ratio = self._calculate_sharpe_ratio(returns, volatility)
            sortino_ratio = self._calculate_sortino_ratio(returns, downside_deviation)
            calmar_ratio = self._calculate_calmar_ratio(annualized_return, max_drawdown)
            
            # 相对基准的指标
            if benchmark_returns is not None:
                beta, alpha = self._calculate_beta_alpha(returns, benchmark_returns)
                information_ratio = self._calculate_information_ratio(returns, benchmark_returns)
                tracking_error = self._calculate_tracking_error(returns, benchmark_returns)
            else:
                beta = alpha = information_ratio = tracking_error = 0.0
            
            # 交易指标
            if trades:
                trade_metrics = self._calculate_trade_metrics(trades)
            else:
                trade_metrics = {
                    'total_trades': 0,
                    'win_rate': 0.0,
                    'profit_factor': 0.0,
                    'avg_win': 0.0,
                    'avg_loss': 0.0,
                    'largest_win': 0.0,
                    'largest_loss': 0.0
                }
            
            # 风险价值指标
            var_95 = self._calculate_var(returns, 0.95)
            cvar_95 = self._calculate_cvar(returns, 0.95)
            
            # 创建性能指标对象
            metrics = PerformanceMetrics(
                total_return=total_return,
                annualized_return=annualized_return,
                cumulative_return=cumulative_return,
                volatility=volatility,
                downside_deviation=downside_deviation,
                max_drawdown=max_drawdown,
                max_drawdown_duration=max_dd_duration,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                calmar_ratio=calmar_ratio,
                information_ratio=information_ratio,
                beta=beta,
                alpha=alpha,
                tracking_error=tracking_error,
                var_95=var_95,
                cvar_95=cvar_95,
                **trade_metrics
            )
            
            self.logger.info("性能指标计算完成")
            return metrics
            
        except Exception as e:
            self.logger.error(f"计算性能指标失败: {e}")
            raise
    
    def _validate_returns(self, returns: pd.Series) -> None:
        """验证收益率数据"""
        if returns.empty:
            raise ValidationError("收益率序列为空", "returns", None, "non-empty Series")
        
        if not isinstance(returns, pd.Series):
            raise ValidationError("收益率必须是pandas Series", "returns", type(returns), "pandas.Series")
    
    def _calculate_total_return(self, returns: pd.Series) -> float:
        """计算总收益率"""
        return (1 + returns).prod() - 1
    
    def _calculate_annualized_return(self, returns: pd.Series) -> float:
        """计算年化收益率"""
        if len(returns) == 0:
            return 0.0
        
        total_return = self._calculate_total_return(returns)
        periods = len(returns) / self.trading_days_per_year
        
        if periods <= 0:
            return 0.0
        
        return (1 + total_return) ** (1 / periods) - 1
    
    def _calculate_cumulative_return(self, returns: pd.Series) -> float:
        """计算累计收益率"""
        return (1 + returns).cumprod().iloc[-1] - 1 if not returns.empty else 0.0
    
    def _calculate_volatility(self, returns: pd.Series) -> float:
        """计算年化波动率"""
        if len(returns) <= 1:
            return 0.0
        return returns.std() * np.sqrt(self.trading_days_per_year)
    
    def _calculate_downside_deviation(self, returns: pd.Series) -> float:
        """计算下行偏差"""
        downside_returns = returns[returns < self.risk_free_rate / self.trading_days_per_year]
        if len(downside_returns) <= 1:
            return 0.0
        return downside_returns.std() * np.sqrt(self.trading_days_per_year)
    
    def _calculate_max_drawdown(self, returns: pd.Series) -> Tuple[float, int]:
        """计算最大回撤和持续期"""
        if returns.empty:
            return 0.0, 0
        
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        
        max_drawdown = drawdown.min()
        
        # 计算最大回撤持续期
        max_dd_duration = 0
        current_duration = 0
        
        for dd in drawdown:
            if dd < 0:
                current_duration += 1
                max_dd_duration = max(max_dd_duration, current_duration)
            else:
                current_duration = 0
        
        return max_drawdown, max_dd_duration
    
    def _calculate_sharpe_ratio(self, returns: pd.Series, volatility: float) -> float:
        """计算夏普比率"""
        if volatility == 0:
            return 0.0
        
        excess_return = returns.mean() - self.risk_free_rate / self.trading_days_per_year
        return excess_return * np.sqrt(self.trading_days_per_year) / volatility
    
    def _calculate_sortino_ratio(self, returns: pd.Series, downside_deviation: float) -> float:
        """计算索提诺比率"""
        if downside_deviation == 0:
            return 0.0
        
        excess_return = returns.mean() - self.risk_free_rate / self.trading_days_per_year
        return excess_return * np.sqrt(self.trading_days_per_year) / downside_deviation
    
    def _calculate_calmar_ratio(self, annualized_return: float, max_drawdown: float) -> float:
        """计算卡玛比率"""
        if max_drawdown == 0:
            return 0.0
        return annualized_return / abs(max_drawdown)
    
    def _calculate_beta_alpha(self, returns: pd.Series, 
                            benchmark_returns: pd.Series) -> Tuple[float, float]:
        """计算贝塔和阿尔法"""
        if len(returns) != len(benchmark_returns) or len(returns) <= 1:
            return 0.0, 0.0
        
        # 对齐数据
        aligned_data = pd.DataFrame({
            'strategy': returns,
            'benchmark': benchmark_returns
        }).dropna()
        
        if len(aligned_data) <= 1:
            return 0.0, 0.0
        
        # 计算贝塔
        covariance = aligned_data['strategy'].cov(aligned_data['benchmark'])
        benchmark_variance = aligned_data['benchmark'].var()
        
        if benchmark_variance == 0:
            beta = 0.0
        else:
            beta = covariance / benchmark_variance
        
        # 计算阿尔法
        strategy_mean = aligned_data['strategy'].mean() * self.trading_days_per_year
        benchmark_mean = aligned_data['benchmark'].mean() * self.trading_days_per_year
        alpha = strategy_mean - (self.risk_free_rate + beta * (benchmark_mean - self.risk_free_rate))
        
        return beta, alpha
    
    def _calculate_information_ratio(self, returns: pd.Series, 
                                   benchmark_returns: pd.Series) -> float:
        """计算信息比率"""
        if len(returns) != len(benchmark_returns):
            return 0.0
        
        excess_returns = returns - benchmark_returns
        tracking_error = excess_returns.std() * np.sqrt(self.trading_days_per_year)
        
        if tracking_error == 0:
            return 0.0
        
        return excess_returns.mean() * np.sqrt(self.trading_days_per_year) / tracking_error
    
    def _calculate_tracking_error(self, returns: pd.Series, 
                                benchmark_returns: pd.Series) -> float:
        """计算跟踪误差"""
        if len(returns) != len(benchmark_returns):
            return 0.0
        
        excess_returns = returns - benchmark_returns
        return excess_returns.std() * np.sqrt(self.trading_days_per_year)
    
    def _calculate_var(self, returns: pd.Series, confidence_level: float) -> float:
        """计算风险价值 (VaR)"""
        if returns.empty:
            return 0.0
        return returns.quantile(1 - confidence_level)
    
    def _calculate_cvar(self, returns: pd.Series, confidence_level: float) -> float:
        """计算条件风险价值 (CVaR)"""
        if returns.empty:
            return 0.0
        
        var = self._calculate_var(returns, confidence_level)
        return returns[returns <= var].mean()
    
    def _calculate_trade_metrics(self, trades: List[Dict[str, Any]]) -> Dict[str, float]:
        """计算交易相关指标"""
        if not trades:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'profit_factor': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'largest_win': 0.0,
                'largest_loss': 0.0
            }
        
        # 简化的交易分析（实际需要更复杂的配对逻辑）
        profits = []
        losses = []
        
        # 这里需要实现更复杂的交易配对逻辑
        # 暂时使用简化版本
        for i in range(0, len(trades) - 1, 2):
            if i + 1 < len(trades):
                buy_trade = trades[i] if trades[i]['side'] == 'buy' else trades[i + 1]
                sell_trade = trades[i + 1] if trades[i + 1]['side'] == 'sell' else trades[i]
                
                if buy_trade['side'] == 'buy' and sell_trade['side'] == 'sell':
                    pnl = (sell_trade['price'] - buy_trade['price']) * buy_trade['quantity']
                    pnl -= buy_trade.get('commission', 0) + sell_trade.get('commission', 0)
                    pnl -= sell_trade.get('stamp_duty', 0)
                    
                    if pnl > 0:
                        profits.append(pnl)
                    else:
                        losses.append(abs(pnl))
        
        total_trades = len(profits) + len(losses)
        win_rate = len(profits) / total_trades if total_trades > 0 else 0
        
        total_profit = sum(profits) if profits else 0
        total_loss = sum(losses) if losses else 0
        profit_factor = total_profit / total_loss if total_loss > 0 else float('inf') if total_profit > 0 else 0
        
        avg_win = np.mean(profits) if profits else 0
        avg_loss = np.mean(losses) if losses else 0
        largest_win = max(profits) if profits else 0
        largest_loss = max(losses) if losses else 0
        
        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'largest_win': largest_win,
            'largest_loss': largest_loss
        }
