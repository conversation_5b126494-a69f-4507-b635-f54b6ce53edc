# ML量化策略系统

专注于机器学习驱动的量化交易策略开发和优化的精简系统。

## 🎯 核心功能

- **随机K线生成**: 生成用于机器学习训练的模拟市场数据
- **ML策略优化**: 使用机器学习算法自动发现和优化交易策略
- **回测验证**: 快速验证ML策略的有效性
- **性能评估**: 全面的策略性能分析
- **策略文件生成**: 自动生成独立的策略文件，便于使用和管理

## 📁 项目结构

```
ML量化策略/
├── mlquant/                    # 核心包
│   ├── __init__.py            # 包初始化
│   ├── data/                  # 数据处理模块
│   │   ├── __init__.py
│   │   └── generator.py       # 随机数据生成器
│   ├── strategies/            # 交易策略模块
│   │   ├── __init__.py
│   │   ├── base.py           # 策略基类
│   │   ├── dual_ma.py        # 双均线策略
│   │   ├── rsi.py            # RSI策略
│   │   └── bollinger.py      # 布林带策略
│   ├── backtest/             # 回测模块
│   │   ├── __init__.py
│   │   ├── engine.py         # 回测引擎
│   │   └── result.py         # 回测结果
│   ├── performance/          # 性能分析模块
│   │   ├── __init__.py
│   │   ├── analyzer.py       # 性能分析器
│   │   ├── metrics.py        # 性能指标
│   │   └── report.py         # 性能报告
│   ├── optimization/         # 优化模块
│   │   ├── __init__.py
│   │   ├── ml_optimizer.py   # ML优化器
│   │   ├── genetic.py        # 遗传算法
│   │   ├── grid_search.py    # 网格搜索
│   │   └── random_search.py  # 随机搜索
│   ├── generators/           # 生成器模块
│   │   ├── __init__.py
│   │   ├── strategy_generator.py  # 策略文件生成器
│   │   └── strategy_manager.py    # 策略管理器
│   └── utils/                # 工具模块
│       ├── __init__.py
│       ├── config.py         # 配置管理
│       ├── logger.py         # 日志记录
│       └── helpers.py        # 辅助函数
├── config/                   # 配置文件
│   └── config.yaml
├── examples/                 # 示例程序
│   ├── demo.py              # 完整演示程序
│   ├── main.py              # 原主程序
│   └── *.py                 # 其他示例
├── generated_strategies/     # 生成的策略文件夹
│   ├── __init__.py          # Python包初始化
│   ├── *_strategy.py        # 具体策略文件
│   ├── strategies_metadata.json  # 策略元数据
│   └── strategy_index.py    # 策略索引
├── tests/                   # 测试文件
├── docs/                    # 文档
├── run.py                   # 主程序入口
├── requirements.txt         # 依赖包
└── README.md               # 项目说明
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行完整演示
```bash
python run.py demo
```

### 3. 运行策略优化
```bash
# 优化双均线策略
python run.py optimize dual_ma --days 1000

# 优化RSI策略
python run.py optimize rsi --days 500

# 查看已生成的策略
python run.py list
```

### 4. 使用生成的策略
```python
# 导入生成的策略
import sys
sys.path.append('generated_strategies')
from simple_dual_ma_strategy import DualMAStrategy

# 使用策略
strategy = DualMAStrategy()
data = pd.read_csv('your_data.csv', index_col=0, parse_dates=True)
signals = strategy.generate_signals(data)
```

### 5. 编程接口使用
```python
from mlquant import (
    RandomDataGenerator,
    MLStrategyOptimizer,
    BacktestEngine,
    PerformanceAnalyzer
)

# 生成数据
generator = RandomDataGenerator()
data = generator.generate(days=1000)

# 优化策略
optimizer = MLStrategyOptimizer()
best_strategy = optimizer.optimize(data, 'dual_ma')

# 回测验证
engine = BacktestEngine()
signals = optimizer.strategies['dual_ma'].generate_signals(data, **best_strategy.params)
result = engine.run(data, signals, best_strategy.name)

# 性能分析
analyzer = PerformanceAnalyzer()
report = analyzer.analyze(result)
report.print_report()
```

## ✨ 核心特性

### 🏗️ 模块化架构
- **清晰分层**: 按功能模块组织代码结构
- **松耦合设计**: 各模块独立，易于扩展和维护
- **标准接口**: 统一的API设计，便于集成
- **可插拔组件**: 支持自定义策略和优化算法

### 🧠 机器学习优化
- **遗传算法**: 自动寻找最优策略参数
- **多种算法**: 支持网格搜索、随机搜索等
- **多策略支持**: 双均线、RSI、布林带等
- **性能导向**: 以夏普比率等指标为优化目标

### 📁 策略文件管理
- **自动生成**: ML优化后自动生成独立策略文件
- **统一存放**: 所有策略存放在 `generated_strategies/` 文件夹
- **完整信息**: 包含参数、性能数据和使用说明
- **即插即用**: 生成的策略可直接在其他项目中使用

### 🔄 完整工作流
1. **数据生成** → 随机K线数据用于训练
2. **ML优化** → 遗传算法寻找最优参数
3. **策略生成** → 自动生成独立策略文件
4. **回测验证** → 在测试数据上验证性能
5. **策略管理** → 统一管理和使用策略

## 📊 支持的功能

### ML算法
- ✅ 遗传算法优化
- ✅ 网格搜索
- ✅ 随机搜索
- 🔄 贝叶斯优化 (可选)
- 🔄 强化学习接口 (预留)

### 策略类型
- ✅ 双均线策略
- ✅ RSI策略
- ✅ 布林带策略
- 🔄 MACD策略 (可扩展)
- 🔄 自定义策略 (可扩展)

### 输出结果
- 📄 独立策略文件 (Python代码)
- 📊 回测性能报告
- 📈 资金曲线数据
- 📋 策略管理索引
- 📝 详细使用说明

## 🎯 适用场景

- **策略研发**: 快速发现和验证新的交易策略
- **参数优化**: 自动寻找策略的最优参数组合
- **策略管理**: 统一管理和复用优化后的策略
- **教学研究**: 理解机器学习在量化交易中的应用

## 🏗️ 模块化架构说明

### 核心模块

#### 📊 数据模块 (`mlquant.data`)
- **RandomDataGenerator**: 随机K线数据生成器
- 支持自定义波动率、趋势等参数
- 为ML训练提供高质量的模拟数据

#### 🎯 策略模块 (`mlquant.strategies`)
- **BaseStrategy**: 策略基类，定义统一接口
- **DualMAStrategy**: 双均线交叉策略
- **RSIStrategy**: RSI超买超卖策略
- **BollingerStrategy**: 布林带突破策略
- 支持自定义策略扩展

#### 🔬 回测模块 (`mlquant.backtest`)
- **BacktestEngine**: 高性能回测引擎
- **BacktestResult**: 标准化回测结果
- 支持手续费、滑点等真实交易成本

#### 📈 性能分析模块 (`mlquant.performance`)
- **PerformanceAnalyzer**: 综合性能分析器
- **PerformanceMetrics**: 标准性能指标计算
- **PerformanceReport**: 专业性能报告生成

#### 🤖 优化模块 (`mlquant.optimization`)
- **MLStrategyOptimizer**: 主优化器
- **GeneticOptimizer**: 遗传算法优化
- **GridSearchOptimizer**: 网格搜索优化
- **RandomSearchOptimizer**: 随机搜索优化

#### 🏭 生成器模块 (`mlquant.generators`)
- **StrategyFileGenerator**: 策略文件自动生成
- **StrategyManager**: 策略统一管理
- 支持策略版本控制和元数据管理

#### 🛠️ 工具模块 (`mlquant.utils`)
- **ConfigManager**: 配置文件管理
- **Logger**: 统一日志记录
- **Helpers**: 通用辅助函数

## 📁 策略文件夹说明

### 文件夹结构
```
generated_strategies/
├── __init__.py                    # Python包初始化文件
├── simple_dual_ma_strategy.py     # 双均线策略文件
├── rsi_strategy.py               # RSI策略文件 (如果生成)
├── strategies_metadata.json      # 策略元数据
├── strategy_index.py             # 策略索引文件
└── README.md                     # 策略使用说明
```

### 策略文件特点
- **独立完整**: 每个策略文件都是独立的Python模块
- **参数保存**: 包含ML优化后的最佳参数
- **性能数据**: 包含历史回测的性能指标
- **使用说明**: 详细的代码注释和使用示例
- **即插即用**: 可直接复制到其他项目使用

### 使用方法
```python
# 方法1: 直接导入
import sys
sys.path.append('generated_strategies')
from simple_dual_ma_strategy import DualMAStrategy

# 方法2: 复制文件夹到目标项目
# 将整个 generated_strategies 文件夹复制到目标项目
from generated_strategies.simple_dual_ma_strategy import DualMAStrategy

# 创建策略实例
strategy = DualMAStrategy()

# 查看策略信息
print(strategy.get_strategy_info())
print(strategy.backtest_summary())

# 使用策略
signals = strategy.generate_signals(your_data)
```

### 策略管理
```python
from strategy_manager import StrategyManager

# 创建管理器
manager = StrategyManager()

# 查看所有策略
strategies_df = manager.list_strategies()
print(strategies_df)

# 查找最佳策略
best_strategies = manager.find_best_strategies('sharpe_ratio', top_n=3)
print(f"最佳策略: {best_strategies}")

# 导出策略摘要
manager.export_strategy_summary()
```
