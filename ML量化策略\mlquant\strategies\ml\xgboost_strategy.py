"""
基于XGBoost的交易策略

XGBoost是一种高效的梯度提升算法，在许多机器学习竞赛中表现优异。
该策略利用XGBoost的强大预测能力来识别市场模式并生成交易信号。

主要特点：
- 高预测准确性
- 内置正则化，减少过拟合
- 支持特征重要性分析
- 训练速度快，支持并行计算
- 对缺失值有良好的处理能力
- 支持早停机制
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Any, Optional, List
import warnings

try:
    import xgboost as xgb
    from sklearn.model_selection import train_test_split
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import accuracy_score, classification_report
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    warnings.warn("xgboost not available. XGBoostStrategy will not work.")

from ..base import BaseStrategy


class XGBoostStrategy(BaseStrategy):
    """
    基于XGBoost的交易策略
    
    使用XGBoost梯度提升算法来预测价格走势，
    具有高预测准确性和良好的泛化能力。
    """
    
    def __init__(self, name: str = "XGBoostStrategy"):
        """
        初始化XGBoost策略
        
        Args:
            name: 策略名称
        """
        super().__init__(name)
        self.model = None
        self.scaler = StandardScaler()
        self.feature_columns = []
        self.feature_importance = None
        self.model_trained = False
        self.eval_results = {}
        
        if not XGBOOST_AVAILABLE:
            raise ImportError("xgboost is required for XGBoostStrategy")
    
    def prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        准备机器学习特征（针对XGBoost优化）
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            包含特征的DataFrame
        """
        df = data.copy()
        
        # 基础价格特征
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        df['price_change'] = df['close'] - df['open']
        df['high_low_ratio'] = df['high'] / df['low']
        df['open_close_ratio'] = df['open'] / df['close']
        df['volume_change'] = df['volume'].pct_change()
        
        # 价格动量特征
        for period in [3, 5, 10, 20]:
            df[f'momentum_{period}'] = df['close'] / df['close'].shift(period) - 1
            df[f'volume_momentum_{period}'] = df['volume'] / df['volume'].shift(period) - 1
        
        # 移动平均线特征
        for window in [5, 10, 20, 50, 100]:
            df[f'sma_{window}'] = df['close'].rolling(window).mean()
            df[f'ema_{window}'] = df['close'].ewm(span=window).mean()
            df[f'price_sma_{window}_ratio'] = df['close'] / df[f'sma_{window}']
            df[f'sma_{window}_slope'] = df[f'sma_{window}'].diff(3)
        
        # 移动平均线交叉和趋势特征
        df['sma_5_10_cross'] = (df['sma_5'] > df['sma_10']).astype(int)
        df['sma_10_20_cross'] = (df['sma_10'] > df['sma_20']).astype(int)
        df['sma_20_50_cross'] = (df['sma_20'] > df['sma_50']).astype(int)
        df['trend_strength'] = (df['sma_5'] - df['sma_50']) / df['sma_50']
        
        # RSI特征
        delta = df['close'].diff()
        up = delta.clip(lower=0)
        down = -1 * delta.clip(upper=0)
        
        for period in [14, 21]:
            ema_up = up.ewm(com=period-1, adjust=False).mean()
            ema_down = down.ewm(com=period-1, adjust=False).mean()
            rs = ema_up / ema_down
            df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
            df[f'rsi_{period}_oversold'] = (df[f'rsi_{period}'] < 30).astype(int)
            df[f'rsi_{period}_overbought'] = (df[f'rsi_{period}'] > 70).astype(int)
        
        # 布林带特征
        for window in [20, 50]:
            bb_middle = df['close'].rolling(window).mean()
            bb_std = df['close'].rolling(window).std()
            df[f'bb_upper_{window}'] = bb_middle + (bb_std * 2)
            df[f'bb_lower_{window}'] = bb_middle - (bb_std * 2)
            df[f'bb_position_{window}'] = (df['close'] - df[f'bb_lower_{window}']) / (df[f'bb_upper_{window}'] - df[f'bb_lower_{window}'])
            df[f'bb_squeeze_{window}'] = bb_std / bb_middle
        
        # MACD特征
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema_12 - ema_26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        df['macd_cross'] = (df['macd'] > df['macd_signal']).astype(int)
        
        # 波动率特征
        for window in [5, 10, 20, 50]:
            df[f'volatility_{window}'] = df['returns'].rolling(window).std()
            df[f'volatility_{window}_rank'] = df[f'volatility_{window}'].rolling(100).rank(pct=True)
        
        # 成交量特征
        for window in [5, 10, 20]:
            df[f'volume_sma_{window}'] = df['volume'].rolling(window).mean()
            df[f'volume_ratio_{window}'] = df['volume'] / df[f'volume_sma_{window}']
            df[f'volume_std_{window}'] = df['volume'].rolling(window).std()
        
        # 价格位置和排名特征
        for window in [20, 50, 100]:
            df[f'price_rank_{window}'] = df['close'].rolling(window).rank(pct=True)
            df[f'high_rank_{window}'] = df['high'].rolling(window).rank(pct=True)
            df[f'low_rank_{window}'] = df['low'].rolling(window).rank(pct=True)
            df[f'volume_rank_{window}'] = df['volume'].rolling(window).rank(pct=True)
        
        # 滞后特征
        for lag in [1, 2, 3, 5, 10]:
            df[f'returns_lag_{lag}'] = df['returns'].shift(lag)
            df[f'volume_change_lag_{lag}'] = df['volume_change'].shift(lag)
            df[f'rsi_14_lag_{lag}'] = df['rsi_14'].shift(lag)
        
        # 统计特征
        for window in [10, 20]:
            df[f'returns_mean_{window}'] = df['returns'].rolling(window).mean()
            df[f'returns_std_{window}'] = df['returns'].rolling(window).std()
            df[f'returns_skew_{window}'] = df['returns'].rolling(window).skew()
            df[f'returns_kurt_{window}'] = df['returns'].rolling(window).kurt()
        
        # 技术形态特征
        df['doji'] = (abs(df['open'] - df['close']) / (df['high'] - df['low']) < 0.1).astype(int)
        df['hammer'] = ((df['close'] > df['open']) & 
                       ((df['open'] - df['low']) > 2 * (df['close'] - df['open'])) &
                       ((df['high'] - df['close']) < (df['close'] - df['open']))).astype(int)
        
        return df
    
    def create_labels(self, data: pd.DataFrame, forward_periods: int = 1, 
                     threshold: float = 0.001) -> pd.Series:
        """
        创建预测标签
        
        Args:
            data: 价格数据
            forward_periods: 前瞻期数
            threshold: 最小变动阈值
            
        Returns:
            标签序列 (1: 上涨, 0: 下跌)
        """
        future_returns = data['close'].shift(-forward_periods) / data['close'] - 1
        
        # 使用阈值过滤
        labels = (future_returns > threshold).astype(int)
        
        return labels
    
    def train_model(self, data: pd.DataFrame, **params) -> None:
        """
        训练XGBoost模型
        
        Args:
            data: 训练数据
            **params: 模型参数
        """
        # 准备特征
        features_df = self.prepare_features(data)
        
        # 创建标签
        labels = self.create_labels(
            data, 
            params.get('forward_periods', 1),
            params.get('threshold', 0.001)
        )
        
        # 选择特征列
        potential_features = [col for col in features_df.columns 
                            if col not in ['open', 'high', 'low', 'close', 'volume']]
        
        # 检查特征的有效性
        valid_features = []
        for col in potential_features:
            if not features_df[col].isna().all() and features_df[col].var() > 1e-10:
                valid_features.append(col)
        
        self.feature_columns = valid_features
        
        # 准备训练数据
        X = features_df[self.feature_columns].fillna(method='ffill').fillna(0)
        y = labels.loc[X.index]
        
        # 移除标签为NaN的样本
        valid_idx = ~y.isna()
        X = X[valid_idx]
        y = y[valid_idx]
        
        if len(X) == 0:
            raise ValueError("No valid training data after feature preparation")
        
        # 分割训练、验证和测试集
        X_temp, X_test, y_temp, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=0.25, random_state=42, stratify=y_temp
        )
        
        # 创建DMatrix
        dtrain = xgb.DMatrix(X_train, label=y_train)
        dval = xgb.DMatrix(X_val, label=y_val)
        
        # XGBoost参数
        xgb_params = {
            'objective': 'binary:logistic',
            'eval_metric': 'logloss',
            'max_depth': params.get('max_depth', 6),
            'learning_rate': params.get('learning_rate', 0.1),
            'subsample': params.get('subsample', 0.8),
            'colsample_bytree': params.get('colsample_bytree', 0.8),
            'min_child_weight': params.get('min_child_weight', 1),
            'reg_alpha': params.get('reg_alpha', 0.1),
            'reg_lambda': params.get('reg_lambda', 1.0),
            'random_state': 42,
            'verbosity': 0
        }
        
        # 训练模型
        evallist = [(dtrain, 'train'), (dval, 'eval')]
        self.model = xgb.train(
            xgb_params,
            dtrain,
            num_boost_round=params.get('n_estimators', 100),
            evals=evallist,
            early_stopping_rounds=params.get('early_stopping_rounds', 20),
            verbose_eval=False,
            evals_result=self.eval_results
        )
        
        # 验证模型
        y_pred_proba = self.model.predict(dval)
        y_pred = (y_pred_proba > 0.5).astype(int)
        accuracy = accuracy_score(y_val, y_pred)
        
        # 保存特征重要性
        importance_dict = self.model.get_score(importance_type='weight')
        self.feature_importance = pd.DataFrame([
            {'feature': k, 'importance': v} 
            for k, v in importance_dict.items()
        ]).sort_values('importance', ascending=False)
        
        self.model_trained = True
        
        print(f"XGBoost模型训练完成:")
        print(f"  验证集准确率: {accuracy:.4f}")
        print(f"  最佳迭代轮数: {self.model.best_iteration}")
        print(f"  训练损失: {self.eval_results['train']['logloss'][-1]:.4f}")
        print(f"  验证损失: {self.eval_results['eval']['logloss'][-1]:.4f}")
        print("前5个重要特征:")
        print(self.feature_importance.head())
    
    def generate_signals(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        生成交易信号
        
        Args:
            data: 价格数据
            **params: 策略参数
            
        Returns:
            交易信号序列 (1: 买入, -1: 卖出, 0: 持有)
        """
        if not self.model_trained:
            # 如果模型未训练，先训练模型
            self.train_model(data, **params)
        
        # 准备特征
        features_df = self.prepare_features(data)
        
        # 提取特征
        X = features_df[self.feature_columns].fillna(method='ffill').fillna(0)
        
        # 创建DMatrix
        dtest = xgb.DMatrix(X)
        
        # 预测
        probabilities = self.model.predict(dtest)
        
        # 生成信号
        signals = pd.Series(0, index=data.index)
        
        # 设置信号阈值
        buy_threshold = params.get('buy_threshold', 0.6)
        sell_threshold = params.get('sell_threshold', 0.4)
        
        # 基于概率生成信号
        signals[probabilities > buy_threshold] = 1   # 买入
        signals[probabilities < sell_threshold] = -1  # 卖出
        
        return signals
    
    def get_param_ranges(self) -> Dict[str, Tuple[float, float]]:
        """
        获取参数搜索范围
        
        Returns:
            参数名称到范围的映射
        """
        return {
            'max_depth': (3, 10),
            'learning_rate': (0.01, 0.3),
            'n_estimators': (50, 300),
            'subsample': (0.6, 1.0),
            'colsample_bytree': (0.6, 1.0),
            'min_child_weight': (1, 10),
            'reg_alpha': (0, 1.0),
            'reg_lambda': (0, 2.0),
            'buy_threshold': (0.55, 0.8),
            'sell_threshold': (0.2, 0.45),
            'forward_periods': (1, 5),
            'threshold': (0.0005, 0.005)
        }
    
    def get_feature_importance(self) -> Optional[pd.DataFrame]:
        """
        获取特征重要性
        
        Returns:
            特征重要性DataFrame
        """
        return self.feature_importance
    
    def get_training_history(self) -> Dict[str, Any]:
        """
        获取训练历史
        
        Returns:
            训练历史字典
        """
        return self.eval_results
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns:
            策略信息字典
        """
        info = super().get_strategy_info()
        info.update({
            'algorithm': 'XGBoost',
            'model_trained': self.model_trained,
            'n_features': len(self.feature_columns) if self.feature_columns else 0,
            'gradient_boosting': True,
            'supports_feature_importance': True,
            'supports_early_stopping': True,
            'handles_missing_values': True
        })
        
        if self.model_trained:
            info.update({
                'best_iteration': self.model.best_iteration,
                'n_trees': self.model.num_boosted_rounds()
            })
        
        return info
