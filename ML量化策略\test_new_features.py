"""
新功能测试脚本

快速测试新实现的ML策略和可视化功能是否正常工作。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import warnings
warnings.filterwarnings('ignore')

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    # 测试核心模块
    try:
        from mlquant.data import RandomDataGenerator, TechnicalIndicators
        print("  ✅ 数据模块导入成功")
    except ImportError as e:
        print(f"  ❌ 数据模块导入失败: {e}")
        return False
    
    # 测试ML策略模块
    try:
        from mlquant.strategies.ml import get_available_strategies
        available = get_available_strategies()
        print(f"  ✅ ML策略模块导入成功，可用策略: {list(available.keys())}")
    except ImportError as e:
        print(f"  ❌ ML策略模块导入失败: {e}")
    
    # 测试可视化模块
    try:
        from mlquant.visualization import check_dependencies
        print("  ✅ 可视化模块导入成功")
        check_dependencies()
    except ImportError as e:
        print(f"  ❌ 可视化模块导入失败: {e}")
    
    return True

def test_data_processing():
    """测试数据处理功能"""
    print("\n📊 测试数据处理功能...")
    
    try:
        from mlquant.data import RandomDataGenerator, TechnicalIndicators
        
        # 生成数据
        generator = RandomDataGenerator()
        data = generator.generate(days=100)
        print(f"  ✅ 生成数据成功: {len(data)} 行")
        
        # 计算技术指标
        indicators = TechnicalIndicators.calculate_all_indicators(data)
        print(f"  ✅ 技术指标计算成功: {len(indicators.columns)} 个指标")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据处理测试失败: {e}")
        return False

def test_ml_strategies():
    """测试ML策略"""
    print("\n🤖 测试ML策略...")
    
    try:
        from mlquant.data import RandomDataGenerator
        from mlquant.strategies.ml import get_available_strategies
        
        # 生成测试数据
        generator = RandomDataGenerator()
        data = generator.generate(days=200)
        
        available_strategies = get_available_strategies()
        
        if 'decision_tree' in available_strategies:
            print("  🌳 测试决策树策略...")
            DecisionTreeStrategy = available_strategies['decision_tree']
            strategy = DecisionTreeStrategy()
            signals = strategy.generate_signals(data, max_depth=5)
            print(f"     ✅ 生成信号成功: {signals.sum()} 个买入信号, {(signals == -1).sum()} 个卖出信号")
        
        if 'random_forest' in available_strategies:
            print("  🌲 测试随机森林策略...")
            RandomForestStrategy = available_strategies['random_forest']
            strategy = RandomForestStrategy()
            signals = strategy.generate_signals(data, n_estimators=10, max_depth=5)
            print(f"     ✅ 生成信号成功: {signals.sum()} 个买入信号, {(signals == -1).sum()} 个卖出信号")
        
        return True
        
    except Exception as e:
        print(f"  ❌ ML策略测试失败: {e}")
        return False

def test_visualization():
    """测试可视化功能"""
    print("\n📈 测试可视化功能...")
    
    try:
        from mlquant.data import RandomDataGenerator
        from mlquant.backtest import BacktestEngine
        from mlquant.visualization import StrategyDashboard, InteractiveCharts
        
        # 生成测试数据和结果
        generator = RandomDataGenerator()
        data = generator.generate(days=100)
        
        # 创建简单信号
        signals = (data['close'].pct_change() > 0).astype(int) * 2 - 1
        
        # 回测
        engine = BacktestEngine()
        result = engine.run(data, signals, "TestStrategy")
        
        # 测试仪表板
        dashboard = StrategyDashboard()
        fig1 = dashboard.create_equity_curve_chart(result)
        print("  ✅ 策略仪表板创建成功")
        
        # 测试交互式图表
        charts = InteractiveCharts()
        fig2 = charts.create_candlestick_chart(data.tail(50))
        print("  ✅ 交互式图表创建成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 可视化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 ML量化策略新功能测试")
    print("="*50)
    
    # 运行测试
    tests = [
        ("模块导入", test_imports),
        ("数据处理", test_data_processing),
        ("ML策略", test_ml_strategies),
        ("可视化", test_visualization)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "="*50)
    print("📋 测试总结")
    print("="*50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！新功能工作正常。")
    else:
        print("⚠️  部分测试失败，可能需要安装额外依赖:")
        print("   pip install scikit-learn xgboost tensorflow plotly yfinance")

if __name__ == "__main__":
    main()
