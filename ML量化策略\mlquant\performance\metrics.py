"""
性能指标计算

提供各种性能指标的计算函数。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any


class PerformanceMetrics:
    """性能指标计算器"""
    
    @staticmethod
    def total_return(equity_curve: pd.Series) -> float:
        """计算总收益率"""
        if len(equity_curve) == 0:
            return 0.0
        return (equity_curve.iloc[-1] / equity_curve.iloc[0]) - 1
    
    @staticmethod
    def annualized_return(equity_curve: pd.Series, periods_per_year: int = 252) -> float:
        """计算年化收益率"""
        if len(equity_curve) <= 1:
            return 0.0
        
        total_ret = PerformanceMetrics.total_return(equity_curve)
        years = len(equity_curve) / periods_per_year
        
        if years <= 0:
            return 0.0
            
        return (1 + total_ret) ** (1 / years) - 1
    
    @staticmethod
    def volatility(returns: pd.Series, periods_per_year: int = 252) -> float:
        """计算年化波动率"""
        if len(returns) <= 1:
            return 0.0
        return returns.std() * np.sqrt(periods_per_year)
    
    @staticmethod
    def sharpe_ratio(returns: pd.Series, risk_free_rate: float = 0.0, 
                    periods_per_year: int = 252) -> float:
        """计算夏普比率"""
        if len(returns) <= 1:
            return 0.0
        
        excess_returns = returns - risk_free_rate / periods_per_year
        if excess_returns.std() == 0:
            return 0.0
        
        return excess_returns.mean() / excess_returns.std() * np.sqrt(periods_per_year)
    
    @staticmethod
    def max_drawdown(equity_curve: pd.Series) -> float:
        """计算最大回撤"""
        if len(equity_curve) == 0:
            return 0.0
        
        peak = equity_curve.expanding().max()
        drawdown = (equity_curve - peak) / peak
        return drawdown.min()
    
    @staticmethod
    def win_rate(returns: pd.Series) -> float:
        """计算胜率"""
        if len(returns) == 0:
            return 0.0
        
        winning_trades = (returns > 0).sum()
        total_trades = len(returns[returns != 0])
        
        if total_trades == 0:
            return 0.0
        
        return winning_trades / total_trades
    
    @staticmethod
    def profit_factor(returns: pd.Series) -> float:
        """计算盈利因子"""
        if len(returns) == 0:
            return 0.0
        
        gross_profit = returns[returns > 0].sum()
        gross_loss = abs(returns[returns < 0].sum())
        
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 0.0
        
        return gross_profit / gross_loss
    
    @staticmethod
    def calculate_all_metrics(equity_curve: pd.Series, 
                            returns: pd.Series = None,
                            risk_free_rate: float = 0.0,
                            periods_per_year: int = 252) -> Dict[str, float]:
        """计算所有性能指标"""
        if returns is None:
            returns = equity_curve.pct_change().dropna()
        
        return {
            'total_return': PerformanceMetrics.total_return(equity_curve),
            'annualized_return': PerformanceMetrics.annualized_return(equity_curve, periods_per_year),
            'volatility': PerformanceMetrics.volatility(returns, periods_per_year),
            'sharpe_ratio': PerformanceMetrics.sharpe_ratio(returns, risk_free_rate, periods_per_year),
            'max_drawdown': PerformanceMetrics.max_drawdown(equity_curve),
            'win_rate': PerformanceMetrics.win_rate(returns),
            'profit_factor': PerformanceMetrics.profit_factor(returns)
        }
