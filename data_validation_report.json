{"loose_mode_report": {"summary": {"total_records": 1005, "valid_records": 1000, "invalid_records": 5, "data_quality_score": 99.50248756218906}, "error_details": {"invalid_prices": 12, "ohlc_logic_errors": 12, "negative_volume": 6, "extreme_price_change": 6, "duplicates": 5}, "recommendations": ["建议检查数据源的价格数据质量", "建议验证OHLC数据的采集逻辑", "建议在数据采集时添加去重机制", "建议检查异常涨跌幅数据，可能存在除权除息等特殊情况", "数据质量优秀"]}, "strict_mode_report": {"summary": {"total_records": 1005, "valid_records": 970, "invalid_records": 35, "data_quality_score": 96.51741293532339}, "error_details": {"invalid_prices": 12, "ohlc_logic_errors": 6, "negative_volume": 6, "extreme_price_change": 6, "duplicates": 5}, "recommendations": ["建议检查数据源的价格数据质量", "建议验证OHLC数据的采集逻辑", "建议在数据采集时添加去重机制", "建议检查异常涨跌幅数据，可能存在除权除息等特殊情况", "数据质量良好，建议进一步优化异常数据处理"]}, "normal_data_report": {"summary": {"total_records": 100, "valid_records": 100, "invalid_records": 0, "data_quality_score": 100.0}, "error_details": {"invalid_prices": 0}, "recommendations": ["数据质量优秀"]}, "stock_data_report": {"summary": {"total_records": 66, "valid_records": 66, "invalid_records": 0, "data_quality_score": 100.0}, "error_details": {"invalid_prices": 0}, "recommendations": ["数据质量优秀"]}}