"""
网格搜索优化器

使用网格搜索优化策略参数。
"""

import numpy as np
from typing import Dict, Tuple, List, Callable
from itertools import product


class GridSearchOptimizer:
    """网格搜索优化器"""
    
    def __init__(self, grid_points: int = 10):
        """
        初始化网格搜索优化器
        
        Args:
            grid_points: 每个参数的网格点数
        """
        self.grid_points = grid_points
        self.results: List[Tuple[Dict[str, float], float]] = []
    
    def optimize(self, param_ranges: Dict[str, Tuple[float, float]], 
                fitness_func: Callable[[Dict[str, float]], float]) -> Dict[str, float]:
        """
        执行网格搜索优化
        
        Args:
            param_ranges: 参数范围字典
            fitness_func: 适应度函数
            
        Returns:
            最优参数
        """
        # 生成参数网格
        param_grids = {}
        for param_name, (min_val, max_val) in param_ranges.items():
            param_grids[param_name] = np.linspace(min_val, max_val, self.grid_points)
        
        # 生成所有参数组合
        param_names = list(param_grids.keys())
        param_values = list(param_grids.values())
        
        best_params = None
        best_fitness = float('-inf')
        
        # 遍历所有组合
        for combination in product(*param_values):
            params = dict(zip(param_names, combination))
            fitness = fitness_func(params)
            
            self.results.append((params.copy(), fitness))
            
            if fitness > best_fitness:
                best_fitness = fitness
                best_params = params.copy()
        
        return best_params
    
    def get_results(self) -> List[Tuple[Dict[str, float], float]]:
        """获取所有搜索结果"""
        return self.results
    
    def get_top_results(self, n: int = 10) -> List[Tuple[Dict[str, float], float]]:
        """获取前N个最佳结果"""
        sorted_results = sorted(self.results, key=lambda x: x[1], reverse=True)
        return sorted_results[:n]
