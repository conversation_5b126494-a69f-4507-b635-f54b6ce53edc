"""
辅助函数

提供通用的辅助函数。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List


def print_header(title: str, width: int = 60, char: str = "="):
    """打印标题头"""
    print(f"\n{char * width}")
    print(f"{title:^{width}}")
    print(f"{char * width}")


def print_step(step: int, description: str):
    """打印步骤信息"""
    print(f"\n{'='*20} 步骤{step}: {description} {'='*20}")


def format_percentage(value: float, decimals: int = 2) -> str:
    """格式化百分比"""
    return f"{value:.{decimals}%}"


def format_number(value: float, decimals: int = 2) -> str:
    """格式化数字"""
    return f"{value:,.{decimals}f}"


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """安全除法"""
    if denominator == 0:
        return default
    return numerator / denominator


def calculate_returns(prices: pd.Series) -> pd.Series:
    """计算收益率"""
    return prices.pct_change().dropna()


def validate_data(data: pd.DataFrame, required_columns: List[str] = None) -> bool:
    """验证数据格式"""
    if required_columns is None:
        required_columns = ['open', 'high', 'low', 'close', 'volume']
    
    if not isinstance(data, pd.DataFrame):
        return False
    
    for col in required_columns:
        if col not in data.columns:
            return False
    
    return True


def resample_data(data: pd.DataFrame, freq: str) -> pd.DataFrame:
    """重采样数据"""
    return data.resample(freq).agg({
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last',
        'volume': 'sum'
    }).dropna()
