"""
参数优化过程可视化工具

提供参数优化过程的可视化功能，包括：
- 优化过程动画
- 参数空间探索可视化
- 收敛过程分析
- 参数敏感性分析
- 多目标优化结果展示
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
import warnings

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    warnings.warn("plotly not available. ParameterOptimizationVisualizer will not work.")


class ParameterOptimizationVisualizer:
    """
    参数优化过程可视化工具
    
    提供参数优化过程的全面可视化，帮助理解优化算法的
    行为和参数空间的特征。
    """
    
    def __init__(self, theme: str = "plotly_white"):
        """
        初始化优化可视化工具
        
        Args:
            theme: 图表主题
        """
        if not PLOTLY_AVAILABLE:
            raise ImportError("plotly is required for ParameterOptimizationVisualizer")
        
        self.theme = theme
        self.colors = {
            'best': '#00CC96',
            'good': '#636EFA',
            'average': '#FFA15A',
            'poor': '#FF6692',
            'generation': '#AB63FA',
            'pareto': '#19D3F3'
        }
    
    def plot_optimization_history(self, history: List[Dict[str, Any]],
                                fitness_key: str = 'fitness') -> go.Figure:
        """
        绘制优化历史
        
        Args:
            history: 优化历史记录
            fitness_key: 适应度键名
            
        Returns:
            Plotly图表对象
        """
        if not history:
            fig = go.Figure()
            fig.add_annotation(
                text="暂无优化历史数据",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=20)
            )
            fig.update_layout(title="优化历史", template=self.theme)
            return fig
        
        # 提取数据
        generations = []
        best_fitness = []
        avg_fitness = []
        worst_fitness = []
        
        for i, gen_data in enumerate(history):
            generations.append(i)
            
            if isinstance(gen_data, dict) and fitness_key in gen_data:
                fitness_values = gen_data[fitness_key]
                if isinstance(fitness_values, list):
                    best_fitness.append(max(fitness_values))
                    avg_fitness.append(np.mean(fitness_values))
                    worst_fitness.append(min(fitness_values))
                else:
                    best_fitness.append(fitness_values)
                    avg_fitness.append(fitness_values)
                    worst_fitness.append(fitness_values)
            else:
                # 如果数据格式不符合预期，尝试直接使用数值
                fitness_val = gen_data if isinstance(gen_data, (int, float)) else 0
                best_fitness.append(fitness_val)
                avg_fitness.append(fitness_val)
                worst_fitness.append(fitness_val)
        
        fig = go.Figure()
        
        # 最佳适应度
        fig.add_trace(go.Scatter(
            x=generations,
            y=best_fitness,
            mode='lines+markers',
            name='最佳适应度',
            line=dict(color=self.colors['best'], width=3),
            marker=dict(size=6)
        ))
        
        # 平均适应度
        fig.add_trace(go.Scatter(
            x=generations,
            y=avg_fitness,
            mode='lines',
            name='平均适应度',
            line=dict(color=self.colors['average'], width=2)
        ))
        
        # 最差适应度
        fig.add_trace(go.Scatter(
            x=generations,
            y=worst_fitness,
            mode='lines',
            name='最差适应度',
            line=dict(color=self.colors['poor'], width=1, dash='dash')
        ))
        
        fig.update_layout(
            title='优化收敛过程',
            xaxis_title='迭代次数',
            yaxis_title='适应度',
            template=self.theme,
            hovermode='x unified'
        )
        
        return fig
    
    def plot_parameter_space_2d(self, results: List[Dict[str, Any]],
                               param1: str, param2: str,
                               fitness_key: str = 'fitness') -> go.Figure:
        """
        绘制2D参数空间探索图
        
        Args:
            results: 优化结果列表
            param1: 第一个参数名
            param2: 第二个参数名
            fitness_key: 适应度键名
            
        Returns:
            Plotly图表对象
        """
        if not results:
            fig = go.Figure()
            fig.add_annotation(
                text="暂无参数空间数据",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=20)
            )
            fig.update_layout(title="参数空间探索", template=self.theme)
            return fig
        
        # 提取参数和适应度数据
        param1_values = []
        param2_values = []
        fitness_values = []
        
        for result in results:
            if param1 in result and param2 in result and fitness_key in result:
                param1_values.append(result[param1])
                param2_values.append(result[param2])
                fitness_values.append(result[fitness_key])
        
        if not param1_values:
            fig = go.Figure()
            fig.add_annotation(
                text=f"未找到参数 {param1} 和 {param2} 的数据",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=16)
            )
            fig.update_layout(title="参数空间探索", template=self.theme)
            return fig
        
        # 创建散点图
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=param1_values,
            y=param2_values,
            mode='markers',
            marker=dict(
                size=8,
                color=fitness_values,
                colorscale='Viridis',
                showscale=True,
                colorbar=dict(title="适应度")
            ),
            text=[f'适应度: {f:.4f}' for f in fitness_values],
            hovertemplate=f'{param1}: %{{x}}<br>{param2}: %{{y}}<br>%{{text}}<extra></extra>',
            name='参数组合'
        ))
        
        # 标记最佳点
        best_idx = np.argmax(fitness_values)
        fig.add_trace(go.Scatter(
            x=[param1_values[best_idx]],
            y=[param2_values[best_idx]],
            mode='markers',
            marker=dict(
                size=15,
                color='red',
                symbol='star',
                line=dict(width=2, color='white')
            ),
            name='最佳参数',
            hovertemplate=f'最佳组合<br>{param1}: %{{x}}<br>{param2}: %{{y}}<extra></extra>'
        ))
        
        fig.update_layout(
            title=f'参数空间探索: {param1} vs {param2}',
            xaxis_title=param1,
            yaxis_title=param2,
            template=self.theme
        )
        
        return fig
    
    def plot_parameter_sensitivity(self, sensitivity_data: Dict[str, Dict[str, float]],
                                 metric_name: str = "夏普比率") -> go.Figure:
        """
        绘制参数敏感性分析
        
        Args:
            sensitivity_data: 参数敏感性数据
            metric_name: 指标名称
            
        Returns:
            Plotly图表对象
        """
        if not sensitivity_data:
            fig = go.Figure()
            fig.add_annotation(
                text="暂无敏感性分析数据",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=20)
            )
            fig.update_layout(title="参数敏感性分析", template=self.theme)
            return fig
        
        # 创建子图
        n_params = len(sensitivity_data)
        cols = min(3, n_params)
        rows = (n_params + cols - 1) // cols
        
        fig = make_subplots(
            rows=rows, cols=cols,
            subplot_titles=list(sensitivity_data.keys()),
            vertical_spacing=0.1,
            horizontal_spacing=0.1
        )
        
        colors = px.colors.qualitative.Set1
        
        for i, (param_name, param_data) in enumerate(sensitivity_data.items()):
            row = i // cols + 1
            col = i % cols + 1
            
            param_values = list(param_data.keys())
            metric_values = list(param_data.values())
            
            # 确保参数值是数值类型
            try:
                param_values = [float(x) for x in param_values]
                # 按参数值排序
                sorted_data = sorted(zip(param_values, metric_values))
                param_values, metric_values = zip(*sorted_data)
            except (ValueError, TypeError):
                # 如果无法转换为数值，保持原顺序
                pass
            
            fig.add_trace(go.Scatter(
                x=param_values,
                y=metric_values,
                mode='lines+markers',
                name=param_name,
                line=dict(color=colors[i % len(colors)], width=2),
                marker=dict(size=6),
                showlegend=False
            ), row=row, col=col)
            
            # 标记最优点
            best_idx = np.argmax(metric_values)
            fig.add_trace(go.Scatter(
                x=[param_values[best_idx]],
                y=[metric_values[best_idx]],
                mode='markers',
                marker=dict(
                    size=10,
                    color='red',
                    symbol='star'
                ),
                showlegend=False,
                hovertemplate=f'最优{param_name}: %{{x}}<br>最优{metric_name}: %{{y:.4f}}<extra></extra>'
            ), row=row, col=col)
        
        fig.update_layout(
            title=f'参数敏感性分析 - {metric_name}',
            template=self.theme,
            height=300 * rows
        )
        
        return fig
    
    def plot_convergence_analysis(self, history: List[Dict[str, Any]],
                                fitness_key: str = 'fitness') -> go.Figure:
        """
        绘制收敛分析
        
        Args:
            history: 优化历史
            fitness_key: 适应度键名
            
        Returns:
            Plotly图表对象
        """
        if not history:
            fig = go.Figure()
            fig.add_annotation(
                text="暂无收敛分析数据",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=20)
            )
            fig.update_layout(title="收敛分析", template=self.theme)
            return fig
        
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('适应度收敛', '适应度方差', '改进率', '停滞检测'),
            vertical_spacing=0.15
        )
        
        generations = list(range(len(history)))
        best_fitness = []
        fitness_variance = []
        improvement_rate = []
        
        for gen_data in history:
            if isinstance(gen_data, dict) and fitness_key in gen_data:
                fitness_values = gen_data[fitness_key]
                if isinstance(fitness_values, list):
                    best_fitness.append(max(fitness_values))
                    fitness_variance.append(np.var(fitness_values))
                else:
                    best_fitness.append(fitness_values)
                    fitness_variance.append(0)
            else:
                fitness_val = gen_data if isinstance(gen_data, (int, float)) else 0
                best_fitness.append(fitness_val)
                fitness_variance.append(0)
        
        # 计算改进率
        for i in range(len(best_fitness)):
            if i == 0:
                improvement_rate.append(0)
            else:
                rate = (best_fitness[i] - best_fitness[i-1]) / max(abs(best_fitness[i-1]), 1e-8)
                improvement_rate.append(rate)
        
        # 1. 适应度收敛
        fig.add_trace(go.Scatter(
            x=generations,
            y=best_fitness,
            mode='lines+markers',
            name='最佳适应度',
            line=dict(color=self.colors['best'])
        ), row=1, col=1)
        
        # 2. 适应度方差
        fig.add_trace(go.Scatter(
            x=generations,
            y=fitness_variance,
            mode='lines',
            name='适应度方差',
            line=dict(color=self.colors['average'])
        ), row=1, col=2)
        
        # 3. 改进率
        fig.add_trace(go.Scatter(
            x=generations,
            y=improvement_rate,
            mode='lines',
            name='改进率',
            line=dict(color=self.colors['generation'])
        ), row=2, col=1)
        
        # 添加零线
        fig.add_hline(y=0, line_dash="dash", line_color="gray", row=2, col=1)
        
        # 4. 停滞检测
        stagnation_threshold = 0.001
        stagnation_periods = []
        current_stagnation = 0
        
        for rate in improvement_rate:
            if abs(rate) < stagnation_threshold:
                current_stagnation += 1
            else:
                current_stagnation = 0
            stagnation_periods.append(current_stagnation)
        
        fig.add_trace(go.Scatter(
            x=generations,
            y=stagnation_periods,
            mode='lines',
            name='停滞期长度',
            line=dict(color=self.colors['poor'])
        ), row=2, col=2)
        
        fig.update_layout(
            title='收敛分析',
            template=self.theme,
            height=600,
            showlegend=False
        )
        
        # 更新坐标轴标题
        fig.update_yaxes(title_text="适应度", row=1, col=1)
        fig.update_yaxes(title_text="方差", row=1, col=2)
        fig.update_yaxes(title_text="改进率", row=2, col=1)
        fig.update_yaxes(title_text="停滞期", row=2, col=2)
        fig.update_xaxes(title_text="迭代次数", row=2, col=1)
        fig.update_xaxes(title_text="迭代次数", row=2, col=2)
        
        return fig
    
    def plot_multi_objective_results(self, results: List[Dict[str, Any]],
                                   objective1: str, objective2: str) -> go.Figure:
        """
        绘制多目标优化结果
        
        Args:
            results: 优化结果
            objective1: 第一个目标
            objective2: 第二个目标
            
        Returns:
            Plotly图表对象
        """
        if not results:
            fig = go.Figure()
            fig.add_annotation(
                text="暂无多目标优化数据",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=20)
            )
            fig.update_layout(title="多目标优化结果", template=self.theme)
            return fig
        
        # 提取目标值
        obj1_values = []
        obj2_values = []
        
        for result in results:
            if objective1 in result and objective2 in result:
                obj1_values.append(result[objective1])
                obj2_values.append(result[objective2])
        
        if not obj1_values:
            fig = go.Figure()
            fig.add_annotation(
                text=f"未找到目标 {objective1} 和 {objective2} 的数据",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=16)
            )
            fig.update_layout(title="多目标优化结果", template=self.theme)
            return fig
        
        fig = go.Figure()
        
        # 所有解
        fig.add_trace(go.Scatter(
            x=obj1_values,
            y=obj2_values,
            mode='markers',
            name='所有解',
            marker=dict(
                size=6,
                color=self.colors['average'],
                opacity=0.6
            )
        ))
        
        # 简单的帕累托前沿识别（这里使用简化方法）
        pareto_indices = []
        for i, (x1, y1) in enumerate(zip(obj1_values, obj2_values)):
            is_pareto = True
            for j, (x2, y2) in enumerate(zip(obj1_values, obj2_values)):
                if i != j and x2 >= x1 and y2 >= y1 and (x2 > x1 or y2 > y1):
                    is_pareto = False
                    break
            if is_pareto:
                pareto_indices.append(i)
        
        if pareto_indices:
            pareto_x = [obj1_values[i] for i in pareto_indices]
            pareto_y = [obj2_values[i] for i in pareto_indices]
            
            fig.add_trace(go.Scatter(
                x=pareto_x,
                y=pareto_y,
                mode='markers',
                name='帕累托前沿',
                marker=dict(
                    size=10,
                    color=self.colors['pareto'],
                    symbol='diamond'
                )
            ))
        
        fig.update_layout(
            title=f'多目标优化结果: {objective1} vs {objective2}',
            xaxis_title=objective1,
            yaxis_title=objective2,
            template=self.theme
        )
        
        return fig
