"""
性能评估模块

提供策略性能分析和可视化功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

from ..backtest.result import BacktestResult
from .metrics import PerformanceMetrics
from .report import PerformanceReport




class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化性能分析器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.performance_config = self.config.get('performance', {})
        
        self.risk_free_rate = self.performance_config.get('risk_free_rate', 0.0)
        self.trading_days_per_year = self.performance_config.get('trading_days_per_year', 252)
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        # 简化版本：直接返回默认配置
        return {'performance': {'risk_free_rate': 0.0, 'trading_days_per_year': 252}}
    
    def analyze(self, backtest_result) -> PerformanceReport:
        """
        分析回测结果
        
        Args:
            backtest_result: 回测结果对象
            
        Returns:
            性能报告
        """
        # 基础指标
        metrics = {
            'total_return': backtest_result.total_return,
            'annualized_return': backtest_result.annualized_return,
            'cumulative_return': backtest_result.total_return,
            'final_capital': backtest_result.final_capital
        }
        
        # 风险指标
        risk_metrics = self._calculate_risk_metrics(backtest_result)
        
        # 交易指标
        trade_metrics = self._calculate_trade_metrics(backtest_result)
        
        return PerformanceReport(
            strategy_name=backtest_result.strategy_name,
            metrics=metrics,
            risk_metrics=risk_metrics,
            trade_metrics=trade_metrics
        )
    
    def _calculate_risk_metrics(self, result) -> Dict[str, float]:
        """计算风险指标"""
        daily_returns = result.daily_returns
        
        risk_metrics = {
            'volatility': result.volatility,
            'max_drawdown': result.max_drawdown,
            'sharpe_ratio': result.sharpe_ratio
        }
        
        # 索提诺比率
        if len(daily_returns) > 1:
            downside_returns = daily_returns[daily_returns < 0]
            if len(downside_returns) > 1:
                downside_deviation = downside_returns.std() * np.sqrt(self.trading_days_per_year)
                if downside_deviation > 0:
                    excess_return = result.annualized_return - self.risk_free_rate
                    risk_metrics['sortino_ratio'] = excess_return / downside_deviation
                else:
                    risk_metrics['sortino_ratio'] = 0.0
            else:
                risk_metrics['sortino_ratio'] = 0.0
        else:
            risk_metrics['sortino_ratio'] = 0.0
        
        # 卡玛比率
        if result.max_drawdown != 0:
            risk_metrics['calmar_ratio'] = result.annualized_return / abs(result.max_drawdown)
        else:
            risk_metrics['calmar_ratio'] = 0.0
        
        # VaR (95%)
        if len(daily_returns) > 0:
            risk_metrics['var_95'] = daily_returns.quantile(0.05)
        else:
            risk_metrics['var_95'] = 0.0
        
        return risk_metrics
    
    def _calculate_trade_metrics(self, result) -> Dict[str, float]:
        """计算交易指标"""
        trades = result.trades
        
        trade_metrics = {
            'total_trades': result.total_trades,
            'win_rate': result.win_rate,
            'profit_factor': result.profit_factor,
            'avg_profit': 0.0,
            'avg_loss': 0.0
        }
        
        if len(trades) >= 2:
            # 计算每笔交易的盈亏
            profits = []
            i = 0
            while i < len(trades) - 1:
                if trades[i]['action'] == 'BUY' and trades[i+1]['action'] == 'SELL':
                    buy_cost = trades[i]['shares'] * trades[i]['price'] + trades[i]['commission']
                    sell_revenue = trades[i+1]['capital_after']
                    profit = sell_revenue - buy_cost
                    profits.append(profit)
                    i += 2
                else:
                    i += 1
            
            if profits:
                winning_trades = [p for p in profits if p > 0]
                losing_trades = [p for p in profits if p < 0]
                
                if winning_trades:
                    trade_metrics['avg_profit'] = np.mean(winning_trades)
                
                if losing_trades:
                    trade_metrics['avg_loss'] = np.mean(losing_trades)
        
        return trade_metrics
    
    def compare_strategies(self, results: List) -> pd.DataFrame:
        """
        比较多个策略的性能
        
        Args:
            results: 回测结果列表
            
        Returns:
            策略比较表
        """
        comparison_data = []
        
        for result in results:
            report = self.analyze(result)
            
            comparison_data.append({
                'Strategy': result.strategy_name,
                'Total Return': report.metrics['total_return'],
                'Annualized Return': report.metrics['annualized_return'],
                'Volatility': report.risk_metrics['volatility'],
                'Sharpe Ratio': report.risk_metrics['sharpe_ratio'],
                'Sortino Ratio': report.risk_metrics['sortino_ratio'],
                'Max Drawdown': report.risk_metrics['max_drawdown'],
                'Calmar Ratio': report.risk_metrics['calmar_ratio'],
                'Total Trades': report.trade_metrics['total_trades'],
                'Win Rate': report.trade_metrics['win_rate'],
                'Profit Factor': report.trade_metrics['profit_factor']
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        if not comparison_df.empty:
            comparison_df.set_index('Strategy', inplace=True)
        
        return comparison_df
    
    def calculate_rolling_metrics(self, equity_curve: pd.Series, 
                                window: int = 252) -> pd.DataFrame:
        """
        计算滚动性能指标
        
        Args:
            equity_curve: 资金曲线
            window: 滚动窗口大小
            
        Returns:
            滚动指标数据框
        """
        returns = equity_curve.pct_change().dropna()
        
        rolling_metrics = pd.DataFrame(index=returns.index)
        
        # 滚动收益率
        rolling_metrics['rolling_return'] = returns.rolling(window).mean() * self.trading_days_per_year
        
        # 滚动波动率
        rolling_metrics['rolling_volatility'] = returns.rolling(window).std() * np.sqrt(self.trading_days_per_year)
        
        # 滚动夏普比率
        rolling_metrics['rolling_sharpe'] = (
            rolling_metrics['rolling_return'] - self.risk_free_rate
        ) / rolling_metrics['rolling_volatility']
        
        # 滚动最大回撤
        rolling_max = equity_curve.rolling(window).max()
        rolling_drawdown = (equity_curve - rolling_max) / rolling_max
        rolling_metrics['rolling_max_drawdown'] = rolling_drawdown.rolling(window).min()
        
        return rolling_metrics.dropna()
    
    def generate_summary_stats(self, results: List) -> Dict[str, Any]:
        """
        生成汇总统计
        
        Args:
            results: 回测结果列表
            
        Returns:
            汇总统计字典
        """
        if not results:
            return {}
        
        returns = [r.total_return for r in results]
        sharpe_ratios = [r.sharpe_ratio for r in results]
        max_drawdowns = [r.max_drawdown for r in results]
        
        summary = {
            'total_strategies': len(results),
            'profitable_strategies': len([r for r in returns if r > 0]),
            'avg_return': np.mean(returns),
            'median_return': np.median(returns),
            'best_return': max(returns),
            'worst_return': min(returns),
            'avg_sharpe': np.mean(sharpe_ratios),
            'best_sharpe': max(sharpe_ratios),
            'avg_max_drawdown': np.mean(max_drawdowns),
            'worst_max_drawdown': min(max_drawdowns)
        }
        
        return summary
    
    def print_summary_stats(self, results: List):
        """打印汇总统计"""
        stats = self.generate_summary_stats(results)
        
        if not stats:
            print("没有可分析的结果")
            return
        
        print(f"\n{'='*50}")
        print(f"策略汇总统计")
        print(f"{'='*50}")
        print(f"总策略数量:     {stats['total_strategies']}")
        print(f"盈利策略数量:   {stats['profitable_strategies']}")
        print(f"盈利策略比例:   {stats['profitable_strategies']/stats['total_strategies']:.1%}")
        print(f"\n收益率统计:")
        print(f"  平均收益率:   {stats['avg_return']:.2%}")
        print(f"  中位收益率:   {stats['median_return']:.2%}")
        print(f"  最佳收益率:   {stats['best_return']:.2%}")
        print(f"  最差收益率:   {stats['worst_return']:.2%}")
        print(f"\n夏普比率统计:")
        print(f"  平均夏普比率: {stats['avg_sharpe']:.3f}")
        print(f"  最佳夏普比率: {stats['best_sharpe']:.3f}")
        print(f"\n回撤统计:")
        print(f"  平均最大回撤: {stats['avg_max_drawdown']:.2%}")
        print(f"  最大回撤:     {stats['worst_max_drawdown']:.2%}")
        print(f"{'='*50}")


def create_simple_chart(equity_curves: Dict[str, pd.Series], 
                       title: str = "策略收益对比") -> str:
    """
    创建简单的文本图表
    
    Args:
        equity_curves: 策略名称到资金曲线的映射
        title: 图表标题
        
    Returns:
        文本图表字符串
    """
    if not equity_curves:
        return "没有数据可显示"
    
    chart_lines = [f"\n{title}", "=" * len(title)]
    
    # 计算最终收益率
    final_returns = {}
    for name, curve in equity_curves.items():
        if len(curve) > 0:
            initial_value = curve.iloc[0]
            final_value = curve.iloc[-1]
            final_return = (final_value - initial_value) / initial_value
            final_returns[name] = final_return
    
    # 排序并显示
    sorted_strategies = sorted(final_returns.items(), key=lambda x: x[1], reverse=True)
    
    chart_lines.append("\n策略收益率排名:")
    for i, (name, return_rate) in enumerate(sorted_strategies, 1):
        bar_length = int(abs(return_rate) * 50)  # 缩放到50个字符
        bar_char = "█" if return_rate >= 0 else "▓"
        bar = bar_char * bar_length
        
        chart_lines.append(f"{i:2d}. {name:<15} {return_rate:>8.2%} {bar}")
    
    return "\n".join(chart_lines)


if __name__ == "__main__":
    # 测试性能分析器
    from data_generator import RandomDataGenerator
    from ml_strategy import DualMAStrategy
    from backtest_engine import BacktestEngine
    
    # 生成测试数据
    generator = RandomDataGenerator()
    data = generator.generate(days=500)
    
    # 创建策略和回测
    strategy = DualMAStrategy()
    signals = strategy.generate_signals(data, short_window=20, long_window=50)
    
    engine = BacktestEngine()
    result = engine.run(data, signals, "TestStrategy")
    
    # 性能分析
    analyzer = PerformanceAnalyzer()
    report = analyzer.analyze(result)
    report.print_report()
    
    # 创建简单图表
    chart = create_simple_chart(
        {"TestStrategy": result.equity_curve},
        "测试策略收益曲线"
    )
    print(chart)
