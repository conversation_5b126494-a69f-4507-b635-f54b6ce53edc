#!/usr/bin/env python3
"""
简化的策略文件演示

展示ML优化后策略的自动生成功能。
"""

import pandas as pd
import numpy as np
from datetime import datetime


class MLOptimizedDualMAStrategy:
    """ML优化的双均线策略 - 演示版"""
    
    def __init__(self):
        """初始化策略"""
        # ML优化的参数 (示例)
        self.short_window = 27
        self.long_window = 200
        
        # 策略信息
        self.name = "ML_Optimized_DualMA"
        self.description = "通过遗传算法优化的双均线策略"
        
        # 历史性能指标 (示例)
        self.historical_performance = {
            'total_return': 0.1234,
            'sharpe_ratio': 1.0233,
            'max_drawdown': -0.0856,
            'win_rate': 0.6500
        }
        
        print(f"✅ 策略初始化完成: {self.name}")
        print(f"   参数: MA{self.short_window}/MA{self.long_window}")
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        df = data.copy()
        
        # 计算移动平均线
        df['MA_short'] = df['close'].rolling(window=self.short_window).mean()
        df['MA_long'] = df['close'].rolling(window=self.long_window).mean()
        
        return df
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """生成交易信号"""
        df = self.calculate_indicators(data)
        
        # 初始化信号
        signals = pd.Series(0, index=data.index)
        
        # 双均线交叉策略
        signals[df['MA_short'] > df['MA_long']] = 1   # 买入
        signals[df['MA_short'] < df['MA_long']] = -1  # 卖出
        
        return signals
    
    def get_strategy_info(self) -> dict:
        """获取策略信息"""
        return {
            'name': self.name,
            'type': 'dual_moving_average',
            'parameters': {
                'short_window': self.short_window,
                'long_window': self.long_window
            },
            'description': self.description,
            'historical_performance': self.historical_performance,
            'optimization_method': 'genetic_algorithm',
            'generated_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def print_summary(self):
        """打印策略摘要"""
        print(f"""
{'='*50}
策略摘要: {self.name}
{'='*50}
策略类型: 双均线交叉策略
参数设置: MA{self.short_window}/MA{self.long_window}
优化方法: 遗传算法

历史表现:
- 总收益率: {self.historical_performance['total_return']:.2%}
- 夏普比率: {self.historical_performance['sharpe_ratio']:.3f}
- 最大回撤: {self.historical_performance['max_drawdown']:.2%}
- 胜率: {self.historical_performance['win_rate']:.2%}

使用说明:
1. 该策略适用于趋势性较强的市场
2. 在震荡市场中可能产生较多假信号
3. 建议结合其他指标进行信号过滤
4. 注意控制仓位和风险管理

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'='*50}
        """)


def demo_strategy_generation():
    """演示策略生成和使用"""
    print("🎯 ML策略文件自动生成演示")
    print("="*60)
    
    # 1. 创建策略实例
    print("\n1. 创建ML优化策略实例...")
    strategy = MLOptimizedDualMAStrategy()
    
    # 2. 显示策略信息
    print("\n2. 策略详细信息:")
    info = strategy.get_strategy_info()
    for key, value in info.items():
        if isinstance(value, dict):
            print(f"   {key}:")
            for k, v in value.items():
                print(f"     {k}: {v}")
        else:
            print(f"   {key}: {value}")
    
    # 3. 生成测试数据
    print("\n3. 生成测试数据...")
    from data_generator import RandomDataGenerator
    generator = RandomDataGenerator()
    test_data = generator.generate(days=100, random_seed=456)
    print(f"   测试数据: {len(test_data)} 天")
    
    # 4. 计算指标和信号
    print("\n4. 计算技术指标和交易信号...")
    data_with_indicators = strategy.calculate_indicators(test_data)
    signals = strategy.generate_signals(test_data)
    
    buy_count = (signals == 1).sum()
    sell_count = (signals == -1).sum()
    hold_count = (signals == 0).sum()
    
    print(f"   买入信号: {buy_count} 个")
    print(f"   卖出信号: {sell_count} 个")
    print(f"   持有信号: {hold_count} 个")
    
    # 5. 显示策略摘要
    print("\n5. 策略摘要:")
    strategy.print_summary()
    
    # 6. 保存结果
    print("6. 保存测试结果...")
    result_df = pd.DataFrame({
        'close': test_data['close'],
        'MA_short': data_with_indicators['MA_short'],
        'MA_long': data_with_indicators['MA_long'],
        'signal': signals
    })
    
    result_df.to_csv('ml_strategy_demo_result.csv')
    print("   ✅ 结果已保存到 ml_strategy_demo_result.csv")
    
    print(f"\n{'='*60}")
    print("🎉 策略文件生成演示完成!")
    print("✅ ML优化后的策略可以自动生成为独立文件")
    print("✅ 包含完整的参数、性能数据和使用说明")
    print("✅ 可以直接导入和使用，便于理解和维护")
    print(f"{'='*60}")


if __name__ == "__main__":
    demo_strategy_generation()
