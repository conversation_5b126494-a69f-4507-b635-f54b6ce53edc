#!/usr/bin/env python3
"""
ML优化双均线策略

这是一个由机器学习算法优化的双均线交叉策略。
策略通过遗传算法在历史数据上进行参数优化，寻找最优的均线组合。

优化参数:
- 短期均线: 27日
- 长期均线: 200日

历史性能 (回测):
- 总收益率: -9.88%
- 夏普比率: -0.807
- 最大回撤: -14.17%

生成时间: 2025-07-30 23:09:46
"""

import pandas as pd
import numpy as np


class DualMAStrategy:
    """ML优化的双均线策略"""
    
    def __init__(self):
        """初始化策略"""
        # ML优化的参数
        self.short_window = 27
        self.long_window = 200
        
        # 策略信息
        self.name = "dual_ma"
        self.description = "通过遗传算法优化的双均线策略"
        
        # 历史性能指标
        self.historical_performance = {
            'total_return': -0.0988,
            'sharpe_ratio': -0.807,
            'max_drawdown': -0.1417,
            'win_rate': 0.0000
        }
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算技术指标
        
        Args:
            data: 包含OHLCV的价格数据
            
        Returns:
            包含指标的数据框
        """
        df = data.copy()
        
        # 计算移动平均线
        df['MA_short'] = df['close'].rolling(window=self.short_window).mean()
        df['MA_long'] = df['close'].rolling(window=self.long_window).mean()
        
        return df
    
    def generate_signals(self, data: pd.DataFrame) -> pd.Series:
        """
        生成交易信号
        
        Args:
            data: 价格数据
            
        Returns:
            交易信号序列 (1: 买入, -1: 卖出, 0: 持有)
        """
        df = self.calculate_indicators(data)
        
        # 初始化信号
        signals = pd.Series(0, index=data.index)
        
        # 双均线交叉策略
        # 金叉：短均线上穿长均线 -> 买入
        # 死叉：短均线下穿长均线 -> 卖出
        signals[df['MA_short'] > df['MA_long']] = 1
        signals[df['MA_short'] < df['MA_long']] = -1
        
        return signals
    
    def get_strategy_info(self) -> dict:
        """获取策略信息"""
        return {
            'name': self.name,
            'type': 'dual_moving_average',
            'parameters': {
                'short_window': self.short_window,
                'long_window': self.long_window
            },
            'description': self.description,
            'historical_performance': self.historical_performance,
            'optimization_method': 'genetic_algorithm'
        }
    
    def backtest_summary(self) -> str:
        """返回回测摘要"""
        return f"""
策略回测摘要:
================
策略名称: {self.name}
参数设置: MA{self.short_window}/MA{self.long_window}

历史表现:
- 总收益率: {self.historical_performance['total_return']:.2%}
- 夏普比率: {self.historical_performance['sharpe_ratio']:.3f}
- 最大回撤: {self.historical_performance['max_drawdown']:.2%}
- 胜率: {self.historical_performance['win_rate']:.2%}

使用建议:
1. 该策略适用于趋势性较强的市场
2. 在震荡市场中可能产生较多假信号
3. 建议结合其他指标进行信号过滤
4. 注意控制仓位和风险管理
"""


# 使用示例
if __name__ == "__main__":
    # 创建策略实例
    strategy = DualMAStrategy()
    
    # 打印策略信息
    print(strategy.backtest_summary())
    
    # 如果有数据，可以这样使用:
    # data = pd.read_csv('your_data.csv', index_col=0, parse_dates=True)
    # signals = strategy.generate_signals(data)
    # print(f"生成了 {len(signals[signals != 0])} 个交易信号")
