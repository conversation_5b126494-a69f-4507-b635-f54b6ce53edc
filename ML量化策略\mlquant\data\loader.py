"""
数据加载器模块

提供多种数据源的统一加载接口，支持真实市场数据获取。
"""

import pandas as pd
import numpy as np
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import warnings

try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    YFINANCE_AVAILABLE = False
    warnings.warn("yfinance not installed. Yahoo Finance data will not be available.")

try:
    import alpha_vantage
    ALPHA_VANTAGE_AVAILABLE = True
except ImportError:
    ALPHA_VANTAGE_AVAILABLE = False
    warnings.warn("alpha_vantage not installed. Alpha Vantage data will not be available.")


class DataLoader:
    """统一数据加载器"""
    
    def __init__(self, cache_dir: str = "cache"):
        """
        初始化数据加载器
        
        Args:
            cache_dir: 缓存目录
        """
        self.cache_dir = cache_dir
        self._ensure_cache_dir()
    
    def _ensure_cache_dir(self):
        """确保缓存目录存在"""
        import os
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
    
    def load_yahoo_data(self, symbols: List[str], start_date: str, 
                       end_date: str, interval: str = '1d') -> Dict[str, pd.DataFrame]:
        """
        从Yahoo Finance加载数据
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期 'YYYY-MM-DD'
            end_date: 结束日期 'YYYY-MM-DD'
            interval: 数据间隔 ('1d', '1h', '5m', etc.)
            
        Returns:
            股票代码到数据框的映射
        """
        if not YFINANCE_AVAILABLE:
            raise ImportError("yfinance is required for Yahoo Finance data")
        
        data_dict = {}
        
        for symbol in symbols:
            try:
                print(f"Loading {symbol} data from Yahoo Finance...")
                ticker = yf.Ticker(symbol)
                data = ticker.history(start=start_date, end=end_date, interval=interval)
                
                if len(data) == 0:
                    print(f"Warning: No data found for {symbol}")
                    continue
                
                # 标准化列名
                data.columns = [col.lower() for col in data.columns]
                data_dict[symbol] = data
                
                print(f"Loaded {len(data)} records for {symbol}")
                
            except Exception as e:
                print(f"Error loading {symbol}: {e}")
                continue
        
        return data_dict
    
    def load_csv_data(self, file_path: str, symbol: str = None) -> pd.DataFrame:
        """
        从CSV文件加载数据
        
        Args:
            file_path: CSV文件路径
            symbol: 股票代码（可选）
            
        Returns:
            价格数据框
        """
        try:
            data = pd.read_csv(file_path, index_col=0, parse_dates=True)
            
            # 标准化列名
            data.columns = [col.lower() for col in data.columns]
            
            # 验证必要的列
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in data.columns]
            
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")
            
            print(f"Loaded {len(data)} records from {file_path}")
            return data
            
        except Exception as e:
            raise ValueError(f"Error loading CSV file {file_path}: {e}")
    
    def preprocess_data(self, data: pd.DataFrame, 
                       fill_method: str = 'forward') -> pd.DataFrame:
        """
        数据预处理
        
        Args:
            data: 原始数据
            fill_method: 缺失值填充方法 ('forward', 'backward', 'interpolate')
            
        Returns:
            预处理后的数据
        """
        df = data.copy()
        
        # 处理缺失值
        if fill_method == 'forward':
            df = df.fillna(method='ffill')
        elif fill_method == 'backward':
            df = df.fillna(method='bfill')
        elif fill_method == 'interpolate':
            df = df.interpolate()
        
        # 删除仍然存在的缺失值
        df = df.dropna()
        
        # 确保数据类型正确
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 验证数据完整性
        if len(df) == 0:
            raise ValueError("No valid data after preprocessing")
        
        # 验证价格数据的逻辑性
        invalid_rows = (df['high'] < df['low']) | (df['high'] < df['close']) | (df['low'] > df['close'])
        if invalid_rows.any():
            print(f"Warning: Found {invalid_rows.sum()} rows with invalid OHLC data")
            df = df[~invalid_rows]
        
        print(f"Preprocessed data: {len(df)} valid records")
        return df
    
    def get_data_info(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        获取数据基本信息
        
        Args:
            data: 价格数据
            
        Returns:
            数据信息字典
        """
        info = {
            'total_records': len(data),
            'date_range': {
                'start': data.index.min().strftime('%Y-%m-%d'),
                'end': data.index.max().strftime('%Y-%m-%d')
            },
            'columns': list(data.columns),
            'missing_values': data.isnull().sum().to_dict(),
            'data_types': data.dtypes.to_dict()
        }
        
        # 价格统计信息
        if 'close' in data.columns:
            info['price_stats'] = {
                'min_price': float(data['close'].min()),
                'max_price': float(data['close'].max()),
                'mean_price': float(data['close'].mean()),
                'volatility': float(data['close'].pct_change().std())
            }
        
        return info
