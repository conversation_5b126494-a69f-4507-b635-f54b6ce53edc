import backtrader as bt
import pandas as pd
import numpy as np
import tushare as ts 
import matplotlib.pyplot as plt
#%matplotlib inline
from pylab import mpl
mpl.rcParams['font.sans-serif']=['SimHei']
mpl.rcParams['axes.unicode_minus']=False

def get_data(code,start='2010-01-01',end='2020-08-31'):
    df=ts.get_k_data(code,autype='qfq',start=start,end=end)
    df.index=pd.to_datetime(df.date)
    df['openinterest']=0
    df=df[['open','high','low','close','volume','openinterest']]
    return df

dataframe=get_data('600000',start='2015-01-01')
print(dataframe.head())

class TestStrategy(bt.Strategy):

    def __init__(self):
        bt.ind.MACD(self.data)
        bt.ind.MACDHisto(self.data)
        bt.ind.RSI(self.data,period=14)
        bt.ind.BBands(self.data)

def main(data,strategy,pf=False):
    cerebro = bt.Cerebro()
    feed = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(feed) 
    #加载策略
    cerebro.addstrategy(strategy)
    # 设置初始资本为10,000
    startcash = 100000
    cerebro.broker.setcash(startcash) 
    # 设置交易手续费为 0.1%
    cerebro.broker.setcommission(commission=0.001) 
    cerebro.run()
    #获取回测结束后的总资金
    portvalue = cerebro.broker.getvalue()
    pnl = portvalue - startcash
    if pf:
        print(f'总资金: {round(portvalue,2)}')
        print(f'净收益: {round(pnl,2)}')
    cerebro.plot()

if __name__ == "__main__":
    data=get_data('601318','2020-03-01')
    main(data,TestStrategy)