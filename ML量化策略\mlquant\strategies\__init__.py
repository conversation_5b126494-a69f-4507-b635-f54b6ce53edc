"""
交易策略模块

提供各种交易策略的实现。

主要组件:
- base: 策略基类
- dual_ma: 双均线策略
- rsi: RSI策略
- bollinger: 布林带策略
"""

from .base import BaseStrategy
from .dual_ma import DualMAStrategy
from .rsi import RSIStrategy
from .bollinger import BollingerStrategy

# 导入ML策略模块
try:
    from .ml import *
except ImportError:
    # ML策略模块可能还未完全实现
    pass

__all__ = [
    'BaseStrategy',
    'DualMAStrategy',
    'RSIStrategy',
    'BollingerStrategy'
]
