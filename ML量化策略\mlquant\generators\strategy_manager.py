"""
策略管理器

管理生成的策略文件，提供策略的组织、查看和使用功能。
"""

import os
import json
import pandas as pd
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path


class StrategyManager:
    """策略管理器"""
    
    def __init__(self, strategies_dir: str = "generated_strategies"):
        """
        初始化策略管理器
        
        Args:
            strategies_dir: 策略文件目录
        """
        self.strategies_dir = strategies_dir
        self.metadata_file = os.path.join(strategies_dir, "strategies_metadata.json")
        
        # 确保目录存在
        if not os.path.exists(strategies_dir):
            os.makedirs(strategies_dir)
        
        # 加载策略元数据
        self.metadata = self._load_metadata()
    
    def _load_metadata(self) -> Dict[str, Any]:
        """加载策略元数据"""
        if os.path.exists(self.metadata_file):
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return {"strategies": {}, "created_time": datetime.now().isoformat()}
        else:
            return {"strategies": {}, "created_time": datetime.now().isoformat()}
    
    def _save_metadata(self):
        """保存策略元数据"""
        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(self.metadata, f, indent=2, ensure_ascii=False)
    
    def register_strategy(self, strategy_name: str, filepath: str, 
                         params: Dict[str, Any], performance: Dict[str, float],
                         description: str = ""):
        """
        注册新策略
        
        Args:
            strategy_name: 策略名称
            filepath: 策略文件路径
            params: 策略参数
            performance: 性能指标
            description: 策略描述
        """
        strategy_info = {
            "name": strategy_name,
            "filepath": filepath,
            "filename": os.path.basename(filepath),
            "params": params,
            "performance": performance,
            "description": description,
            "created_time": datetime.now().isoformat(),
            "file_size": os.path.getsize(filepath) if os.path.exists(filepath) else 0
        }
        
        self.metadata["strategies"][strategy_name] = strategy_info
        self.metadata["last_updated"] = datetime.now().isoformat()
        self._save_metadata()
        
        print(f"✅ 策略已注册: {strategy_name}")
    
    def list_strategies(self) -> pd.DataFrame:
        """
        列出所有策略
        
        Returns:
            策略列表DataFrame
        """
        if not self.metadata["strategies"]:
            print("📭 暂无已生成的策略")
            return pd.DataFrame()
        
        strategies_data = []
        for name, info in self.metadata["strategies"].items():
            strategies_data.append({
                "策略名称": name,
                "文件名": info["filename"],
                "总收益率": f"{info['performance'].get('total_return', 0):.2%}",
                "夏普比率": f"{info['performance'].get('sharpe_ratio', 0):.3f}",
                "最大回撤": f"{info['performance'].get('max_drawdown', 0):.2%}",
                "创建时间": info["created_time"][:19].replace('T', ' '),
                "文件大小": f"{info['file_size']/1024:.1f}KB"
            })
        
        df = pd.DataFrame(strategies_data)
        return df
    
    def get_strategy_info(self, strategy_name: str) -> Optional[Dict[str, Any]]:
        """
        获取策略详细信息
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            策略信息字典
        """
        return self.metadata["strategies"].get(strategy_name)
    
    def delete_strategy(self, strategy_name: str) -> bool:
        """
        删除策略
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            是否删除成功
        """
        if strategy_name not in self.metadata["strategies"]:
            print(f"❌ 策略不存在: {strategy_name}")
            return False
        
        strategy_info = self.metadata["strategies"][strategy_name]
        filepath = strategy_info["filepath"]
        
        # 删除文件
        if os.path.exists(filepath):
            os.remove(filepath)
            print(f"🗑️ 已删除策略文件: {filepath}")
        
        # 从元数据中移除
        del self.metadata["strategies"][strategy_name]
        self.metadata["last_updated"] = datetime.now().isoformat()
        self._save_metadata()
        
        print(f"✅ 策略已删除: {strategy_name}")
        return True
    
    def export_strategy_summary(self, output_file: str = "strategies_summary.txt"):
        """
        导出策略摘要
        
        Args:
            output_file: 输出文件名
        """
        if not self.metadata["strategies"]:
            print("📭 暂无策略可导出")
            return
        
        summary_lines = [
            "ML量化策略摘要报告",
            "=" * 50,
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"策略总数: {len(self.metadata['strategies'])}",
            f"策略目录: {self.strategies_dir}",
            "",
            "策略列表:",
            "-" * 30
        ]
        
        for i, (name, info) in enumerate(self.metadata["strategies"].items(), 1):
            perf = info["performance"]
            summary_lines.extend([
                f"{i}. {name}",
                f"   文件: {info['filename']}",
                f"   参数: {info['params']}",
                f"   性能: 收益率{perf.get('total_return', 0):.2%}, "
                f"夏普{perf.get('sharpe_ratio', 0):.3f}, "
                f"回撤{perf.get('max_drawdown', 0):.2%}",
                f"   创建: {info['created_time'][:19].replace('T', ' ')}",
                ""
            ])
        
        summary_lines.extend([
            "=" * 50,
            "使用说明:",
            "1. 策略文件位于 generated_strategies/ 目录",
            "2. 可直接导入使用: from generated_strategies.策略文件名 import 策略类名",
            "3. 每个策略文件包含完整的使用说明和历史性能数据",
            "4. 建议在使用前仔细阅读策略描述和风险提示"
        ])
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(summary_lines))
        
        print(f"📄 策略摘要已导出到: {output_file}")
    
    def find_best_strategies(self, metric: str = "sharpe_ratio", top_n: int = 5) -> List[str]:
        """
        查找最佳策略
        
        Args:
            metric: 评价指标 (sharpe_ratio, total_return, max_drawdown)
            top_n: 返回前N个策略
            
        Returns:
            最佳策略名称列表
        """
        if not self.metadata["strategies"]:
            return []
        
        strategies_with_metric = []
        for name, info in self.metadata["strategies"].items():
            metric_value = info["performance"].get(metric, 0)
            if metric == "max_drawdown":
                # 回撤越小越好，取负值排序
                metric_value = -abs(metric_value)
            strategies_with_metric.append((name, metric_value))
        
        # 按指标排序
        strategies_with_metric.sort(key=lambda x: x[1], reverse=True)
        
        return [name for name, _ in strategies_with_metric[:top_n]]
    
    def create_strategy_index(self):
        """创建策略索引文件"""
        if not self.metadata["strategies"]:
            return
        
        index_content = [
            '"""',
            '生成的策略索引',
            '',
            '这个文件提供了所有生成策略的快速导入接口。',
            '"""',
            '',
            '# 策略导入'
        ]
        
        for name, info in self.metadata["strategies"].items():
            filename = info["filename"].replace('.py', '')
            class_name = self._filename_to_classname(filename)
            index_content.append(f"# from .{filename} import {class_name}")
        
        index_content.extend([
            '',
            '# 策略列表',
            'AVAILABLE_STRATEGIES = {'
        ])
        
        for name, info in self.metadata["strategies"].items():
            filename = info["filename"].replace('.py', '')
            class_name = self._filename_to_classname(filename)
            index_content.append(f'    "{name}": "{filename}.{class_name}",')
        
        index_content.extend([
            '}',
            '',
            'def get_strategy_list():',
            '    """获取可用策略列表"""',
            '    return list(AVAILABLE_STRATEGIES.keys())',
            '',
            'def get_strategy_info():',
            '    """获取策略信息"""',
            f'    return {self.metadata["strategies"]}'
        ])
        
        index_file = os.path.join(self.strategies_dir, "strategy_index.py")
        with open(index_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(index_content))
        
        print(f"📋 策略索引已创建: {index_file}")
    
    def _filename_to_classname(self, filename: str) -> str:
        """将文件名转换为类名"""
        # 移除_strategy后缀
        name = filename.replace('_strategy', '')
        # 转换为驼峰命名
        parts = name.split('_')
        class_name = ''.join(word.capitalize() for word in parts)
        return class_name + 'Strategy'
    
    def print_summary(self):
        """打印策略管理摘要"""
        print(f"\n📁 策略管理器摘要")
        print(f"{'='*40}")
        print(f"策略目录: {self.strategies_dir}")
        print(f"策略总数: {len(self.metadata['strategies'])}")
        
        if self.metadata["strategies"]:
            print(f"最新策略: {max(self.metadata['strategies'].items(), key=lambda x: x[1]['created_time'])[0]}")
            
            # 最佳策略
            best_sharpe = self.find_best_strategies("sharpe_ratio", 1)
            if best_sharpe:
                print(f"最佳夏普: {best_sharpe[0]}")
            
            best_return = self.find_best_strategies("total_return", 1)
            if best_return:
                print(f"最佳收益: {best_return[0]}")
        
        print(f"{'='*40}")


if __name__ == "__main__":
    # 测试策略管理器
    manager = StrategyManager()
    
    # 显示摘要
    manager.print_summary()
    
    # 列出策略
    strategies_df = manager.list_strategies()
    if not strategies_df.empty:
        print("\n📊 策略列表:")
        print(strategies_df.to_string(index=False))
    
    # 创建索引
    manager.create_strategy_index()
    
    # 导出摘要
    manager.export_strategy_summary()
