"""
量化交易策略优化系统

企业级量化交易框架，支持多种策略、回测、风险管理和机器学习优化。

主要模块：
- config: 配置管理
- data: 数据生成和处理
- indicators: 技术指标计算
- strategies: 交易策略
- backtest: 回测引擎
- risk: 风险管理
- performance: 性能分析
- ml: 机器学习接口
- utils: 工具函数

作者: 量化交易团队
版本: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "量化交易团队"

# 导入核心模块
from . import config
from . import data
from . import indicators
from . import strategies
from . import backtest
from . import risk
from . import performance
from . import utils

__all__ = [
    'config',
    'data', 
    'indicators',
    'strategies',
    'backtest',
    'risk',
    'performance',
    'utils'
]
