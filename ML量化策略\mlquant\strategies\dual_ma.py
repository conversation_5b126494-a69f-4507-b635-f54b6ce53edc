"""
双均线策略

基于短期和长期移动平均线交叉的交易策略。
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple

from .base import BaseStrategy


class DualMAStrategy(BaseStrategy):
    """双均线策略"""
    
    def __init__(self, short_window: int = 20, long_window: int = 50):
        """
        初始化双均线策略
        
        Args:
            short_window: 短期均线窗口
            long_window: 长期均线窗口
        """
        super().__init__(f"DualMA_{short_window}_{long_window}")
        self.short_window = short_window
        self.long_window = long_window
    
    def generate_signals(self, data: pd.DataFrame, **params) -> pd.Series:
        """生成双均线交叉信号"""
        short_window = params.get('short_window', self.short_window)
        long_window = params.get('long_window', self.long_window)
        
        # 计算移动平均线
        short_ma = data['close'].rolling(window=int(short_window)).mean()
        long_ma = data['close'].rolling(window=int(long_window)).mean()
        
        # 生成信号：1买入，-1卖出，0持有
        signals = pd.Series(0, index=data.index)
        signals[short_ma > long_ma] = 1
        signals[short_ma < long_ma] = -1
        
        return signals
    
    def get_param_ranges(self) -> Dict[str, Tuple[float, float]]:
        """获取参数范围"""
        return {
            'short_window': (5, 50),
            'long_window': (20, 200)
        }
    
    def calculate_indicators(self, data: pd.DataFrame, 
                           short_window: int = None, 
                           long_window: int = None) -> pd.DataFrame:
        """
        计算技术指标
        
        Args:
            data: 价格数据
            short_window: 短期窗口
            long_window: 长期窗口
            
        Returns:
            包含指标的数据框
        """
        df = data.copy()
        
        short_window = short_window or self.short_window
        long_window = long_window or self.long_window
        
        df['MA_short'] = df['close'].rolling(window=short_window).mean()
        df['MA_long'] = df['close'].rolling(window=long_window).mean()
        
        return df
