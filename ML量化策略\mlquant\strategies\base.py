"""
策略基类

定义所有交易策略的通用接口。
"""

import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, Tuple, Any


class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, name: str = "BaseStrategy"):
        """
        初始化策略
        
        Args:
            name: 策略名称
        """
        self.name = name
    
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        生成交易信号
        
        Args:
            data: 价格数据
            **params: 策略参数
            
        Returns:
            交易信号序列 (1: 买入, -1: 卖出, 0: 持有)
        """
        pass
    
    @abstractmethod
    def get_param_ranges(self) -> Dict[str, Tuple[float, float]]:
        """
        获取参数搜索范围
        
        Returns:
            参数名称到范围的映射
        """
        pass
    
    def validate_params(self, **params) -> bool:
        """
        验证参数有效性
        
        Args:
            **params: 策略参数
            
        Returns:
            参数是否有效
        """
        param_ranges = self.get_param_ranges()
        
        for param_name, value in params.items():
            if param_name in param_ranges:
                min_val, max_val = param_ranges[param_name]
                if not (min_val <= value <= max_val):
                    return False
        
        return True
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns:
            策略信息字典
        """
        return {
            'name': self.name,
            'type': self.__class__.__name__,
            'param_ranges': self.get_param_ranges()
        }
