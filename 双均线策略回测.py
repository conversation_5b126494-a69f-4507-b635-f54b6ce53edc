import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates

# 解决中文显示问题
plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像是负号'-'显示为方块的问题

# --- 模拟参数 ---
num_days = 252 * 3  # 模拟大约3年的交易日
initial_price = 100  # 初始股价
drift = 0.0001  # 日对数收益率均值 (趋势)
volatility = 0.015  # 日对数收益率标准差 (波动率)
initial_capital = 100000  # 初始资金

# --- 交易成本和滑点参数 ---
commission_rate_open = 0.0001  # 买入佣金万1
commission_rate_close = 0.0001 # 卖出佣金万1
stamp_duty_rate_close = 0.0005 # 卖出印花税万5
min_commission = 5             # 每笔最低佣金5元
slippage_per_trade = 0.002     # 每笔滑点0.3%

# --- 1. 生成模拟数据 ---
# 生成日对数收益率
log_returns = np.random.normal(drift, volatility, num_days)
# 计算价格路径
price_path = np.exp(np.cumsum(log_returns)) * initial_price
# 创建日期索引
dates = pd.date_range(start='2020-01-01', periods=num_days, freq='B')  # 'B' for business day frequency
# 创建DataFrame
df = pd.DataFrame(data={'close': price_path}, index=dates)

# --- 2. 计算技术指标 ---
df['MA20'] = df['close'].rolling(window=20).mean()
df['MA50'] = df['close'].rolling(window=50).mean()

# --- 3. 生成交易信号 ---
# 原始信号：MA20 > MA50 时为 1 (买入状态)，否则为 -1 (卖出/空仓状态)
df['Signal_State'] = np.where(df['MA20'] > df['MA50'], 1, -1)

# 延迟信号：将信号延迟一天，模拟“下一日开盘”交易
# 使用 .fillna() 处理初始NaN值，假设期初处于 "卖出/空仓" 状态
df['Trading_Signal'] = df['Signal_State'].shift(1).fillna(-1)

# --- 4. 计算收益与成本 ---
# 市场日收益率 (基于收盘价的百分比变化)
df['Market_Return'] = df['close'].pct_change().fillna(0)

# 确定实际持仓 (1 表示持有多头，0 表示空仓)
# 如果 Trading_Signal 为 1，则持有多头；如果为 -1，则空仓
df['Position'] = np.where(df['Trading_Signal'] == 1, 1, 0)

# 策略的原始市场收益 (未扣除成本)
# df['Strategy_Market_Return'] = df['Position'] * df['Market_Return'] # 旧的计算方式
df['Strategy_Gross_Return'] = df['Position'].shift(1).fillna(0) * df['Market_Return'] # 修正：使用前一天的仓位计算当日毛收益

# 计算仓位变化 (用于计算交易成本)
# diff() 后，1表示开仓，-1表示平仓。fillna(0)处理第一个值。
df['Position_Change'] = df['Position'].diff().fillna(0)

# 初始化成本列 (百分比形式)
df['Cost_Pct_Commission'] = 0.0
df['Cost_Pct_Stamp_Duty'] = 0.0
df['Cost_Pct_Slippage'] = 0.0

# 标记买入和卖出日 (移到这里定义)
is_buy = df['Position_Change'] == 1
is_sell = df['Position_Change'] == -1

# 计算滑点成本百分比 (滑点总是发生)
df.loc[is_buy | is_sell, 'Cost_Pct_Slippage'] = slippage_per_trade

# 计算买入时的佣金成本百分比
if is_buy.any():
    trade_price_buy = df.loc[is_buy, 'close']
    buy_price_after_slippage = trade_price_buy * (1 + slippage_per_trade)
    commission_buy_val = np.maximum(buy_price_after_slippage * commission_rate_open, min_commission)
    df.loc[is_buy, 'Cost_Pct_Commission'] = commission_buy_val / trade_price_buy

# 计算卖出时的佣金和印花税成本百分比
if is_sell.any():
    trade_price_sell = df.loc[is_sell, 'close']
    sell_price_after_slippage = trade_price_sell * (1 - slippage_per_trade)
    commission_sell_val = np.maximum(sell_price_after_slippage * commission_rate_close, min_commission)
    stamp_duty_sell_val = sell_price_after_slippage * stamp_duty_rate_close
    df.loc[is_sell, 'Cost_Pct_Commission'] = commission_sell_val / trade_price_sell
    df.loc[is_sell, 'Cost_Pct_Stamp_Duty'] = stamp_duty_sell_val / trade_price_sell
    
# 总交易成本百分比
df['Total_Transaction_Cost_Pct'] = df['Cost_Pct_Commission'] + df['Cost_Pct_Stamp_Duty'] + df['Cost_Pct_Slippage']

# 策略的净日收益率
df['Strategy_Net_Return'] = df['Strategy_Gross_Return'] - df['Total_Transaction_Cost_Pct']

# --- 5. 计算累计收益 ---
df['Cumulative_Market_Return_Value'] = initial_capital * (1 + df['Market_Return']).cumprod()
df['Cumulative_Strategy_Return_Value'] = initial_capital * (1 + df['Strategy_Net_Return']).cumprod()

# --- 计算近似总交易成本金额 (重新加入) ---
# 使用上一日的累计策略价值乘以当日毛收益（1+毛收益率）作为当日交易前价值的代理
# 或者更直接地，使用交易发生日的开盘前资金价值
# 我们将使用 'Cumulative_Strategy_Value_StartOfDay' (下面会定义) 作为交易时的名义价值基础
df['Cumulative_Strategy_Value_StartOfDay'] = df['Cumulative_Strategy_Return_Value'].shift(1).fillna(initial_capital)

df['Monetary_Commission'] = 0.0
df['Monetary_Stamp_Duty'] = 0.0
df['Monetary_Slippage'] = 0.0

# 在发生交易的日子，根据当日开盘前的累计价值计算实际成本金额
# 买入成本
df.loc[is_buy, 'Monetary_Slippage'] += df.loc[is_buy, 'Cumulative_Strategy_Value_StartOfDay'] * slippage_per_trade
commission_buy_amount = df.loc[is_buy, 'Cumulative_Strategy_Value_StartOfDay'] * (1 + slippage_per_trade) * commission_rate_open
actual_commission_buy = np.maximum(commission_buy_amount, min_commission)
df.loc[is_buy, 'Monetary_Commission'] += actual_commission_buy

# 卖出成本
df.loc[is_sell, 'Monetary_Slippage'] += df.loc[is_sell, 'Cumulative_Strategy_Value_StartOfDay'] * slippage_per_trade # 滑点影响卖出价
value_before_sell_commission_stamp = df.loc[is_sell, 'Cumulative_Strategy_Value_StartOfDay'] * (1 - slippage_per_trade) # 考虑滑点后的卖出价值基础
commission_sell_amount = value_before_sell_commission_stamp * commission_rate_close
actual_commission_sell = np.maximum(commission_sell_amount, min_commission)
df.loc[is_sell, 'Monetary_Commission'] += actual_commission_sell
df.loc[is_sell, 'Monetary_Stamp_Duty'] += value_before_sell_commission_stamp * stamp_duty_rate_close


total_monetary_commission = df['Monetary_Commission'].sum()
total_monetary_stamp_duty = df['Monetary_Stamp_Duty'].sum()
total_monetary_slippage = df['Monetary_Slippage'].sum() # 注意：此滑点金额是基于开盘前价值估算，实际影响已在Net_Return中
total_monetary_costs = total_monetary_commission + total_monetary_stamp_duty # 滑点成本已体现在收益中，这里主要统计佣金和税

# --- 计算买入和卖出信号点，用于统计交易次数 ---
buy_signals_count = len(df[df['Trading_Signal'].diff() == 2])
sell_signals_count = len(df[df['Trading_Signal'].diff() == -2])
total_trades = buy_signals_count + sell_signals_count

# --- 6. 计算回测性能指标 ---
# 确保数据有效性，去除可能由rolling mean等产生的早期NaN值影响的收益计算
# 对于收益率序列，通常第一天的pct_change是NaN，我们已经用fillna(0)处理
# MA计算会产生NaN，但信号生成基于MA，所以信号和后续收益计算是有效的
# 我们将使用整个df的收益率序列进行计算，因为它们已经处理了初始NaN

trading_days_per_year = 252
risk_free_rate = 0.0 # 年化无风险收益率

# 市场指标
market_daily_returns = df['Market_Return']
market_total_return = (df['Cumulative_Market_Return_Value'].iloc[-1] / initial_capital) - 1
market_annualized_return = ((1 + market_daily_returns.mean()) ** trading_days_per_year) - 1
market_annualized_volatility = market_daily_returns.std() * np.sqrt(trading_days_per_year)
market_sharpe_ratio = (market_annualized_return - risk_free_rate) / market_annualized_volatility if market_annualized_volatility != 0 else 0

# 计算市场最大回撤
market_cumulative_returns_pct = (1 + market_daily_returns).cumprod() -1
market_running_max = np.maximum.accumulate(market_cumulative_returns_pct)
market_drawdown = (market_cumulative_returns_pct - market_running_max) / (1 + market_running_max) # Drawdown relative to peak
market_max_drawdown = market_drawdown.min()

# 新增：计算胜率、平均盈亏等交易分析指标 ---
trade_details = []
entry_capital_for_trade = 0
is_holding_trade = False

for i in range(len(df)):
    pos_change = df['Position_Change'].iloc[i]
    
    if pos_change == 1: # 买入开仓
        entry_capital_for_trade = df['Cumulative_Strategy_Value_StartOfDay'].iloc[i]
        is_holding_trade = True
    elif pos_change == -1 and is_holding_trade: # 卖出平仓
        exit_capital_from_trade = df['Cumulative_Strategy_Return_Value'].iloc[i] # 这是当日结束时的价值，已包含当日盈亏和交易成本
        
        trade_pnl_monetary = exit_capital_from_trade - entry_capital_for_trade
        trade_details.append({
            "pnl_monetary": trade_pnl_monetary,
        })
        is_holding_trade = False
        entry_capital_for_trade = 0 # 重置

num_completed_trades = 0
win_rate = 0.0
avg_pnl_monetary_per_trade = 0.0
avg_profit_monetary = 0.0
avg_loss_monetary = 0.0 # 通常为负值
payoff_ratio = 0.0 # 盈亏比

if trade_details:
    num_completed_trades = len(trade_details)
    winning_trades_details = [t for t in trade_details if t['pnl_monetary'] > 0]
    losing_trades_details = [t for t in trade_details if t['pnl_monetary'] < 0]

    if num_completed_trades > 0:
        win_rate = (len(winning_trades_details) / num_completed_trades) * 100
        avg_pnl_monetary_per_trade = sum(t['pnl_monetary'] for t in trade_details) / num_completed_trades
    
    if winning_trades_details:
        avg_profit_monetary = sum(t['pnl_monetary'] for t in winning_trades_details) / len(winning_trades_details)
    
    if losing_trades_details:
        avg_loss_monetary = sum(t['pnl_monetary'] for t in losing_trades_details) / len(losing_trades_details)
        if avg_loss_monetary != 0 and avg_profit_monetary > 0 : # 确保有盈利和亏损才计算盈亏比
             payoff_ratio = abs(avg_profit_monetary / avg_loss_monetary)
    elif winning_trades_details and not losing_trades_details: # 只有盈利没有亏损
        payoff_ratio = float('inf') # 盈亏比为无穷大


# 策略指标
strategy_daily_returns = df['Strategy_Net_Return']
strategy_total_return = (df['Cumulative_Strategy_Return_Value'].iloc[-1] / initial_capital) - 1
strategy_annualized_return = ((1 + strategy_daily_returns.mean()) ** trading_days_per_year) - 1
strategy_annualized_volatility = strategy_daily_returns.std() * np.sqrt(trading_days_per_year)
strategy_sharpe_ratio = (strategy_annualized_return - risk_free_rate) / strategy_annualized_volatility if strategy_annualized_volatility != 0 else 0

# 计算策略最大回撤
strategy_cumulative_returns_pct = (1 + strategy_daily_returns).cumprod() - 1
strategy_running_max = np.maximum.accumulate(strategy_cumulative_returns_pct)
strategy_drawdown = (strategy_cumulative_returns_pct - strategy_running_max) / (1+ strategy_running_max)
strategy_max_drawdown = strategy_drawdown.min()

# 索提诺比率 (Sortino Ratio)
market_negative_returns = market_daily_returns[market_daily_returns < 0]
market_downside_deviation = market_negative_returns.std() * np.sqrt(trading_days_per_year)
market_sortino_ratio = (market_annualized_return - risk_free_rate) / market_downside_deviation if market_downside_deviation != 0 else 0

strategy_negative_returns = strategy_daily_returns[strategy_daily_returns < 0]
strategy_downside_deviation = strategy_negative_returns.std() * np.sqrt(trading_days_per_year)
strategy_sortino_ratio = (strategy_annualized_return - risk_free_rate) / strategy_downside_deviation if strategy_downside_deviation != 0 else 0

# 卡玛比率 (Calmar Ratio)
market_calmar_ratio = market_annualized_return / abs(market_max_drawdown) if market_max_drawdown != 0 else 0
strategy_calmar_ratio = strategy_annualized_return / abs(strategy_max_drawdown) if strategy_max_drawdown != 0 else 0


# --- 7. 打印回测结果 ---
print("-" * 70)
print("模拟回测性能指标 (已更新交易成本和滑点)")
print("-" * 70)
print(f"模拟周期: {df.index[0].strftime('%Y-%m-%d')} 到 {df.index[-1].strftime('%Y-%m-%d')}")
print(f"初始资金: {initial_capital:,.2f}")
print(f"买入佣金费率: {commission_rate_open:.4%}, 卖出佣金费率: {commission_rate_close:.4%}") # 新增打印成本参数
print(f"卖出印花税费率: {stamp_duty_rate_close:.4%}, 最低佣金: {min_commission:.2f}元") # 新增打印成本参数
print(f"每笔交易滑点: {slippage_per_trade:.4%}") # 新增打印成本参数
print("-" * 70)

print(f"{'指标':<30} | {'市场':<20} | {'策略':<20}")
print("-" * 70)
print(f"{'总收益率':<30} | {market_total_return:>19.2%} | {strategy_total_return:>19.2%}")
print(f"{'年化收益率':<30} | {market_annualized_return:>19.2%} | {strategy_annualized_return:>19.2%}")
print(f"{'年化波动率':<30} | {market_annualized_volatility:>19.2%} | {strategy_annualized_volatility:>19.2%}")
print(f"{'夏普比率 (无风险利率=0%)':<30} | {market_sharpe_ratio:>19.2f} | {strategy_sharpe_ratio:>19.2f}")
print(f"{'索提诺比率 (无风险利率=0%)':<30} | {market_sortino_ratio:>19.2f} | {strategy_sortino_ratio:>19.2f}")
print(f"{'最大回撤':<30} | {market_max_drawdown:>19.2%} | {strategy_max_drawdown:>19.2%}")
print(f"{'卡玛比率':<30} | {market_calmar_ratio:>19.2f} | {strategy_calmar_ratio:>19.2f}")
print(f"{'总交易动作数 (买+卖)':<30} | {'N/A':<20} | {total_trades:<20}")
print(f"{'已完成交易次数':<30} | {'N/A':<20} | {num_completed_trades:<20}")
print(f"{'胜率':<30} | {'N/A':<20} | {win_rate:>19.2f}%")
print(f"{'平均每笔交易盈亏':<30} | {'N/A':<20} | {avg_pnl_monetary_per_trade:>19.2f}")
print(f"{'平均盈利(盈利交易)':<30} | {'N/A':<20} | {avg_profit_monetary:>19.2f}")
print(f"{'平均亏损(亏损交易)':<30} | {'N/A':<20} | {avg_loss_monetary:>19.2f}")
print(f"{'盈亏比 (平均盈利/平均亏损)':<30} | {'N/A':<20} | {payoff_ratio:>19.2f}")
print("-" * 70)
print("策略成本统计 (近似值):")
print(f"{'总佣金成本':<30} | {total_monetary_commission:>19,.2f}")
print(f"{'总印花税成本':<30} | {total_monetary_stamp_duty:>19,.2f}")
# print(f"{'总滑点成本(估算)':<30} | {total_monetary_slippage:>19,.2f}") # 滑点影响已在收益中，此为估算，可选择性打印
print(f"{'总计显性交易成本':<30} | {(total_monetary_commission + total_monetary_stamp_duty):>19,.2f}")
print("-" * 70)


# --- 8. 绘制结果 ---
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10), sharex=True, gridspec_kw={'height_ratios': [2, 1]})

ax1.plot(df.index, df['close'], label='模拟收盘价 (Close)', color='blue', linewidth=1.5)
ax1.plot(df.index, df['MA20'], label='20日均线 (MA20)', color='orange', linestyle='--', linewidth=1)
ax1.plot(df.index, df['MA50'], label='50日均线 (MA50)', color='green', linestyle='--', linewidth=1)
buy_signals_plot = df[df['Trading_Signal'].diff() == 2]
ax1.plot(buy_signals_plot.index, df['close'][buy_signals_plot.index], '^', markersize=10, color='red', lw=0, label='买入信号')
sell_signals_plot = df[df['Trading_Signal'].diff() == -2]
ax1.plot(sell_signals_plot.index, df['close'][sell_signals_plot.index], 'v', markersize=10, color='green', lw=0, label='卖出信号')
ax1.set_title('模拟股票价格、均线及交易信号', fontsize=16)
ax1.set_ylabel('价格', fontsize=12)
ax1.legend(loc='upper left')
ax1.grid(True)

ax2.plot(df.index, df['Cumulative_Market_Return_Value'], label='市场累计收益', color='gray', linewidth=1.5)
ax2.plot(df.index, df['Cumulative_Strategy_Return_Value'], label='策略累计收益 (含详细成本)', color='purple', linewidth=1.5)
ax2.set_title('市场累计收益 vs. 策略累计收益', fontsize=16)
ax2.set_ylabel('累计资金价值', fontsize=12)
ax2.set_xlabel('日期', fontsize=12)
ax2.legend(loc='upper left')
ax2.grid(True)

plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
plt.gca().xaxis.set_major_locator(mdates.AutoDateLocator(minticks=5, maxticks=10))
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()