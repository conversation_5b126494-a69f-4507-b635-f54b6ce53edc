"""
机器学习策略优化模块

使用各种机器学习算法自动发现和优化交易策略。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple, Optional, Callable
from abc import ABC, abstractmethod
import random
from dataclasses import dataclass

from ..strategies.base import BaseStrategy
from ..strategies.dual_ma import DualMAStrategy
from ..strategies.rsi import RSIStrategy
from ..strategies.bollinger import BollingerStrategy
from ..backtest.engine import BacktestEngine
from .genetic import GeneticOptimizer
from .grid_search import GridSearchOptimizer
from .random_search import RandomSearchOptimizer


@dataclass
class StrategyParams:
    """策略参数类"""
    name: str
    params: Dict[str, Any]
    fitness: float = 0.0
    
    def __repr__(self):
        return f"Strategy({self.name}, fitness={self.fitness:.4f}, params={self.params})"




class MLStrategyOptimizer:
    """机器学习策略优化器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化优化器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.ml_config = self.config.get('ml', {})
        self.strategy_config = self.config.get('strategy', {})
        
        # 可用策略
        self.strategies = {
            'dual_ma': DualMAStrategy(),
            'rsi': RSIStrategy(),
            'bollinger': BollingerStrategy()
        }
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        # 简化版本：直接返回默认配置
        return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'ml': {'algorithm': 'genetic'},
            'strategy': {
                'dual_ma': {'short_window': [5, 50], 'long_window': [20, 200]}
            }
        }
    
    def optimize(self, data: pd.DataFrame, strategy_name: str = 'dual_ma',
                fitness_func: Optional[Callable] = None) -> StrategyParams:
        """
        优化策略参数
        
        Args:
            data: 训练数据
            strategy_name: 策略名称
            fitness_func: 自定义适应度函数
            
        Returns:
            最优策略参数
        """
        if strategy_name not in self.strategies:
            raise ValueError(f"不支持的策略: {strategy_name}")
        
        strategy = self.strategies[strategy_name]
        algorithm = self.ml_config.get('algorithm', 'genetic')
        
        print(f"开始优化策略: {strategy_name}, 算法: {algorithm}")
        
        if fitness_func is None:
            fitness_func = self._default_fitness_function
        
        if algorithm == 'genetic':
            return self._genetic_algorithm(data, strategy, strategy_name, fitness_func)
        elif algorithm == 'grid_search':
            return self._grid_search(data, strategy, strategy_name, fitness_func)
        elif algorithm == 'random_search':
            return self._random_search(data, strategy, strategy_name, fitness_func)
        else:
            raise ValueError(f"不支持的优化算法: {algorithm}")
    
    def _default_fitness_function(self, data: pd.DataFrame, signals: pd.Series) -> float:
        """默认适应度函数：夏普比率"""
        # 计算策略收益
        returns = data['close'].pct_change().fillna(0)
        strategy_returns = signals.shift(1).fillna(0) * returns
        
        if strategy_returns.std() == 0:
            return 0.0
        
        # 计算夏普比率
        sharpe_ratio = strategy_returns.mean() / strategy_returns.std() * np.sqrt(252)
        return sharpe_ratio if not np.isnan(sharpe_ratio) else 0.0
    
    def _genetic_algorithm(self, data: pd.DataFrame, strategy: BaseStrategy,
                          strategy_name: str, fitness_func: Callable) -> StrategyParams:
        """遗传算法优化"""
        config = self.ml_config.get('genetic', {})
        population_size = config.get('population_size', 50)
        generations = config.get('generations', 100)
        mutation_rate = config.get('mutation_rate', 0.1)
        crossover_rate = config.get('crossover_rate', 0.8)
        elite_ratio = config.get('elite_ratio', 0.1)
        
        param_ranges = strategy.get_param_ranges()
        
        # 初始化种群
        population = self._create_initial_population(param_ranges, population_size)
        
        best_fitness = -np.inf
        best_params = None
        
        for generation in range(generations):
            # 评估适应度
            fitness_scores = []
            for params in population:
                signals = strategy.generate_signals(data, **params)
                fitness = fitness_func(data, signals)
                fitness_scores.append(fitness)
                
                if fitness > best_fitness:
                    best_fitness = fitness
                    best_params = params.copy()
            
            # 选择、交叉、变异
            population = self._evolve_population(
                population, fitness_scores, param_ranges,
                crossover_rate, mutation_rate, elite_ratio
            )
            
            if generation % 20 == 0:
                print(f"第 {generation} 代, 最佳适应度: {best_fitness:.4f}")
        
        print(f"优化完成! 最佳适应度: {best_fitness:.4f}")
        return StrategyParams(strategy_name, best_params, best_fitness)
    
    def _create_initial_population(self, param_ranges: Dict[str, Tuple[float, float]],
                                 population_size: int) -> List[Dict[str, Any]]:
        """创建初始种群"""
        population = []
        for _ in range(population_size):
            params = {}
            for param_name, (min_val, max_val) in param_ranges.items():
                if param_name.endswith('_window') or param_name == 'period':
                    # 整数参数
                    params[param_name] = random.randint(int(min_val), int(max_val))
                else:
                    # 浮点数参数
                    params[param_name] = random.uniform(min_val, max_val)
            population.append(params)
        return population
    
    def _evolve_population(self, population: List[Dict[str, Any]], 
                          fitness_scores: List[float],
                          param_ranges: Dict[str, Tuple[float, float]],
                          crossover_rate: float, mutation_rate: float,
                          elite_ratio: float) -> List[Dict[str, Any]]:
        """进化种群"""
        population_size = len(population)
        elite_size = int(population_size * elite_ratio)
        
        # 选择精英
        elite_indices = np.argsort(fitness_scores)[-elite_size:]
        new_population = [population[i].copy() for i in elite_indices]
        
        # 生成新个体
        while len(new_population) < population_size:
            # 选择父母
            parent1 = self._tournament_selection(population, fitness_scores)
            parent2 = self._tournament_selection(population, fitness_scores)
            
            # 交叉
            if random.random() < crossover_rate:
                child = self._crossover(parent1, parent2, param_ranges)
            else:
                child = parent1.copy()
            
            # 变异
            if random.random() < mutation_rate:
                child = self._mutate(child, param_ranges)
            
            new_population.append(child)
        
        return new_population[:population_size]
    
    def _tournament_selection(self, population: List[Dict[str, Any]], 
                            fitness_scores: List[float], tournament_size: int = 3) -> Dict[str, Any]:
        """锦标赛选择"""
        tournament_indices = random.sample(range(len(population)), tournament_size)
        tournament_fitness = [fitness_scores[i] for i in tournament_indices]
        winner_index = tournament_indices[np.argmax(tournament_fitness)]
        return population[winner_index].copy()
    
    def _crossover(self, parent1: Dict[str, Any], parent2: Dict[str, Any],
                  param_ranges: Dict[str, Tuple[float, float]]) -> Dict[str, Any]:
        """交叉操作"""
        child = {}
        for param_name in parent1.keys():
            if random.random() < 0.5:
                child[param_name] = parent1[param_name]
            else:
                child[param_name] = parent2[param_name]
        return child
    
    def _mutate(self, individual: Dict[str, Any],
               param_ranges: Dict[str, Tuple[float, float]]) -> Dict[str, Any]:
        """变异操作"""
        mutated = individual.copy()
        param_to_mutate = random.choice(list(individual.keys()))
        min_val, max_val = param_ranges[param_to_mutate]
        
        if param_to_mutate.endswith('_window') or param_to_mutate == 'period':
            mutated[param_to_mutate] = random.randint(int(min_val), int(max_val))
        else:
            mutated[param_to_mutate] = random.uniform(min_val, max_val)
        
        return mutated
    
    def _random_search(self, data: pd.DataFrame, strategy: BaseStrategy,
                      strategy_name: str, fitness_func: Callable) -> StrategyParams:
        """随机搜索优化"""
        config = self.ml_config.get('random_search', {})
        n_trials = config.get('n_trials', 200)
        
        param_ranges = strategy.get_param_ranges()
        best_fitness = -np.inf
        best_params = None
        
        print(f"开始随机搜索，试验次数: {n_trials}")
        
        for trial in range(n_trials):
            # 随机生成参数
            params = {}
            for param_name, (min_val, max_val) in param_ranges.items():
                if param_name.endswith('_window') or param_name == 'period':
                    params[param_name] = random.randint(int(min_val), int(max_val))
                else:
                    params[param_name] = random.uniform(min_val, max_val)
            
            # 评估适应度
            signals = strategy.generate_signals(data, **params)
            fitness = fitness_func(data, signals)
            
            if fitness > best_fitness:
                best_fitness = fitness
                best_params = params.copy()
            
            if trial % 50 == 0:
                print(f"试验 {trial}, 当前最佳适应度: {best_fitness:.4f}")
        
        print(f"随机搜索完成! 最佳适应度: {best_fitness:.4f}")
        return StrategyParams(strategy_name, best_params, best_fitness)
    
    def _grid_search(self, data: pd.DataFrame, strategy: BaseStrategy,
                    strategy_name: str, fitness_func: Callable) -> StrategyParams:
        """网格搜索优化（简化版）"""
        param_ranges = strategy.get_param_ranges()
        best_fitness = -np.inf
        best_params = None
        
        # 为每个参数创建网格点
        param_grids = {}
        for param_name, (min_val, max_val) in param_ranges.items():
            if param_name.endswith('_window') or param_name == 'period':
                param_grids[param_name] = list(range(int(min_val), int(max_val) + 1, 5))
            else:
                param_grids[param_name] = np.linspace(min_val, max_val, 10)
        
        # 计算总组合数
        total_combinations = 1
        for values in param_grids.values():
            total_combinations *= len(values)
        
        print(f"开始网格搜索，总组合数: {total_combinations}")
        
        # 遍历所有组合（简化版，只取部分组合）
        combinations_tested = 0
        max_combinations = min(1000, total_combinations)  # 限制最大组合数
        
        import itertools
        param_names = list(param_grids.keys())
        param_values = list(param_grids.values())
        
        for combination in itertools.product(*param_values):
            if combinations_tested >= max_combinations:
                break
                
            params = dict(zip(param_names, combination))
            
            # 评估适应度
            signals = strategy.generate_signals(data, **params)
            fitness = fitness_func(data, signals)
            
            if fitness > best_fitness:
                best_fitness = fitness
                best_params = params.copy()
            
            combinations_tested += 1
            
            if combinations_tested % 100 == 0:
                print(f"已测试 {combinations_tested} 组合, 当前最佳适应度: {best_fitness:.4f}")
        
        print(f"网格搜索完成! 最佳适应度: {best_fitness:.4f}")
        return StrategyParams(strategy_name, best_params, best_fitness)


if __name__ == "__main__":
    # 测试ML策略优化器
    from data_generator import RandomDataGenerator
    
    # 生成测试数据
    generator = RandomDataGenerator()
    data = generator.generate(days=500)
    
    # 创建优化器
    optimizer = MLStrategyOptimizer()
    
    # 优化双均线策略
    best_strategy = optimizer.optimize(data, 'dual_ma')
    print(f"\n最优策略: {best_strategy}")
    
    # 测试最优策略
    strategy = optimizer.strategies['dual_ma']
    signals = strategy.generate_signals(data, **best_strategy.params)
    print(f"信号统计: 买入={sum(signals==1)}, 卖出={sum(signals==-1)}, 持有={sum(signals==0)}")
