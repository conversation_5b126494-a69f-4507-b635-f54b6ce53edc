# 量化交易系统默认配置

# 数据配置
data:
  # 随机数据生成参数
  random_data:
    num_days: 756  # 3年交易日
    initial_price: 100.0
    drift: 0.0001  # 日对数收益率均值
    volatility: 0.015  # 日对数收益率标准差
    start_date: "2020-01-01"
    
  # 真实数据加载参数
  real_data:
    source: "tushare"  # 数据源: tushare, csv
    symbol: "600315.SH"
    start_date: "2020-01-01"
    end_date: "2023-12-31"

# 策略配置
strategies:
  dual_ma:
    short_window: 20
    long_window: 50
    signal_delay: 1  # 信号延迟天数
    
# 回测配置
backtest:
  initial_capital: 100000.0
  
  # 交易成本配置
  costs:
    commission_rate_buy: 0.0001   # 买入佣金万1
    commission_rate_sell: 0.0001  # 卖出佣金万1
    stamp_duty_rate: 0.0005       # 印花税万5
    min_commission: 5.0           # 最低佣金5元
    slippage_rate: 0.002          # 滑点0.2%
    
  # 风险参数
  risk:
    max_position_size: 1.0        # 最大仓位比例
    stop_loss: 0.05               # 止损比例5%
    take_profit: 0.10             # 止盈比例10%
    max_drawdown: 0.20            # 最大回撤20%

# 性能分析配置
performance:
  risk_free_rate: 0.0             # 无风险收益率
  trading_days_per_year: 252      # 年交易日数
  
  # 可视化配置
  visualization:
    figure_size: [14, 10]
    dpi: 100
    style: "seaborn"
    
# 日志配置
logging:
  level: "INFO"                   # DEBUG, INFO, WARNING, ERROR
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file_rotation: "1 day"
  file_retention: "30 days"
  console_output: true
  file_output: true
  
# 机器学习配置
ml:
  optimization:
    method: "grid_search"         # grid_search, random_search, bayesian
    cv_folds: 5                   # 交叉验证折数
    scoring: "sharpe_ratio"       # 优化目标
    n_jobs: -1                    # 并行任务数
    
  # 参数搜索空间
  parameter_space:
    dual_ma:
      short_window: [5, 10, 15, 20, 25, 30]
      long_window: [30, 40, 50, 60, 70, 80]

# 系统配置
system:
  random_seed: 42                 # 随机种子
  parallel_processing: true       # 是否启用并行处理
  cache_enabled: true             # 是否启用缓存
  debug_mode: false               # 调试模式
