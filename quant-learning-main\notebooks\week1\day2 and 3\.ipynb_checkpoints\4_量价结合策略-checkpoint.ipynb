{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Day 2：基于交易量的量化指标 - 量价结合策略\n", "\n", "本notebook演示如何将价格指标与交易量指标结合，制定简单的交易策略，并进行初步的回测。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib.dates as mdates\n", "import seaborn as sns\n", "import warnings\n", "\n", "# 忽略警告信息\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置绘图风格\n", "plt.style.use('ggplot')\n", "%matplotlib inline\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 加载数据和计算指标\n", "\n", "首先，我们需要加载数据并计算相关的技术指标。这里我们继续使用前面notebook中的平安银行数据。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 从CSV文件加载数据\n", "def load_data_from_csv(file_path):\n", "    df = pd.read_csv(file_path, index_col=0)\n", "    df.index = pd.to_datetime(df.index)\n", "    return df\n", "\n", "# 尝试从CSV加载数据，如果文件不存在，则从Tushare获取\n", "import os\n", "\n", "file_path = 'data/平安银行_data.csv'\n", "if os.path.exists(file_path):\n", "    data = load_data_from_csv(file_path)\n", "    print(\"从CSV文件加载平安银行的数据\")\n", "else:\n", "    import tushare as ts\n", "    ts.set_token('YOUR_TUSHARE_TOKEN')  # 替换为您的Token\n", "    pro = ts.pro_api()\n", "    \n", "    # 获取数据\n", "    data = pro.daily(ts_code='000001.SZ', start_date='20220101', end_date='20230101')\n", "    data = data.sort_values('trade_date')\n", "    data['trade_date'] = pd.to_datetime(data['trade_date'])\n", "    data.set_index('trade_date', inplace=True)\n", "    \n", "    print(\"从Tushare获取平安银行的数据\")\n", "\n", "# 查看数据\n", "data.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义计算指标的函数\n", "def calculate_indicators(data):\n", "    \"\"\"计算各种技术指标\"\"\"\n", "    df = data.copy()\n", "    \n", "    # 价格MA\n", "    df['ma5'] = df['close'].rolling(window=5).mean()\n", "    df['ma10'] = df['close'].rolling(window=10).mean()\n", "    df['ma20'] = df['close'].rolling(window=20).mean()\n", "    df['ma60'] = df['close'].rolling(window=60).mean()\n", "    \n", "    # 成交量MA\n", "    df['vol_ma5'] = df['vol'].rolling(window=5).mean()\n", "    df['vol_ma10'] = df['vol'].rolling(window=10).mean()\n", "    df['vol_ma20'] = df['vol'].rolling(window=20).mean()\n", "    \n", "    # OBV\n", "    obv = [0]\n", "    for i in range(1, len(df)):\n", "        if df['close'].iloc[i] > df['close'].iloc[i-1]:\n", "            obv.append(obv[-1] + df['vol'].iloc[i])\n", "        elif df['close'].iloc[i] < df['close'].iloc[i-1]:\n", "            obv.append(obv[-1] - df['vol'].iloc[i])\n", "        else:\n", "            obv.append(obv[-1])\n", "    df['obv'] = obv\n", "    df['obv_ma10'] = df['obv'].rolling(window=10).mean()\n", "    \n", "    # 量比\n", "    df['vol_ratio'] = df['vol'] / df['vol'].rolling(window=5).mean().shift(1)\n", "    \n", "    return df\n", "\n", "# 计算指标\n", "data = calculate_indicators(data)\n", "\n", "# 移除包含NaN的行（因为计算移动平均等指标会产生NaN值）\n", "data = data.dropna()\n", "\n", "# 查看数据\n", "data.tail()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 定义量价结合策略\n", "\n", "我们将实现几个简单的量价结合策略，并使用pandas进行模拟回测。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1 策略1：MA金叉/死叉 + 成交量确认"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def strategy_ma_volume(data):\n", "    \"\"\"\n", "    MA金叉/死叉 + 成交量确认策略\n", "    \n", "    买入条件：\n", "    1. 5日MA上穿10日MA（金叉）\n", "    2. 当日成交量大于5日成交量均线的1.5倍\n", "    \n", "    卖出条件：\n", "    1. 5日MA下穿10日MA（死叉）\n", "    2. 或者连续3天成交量萎缩（低于5日成交量均线）\n", "    \"\"\"\n", "    df = data.copy()\n", "    \n", "    # 计算5日MA相对于10日MA的位置\n", "    df['ma5_gt_ma10'] = df['ma5'] > df['ma10']\n", "    \n", "    # 计算金叉和死叉\n", "    df['golden_cross'] = (df['ma5_gt_ma10'] != df['ma5_gt_ma10'].shift(1)) & df['ma5_gt_ma10']\n", "    df['death_cross'] = (df['ma5_gt_ma10'] != df['ma5_gt_ma10'].shift(1)) & ~df['ma5_gt_ma10']\n", "    \n", "    # 成交量是否大于5日均线的1.5倍\n", "    df['vol_surge'] = df['vol'] > df['vol_ma5'] * 1.5\n", "    \n", "    # 是否连续3天成交量萎缩\n", "    df['vol_below_ma'] = df['vol'] < df['vol_ma5']\n", "    df['vol_shrink_3d'] = df['vol_below_ma'] & df['vol_below_ma'].shift(1) & df['vol_below_ma'].shift(2)\n", "    \n", "    # 生成买入和卖出信号\n", "    df['buy_signal'] = df['golden_cross'] & df['vol_surge']\n", "    df['sell_signal'] = df['death_cross'] | df['vol_shrink_3d']\n", "    \n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 策略2：OBV + MA 策略"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def strategy_obv_ma(data):\n", "    \"\"\"\n", "    OBV + MA策略\n", "    \n", "    买入条件：\n", "    1. 收盘价站上20日均线\n", "    2. OBV大于其10日均线\n", "    \n", "    卖出条件：\n", "    1. 收盘价跌破20日均线\n", "    2. 或者OBV跌破其10日均线\n", "    \"\"\"\n", "    df = data.copy()\n", "    \n", "    # 价格相对于20日均线的位置\n", "    df['price_gt_ma20'] = df['close'] > df['ma20']\n", "    \n", "    # OBV相对于其10日均线的位置\n", "    df['obv_gt_ma10'] = df['obv'] > df['obv_ma10']\n", "    \n", "    # 生成买入和卖出信号\n", "    df['buy_signal'] = df['price_gt_ma20'] & df['obv_gt_ma10'] & (~df['price_gt_ma20'].shift(1) | ~df['obv_gt_ma10'].shift(1))\n", "    df['sell_signal'] = (~df['price_gt_ma20'] | ~df['obv_gt_ma10']) & (df['price_gt_ma20'].shift(1) & df['obv_gt_ma10'].shift(1))\n", "    \n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 策略3：量比突增 + 价格突破策略"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def strategy_volume_ratio_breakout(data):\n", "    \"\"\"\n", "    量比突增 + 价格突破策略\n", "    \n", "    买入条件：\n", "    1. 量比大于2（当日成交量是5日平均的2倍以上）\n", "    2. 价格突破60日均线\n", "    \n", "    卖出条件：\n", "    1. 价格跌破10日均线\n", "    2. 或者连续3天成交量萎缩\n", "    \"\"\"\n", "    df = data.copy()\n", "    \n", "    # 量比是否大于2\n", "    df['vol_ratio_gt2'] = df['vol_ratio'] > 2\n", "    \n", "    # 价格是否突破60日均线\n", "    df['price_cross_ma60'] = (df['close'] > df['ma60']) & (df['close'].shift(1) <= df['ma60'].shift(1))\n", "    \n", "    # 价格是否跌破10日均线\n", "    df['price_below_ma10'] = df['close'] < df['ma10']\n", "    \n", "    # 是否连续3天成交量萎缩\n", "    df['vol_below_ma'] = df['vol'] < df['vol_ma5']\n", "    df['vol_shrink_3d'] = df['vol_below_ma'] & df['vol_below_ma'].shift(1) & df['vol_below_ma'].shift(2)\n", "    \n", "    # 生成买入和卖出信号\n", "    df['buy_signal'] = df['vol_ratio_gt2'] & df['price_cross_ma60']\n", "    df['sell_signal'] = df['price_below_ma10'] | df['vol_shrink_3d']\n", "    \n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 模拟回测\n", "\n", "接下来，我们使用pandas来模拟回测这些策略的表现。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def backtest(data, strategy_func, initial_capital=100000, position_size=0.9, commission_rate=0.001):\n", "    \"\"\"\n", "    简单的回测函数\n", "    \n", "    参数:\n", "    data: DataFrame, 包含价格数据\n", "    strategy_func: function, 策略函数\n", "    initial_capital: float, 初始资金\n", "    position_size: float, 仓位比例 (0-1)\n", "    commission_rate: float, 手续费率\n", "    \n", "    返回:\n", "    包含回测结果的DataFrame\n", "    \"\"\"\n", "    # 应用策略函数\n", "    df = strategy_func(data)\n", "    \n", "    # 初始化回测结果\n", "    df['position'] = 0  # 0表示空仓，1表示持仓\n", "    df['capital'] = initial_capital  # 资金\n", "    df['holdings'] = 0  # 持股数\n", "    df['equity'] = initial_capital  # 总资产（现金+持股价值）\n", "    \n", "    # 模拟交易\n", "    current_position = 0\n", "    capital = initial_capital\n", "    holdings = 0\n", "    \n", "    for i in range(1, len(df)):\n", "        # 复制前一天的状态\n", "        df.iloc[i, df.columns.get_loc('position')] = current_position\n", "        df.iloc[i, df.columns.get_loc('capital')] = capital\n", "        df.iloc[i, df.columns.get_loc('holdings')] = holdings\n", "        \n", "        # 更新持股价值\n", "        holdings_value = holdings * df['close'].iloc[i]\n", "        \n", "        # 买入信号\n", "        if df['buy_signal'].iloc[i] and current_position == 0:\n", "            # 计算可买入的股数（考虑手续费）\n", "            max_shares = int((capital * position_size) / (df['close'].iloc[i] * (1 + commission_rate)))\n", "            holdings = max_shares\n", "            cost = holdings * df['close'].iloc[i] * (1 + commission_rate)\n", "            capital -= cost\n", "            current_position = 1\n", "        \n", "        # 卖出信号\n", "        elif df['sell_signal'].iloc[i] and current_position == 1:\n", "            # 卖出所有持股\n", "            revenue = holdings * df['close'].iloc[i] * (1 - commission_rate)\n", "            capital += revenue\n", "            holdings = 0\n", "            current_position = 0\n", "        \n", "        # 更新当天状态\n", "        df.iloc[i, df.columns.get_loc('position')] = current_position\n", "        df.iloc[i, df.columns.get_loc('capital')] = capital\n", "        df.iloc[i, df.columns.get_loc('holdings')] = holdings\n", "        df.iloc[i, df.columns.get_loc('equity')] = capital + holdings * df['close'].iloc[i]\n", "    \n", "    # 计算每日收益率\n", "    df['daily_return'] = df['equity'].pct_change()\n", "    \n", "    # 计算累积收益率\n", "    df['cumulative_return'] = (1 + df['daily_return']).cumprod() - 1\n", "    \n", "    # 计算买入和卖出点\n", "    df['buy_execute'] = df['position'].diff() > 0\n", "    df['sell_execute'] = df['position'].diff() < 0\n", "    \n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.1 回测策略1：MA金叉/死叉 + 成交量确认"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 回测策略1\n", "results_1 = backtest(data, strategy_ma_volume)\n", "\n", "# 查看回测结果\n", "print(\"策略1：MA金叉/死叉 + 成交量确认\")\n", "print(f\"起始资金: 100,000元\")\n", "print(f\"最终资产: {results_1['equity'].iloc[-1]:.2f}元\")\n", "print(f\"总收益率: {results_1['cumulative_return'].iloc[-1] * 100:.2f}%\")\n", "print(f\"交易次数: {results_1['buy_execute'].sum()}次\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 可视化策略1的回测结果\n", "fig, axes = plt.subplots(2, 1, figsize=(14, 10), sharex=True, gridspec_kw={'height_ratios': [2, 1]})\n", "\n", "# 绘制股价和交易信号\n", "axes[0].plot(results_1.index, results_1['close'], label='收盘价', color='blue', alpha=0.6)\n", "axes[0].scatter(results_1[results_1['buy_execute']].index, \n", "               results_1.loc[results_1['buy_execute'], 'close'], \n", "               marker='^', color='green', s=100, label='买入')\n", "axes[0].scatter(results_1[results_1['sell_execute']].index, \n", "               results_1.loc[results_1['sell_execute'], 'close'], \n", "               marker='v', color='red', s=100, label='卖出')\n", "axes[0].set_title('策略1：MA金叉/死叉 + 成交量确认 - 交易信号')\n", "axes[0].set_ylabel('价格')\n", "axes[0].legend(loc='upper left')\n", "axes[0].grid(True)\n", "\n", "# 绘制资产曲线\n", "axes[1].plot(results_1.index, results_1['equity'], label='资产', color='green')\n", "axes[1].plot(results_1.index, [100000] * len(results_1), '--', color='gray', alpha=0.5, label='基准线')\n", "axes[1].set_title('策略1：资产曲线')\n", "axes[1].set_xlabel('日期')\n", "axes[1].set_ylabel('资产(元)')\n", "axes[1].legend(loc='upper left')\n", "axes[1].grid(True)\n", "\n", "# 添加日期格式化\n", "date_format = mdates.DateFormatter('%Y-%m')\n", "axes[1].xaxis.set_major_formatter(date_format)\n", "fig.autofmt_xdate()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 回测策略2：OBV + MA 策略"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 回测策略2\n", "results_2 = backtest(data, strategy_obv_ma)\n", "\n", "# 查看回测结果\n", "print(\"策略2：OBV + MA策略\")\n", "print(f\"起始资金: 100,000元\")\n", "print(f\"最终资产: {results_2['equity'].iloc[-1]:.2f}元\")\n", "print(f\"总收益率: {results_2['cumulative_return'].iloc[-1] * 100:.2f}%\")\n", "print(f\"交易次数: {results_2['buy_execute'].sum()}次\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 可视化策略2的回测结果\n", "fig, axes = plt.subplots(2, 1, figsize=(14, 10), sharex=True, gridspec_kw={'height_ratios': [2, 1]})\n", "\n", "# 绘制股价和交易信号\n", "axes[0].plot(results_2.index, results_2['close'], label='收盘价', color='blue', alpha=0.6)\n", "axes[0].scatter(results_2[results_2['buy_execute']].index, \n", "               results_2.loc[results_2['buy_execute'], 'close'], \n", "               marker='^', color='green', s=100, label='买入')\n", "axes[0].scatter(results_2[results_2['sell_execute']].index, \n", "               results_2.loc[results_2['sell_execute'], 'close'], \n", "               marker='v', color='red', s=100, label='卖出')\n", "axes[0].set_title('策略2：OBV + MA策略 - 交易信号')\n", "axes[0].set_ylabel('价格')\n", "axes[0].legend(loc='upper left')\n", "axes[0].grid(True)\n", "\n", "# 绘制资产曲线\n", "axes[1].plot(results_2.index, results_2['equity'], label='资产', color='purple')\n", "axes[1].plot(results_2.index, [100000] * len(results_2), '--', color='gray', alpha=0.5, label='基准线')\n", "axes[1].set_title('策略2：资产曲线')\n", "axes[1].set_xlabel('日期')\n", "axes[1].set_ylabel('资产(元)')\n", "axes[1].legend(loc='upper left')\n", "axes[1].grid(True)\n", "\n", "# 添加日期格式化\n", "date_format = mdates.DateFormatter('%Y-%m')\n", "axes[1].xaxis.set_major_formatter(date_format)\n", "fig.autofmt_xdate()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 回测策略3：量比突增 + 价格突破策略"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 回测策略3\n", "results_3 = backtest(data, strategy_volume_ratio_breakout)\n", "\n", "# 查看回测结果\n", "print(\"策略3：量比突增 + 价格突破策略\")\n", "print(f\"起始资金: 100,000元\")\n", "print(f\"最终资产: {results_3['equity'].iloc[-1]:.2f}元\")\n", "print(f\"总收益率: {results_3['cumulative_return'].iloc[-1] * 100:.2f}%\")\n", "print(f\"交易次数: {results_3['buy_execute'].sum()}次\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 可视化策略3的回测结果\n", "fig, axes = plt.subplots(2, 1, figsize=(14, 10), sharex=True, gridspec_kw={'height_ratios': [2, 1]})\n", "\n", "# 绘制股价和交易信号\n", "axes[0].plot(results_3.index, results_3['close'], label='收盘价', color='blue', alpha=0.6)\n", "axes[0].scatter(results_3[results_3['buy_execute']].index, \n", "               results_3.loc[results_3['buy_execute'], 'close'], \n", "               marker='^', color='green', s=100, label='买入')\n", "axes[0].scatter(results_3[results_3['sell_execute']].index, \n", "               results_3.loc[results_3['sell_execute'], 'close'], \n", "               marker='v', color='red', s=100, label='卖出')\n", "axes[0].set_title('策略3：量比突增 + 价格突破策略 - 交易信号')\n", "axes[0].set_ylabel('价格')\n", "axes[0].legend(loc='upper left')\n", "axes[0].grid(True)\n", "\n", "# 绘制资产曲线\n", "axes[1].plot(results_3.index, results_3['equity'], label='资产', color='orange')\n", "axes[1].plot(results_3.index, [100000] * len(results_3), '--', color='gray', alpha=0.5, label='基准线')\n", "axes[1].set_title('策略3：资产曲线')\n", "axes[1].set_xlabel('日期')\n", "axes[1].set_ylabel('资产(元)')\n", "axes[1].legend(loc='upper left')\n", "axes[1].grid(True)\n", "\n", "# 添加日期格式化\n", "date_format = mdates.DateFormatter('%Y-%m')\n", "axes[1].xaxis.set_major_formatter(date_format)\n", "fig.autofmt_xdate()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 策略比较"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建资产曲线比较图\n", "fig, ax = plt.subplots(figsize=(14, 8))\n", "\n", "ax.plot(results_1.index, results_1['equity'], label='策略1：MA金叉/死叉 + 成交量确认', color='green')\n", "ax.plot(results_2.index, results_2['equity'], label='策略2：OBV + MA策略', color='purple')\n", "ax.plot(results_3.index, results_3['equity'], label='策略3：量比突增 + 价格突破策略', color='orange')\n", "ax.plot(results_1.index, [100000] * len(results_1), '--', color='gray', alpha=0.5, label='基准线')\n", "\n", "ax.set_title('三种量价结合策略的资产曲线对比')\n", "ax.set_xlabel('日期')\n", "ax.set_ylabel('资产(元)')\n", "ax.legend(loc='upper left')\n", "ax.grid(True)\n", "\n", "# 添加日期格式化\n", "date_format = mdates.DateFormatter('%Y-%m')\n", "ax.xaxis.set_major_formatter(date_format)\n", "fig.autofmt_xdate()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 计算各策略的性能指标\n", "def calculate_metrics(results):\n", "    metrics = {}\n", "    \n", "    # 总收益率\n", "    metrics['total_return'] = results['cumulative_return'].iloc[-1] * 100\n", "    \n", "    # 年化收益率 (假设252个交易日)\n", "    days = (results.index[-1] - results.index[0]).days\n", "    metrics['annual_return'] = ((1 + results['cumulative_return'].iloc[-1]) ** (252 / days) - 1) * 100\n", "    \n", "    # 最大回撤\n", "    cumulative_max = results['equity'].cummax()\n", "    drawdown = (results['equity'] - cumulative_max) / cumulative_max\n", "    metrics['max_drawdown'] = drawdown.min() * 100\n", "    \n", "    # 夏普比率 (假设无风险利率为0.03)\n", "    risk_free_rate = 0.03\n", "    excess_return = results['daily_return'].mean() * 252 - risk_free_rate\n", "    volatility = results['daily_return'].std() * (252 ** 0.5)\n", "    metrics['sharpe_ratio'] = excess_return / volatility if volatility != 0 else 0\n", "    \n", "    # 交易次数\n", "    metrics['trade_count'] = results['buy_execute'].sum()\n", "    \n", "    # 日胜率\n", "    win_days = (results['daily_return'] > 0).sum()\n", "    total_days = len(results)\n", "    metrics['win_rate'] = win_days / total_days * 100 if total_days > 0 else 0\n", "    \n", "    return metrics\n", "\n", "# 计算三个策略的性能指标\n", "metrics_1 = calculate_metrics(results_1)\n", "metrics_2 = calculate_metrics(results_2)\n", "metrics_3 = calculate_metrics(results_3)\n", "\n", "# 创建性能指标比较表格\n", "metrics_df = pd.DataFrame({\n", "    '策略1：MA金叉/死叉 + 成交量确认': metrics_1,\n", "    '策略2：OBV + MA策略': metrics_2,\n", "    '策略3：量比突增 + 价格突破策略': metrics_3\n", "})\n", "\n", "# 重命名索引\n", "metrics_df.index = ['总收益率(%)', '年化收益率(%)', '最大回撤(%)', '夏普比率', '交易次数', '日胜率(%)']\n", "\n", "# 显示性能指标比较表格\n", "metrics_df.round(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 策略分析与讨论\n", "\n", "通过上述的回测和性能比较，我们可以得出以下几点观察和分析：\n", "\n", "1. **策略表现对比**：\n", "   - 策略X表现最佳，总收益率为X%，年化收益率为X%\n", "   - 策略Y的最大回撤最小，为X%\n", "   - 策略Z的夏普比率最高，为X\n", "\n", "2. **交易频率**：\n", "   - 策略X的交易次数最多，可能带来较高的交易成本\n", "   - 策略Y的交易次数最少，但单次交易的平均收益率较高\n", "\n", "3. **胜率和风险**：\n", "   - 策略X的日胜率最高，但最大回撤也较大\n", "   - 策略Y虽然胜率不高，但风险控制较好，最大回撤较小\n", "\n", "4. **量价结合的有效性**：\n", "   - 成交量确认似乎能有效减少价格突破的假信号\n", "   - OBV指标在识别趋势变化方面表现较好\n", "   - 量比突增作为短期交易信号的有效性有待进一步验证\n", "\n", "5. **改进方向**：\n", "   - 优化参数：通过调整MA周期、成交量阈值等参数可能获得更好的结果\n", "   - 组合策略：将不同策略的买入和卖出信号结合可能提高表现\n", "   - 增加过滤条件：加入市场状态判断，在不同市场环境下使用不同策略\n", "   - 扩大样本：在更多股票和更长时间周期上测试策略的稳定性\n", "\n", "需要注意的是，这些策略仅在特定时间范围内的特定股票上进行了测试，其普适性和稳定性还需要进一步验证。此外，回测结果也可能受到「生存偏差」和「前视偏差」的影响，实际交易中的表现可能会有所不同。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 总结\n", "\n", "在本notebook中，我们完成了以下任务：\n", "\n", "1. 定义了三种不同的量价结合策略：\n", "   - MA金叉/死叉 + 成交量确认\n", "   - OBV + MA策略\n", "   - 量比突增 + 价格突破策略\n", "\n", "2. 实现了一个简单的回测框架，用于模拟策略在历史数据上的表现\n", "\n", "3. 对三种策略进行了回测，并可视化了交易信号和资产曲线\n", "\n", "4. 计算了各策略的性能指标，包括总收益率、年化收益率、最大回撤、夏普比率等\n", "\n", "5. 分析了各策略的优缺点，并提出了可能的改进方向\n", "\n", "这些量价结合策略展示了交易量指标如何与价格指标一起使用，以提高交易决策的准确性。通过合理结合价格和交易量信息，我们可以更全面地分析市场状况，识别更可靠的交易信号。"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}