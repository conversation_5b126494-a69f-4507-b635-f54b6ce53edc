"""
随机搜索优化器

使用随机搜索优化策略参数。
"""

import numpy as np
from typing import Dict, Tuple, List, Callable


class RandomSearchOptimizer:
    """随机搜索优化器"""
    
    def __init__(self, n_iterations: int = 100):
        """
        初始化随机搜索优化器
        
        Args:
            n_iterations: 搜索迭代次数
        """
        self.n_iterations = n_iterations
        self.results: List[Tuple[Dict[str, float], float]] = []
    
    def optimize(self, param_ranges: Dict[str, Tuple[float, float]], 
                fitness_func: Callable[[Dict[str, float]], float]) -> Dict[str, float]:
        """
        执行随机搜索优化
        
        Args:
            param_ranges: 参数范围字典
            fitness_func: 适应度函数
            
        Returns:
            最优参数
        """
        best_params = None
        best_fitness = float('-inf')
        
        for _ in range(self.n_iterations):
            # 随机生成参数
            params = {}
            for param_name, (min_val, max_val) in param_ranges.items():
                params[param_name] = np.random.uniform(min_val, max_val)
            
            # 计算适应度
            fitness = fitness_func(params)
            self.results.append((params.copy(), fitness))
            
            # 更新最佳结果
            if fitness > best_fitness:
                best_fitness = fitness
                best_params = params.copy()
        
        return best_params
    
    def get_results(self) -> List[Tuple[Dict[str, float], float]]:
        """获取所有搜索结果"""
        return self.results
    
    def get_top_results(self, n: int = 10) -> List[Tuple[Dict[str, float], float]]:
        """获取前N个最佳结果"""
        sorted_results = sorted(self.results, key=lambda x: x[1], reverse=True)
        return sorted_results[:n]
