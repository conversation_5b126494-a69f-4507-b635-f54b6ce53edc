# 量化交易策略优化系统

企业级量化交易框架，支持多种策略、回测、风险管理和机器学习优化。

## 项目特性

- 🏗️ **模块化架构**: 松耦合的模块设计，易于扩展和维护
- 📊 **多策略支持**: 支持多种交易策略的插件化管理
- 🔄 **完整回测**: 包含交易成本、滑点、印花税的精确回测
- 📈 **性能分析**: 全面的绩效指标和可视化分析
- ⚠️ **风险管理**: 内置仓位管理、止损止盈等风险控制
- 🤖 **机器学习**: 预留ML接口，支持策略参数优化
- 📝 **配置管理**: 灵活的配置文件系统
- 🧪 **测试覆盖**: 完整的单元测试保证代码质量

## 项目结构

```
策略优化/
├── config/                 # 配置管理
│   ├── __init__.py
│   ├── settings.py         # 配置加载器
│   └── default.yaml        # 默认配置
├── data/                   # 数据模块
│   ├── __init__.py
│   ├── generators.py       # 数据生成器
│   └── loaders.py         # 数据加载器
├── indicators/             # 技术指标
│   ├── __init__.py
│   ├── base.py            # 指标基类
│   └── moving_average.py   # 移动平均线
├── strategies/             # 交易策略
│   ├── __init__.py
│   ├── base.py            # 策略基类
│   └── dual_ma.py         # 双均线策略
├── backtest/              # 回测引擎
│   ├── __init__.py
│   ├── engine.py          # 回测引擎
│   └── broker.py          # 模拟经纪商
├── risk/                  # 风险管理
│   ├── __init__.py
│   └── manager.py         # 风险管理器
├── performance/           # 性能分析
│   ├── __init__.py
│   ├── metrics.py         # 性能指标
│   └── visualizer.py      # 可视化
├── ml/                    # 机器学习接口
│   ├── __init__.py
│   └── optimizer.py       # 参数优化器
├── utils/                 # 工具函数
│   ├── __init__.py
│   ├── logger.py          # 日志工具
│   └── exceptions.py      # 异常定义
├── tests/                 # 单元测试
├── logs/                  # 日志文件
├── main.py               # 主程序入口
├── requirements.txt       # 依赖包
└── README.md             # 项目说明
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行示例

```bash
python main.py
```

### 3. 自定义配置

编辑 `config/default.yaml` 文件来调整策略参数和系统配置。

## 使用示例

```python
from 策略优化 import backtest, strategies, data

# 创建数据生成器
data_gen = data.RandomDataGenerator()
df = data_gen.generate(days=252*3)

# 创建策略
strategy = strategies.DualMAStrategy(short_window=20, long_window=50)

# 运行回测
engine = backtest.BacktestEngine()
results = engine.run(df, strategy)

# 分析结果
print(f"总收益率: {results.total_return:.2%}")
print(f"夏普比率: {results.sharpe_ratio:.2f}")
```

## 开发指南

### 添加新策略

1. 继承 `strategies.BaseStrategy` 类
2. 实现 `generate_signals` 方法
3. 在配置文件中注册策略

### 添加新指标

1. 继承 `indicators.BaseIndicator` 类
2. 实现 `calculate` 方法
3. 添加必要的参数验证

## 测试

```bash
# 运行所有测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=策略优化 --cov-report=html
```

## 许可证

MIT License
