import pandas as pd
import os
import re # 导入 re 模块

def process_single_csv(input_file_path, output_file_path, target_symbol):
    """
    从单个CSV文件中提取指定symbol的数据并保存到新的CSV文件。
    """
    # 常见的股票代码列名候选项
    symbol_column_candidates = ['symbol', 'Symbol', '代码', '证券代码', 'SYMBOL', 'stkcd', 'Stkcd']
    
    if not os.path.isfile(input_file_path):
        print(f"错误：找不到指定的CSV文件 '{input_file_path}'。请确保路径正确并且文件存在。")
        return

    print(f"正在处理文件: {input_file_path}")
    try:
        # 尝试使用不同的编码读取CSV
        try:
            df = pd.read_csv(input_file_path, low_memory=False)
        except UnicodeDecodeError:
            print(f"    文件 {os.path.basename(input_file_path)} 使用UTF-8编码读取失败，尝试使用GBK编码...")
            df = pd.read_csv(input_file_path, low_memory=False, encoding='gbk')
        except Exception as e_read: # 捕获其他潜在的读取错误
            print(f"    读取文件 {os.path.basename(input_file_path)} 时发生错误: {e_read}")
            return
        
        actual_symbol_column = None
        for col_name in symbol_column_candidates:
            if col_name in df.columns:
                actual_symbol_column = col_name
                break
        
        if actual_symbol_column:
            # 将目标symbol和CSV中的symbol列都转换为字符串进行比较
            # 确保原始数据的symbol列是字符串类型，以防数字代码（如000315）被错误处理
            df[actual_symbol_column] = df[actual_symbol_column].astype(str)
            target_symbol_str = str(target_symbol)
            
            # 清理操作：移除可能的'.SH'或'.SZ'等交易所后缀，以及股票代码字符串前导的零
            # 这样做是为了更灵活地匹配，例如 '600315.SH' 或 '000315' 都能匹配到 '600315'
            # 注意: df[...].str.replace() 使用 regex=True 需要 pandas 版本 >= 0.23.0
            df_symbol_cleaned = df[actual_symbol_column].str.replace(r'\.S[HZ]$', '', regex=True).str.lstrip('0')
            
            # --- 修改开始 ---
            # 使用 re.sub() 来对普通的 Python 字符串进行正则替换
            target_symbol_cleaned = re.sub(r'\.S[HZ]$', '', target_symbol_str)
            target_symbol_cleaned = target_symbol_cleaned.lstrip('0')
            # --- 修改结束 ---

            # 进行筛选
            filtered_df = df[df_symbol_cleaned == target_symbol_cleaned]
            
            if not filtered_df.empty:
                print(f"    在文件 {os.path.basename(input_file_path)} 中找到 {len(filtered_df)} 条关于 symbol '{target_symbol}' 的记录。")
                try:
                    # 使用 utf-8-sig 编码确保Excel打开CSV文件时能正确显示中文等字符
                    filtered_df.to_csv(output_file_path, index=False, encoding='utf-8-sig')
                    print(f"\n成功! 关于 symbol '{target_symbol}' 的数据已保存到: {output_file_path}")
                    print(f"总共找到 {len(filtered_df)} 条记录。")
                except Exception as e_write:
                    print(f"    保存筛选结果到 {output_file_path} 时发生错误: {e_write}")
            else:
                print(f"    在文件 {os.path.basename(input_file_path)} 中未找到 symbol '{target_symbol}' 的记录。")
        else:
            print(f"    警告: 文件 {os.path.basename(input_file_path)} 中未找到以下任何symbol列: {', '.join(symbol_column_candidates)}。")

    except pd.errors.EmptyDataError:
        print(f"    警告: 文件 {os.path.basename(input_file_path)} 为空，无法处理。")
    except Exception as e: # 捕获其他所有未预料到的错误
        print(f"    处理文件 {os.path.basename(input_file_path)} 时发生未知错误: {e}")

if __name__ == '__main__':
    # --- 用户配置区域 ---
    # 1. 输入的CSV文件路径
    #    这是你想要从中提取数据的单个CSV文件。
    #    请确保这里的路径是你实际的CSV文件路径。
    input_csv_file = r'C:\Users\<USER>\Desktop\量化第一步\Exp_daily_20200101-20241231.csv' 
    
    # 2. 输出筛选后的CSV文件的完整路径和文件名
    #    处理后的数据将保存到这个文件中。
    output_csv_file = r'c:\Users\<USER>\Desktop\量化第一步\symbol_600315_filtered_data.csv'
    
    # 3. 目标symbol (股票代码)
    #    你想要提取哪个symbol的数据。
    symbol_to_extract = '600315'
    # --- 用户配置区域结束 ---
    
    print("脚本开始执行...")
    print(f"将从文件 '{input_csv_file}' 中提取 symbol '{symbol_to_extract}' 的数据。")
    print(f"提取的数据将保存到 '{output_csv_file}'。")
    
    # 确保用户已安装pandas
    try:
        import pandas
    except ImportError:
        print("错误：未找到 pandas 库。请先安装 pandas: pip install pandas")
        raise SystemExit("Pandas未安装，请先安装。")

    process_single_csv(input_csv_file, output_csv_file, symbol_to_extract)
    print("脚本执行完毕。")