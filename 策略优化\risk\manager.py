"""
风险管理模块

实现仓位管理、止损止盈等风险控制功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from enum import Enum

from ..config import get_config
from ..utils.logger import get_logger
from ..utils.exceptions import RiskError, ValidationError


class RiskLevel(Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


@dataclass
class RiskLimits:
    """风险限制"""
    max_position_size: float = 1.0  # 最大仓位比例
    max_single_loss: float = 0.02   # 单笔最大亏损比例
    max_daily_loss: float = 0.05    # 单日最大亏损比例
    max_drawdown: float = 0.20      # 最大回撤比例
    stop_loss: float = 0.05         # 止损比例
    take_profit: float = 0.10       # 止盈比例
    max_correlation: float = 0.8    # 最大相关性


class RiskManager:
    """风险管理器"""
    
    def __init__(self, initial_capital: float = 100000.0, **kwargs):
        """
        初始化风险管理器
        
        Args:
            initial_capital: 初始资金
            **kwargs: 风险参数
        """
        self.logger = get_logger("risk.RiskManager")
        self.config = get_config()
        
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        
        # 加载风险配置
        risk_config = self.config.get('backtest.risk', {})
        self.limits = RiskLimits(
            max_position_size=kwargs.get('max_position_size', 
                                       risk_config.get('max_position_size', 1.0)),
            max_single_loss=kwargs.get('max_single_loss',
                                     risk_config.get('max_single_loss', 0.02)),
            max_daily_loss=kwargs.get('max_daily_loss',
                                    risk_config.get('max_daily_loss', 0.05)),
            max_drawdown=kwargs.get('max_drawdown',
                                  risk_config.get('max_drawdown', 0.20)),
            stop_loss=kwargs.get('stop_loss',
                               risk_config.get('stop_loss', 0.05)),
            take_profit=kwargs.get('take_profit',
                                 risk_config.get('take_profit', 0.10))
        )
        
        # 风险监控状态
        self.positions: Dict[str, float] = {}
        self.entry_prices: Dict[str, float] = {}
        self.daily_pnl: List[float] = []
        self.peak_capital = initial_capital
        self.current_drawdown = 0.0
        
        # 风险事件记录
        self.risk_events: List[Dict[str, Any]] = []
        
        self.logger.info(f"风险管理器初始化完成: 初始资金 {initial_capital:,.2f}")
    
    def check_position_size(self, symbol: str, target_position: float,
                          current_price: float) -> Tuple[bool, float, str]:
        """
        检查仓位大小是否符合风险限制
        
        Args:
            symbol: 交易标的
            target_position: 目标仓位比例
            current_price: 当前价格
            
        Returns:
            (是否允许, 调整后的仓位, 原因)
        """
        try:
            # 检查最大仓位限制
            if abs(target_position) > self.limits.max_position_size:
                adjusted_position = np.sign(target_position) * self.limits.max_position_size
                reason = f"仓位超过最大限制 {self.limits.max_position_size:.1%}"
                
                self._log_risk_event("POSITION_SIZE_LIMIT", symbol, {
                    'target_position': target_position,
                    'adjusted_position': adjusted_position,
                    'limit': self.limits.max_position_size
                })
                
                return False, adjusted_position, reason
            
            return True, target_position, "通过仓位检查"
            
        except Exception as e:
            self.logger.error(f"检查仓位大小失败: {e}")
            raise RiskError(f"仓位检查失败: {e}", "position_size", target_position)
    
    def check_stop_loss_take_profit(self, symbol: str, current_price: float) -> Tuple[bool, str]:
        """
        检查止损止盈条件
        
        Args:
            symbol: 交易标的
            current_price: 当前价格
            
        Returns:
            (是否需要平仓, 原因)
        """
        try:
            if symbol not in self.positions or symbol not in self.entry_prices:
                return False, "无持仓"
            
            position = self.positions[symbol]
            entry_price = self.entry_prices[symbol]
            
            if abs(position) < 1e-6:  # 无持仓
                return False, "无持仓"
            
            # 计算收益率
            if position > 0:  # 多头持仓
                return_rate = (current_price - entry_price) / entry_price
            else:  # 空头持仓
                return_rate = (entry_price - current_price) / entry_price
            
            # 检查止损
            if return_rate <= -self.limits.stop_loss:
                reason = f"触发止损: 亏损 {return_rate:.2%} >= {self.limits.stop_loss:.2%}"
                self._log_risk_event("STOP_LOSS", symbol, {
                    'position': position,
                    'entry_price': entry_price,
                    'current_price': current_price,
                    'return_rate': return_rate
                })
                return True, reason
            
            # 检查止盈
            if return_rate >= self.limits.take_profit:
                reason = f"触发止盈: 盈利 {return_rate:.2%} >= {self.limits.take_profit:.2%}"
                self._log_risk_event("TAKE_PROFIT", symbol, {
                    'position': position,
                    'entry_price': entry_price,
                    'current_price': current_price,
                    'return_rate': return_rate
                })
                return True, reason
            
            return False, "未触发止损止盈"
            
        except Exception as e:
            self.logger.error(f"检查止损止盈失败: {e}")
            raise RiskError(f"止损止盈检查失败: {e}", "stop_loss_take_profit", current_price)
    
    def check_drawdown_limit(self) -> Tuple[bool, str]:
        """
        检查回撤限制
        
        Returns:
            (是否超过限制, 原因)
        """
        try:
            # 更新当前回撤
            if self.current_capital > self.peak_capital:
                self.peak_capital = self.current_capital
                self.current_drawdown = 0.0
            else:
                self.current_drawdown = (self.peak_capital - self.current_capital) / self.peak_capital
            
            # 检查是否超过最大回撤限制
            if self.current_drawdown > self.limits.max_drawdown:
                reason = f"回撤 {self.current_drawdown:.2%} 超过限制 {self.limits.max_drawdown:.2%}"
                self._log_risk_event("MAX_DRAWDOWN", "PORTFOLIO", {
                    'current_drawdown': self.current_drawdown,
                    'limit': self.limits.max_drawdown,
                    'peak_capital': self.peak_capital,
                    'current_capital': self.current_capital
                })
                return True, reason
            
            return False, f"回撤 {self.current_drawdown:.2%} 在限制内"
            
        except Exception as e:
            self.logger.error(f"检查回撤限制失败: {e}")
            raise RiskError(f"回撤检查失败: {e}", "drawdown", self.current_drawdown)
    
    def check_daily_loss_limit(self, daily_pnl: float) -> Tuple[bool, str]:
        """
        检查单日亏损限制
        
        Args:
            daily_pnl: 当日盈亏
            
        Returns:
            (是否超过限制, 原因)
        """
        try:
            daily_loss_rate = -daily_pnl / self.current_capital if daily_pnl < 0 else 0
            
            if daily_loss_rate > self.limits.max_daily_loss:
                reason = f"单日亏损 {daily_loss_rate:.2%} 超过限制 {self.limits.max_daily_loss:.2%}"
                self._log_risk_event("DAILY_LOSS_LIMIT", "PORTFOLIO", {
                    'daily_pnl': daily_pnl,
                    'daily_loss_rate': daily_loss_rate,
                    'limit': self.limits.max_daily_loss
                })
                return True, reason
            
            return False, f"单日亏损 {daily_loss_rate:.2%} 在限制内"
            
        except Exception as e:
            self.logger.error(f"检查单日亏损限制失败: {e}")
            raise RiskError(f"单日亏损检查失败: {e}", "daily_loss", daily_pnl)
    
    def update_position(self, symbol: str, position: float, entry_price: float) -> None:
        """
        更新持仓信息
        
        Args:
            symbol: 交易标的
            position: 持仓比例
            entry_price: 入场价格
        """
        self.positions[symbol] = position
        if abs(position) > 1e-6:  # 有持仓时记录入场价格
            self.entry_prices[symbol] = entry_price
        elif symbol in self.entry_prices:  # 平仓时清除入场价格
            del self.entry_prices[symbol]
    
    def update_capital(self, new_capital: float) -> None:
        """
        更新当前资金
        
        Args:
            new_capital: 新的资金数额
        """
        self.current_capital = new_capital
    
    def calculate_position_size(self, symbol: str, signal_strength: float,
                              volatility: float, current_price: float) -> float:
        """
        基于风险计算仓位大小
        
        Args:
            symbol: 交易标的
            signal_strength: 信号强度 (-1 到 1)
            volatility: 波动率
            current_price: 当前价格
            
        Returns:
            建议仓位比例
        """
        try:
            # 基础仓位大小（基于信号强度）
            base_position = abs(signal_strength) * self.limits.max_position_size
            
            # 基于波动率调整仓位
            # 波动率越高，仓位越小
            volatility_adjustment = 1.0 / (1.0 + volatility * 10)  # 简化的调整公式
            
            # 基于当前回撤调整仓位
            # 回撤越大，仓位越小
            drawdown_adjustment = 1.0 - (self.current_drawdown / self.limits.max_drawdown) * 0.5
            drawdown_adjustment = max(0.1, drawdown_adjustment)  # 最小保留10%仓位
            
            # 计算最终仓位
            adjusted_position = base_position * volatility_adjustment * drawdown_adjustment
            adjusted_position = min(adjusted_position, self.limits.max_position_size)
            
            # 保持信号方向
            final_position = np.sign(signal_strength) * adjusted_position
            
            self.logger.debug(f"计算仓位: {symbol} 信号={signal_strength:.2f} "
                            f"波动率={volatility:.4f} 最终仓位={final_position:.2f}")
            
            return final_position
            
        except Exception as e:
            self.logger.error(f"计算仓位大小失败: {e}")
            raise RiskError(f"仓位计算失败: {e}", "position_calculation", signal_strength)
    
    def _log_risk_event(self, event_type: str, symbol: str, details: Dict[str, Any]) -> None:
        """记录风险事件"""
        event = {
            'timestamp': pd.Timestamp.now(),
            'type': event_type,
            'symbol': symbol,
            'details': details
        }
        self.risk_events.append(event)
        self.logger.warning(f"风险事件: {event_type} - {symbol} - {details}")
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """获取风险摘要"""
        return {
            'current_capital': self.current_capital,
            'peak_capital': self.peak_capital,
            'current_drawdown': self.current_drawdown,
            'total_positions': len([p for p in self.positions.values() if abs(p) > 1e-6]),
            'risk_events_count': len(self.risk_events),
            'risk_limits': {
                'max_position_size': self.limits.max_position_size,
                'max_drawdown': self.limits.max_drawdown,
                'stop_loss': self.limits.stop_loss,
                'take_profit': self.limits.take_profit
            },
            'recent_events': self.risk_events[-10:] if self.risk_events else []
        }
    
    def reset(self) -> None:
        """重置风险管理器状态"""
        self.current_capital = self.initial_capital
        self.positions.clear()
        self.entry_prices.clear()
        self.daily_pnl.clear()
        self.peak_capital = self.initial_capital
        self.current_drawdown = 0.0
        self.risk_events.clear()
        
        self.logger.info("风险管理器状态已重置")
