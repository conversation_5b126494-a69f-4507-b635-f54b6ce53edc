"""
RSI策略

基于相对强弱指标的超买超卖交易策略。
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple

from .base import BaseStrategy


class RSIStrategy(BaseStrategy):
    """RSI策略"""
    
    def __init__(self, period: int = 14, oversold: float = 30, overbought: float = 70):
        """
        初始化RSI策略
        
        Args:
            period: RSI计算周期
            oversold: 超卖阈值
            overbought: 超买阈值
        """
        super().__init__(f"RSI_{period}_{oversold}_{overbought}")
        self.period = period
        self.oversold = oversold
        self.overbought = overbought
    
    def generate_signals(self, data: pd.DataFrame, **params) -> pd.Series:
        """生成RSI信号"""
        period = int(params.get('period', self.period))
        oversold = params.get('oversold', self.oversold)
        overbought = params.get('overbought', self.overbought)
        
        # 计算RSI
        rsi = self._calculate_rsi(data['close'], period)
        
        # 生成信号
        signals = pd.Series(0, index=data.index)
        signals[rsi < oversold] = 1   # 超卖买入
        signals[rsi > overbought] = -1  # 超买卖出
        
        return signals
    
    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """计算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def get_param_ranges(self) -> Dict[str, Tuple[float, float]]:
        """获取参数范围"""
        return {
            'period': (10, 30),
            'oversold': (20, 35),
            'overbought': (65, 80)
        }
    
    def calculate_indicators(self, data: pd.DataFrame, 
                           period: int = None) -> pd.DataFrame:
        """
        计算技术指标
        
        Args:
            data: 价格数据
            period: RSI周期
            
        Returns:
            包含指标的数据框
        """
        df = data.copy()
        
        period = period or self.period
        df['RSI'] = self._calculate_rsi(df['close'], period)
        
        return df
