# -*- coding: utf-8 -*-
# --- 必要的库导入 ---
import pandas as pd  # 用于数据处理，特别是DataFrame
import backtrader as bt # 强大的量化回测框架
import os # 用于处理文件路径

# --- 策略定义: VolumeBreakoutStrategy ---
# 金融意义: 这个策略试图捕捉成交量突然放大的时刻，认为这可能预示着价格将发生显著变动。
# 当成交量超过其近期平均值的一定倍数时，做多（买入）。
# 策略包含基于持有时间、止损、止盈的退出机制。
class VolumeBreakoutStrategy(bt.Strategy):
    """
    成交量突破策略:
    - 入场条件: 当日成交量 > N日平均成交量 * M倍
    - 离场条件:
        1. 持有时间达到预设天数 (exit_bars)
        2. 触及止损位 (stop_loss)
        3. 触及止盈位 (take_profit)
    """
    # --- 策略参数定义 ---
    # 这些参数可以在运行回测时从外部传入，方便调优
    params = (
        ('volume_period', 20),   # 金融意义: 计算成交量移动平均线的回看期（例如20天）。用于判断“正常”成交量水平。
        ('volume_mult', 2.0),    # 金融意义: 成交量放大倍数阈值。当前成交量需要超过 volume_period 天平均成交量的 volume_mult 倍（例如2倍）才触发信号。值越高，表示需要越强的成交量确认。
        ('exit_bars', 5),        # 金融意义: 持仓时间限制。入场后最多持有 exit_bars 个交易周期（例如5天）后强制平仓。这是一种基于时间的退出逻辑。
        ('stop_loss', 0.05),     # 金融意义: 止损百分比。从入场价计算，如果价格下跌达到此比例（例如5%），则立即平仓以限制损失。
        ('take_profit', 0.10),   # 金融意义: 止盈百分比。从入场价计算，如果价格上涨达到此比例（例如10%），则立即平仓以锁定利润。
    )

    def log(self, txt, dt=None):
        ''' 辅助函数，用于打印带时间戳的日志 '''
        dt = dt or self.datas[0].datetime.date(0) # 获取当前 K 线的日期
        print(f'{dt.isoformat()} - {txt}') # 打印格式化的日志信息

    def __init__(self):
        """策略初始化：只执行一次"""
        self.log("策略初始化开始...")
        # --- 获取数据线 ---
        # self.datas[0] 代表默认的第一个数据源（我们只加载了一个CSV）
        self.dataclose = self.datas[0].close     # 收盘价 时间序列
        self.datavolume = self.datas[0].volume   # 成交量 时间序列

        # --- 计算指标 ---
        # 计算成交量的简单移动平均线 (SMA)
        self.volume_sma = bt.indicators.SimpleMovingAverage(
            self.datavolume, period=self.params.volume_period)

        # --- 状态变量 ---
        self.order = None             # 用于跟踪当前待处理的订单
        self.buyprice = None          # 记录买入价格
        self.buycomm = None           # 记录买入佣金
        self.bar_executed = 0       # 记录入场时的 K 线索引（用于计算持仓时间）

        self.log(f"参数: Volume Period={self.params.volume_period}, Volume Mult={self.params.volume_mult}, Exit Bars={self.params.exit_bars}, Stop Loss={self.params.stop_loss:.2%}, Take Profit={self.params.take_profit:.2%}")
        self.log("策略初始化完成.")


    def notify_order(self, order):
        """
        订单状态通知：当订单状态改变时（提交、接受、完成、取消、保证金不足、拒绝），此方法会被调用。
        """
        if order.status in [order.Submitted, order.Accepted]:
            # 订单已提交或已被接受，无需处理
            return

        # 检查订单是否已完成
        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(
                    f'买入单执行成功, 价格: {order.executed.price:.2f}, '
                    f'成本: {order.executed.value:.2f}, '
                    f'佣金: {order.executed.comm:.2f}, '
                    f'数量: {order.executed.size:.2f}'
                )
                # 记录买入价格和佣金
                self.buyprice = order.executed.price
                self.buycomm = order.executed.comm
                # 记录入场时的K线编号 (从1开始计数)
                self.bar_executed = len(self)
            elif order.issell():
                self.log(
                    f'卖出单执行成功, 价格: {order.executed.price:.2f}, '
                    f'成本: {order.executed.value:.2f}, ' # 卖出时 value 通常为负
                    f'佣金: {order.executed.comm:.2f}, '
                    f'数量: {order.executed.size:.2f}' # 卖出时 size 通常为负
                 )
                # 可以在这里计算并记录单笔交易的盈亏

            # 重置 bar_executed 以便 notify_trade 可以正确工作 (或者在 notify_trade 中处理)
            # self.bar_executed = len(self) # 如果需要跟踪每次交易的 bar

        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log(f'订单 Canceled/Margin/Rejected: {order.Status[order.status]}') # Backtrader 使用数字代表状态，这里转为文本

        # 重置订单跟踪变量，表示当前没有待处理订单
        self.order = None

    def notify_trade(self, trade):
        """
        交易通知：当一笔交易（从开仓到平仓）完成时，此方法会被调用。
        """
        if not trade.isclosed:
            # 交易未关闭，则忽略
            return

        # 打印交易的毛利润和净利润（扣除佣金后）
        self.log(f'交易利润, 毛利 {trade.pnl:.2f}, 净利 {trade.pnlcomm:.2f}')

    def next(self):
        """
        核心逻辑：每根 K 线（每天的数据）都会调用一次。用于判断交易信号和执行订单。
        """
        # 打印当前 K 线的信息 (可选，用于调试)
        # self.log(f'Close: {self.dataclose[0]:.2f}, Volume: {self.datavolume[0]:.0f}, Vol SMA: {self.volume_sma[0]:.0f}')

        # --- 检查是否有待处理订单 ---
        if self.order:
            # 如果有订单正在处理中，则等待其完成，不做任何操作
            return

        # --- 检查是否持有仓位 ---
        if not self.position:  # 如果当前没有持仓
            # --- 入场条件判断 ---
            # 条件1: 当前成交量 > N日平均成交量 * M倍
            if self.datavolume[0] > self.volume_sma[0] * self.params.volume_mult:
                self.log(f'成交量突破 - 买入信号, 价格 {self.dataclose[0]:.2f}, 成交量 {self.datavolume[0]:.0f} > SMA({self.params.volume_period}) {self.volume_sma[0]:.0f} * {self.params.volume_mult}')

                # --- 计算买入数量 ---
                # 示例：使用固定比例的现金买入 (例如可用现金的 90%)
                cash_to_use = self.broker.get_cash() * 0.90
                size = int(cash_to_use / self.dataclose[0]) # 向下取整股数

                if size > 0: # 确保有足够现金买入至少1股 (或其他最小单位)
                    self.log(f'执行买入, 数量: {size}, 价格: {self.dataclose[0]:.2f}')
                    # --- 发出买入订单 ---
                    self.order = self.buy(size=size)
                else:
                    self.log(f"现金不足以买入, 需要 {self.dataclose[0]:.2f}, 可用 {self.broker.get_cash():.2f}")

        else:  # 如果当前持有仓位
            # --- 离场条件判断 ---
            # 计算已持仓的 K 线数量
            bars_held = len(self) - self.bar_executed

            # 条件1: 持仓时间达到 exit_bars
            if bars_held >= self.params.exit_bars:
                self.log(f'达到持仓时间限制 ({self.params.exit_bars} bars) - 卖出信号, 价格 {self.dataclose[0]:.2f}')
                # --- 发出卖出订单 (平仓) ---
                # self.position.size 获取当前持仓数量
                self.order = self.sell(size=self.position.size)

            # 条件2: 触及止损位
            elif self.dataclose[0] < self.buyprice * (1.0 - self.params.stop_loss):
                self.log(f'触发止损 - 卖出信号, 价格 {self.dataclose[0]:.2f} < 止损价 {self.buyprice * (1.0 - self.params.stop_loss):.2f}')
                # --- 发出卖出订单 (平仓) ---
                self.order = self.sell(size=self.position.size)

            # 条件3: 触及止盈位
            elif self.dataclose[0] > self.buyprice * (1.0 + self.params.take_profit):
                self.log(f'触发止盈 - 卖出信号, 价格 {self.dataclose[0]:.2f} > 止盈价 {self.buyprice * (1.0 + self.params.take_profit):.2f}')
                # --- 发出卖出订单 (平仓) ---
                self.order = self.sell(size=self.position.size)

    def stop(self):
        """
        策略结束：在回测结束时调用一次。
        """
        self.log(f'(Volume Period {self.params.volume_period}, Vol Mult {self.params.volume_mult}, Exit Bars {self.params.exit_bars}) --- 策略结束 --- 最终资产价值 {self.broker.getvalue():.2f}')


# --- 回测执行函数 ---
def run_backtest(df, strategy_class, strategy_params, initial_cash, commission):
    """
    封装的回测运行函数

    Args:
        df (pd.DataFrame): 包含OHLCV数据的DataFrame，必须有DatetimeIndex。
                           列名应符合backtrader预期(Open, High, Low, Close, Volume - 大小写不敏感)。
        strategy_class (bt.Strategy): 要运行的策略类 (例如 VolumeBreakoutStrategy)。
        strategy_params (dict): 传递给策略的参数字典。
        initial_cash (float): 初始资金。
        commission (float): 手续费率 (例如 0.001 代表 0.1%)。

    Returns:
        tuple: (results, strategy_instance)
               results 是 cerebro.run() 的返回列表, strategy_instance 是运行后的策略实例。
               如果运行失败，返回 (None, None)。
    """
    print("\n--- 开始执行回测 ---")
    # 1. 创建 Cerebro 引擎实例
    # Cerebro 是 backtrader 的核心，负责协调数据、策略、订单、资金等
    cerebro = bt.Cerebro()
    print("Cerebro 引擎已创建.")

    # 2. 添加策略到 Cerebro
    # 使用 **strategy_params 将字典解包为关键字参数传递给策略的 __init__
    cerebro.addstrategy(strategy_class, **strategy_params)
    print(f"策略 '{strategy_class.__name__}' 已添加，参数: {strategy_params}")

    # 3. 准备数据源 (Data Feed)
    # 将 Pandas DataFrame 包装成 Backtrader 能理解的数据格式
    # 确保 df 的索引是 DatetimeIndex，并且包含 Open, High, Low, Close, Volume 列
    # Backtrader 会自动识别这些列名（大小写不敏感）
    # 如果你的列名不同，需要先用 df.rename() 修改列名
    data_feed = bt.feeds.PandasData(dataname=df)
    cerebro.adddata(data_feed)
    print(f"数据已添加. 时间范围: {df.index.min()} 至 {df.index.max()}")

    # 4. 设置初始资金
    cerebro.broker.setcash(initial_cash)
    print(f"设置初始资金: {initial_cash:,.2f}") # 使用逗号分隔符格式化金额

    # 5. 设置交易手续费
    # commission 是双边手续费率（买入收一次，卖出收一次）
    cerebro.broker.setcommission(commission=commission)
    print(f"设置手续费率: {commission:.4f} ({commission * 100:.2f}%)")

    # 6. 添加分析器 (Analyzers) - 用于评估策略表现
    # SharpeRatio: 夏普比率，衡量风险调整后收益
    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe_ratio', timeframe=bt.TimeFrame.Days, compression=1) # 假设是日线数据计算年化夏普
    # DrawDown: 最大回撤，衡量策略可能经历的最大资金损失
    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')
    # TradeAnalyzer: 交易分析，统计总交易次数、胜率、盈亏比等
    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trade_analyzer')
    # Returns: 回报率分析
    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')
    # SQN: 系统质量数 (System Quality Number)
    cerebro.addanalyzer(bt.analyzers.SQN, _name='sqn')
    print("已添加分析器: SharpeRatio, DrawDown, TradeAnalyzer, Returns, SQN")

    # 7. 运行回测
    print("\n--- Cerebro 运行中... ---")
    # cerebro.run() 返回一个包含策略实例的列表 (如果 addstrategy 多次，则有多个实例)
    results = cerebro.run()
    print("--- Cerebro 运行完成 ---")

    # 8. 返回结果
    if results:
        strategy_instance = results[0] # 获取第一个策略实例
        final_value = cerebro.broker.getvalue() # 获取最终资产价值
        print(f"\n--- 回测结束 ---")
        print(f"最终资产价值: {final_value:,.2f}")
        return results, strategy_instance
    else:
        print("错误: Cerebro 运行未产生结果。")
        return None, None

# --- 主程序入口 ---
if __name__ == "__main__":
    print("--- 量化回测脚本启动 ---")

    # 1. 定义数据文件路径
    data_path = r"C:\Users\<USER>\Desktop\量化第一步\symbol_600315_filtered_data.csv"
    print(f"数据文件路径: {data_path}")

    # 2. 加载和准备数据
    try:
        print("开始加载数据...")
        # 使用 pandas 读取 CSV 文件
        # --- 修改点 1: 将 index_col 改为 'date' (小写) ---
        df = pd.read_csv(
            data_path,
            index_col='date', # <--- 使用 CSV 文件中实际的日期列名 (小写)
            parse_dates=True, # 尝试将 'date' 列解析为日期时间格式
            header=0
        )
        print(f"CSV 文件 '{os.path.basename(data_path)}' 加载成功.")

        # --- 修改点 2: 重命名成交量列 'vol' 为 'volume' ---
        if 'vol' in df.columns:
            print("正在将列 'vol' 重命名为 'volume'...")
            df.rename(columns={'vol': 'volume'}, inplace=True)
            print("列已重命名。")
        elif 'volume' not in df.columns:
             # 如果 'vol' 和 'volume' 都没有，则发出警告
             print("警告: 数据中未找到 'vol' 或 'volume' 列。Backtrader 需要成交量数据。")
             # 根据情况，你可能需要在这里决定是否退出或继续


        # --- 数据清洗与准备 (关键步骤!) ---
        # a. 检查索引是否为 DatetimeIndex
        if not isinstance(df.index, pd.DatetimeIndex):
            # 如果 parse_dates=True 失败，这会捕获错误
            raise TypeError("DataFrame 索引不是 DatetimeIndex。请检查 'index_col' 设置和CSV中的日期格式。")
        print("数据索引已确认为 DatetimeIndex.")

        # b. 检查必需的列是否存在 (大小写不敏感)
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        # 将 DataFrame 列名转为小写，方便比较 (在重命名之后执行)
        df.columns = [col.lower() for col in df.columns]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"数据文件中缺少必需的列: {missing_cols}。请确保CSV包含 Open, High, Low, Close, Volume 列 (或可重命名的 vol)。 当前列: {list(df.columns)}")
        print("必需列 (open, high, low, close, volume) 已确认存在。")

        # c. 确保数据按时间升序排列
        if not df.index.is_monotonic_increasing:
            print("数据未按时间升序排列，正在排序...")
            df.sort_index(inplace=True)
            print("数据已按时间升序排列。")

        # d. 检查数据中是否有 NaN 值 (可选但推荐)
        # 注意：现在我们检查小写的列名
        if df[['open', 'high', 'low', 'close', 'volume']].isnull().values.any():
            print("警告: OHLCV 数据中检测到 NaN 值，可能影响回测准确性。考虑进行处理 (例如填充或删除)。")
            # df.fillna(method='ffill', inplace=True) # 例如，向前填充 NaN

        print(f"数据准备完成。数据形状: {df.shape} (行数, 列数)")
        print("数据预览 (前5行):\n", df.head())

    except FileNotFoundError:
        print(f"错误: 无法找到数据文件 '{data_path}'。请检查路径是否正确。")
        exit() # 文件找不到，退出程序
    except Exception as e:
        print(f"加载或准备数据时发生错误: {e}")
        # 尝试打印列名帮助调试
        try:
            print("尝试读取CSV列名...")
            cols = pd.read_csv(data_path, nrows=1).columns.tolist()
            print(f"CSV文件中的列名: {cols}")
        except Exception as e_inner:
            print(f"无法读取CSV列名: {e_inner}")
        exit() # 发生其他错误，退出程序

    # 3. 定义策略参数 (从原始代码复制，并已在策略类中解释)
    strategy_params = {
        'volume_period': 20,
        'volume_mult': 2.0,
        'exit_bars': 5,
        'stop_loss': 0.05,
        'take_profit': 0.10
    }
    print(f"\n策略参数已设置: {strategy_params}")

    # 4. 定义回测配置
    initial_cash = 100000.0   # 初始模拟资金: 100,000
    commission = 0.001      # 手续费率: 0.1% (千分之一)
    print(f"回测配置: 初始资金={initial_cash:,.2f}, 手续费={commission:.4f}")

    # 5. 执行回测
    # 调用封装好的 run_backtest 函数
    results, strategy_instance = run_backtest(
        df=df,                               # 准备好的数据
        strategy_class=VolumeBreakoutStrategy, # 要运行的策略
        strategy_params=strategy_params,     # 策略参数
        initial_cash=initial_cash,           # 初始资金
        commission=commission                # 手续费
    )

    # 6. 分析和展示结果 (如果回测成功)
    if results and strategy_instance:
        print("\n--- 回测结果分析 ---")
        try:
            # 从策略实例中获取分析器的结果
            sharpe_ratio = strategy_instance.analyzers.sharpe_ratio.get_analysis().get('sharperatio', 'N/A')
            drawdown = strategy_instance.analyzers.drawdown.get_analysis()
            trade_analysis = strategy_instance.analyzers.trade_analyzer.get_analysis()
            returns_analysis = strategy_instance.analyzers.returns.get_analysis()
            sqn_analysis = strategy_instance.analyzers.sqn.get_analysis()

            print(f"年化夏普比率 (Sharpe Ratio): {sharpe_ratio if sharpe_ratio else 'N/A'}") # Sharpe 可能为 None
            print(f"最大回撤 (Max Drawdown): {drawdown.max.drawdown:.2f}%")
            print(f"最大回撤期间金额 (Max Drawdown Money): {drawdown.max.moneydown:.2f}")
            print(f"累计回报率 (Total Return): {returns_analysis.get('rtot', 0) * 100:.2f}%") # rtot = total return rate
            print(f"年化回报率 (Annualized Return): {returns_analysis.get('ravg', 0) * 100:.2f}%") # ravg = average return rate (annualized)
            print(f"系统质量数 (SQN): {sqn_analysis.get('sqn', 'N/A'):.2f}")


            print("\n--- 交易统计 ---")
            if 'total' in trade_analysis and trade_analysis.total.total > 0:
                print(f"总交易次数: {trade_analysis.total.total}")
                print(f"总盈利次数: {trade_analysis.won.total}")
                print(f"总亏损次数: {trade_analysis.lost.total}")
                win_rate = (trade_analysis.won.total / trade_analysis.total.total * 100) if trade_analysis.total.total else 0
                print(f"胜率 (Win Rate): {win_rate:.2f}%")
                if trade_analysis.won.total > 0:
                     print(f"平均盈利金额: {trade_analysis.won.pnl.average:.2f}")
                     print(f"最大单笔盈利: {trade_analysis.won.pnl.max:.2f}")
                if trade_analysis.lost.total > 0:
                     print(f"平均亏损金额: {trade_analysis.lost.pnl.average:.2f}")
                     print(f"最大单笔亏损: {trade_analysis.lost.pnl.max:.2f}")
                # 盈亏比 = 平均盈利 / abs(平均亏损)
                profit_factor = (trade_analysis.won.pnl.total / abs(trade_analysis.lost.pnl.total)) if trade_analysis.lost.pnl.total else float('inf')
                print(f"盈亏比 (Profit Factor): {profit_factor:.2f}")

            else:
                print("回测期间没有发生任何交易。")

            # 可选: 绘制资产曲线图 (需要 matplotlib)
            try:
                import matplotlib.pyplot as plt
                print("\n正在尝试绘制资产曲线图...")
                # 设置 matplotlib 支持中文显示 (如果需要)
                # plt.rcParams['font.sans-serif'] = ['SimHei'] # 例如使用黑体
                # plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
                figure = cerebro.plot(style='candlestick', barup='red', bardown='green')[0][0] # 使用K线图样式
                # figure.suptitle(f'Volume Breakout Strategy Backtest Results ({os.path.basename(data_path)})')
                plt.show()
                print("图表已显示。")
            except ImportError:
                print("\n提示: 未安装 matplotlib 库，无法绘制图表。请运行 'pip install matplotlib' 安装。")
            except Exception as plot_err:
                 print(f"绘制图表时出错: {plot_err}")

        except AttributeError as e:
            print(f"错误: 无法访问分析器结果，请确认分析器已在 run_backtest 函数中正确添加。错误信息: {e}")
        except Exception as e:
            print(f"处理或展示结果时发生错误: {e}")
    else:
        print("\n回测未能成功完成或未产生有效结果。")

    print("\n--- 量化回测脚本执行完毕 ---")