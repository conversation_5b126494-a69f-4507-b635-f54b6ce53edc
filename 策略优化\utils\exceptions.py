"""
自定义异常类

定义量化交易系统中使用的各种异常类型。
"""

from typing import Optional, Any


class QuantTradingError(Exception):
    """量化交易系统基础异常类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 details: Optional[Any] = None):
        """
        初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误代码
            details: 错误详情
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details
    
    def __str__(self) -> str:
        """返回异常字符串表示"""
        result = self.message
        if self.error_code:
            result = f"[{self.error_code}] {result}"
        if self.details:
            result = f"{result} | 详情: {self.details}"
        return result


class ConfigError(QuantTradingError):
    """配置相关异常"""
    
    def __init__(self, message: str, config_key: Optional[str] = None, 
                 config_value: Optional[Any] = None):
        """
        初始化配置异常
        
        Args:
            message: 错误消息
            config_key: 配置键
            config_value: 配置值
        """
        details = {}
        if config_key:
            details['config_key'] = config_key
        if config_value is not None:
            details['config_value'] = config_value
            
        super().__init__(message, "CONFIG_ERROR", details)
        self.config_key = config_key
        self.config_value = config_value


class DataError(QuantTradingError):
    """数据相关异常"""
    
    def __init__(self, message: str, data_source: Optional[str] = None,
                 data_type: Optional[str] = None):
        """
        初始化数据异常
        
        Args:
            message: 错误消息
            data_source: 数据源
            data_type: 数据类型
        """
        details = {}
        if data_source:
            details['data_source'] = data_source
        if data_type:
            details['data_type'] = data_type
            
        super().__init__(message, "DATA_ERROR", details)
        self.data_source = data_source
        self.data_type = data_type


class StrategyError(QuantTradingError):
    """策略相关异常"""
    
    def __init__(self, message: str, strategy_name: Optional[str] = None,
                 strategy_params: Optional[dict] = None):
        """
        初始化策略异常
        
        Args:
            message: 错误消息
            strategy_name: 策略名称
            strategy_params: 策略参数
        """
        details = {}
        if strategy_name:
            details['strategy_name'] = strategy_name
        if strategy_params:
            details['strategy_params'] = strategy_params
            
        super().__init__(message, "STRATEGY_ERROR", details)
        self.strategy_name = strategy_name
        self.strategy_params = strategy_params


class BacktestError(QuantTradingError):
    """回测相关异常"""
    
    def __init__(self, message: str, backtest_period: Optional[str] = None,
                 backtest_params: Optional[dict] = None):
        """
        初始化回测异常
        
        Args:
            message: 错误消息
            backtest_period: 回测周期
            backtest_params: 回测参数
        """
        details = {}
        if backtest_period:
            details['backtest_period'] = backtest_period
        if backtest_params:
            details['backtest_params'] = backtest_params
            
        super().__init__(message, "BACKTEST_ERROR", details)
        self.backtest_period = backtest_period
        self.backtest_params = backtest_params


class IndicatorError(QuantTradingError):
    """技术指标相关异常"""
    
    def __init__(self, message: str, indicator_name: Optional[str] = None,
                 indicator_params: Optional[dict] = None):
        """
        初始化指标异常
        
        Args:
            message: 错误消息
            indicator_name: 指标名称
            indicator_params: 指标参数
        """
        details = {}
        if indicator_name:
            details['indicator_name'] = indicator_name
        if indicator_params:
            details['indicator_params'] = indicator_params
            
        super().__init__(message, "INDICATOR_ERROR", details)
        self.indicator_name = indicator_name
        self.indicator_params = indicator_params


class RiskError(QuantTradingError):
    """风险管理相关异常"""
    
    def __init__(self, message: str, risk_type: Optional[str] = None,
                 risk_value: Optional[float] = None):
        """
        初始化风险异常
        
        Args:
            message: 错误消息
            risk_type: 风险类型
            risk_value: 风险值
        """
        details = {}
        if risk_type:
            details['risk_type'] = risk_type
        if risk_value is not None:
            details['risk_value'] = risk_value
            
        super().__init__(message, "RISK_ERROR", details)
        self.risk_type = risk_type
        self.risk_value = risk_value


class ValidationError(QuantTradingError):
    """数据验证异常"""
    
    def __init__(self, message: str, field_name: Optional[str] = None,
                 field_value: Optional[Any] = None, expected_type: Optional[str] = None):
        """
        初始化验证异常
        
        Args:
            message: 错误消息
            field_name: 字段名称
            field_value: 字段值
            expected_type: 期望类型
        """
        details = {}
        if field_name:
            details['field_name'] = field_name
        if field_value is not None:
            details['field_value'] = field_value
        if expected_type:
            details['expected_type'] = expected_type
            
        super().__init__(message, "VALIDATION_ERROR", details)
        self.field_name = field_name
        self.field_value = field_value
        self.expected_type = expected_type


class OptimizationError(QuantTradingError):
    """优化相关异常"""
    
    def __init__(self, message: str, optimization_method: Optional[str] = None,
                 optimization_params: Optional[dict] = None):
        """
        初始化优化异常
        
        Args:
            message: 错误消息
            optimization_method: 优化方法
            optimization_params: 优化参数
        """
        details = {}
        if optimization_method:
            details['optimization_method'] = optimization_method
        if optimization_params:
            details['optimization_params'] = optimization_params
            
        super().__init__(message, "OPTIMIZATION_ERROR", details)
        self.optimization_method = optimization_method
        self.optimization_params = optimization_params
