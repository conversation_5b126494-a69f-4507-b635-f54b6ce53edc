{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c4b0aa8f-29f9-4b3e-a395-eecfbd1c3e5a", "metadata": {}, "outputs": [], "source": ["import yfinance as yf\n", "import pandas as pd\n", "import numpy as np\n", "import math\n", "import talib\n", "\n", "# 随机森林改成分类器\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import TimeSeriesSplit, RandomizedSearchCV\n", "from sklearn.metrics import accuracy_score, f1_score\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "import pickle\n", "import backtrader as bt\n", "import sys\n", "import os\n", "\n", "# Add the parent directory to the sys.path list\n", "sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))\n", "\n", "from data_processing import flatten_yf_columns, standardize_columns\n", "from strategy.buy_and_hold import BuyAndHoldStrategy\n", "from back_test import run_backtest\n", "\n", "# 设置显示选项\n", "pd.set_option('display.float_format', lambda x: '%.4f' % x)\n", "plt.style.use('seaborn-v0_8-bright')\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['PingFang HK']\n", "plt.rcParams['axes.unicode_minus'] = False"]}, {"cell_type": "markdown", "id": "b52bbfd7-6894-4186-8422-a8ddf61e3959", "metadata": {}, "source": ["## 1. 数据获取与预处理\n", "\n", "使用yfinance获取股票数据："]}, {"cell_type": "code", "execution_count": 7, "id": "1affb66b-bd5c-4938-a2dc-afc101dac780", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[*********************100%***********************]  1 of 1 completed"]}, {"name": "stdout", "output_type": "stream", "text": ["获取数据时间范围：2020-03-11 到 2025-03-10\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# ======================\n", "# 1. 数据获取与预处理\n", "# ======================\n", "\n", "pd.set_option('display.float_format', lambda x: '%.4f' % x)\n", "plt.style.use('seaborn-v0_8-bright')\n", "\n", "# 设定时间范围（从现在往前推5年）\n", "end_date = datetime.now()\n", "start_date = end_date - <PERSON><PERSON><PERSON>(days=5*365)\n", "print(f\"获取数据时间范围：{start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}\")\n", "\n", "ticker = 'TSLA'\n", "data = yf.download(ticker, start=start_date, end=end_date, auto_adjust=True) \n", "# auto_adjust=True时，下载下来的数据是后复权的价格，也可以视需求调整\n", "\n", "# 转为标准列名\n", "data = data.rename(columns={\n", "    'Close': 'close',\n", "    'High': 'high',\n", "    'Low': 'low',\n", "    'Open': 'open',\n", "    'Volume': 'volume'\n", "})\n", "data = data.dropna()"]}, {"cell_type": "code", "execution_count": 8, "id": "545da400-405b-4ef1-8a2c-26c2d8d33d4d", "metadata": {}, "outputs": [], "source": ["data = flatten_yf_columns(data)\n", "data = standardize_columns(data)"]}, {"cell_type": "code", "execution_count": 9, "id": "b9775ec2-3998-488b-a9dd-7d007b793037", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "数据概览：\n", "             close    high     low    open     volume\n", "Date                                                 \n", "2020-03-11 42.2820 43.5720 40.8667 42.6800  199837500\n", "2020-03-12 37.3700 39.6333 36.4167 38.7260  283636500\n", "2020-03-13 36.4413 40.5047 33.4667 39.6667  339604500\n", "2020-03-16 29.6713 32.9913 29.4780 31.3000  307342500\n", "2020-03-17 28.6800 31.4567 26.4000 29.3340  359919000\n", "\n", "数据基本信息：\n", "<class 'pandas.core.frame.DataFrame'>\n", "DatetimeIndex: 1255 entries, 2020-03-11 to 2025-03-07\n", "Data columns (total 5 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   close   1255 non-null   float64\n", " 1   high    1255 non-null   float64\n", " 2   low     1255 non-null   float64\n", " 3   open    1255 non-null   float64\n", " 4   volume  1255 non-null   int64  \n", "dtypes: float64(4), int64(1)\n", "memory usage: 58.8 KB\n", "None\n"]}], "source": ["print(\"\\n数据概览：\")\n", "print(data.head())\n", "\n", "print(\"\\n数据基本信息：\")\n", "print(data.info())"]}, {"cell_type": "code", "execution_count": 10, "id": "ffea1de7-14ed-4eb5-a59a-7a6a3c29539e", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABNUAAAIiCAYAAADvkf6JAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8ekN5oAAAACXBIWXMAAA9hAAAPYQGoP6dpAADaR0lEQVR4nOzdB5gTVRcG4G/pvffeQUCKKKKggAUUQRFRLNjArihixwb23y42VAS7YgVBFJCOIEhHQHrvvdeF/zlzc3duJmWTbMok+73Ps2Q2yWZnl00yc+4paadOnToFIiIiIiIiIiIiClmO0O9KREREREREREREgkE1IiIiIiIiIiKiMDGoRkREREREREREFCYG1YiIiIiIiIiIiMLEoBoREREREREREVGYGFQjIiIiIiIiIiIKE4NqREREREREREREYWJQjYiIiIgylZ6eDrc6ceIETp06lejdICIiomyGQTUiIiKiFHXy5Ekr4GR+yHWRuOCCCzB8+HCv6z755BOkpaVhwYIFYT/ewIED0aJFi7C+plChQvj77799rs+dOzeWLl0a9j4QERERZQWDakREREQp6qWXXrICTubH4MGDrdu6d+9uBcTMj969e/t9nMWLF2Py5MmoUqVKxnXHjh3Diy++iNatW6N///5x+5mIiIiI3IJBNSIiIqIwtWnTxicg5e9DSGZVly5dUKlSJeTNmxfVqlXDddddh3///dfrMeX+3333Xcj7IBln8lg5c+bEpk2bAt5Pgme7d++2Plq2bOl122233YbVq1dbH1dddVXAx/jyyy9Rr149NGnSJOO61157DY0bN8Yvv/yC8ePHY9iwYQG//pJLLvH53dx9992YMWOG39/bjh07Mr5WHldff/DgQZxzzjnW9tSpU71+z6eddlrG57NmzQr590hEREQUKQbViIiIiML09ddfY/ny5RkfvXr1QvXq1b2uk4/Ro0dbAbjatWtj6NChmD17Nj744AMrwCWljwsXLox4H/78809s3boVNWvWxGeffRbwfhLIK1asmPWRK1eujOslcCX7LIE5+ZDSykC91OTnveGGGzICWBIQlKDaW2+9heLFi+N///ufFaBbs2aN38f45ptvsHnzZq+PV155BU2bNvW5Xj5KlCiR8bUXXXRRxu8zf/78+P77763tZs2aWZd//fWXdb85c+ZYn1eoUCHi3ykRERFROOwjKyIiIiIKScWKFb0+lyBQnjx5UKtWLa/r77nnHisrTYJOWsOGDdGuXTuce+65ePrpp4NmeAUzaNAgdOvWDWeddRbefPNNPP7448iRw3e9dNeuXdi/fz8KFy6ccZ1kfEkGXZ8+fTKuk6+VYJ80/NfBM/191q9fb+2v2LZtG6644gqrVLRGjRrWdbfffjt+//13XHzxxVYGWdmyZX1+P05Fixa1fmflypUL+nNKsE9+r3q/1q5dawXPJNj222+/WYFB+ZAAnZASVyIiIqJ4YKYaERERUYxIMMvfYADJGHv22WdRtWrViB53+/btVjDuzjvvtDLIJLtr4sSJfu8r10uQq379+tZAAQlOXX311VbZqDkoQEo0v/32Wyu4JoE0nc32xBNPWNty/c6dO9GxY0crECZBPE2CXV988YWVtSaZeStXrsy4zTkoQX9IBpzsS6Db9e9NMtMaNGiAggUL4tChQ9ZwhP/++w9Hjhyxvk/Pnj2tLDwJFOqgmtx2+PDhiH63RERERKFiUI2IiIgoRiTgI8EmCX7NnDnTCiJpl112Gd55552IHlf3OJOgmASUrr322oxAmJP0c5Ng2LvvvouSJUtaATDJbpOSziJFimTc75prrrGCaNJfTTLghA6o6Qw16cm2b98+fPzxx/jjjz+8vo+Ut8pjSgZa8+bNrUCfyJcvn8+wBPm47777rN+Jv9vkQ35vQh5LSkWldLZAgQL4/PPPrdvOP/98KxNQ3//000+3SkAlsCffW4KWH330UUS/XyIiIqJQsPyTiIiIKEakz5iUPkofNWmwL8MKrr/+etx4441W5lgkJDAnATTp46bLNO+44w4rQ0xKPf2VWkovsgsvvDCjbFXKNQ8cOGBlfDmVL1/eKhWV4NmQIUOsEksJAErw7uyzz8Ybb7xh9VS766670Llz54yvk8eUHnETJkywfl5d1hmoz5oEBiULbcSIEX5vl+CY0D3fjh49amWw6Z9Zvo8EAiVj7plnnrEy2OR7NmrUyAr4yRAECf4RERERxQoz1YiIiIhiSDLFZKjAxo0b8fDDD1uN9aWvmgSoAgWcgpk+fTrWrVtnlX1q8lh16tTBV199FdJjSJaYTMv09yHTPIVksUnmWfv27a3Ppf+ZZImVKlUq6GNLAO+hhx7KCH5JIFE+JDgnATT9uQTNZIiC/tz5IeWe2hlnnGHdv0yZMlbQTEgpqATVnnzySas0VbIBZUjBgw8+aPV3k/2QLDciIiKiWGFQjYiIiCgGJKNMenvp3mCSRSXZZZMnT84Ipl1wwQV+e64FI1lq0j9MMtKkN5t8SPmjZInJbWaJqWR3LVmyxMrm6tChA/755x/reunH1rVrV7z00kvW/eVDSkRlKIAE6DRdBuokP5cExMIhQTUpITXJ/snvwvzYsGGDz9dKkGzx4sXW7RIsE1LKKplozt+f9F+TAKaUlprTTomIiIiijUE1IiIiohiQRvkSpBo9erTPbVWqVMGAAQOs/mUSLAqVlGRK7zLJSJs3b57Xh2SwLVq0KCNwJqS32KZNm6wgnATRzOmk0o9M+qxJxpeQ/ZEJmpKtFsqghMwy1kwylED2y/nYst96eqf+aNWqldd9Zs2aZQUk5TYJkkkGnHxIOa2Ug0rPNn2d/pABEOYEUyIiIqJY4PIdERERUQxIU31pni/N8mWypjPIoxv5695jofjuu++sQJ0MJpDpnU5t27a1stV02aN8j/vvv98qiRS6+b+QHmzNmjWz+rHJfWQYwMiRI0PajxUrVqBy5coh77eUvG7ZssXKOJOSTU2y4v7++++gXyuZZ5JtZ5LyV+nz9umnn/qUeI4aNSrg0AYiIiKiaGJQjYiIiChG3nvvPSvQJVli0shfeoXt3bsX48ePx6uvvmr1NjMzviTwJAErf2TIgASLpEebv4CakO/zyCOP4M0337SCb2vXrrWmhPojQT7pkSb9yqQcVDLXLrroopB+rokTJ6JTp04IlUw5lSCY9JaT7LQmTZqE/LVS7mn+DJJZd++99+LSSy/FTTfd5HN/Ka+Vn52IiIgo1hhUIyIiIoqRli1bWllaEkCTaZmSOSYN92vXrm1NyJRJoCbJKNNZZU6TJk2ySigloywQCbhJwEmmasq0TykHDTRlVPqojRkzBvv378fx48etbLD169dnmoEmvdumTp0a8mRNmcIpQbv58+db+3PFFVdYGWuRkKy2m2++2eqjNnbsWL/32bFjR1hZdERERESRYlCNiIiIKIv69etnffhz1lln4Ycffsj0McwBA5HeR6ZjSv8y8frrr1vlndJfTA9MkP5q0oNMglqvvfaaFfB78cUXccstt+Duu++2AnCSPSeBPXksp2PHjlm3S0Zc3bp1M91fCdjdeOON1mROmXgqpZxLly61yj6lv5v0ehs3bpw1dEGCjTJwQYYXyPeR8lnpPSfDDaSP3Lfffmv1jbv11lut/m9mNpoEBCWjTX6+Tz75BL17985034iIiIiyioMKiIiIiFKQNPWXYJmQKZ8FCxa0AmotWrSweqhJSaUMSXj44YetElTJbpMPGawQKAgomWa7d++2ylqdypYt6zNtU+4vAbM33ngjo+T0mWeewYQJE3D55Zdb/eQkSCelnLI/Uh5bs2ZNa6CB7v8mgTQpH23Xrh2WLVuGwYMH+5R3SsmrBOlkwqlkB0qfOCIiIqJYSzsVyrIoERERESUdyVrz138t0PVCMtpEjhz+116DfW1W7y+HpfL95VK+v3zI53qqJxEREZGbMKhGREREREREREQUJpZ/EhERERERERERJVNQTRri6nR+80MmO4nVq1ejY8eOVi+OVq1aWROqnGRkvDTVrVChgtUEV5rbEhERERERERERpWxQbfv27VYTWhnjbn7IRCiZFnXJJZdYU6tkdHuvXr3QrVs3zJs3L+PrZZT7wIEDMWjQIEycONGa+NSjR49E/khERERERERERJQNJLSnWuvWrfHkk09a05ycPvvsMwwZMsQKlunGtH379sXmzZut66VprUyHkglQbdu2tW7fsWOHdZ0E3qpXrx7SPujx8oULF2YDXCIiIiIiIiKibOzUqVNWopdURAYa3KR5zz1PQKZa6dKl/d42YsQIdO7c2SvQ1aVLF7Rv3z5jRPuePXuswJwm4+DPO+88jBw50spsC4UE1CpXrpzln4WIiIiIiIiIiFLD+vXrUalSJXcH1STzTLLVli1bhnr16uHFF19E48aNsWHDBp9ssxo1amDXrl04fPiwdXvVqlV9ooZyn40bNwb8ntJzzey7phP1pH+bZKtlRspTJ0yYYGXH5c6dO4Kfmog0Pp+IoofPJ6Lo4fOJKDr4XCKKLj6n4kOy1CQeFUqMKGFBtfT0dOzcuROvvfYannrqKSut7vPPP8f555+PpUuXWsGzQoUKeX2N/oF2797t93Z9HwnWBfLyyy+jf//+PtdPnz4dBQoUCGnf5X4zZswI6b5EFByfT0TRw+cTUfTw+UQUHXwuEUUXn1Oxd+jQIesylBZhCQuqSYbYr7/+apVv6mDZK6+8gpkzZ+LLL7+0Jn4eOHDAJ1ooihcv7vd2fR+5LZAnnngCffr0yfh83759Vvmn9HUrUqRISJHhsWPH4uKLL2ZkmCiL+Hwiih4+n4iih88noujgc4kouvicig+JE4UqYUG1XLlyoWPHjj7Xy7TPNWvWWHWrUpJpWrVqlRUwy58/v3X72rVrrUEDZgmo3Ef3XfMnb9681oeT/EGG80cZ7v2JKDA+n4iih88noujh84koOvhcIoouPqdiK5zfbfAxBjG0ZMkS1K5dGydOnPC6XgYQyARPCbgNHz48o+eZGDZsGDp16mRtN2jQAEWLFsXkyZMzbpdy0ilTpvgN1hEREREREREREUVLwjLV6tati5IlS6Jnz57o3bs3KlasiI8++gjz5s3DF198YWWTSf+zfv364Y477sDUqVPx/vvvW035hGSn9e3b17pNerHJYz300ENWQM054CAa/d8kzVLIpWTZHTlyxLqeUkvOnDmt/99QaqeJiIiIiIiIKPtKWFBNgmIjRozAo48+apVrSoBKhhRMmjQJpUqVsu4zevRo9OrVCw0bNkT9+vUxdOhQNGnSJOMxJKAmfdUkMLdnzx4ri23AgAFR3U95fJk0qjPm5LJcuXLWaFUGXlK38WP58uWRJ0+eRO8KEREREREREblUwoJqonTp0hgyZEjA2yXjbOTIkUEfQ4YOmIMHokkCfRJQkyCL7KsE0aSHmwTaZPKo2cuNkp8ETI8dO2ZNj5V+flKezP9jIiIiIiIiInJdUM3tpNRTAi0SUJPhCEKCahJ4yZcvHwMuKUj+n6UpoQzB0P/PREREREREREROjAqFgGWe2QuDpURERERERESUGUYPKCmYU2CJiIiIiIiIiBKNQTWKu3Anp06bNg3nnHNOSIE1uY+U6BIRERERERERxRKDaimmTZs2VrlqoI/PPvsMEyZMQNu2bVGsWDEUL17c2p48eXLGY/Tr1w+33HJLSN/v+uuvt/qO7du3L+j9cuWy2/c988wzeOqpp4LeXwZEyNdUqlQJ11xzjfV55cqVrc9LliyJzp07+/26qVOnol27dl598Vq1aoVmzZrh8OHDIf1MRERERERERESZYVAtxYwbN84KJMnHn3/+iSpVqmR8Lh+nnXYarr32WvTs2RMLFizAkiVLcPnll+OSSy6xPg+HTEEdPny4Fez6+eeffW5v1KiR36/7/fffrUBXZmrVqmUF03755Rf89NNP1rZ8fP755wG/5q233sJVV12V8Xn//v3RunVrXH311XjggQdC/tmIiIiIiIiIiIJhUC3F5MyZ08rwkg/Zluw0/bl8/Pjjj1bmV/fu3a2AW7ly5fDggw9agbaPP/44rO81bNgw1KtXzwpWff311z63L1u2zOe6devW4d9//8UVV1xh7Y8MBZB9bNmyZcDvI/fr2LEjBg8eHHR//vvvP0ycODEjy072TzLwJLD26KOPYsuWLXjhhRdYHkpEREREREREWcagWhikpdfBw8DBI57LOH5Eq0+/lHzOnDnTpxRSyjElmyscEkiTAF2XLl2sYNbmzZsz/ZqXXnoJ999/P06cOGF9vPfee3jkkUfw119/Bfyapk2bYvTo0Zg/f37A+xw9ehQ33HADihQpYpWjyveRn+mbb77JCN59+eWXmDVrFi677DLs2LEjrJ+ViIiIiIiIiMhkN7qiTB06AhS5TOKQxeL+vQ/8DhTMn/XHuf32261Ak2SY3XTTTdZH7dq1UaNGDesjVNu2bcPYsWPx/vvvo0KFCmjRogW+++47K+stkH/++ce6z99//51xnZSfnnnmmRmfT58+HZdeeqkVcJPAnwQBTVL6KWWsx44ds257/PHHrY/HHnvMyrrbuXMnXnzxRetxpH/cc889l5GB9+qrr+KOO+6wvqdc+itZJSIiIiIiIiIKBTPVspkyZcpg7ty5ePvttzFv3jzUr18fF1xwAb7//vuwHmfo0KFWBpkOxEmWm78SUNPixYutAFeHDh2sUk0h+yL7oMmUzz179ljDEiSjbffu3VaPtPHjx1vXy4d8b8k2k20JqIk8efLgf//7n7V933334ddff7Uy1OR7ahs3brQGKjz00ENh/7xERERERERERCZmqoWhQD5g328nsW//PhQpXMQqKYzn944WCUBdeeWV1sfWrVutzLXbbrvN6nf28MMPh1X6qUkJqPRWW7p0KerWrev3a26++WbrUjLFnn32WTz99NNWoKtJkyZe95NMtnfffdcatCD91nr16mXt3913320F8UaNGmWVeZokC00H6nR226FDh5A7d+5Mp5ESERERERERJdJ/a4EiBYEKpRK9JxQORhbCkJamSjDTj6vLOMbUokaCZpJVdvbZZ1ufly1b1irZrFSpknVbKEG1FStWYMaMGVYWmM4OM4NtUnJpWr16tddwgN69e1vZaffcc4/1/ZyBLyntlMeRslQhQbdp06bh4MGDmDJlihW0k4BgZiTAV6oUX5GIiIiIiIjIvTbtAE5TOSg4NTHRe0PhYFAtm9mwYYOVmaaDapoE1aSPWSjk66VM01lCKf3SBg4caE3bXLNmjRUca968uZXRd8qYtFC0aFGrp5ncT77GqUSJElYgTQJi0ldNHke+5sCBA9bthQoVsso+ZfBA165dA+6nZMTVqlUrpJ+JiIiIiIiIKBHmrUj0HlCkkjDXirJCssQ++eQTa+qmTOvcu3ev1a9Mglw9evTIuJ9klukJneZHenq6lUV26623WoE480O+fv369VYWmwTEBg0ahBEjRljlnDlz5sx47OHDh1vBN5nWKWWj+/fv99lPCapt2bIFH330kXU/2ZZAmkz0lO1u3bpl+rPKRNG2bdtG8bdHREREREREFF3HQ8tvIRdiUC2bkSmdI0eOtCZfnn766ahYsSL69OmDO++80xoOoEkWmJRlOj9eeOEFrF271ioh9Zdh1qlTJyvoVrhwYSvIJuWl2q5du3DvvfdaPdLGjBmDL774wpr82bp1ayuzLZp+//136/tdeOGFUX1cIiIiIiIiomg6xqBa0mL5Zwpr06aN32CVTPuUj0AkuGYG2JxkyEAgP/74Y8DbpP+alIEuWLAgY5iADCQYMGAArr32WkyfPt3KpPvqq6+sDLpy5cpllH9KIFCXf8rkUrl92LBheP31161MOJN8D7leMuXMDDkpG5UhDURERERERERuzFSTduTJ2L89u2JQjeJCSkcl2CXTPE3yuUwNlcmesv3OO+9YH5GoV69eRhBx7NixPtNZpeSViIiIiIiIyE2OHbe3T6QDeRhUSxr8r6K4cQbUTNHOIHMG1IiIiIiIiIjc6Hi6d1CNkgcjD0RERERERERELslUo+TBoBoRERERERERkQt6qjGollwYVAuB9AKj7IP/30RERERERBQvR5mplrQYVAtCT46U6ZOUfRw6dMi6zJ07d6J3hYiIiIiIiFLc4aP2NoNqyYXTP4PIlSsXChQogO3bt1sBFml+f/LkSSvIduTIETbDT8EMNQmobdu2DcWKFcsIqhIRERERERHFyqGj/ktByf0YVMtkWmX58uWxevVqrF27NiPwcvjwYeTPnz/oNEtKXhJQK1euXKJ3g4iIiIiIiLKBQ0fsbWaqJRcG1TKRJ08e1K5dO6ME9Pjx45g8eTLOP/98lgemIPk/ZYYaERERERERxctho+MUg2rJhUG1EEiZZ758+axtCbicOHHC+pxBNSIiIiIiIiLKCmaqJS82BSMiIiIiIiIictGggqPHgHQG2FyPQTUiIiIiIiIiogTZf9g7qHbiBNDgFvVx8mQi94wyw/JPIiIiIiIiIqIE2b3fO6i2fS+wcpP6fOMOoHKZhO0aZYKZakRERERERERECbJrn3dQzcxOW7ouIbtEIWJQjYiIiIiIiIgoQXY5MtWOn7A//49BNVdjUI2IiIiIiIiIKAGOHQf2H3IE1YwBBQyquRuDakRERERERERECe6n5i9Tben6uO8ShYFBNSIiIiIiIiKiBJd+CpZ/JhcG1YiIiIiIiIiIEjykQAfVjhlBtQ3bgQNGeSi5C4NqREREREREREQuyFSTLDUzU02s3RrXXaIwMKhGREREREREROSSTDVnUO3w0bjuEoWBQTUiIiIiIiIiIhf2VBNHj8d1lygMDKoREREREREREbklUy3d+zoG1dyLQTUiIiIiIiIiogRgplpyY1CNiIiIiIiIiMilPdWOHovrLlEYGFQjIiIiIiIiInJpptoRBtVci0E1IiIiIiIiIqIEZqoVKagu2VMtuTCoRkRERERERESUwEy1MsXUJXuqJRcG1YiIiIiIiIiIEpipVqa4umRQLbkwqEZEREREREREFGfp6cCeAyFkqrGnmmsxqEZEREREREREFGc6oCZKG0G1Y8xUSxoMqhERERERERERxdlOY0hB/rxqW4YUcPpn8mBQjYiIiIiIiIgoQf3UShQGCniCansPsKdaMmFQjYiIiIiIiIgoQZM/SxQBaldS20vXq2w1E4Nq7sWgGhERERERERFRAjPVTquqtv9bx0EFyYRBNSIiIiIiIiKiBGWqFS8M1KuitjdsB3Z7rteYqeZeDKoREREREREREcXZgcPqskgBFVgrkE99vmmnutSfM6jmXgyqERERERERERHF2eGj6lJP/izoCaLtOaAuC+VXl5z+6V4MqhERERERERERJSioli+Pd1BNJoCaQTVmqrkXg2pERERERERERHF2+FjwTLXCOqjGTDXXYlCNiIiIiIiIiCjB5Z8FHEG1UkXV5Y69idg7CgWDakREREREREREiQqqOcs/D6rLZnXV5eotwKlTidhDygyDakRERERERERELhlUoF11vrrcdxDYvT/4Yx08DPR8Ffh9Riz2lAJhUI2IiIiIiIiIKM70VE9n+aceXnBGbaB8SfX5qk3BH+uN74HBo4AOjwEnTsRqj8mJQTUiIiIiIiIiIpcMKhCNawK5cgE1yqvPV20O/lgbd9jbDW4F1m6J+u6SHwyqERERERERERHF0fptwOT5dlaaM6im+6nVqBBaplqeXPb2svXAefcDKzZEd5/JF4NqRERERERERERxJGWamh5UYJZ/NqujLkPNVMuT2zdo17q36sdGscOgGhERERERERFRHP272t72V/4p/dQizVSTr5FebJt2ANP+jd4+ky/j105ERERERERERPHkDKpJ1lmD6pFnqlUsBeTOBWzeCew+EJNdJg9mqhERERERERERJYhz+mejGiooJqp7gmrrtgLHT4SWqZYrJ1C8kNretS82+0wKg2pERERERERERAkIpJk91S48A6hbGbj7Cvs2KePMmxtIPwms3x748XQQTqSlASWKqO1d+6O+62Rg+ScRERERERERURwVKwQcPuodYKtbBfjvS+/75cihstX+Wwes3pwW8PFOpNvbcq8ShdU2M9Vii5lqRERERERERERxDqpp+TyZaoHoYQWrg/RVM0tDJRDHTLX4YFCNiIiIiIiIiCiOdHbapWcDRY0Amz9VyqjLjTvSQgqqWeWfzFSLCwbViIiIiIiIiIji6Ogxdflwt8zvW6SgujxwOPB9jhvlnznYUy1uGFQjIiIiIiIiIoqjo8fVpQwhyEyh/OrywOHAmWrHPI+nM9XKFlfba7dkbT8pOAbViIiIiIiIiIhcGlQr7Amq7T+EkMo/m9UBmtQCcuaQklFg3das7i0FwqAaEREREREREVEc6cyyPKEE1QqEV/7ZtztQML8KrIlp/2ZpVykIBtWIiIiIiIiIiFxf/gnMXVMaN7+SE01vA6Yv8s1Ue+l2ewhCo5rqcuWm6O472XIZ20RERBSmD4YBQycAP/UHShVL9N4QERERUcqVf3oy1SYvyIHJC87NuP678cA5DbyDarlz2l9XqbS63LA9WntNTsxUIyIiyoJ73wYmzwee/DTRe0JERERESRdUyxN6UM3p8FF7OyOoZqROMagWewyqERERRejUKXt7zrJE7gkRERERJYsTJ4CTJ9V2nlyhl386HTri21ONQbX4YlCNiIgoQjv32tubdiZyT4iIiIgoWRwzJnWGM/3T6VAmmWoVS6lLmQBKscGgGhERUYTWbbO3d+z1zlwjIiIiIgpW+hmT8k+jp1q1cupy+x5g9/7I9pWCY1CNiIgoQuu2eo9F33MgkXtDRERERMng6DF7O5cRBAu7/DOTTLWihYAaFexWJbIA3OkJ4NJHuRicckG1FStWoFChQvjss88yrlu9ejU6duyIEiVKoFWrVhgzZozP17355puoX78+KlSogDvvvBNHjxp/VURERDE8GPpmnPd1m1kCSkRERERhTP5MS8v8/gXyATmM6E2OHKe8eqqt3AiMm6O28zjKSZvVUZezl6neaiOnA3/M5HFrSgXVjh8/jhtuuAG5ctkh1f379+OSSy5Bs2bNsHDhQvTq1QvdunXDvHnzMu7z8ccfY+DAgRg0aBAmTpyITZs2oUePHgn6KYiIKDvp/R7ww0Tv67bsStTeEBEREVGy9VQLpfRTSODNzFYrUsC7/POKJ+3bzPJPM6g2aymwYqN9vZSEUtaFMGci9p599lk0b94c+fPbfyU//fQTypUrh379+iEtLc0KqM2fPx/vvPMOhgwZgpMnT+Lll1/G4MGDce6551pfI9fXrFnTynCrXr16An8iIiJKdQN/9b2OK35EREREFGr5ZyhDCjQJqu07qLaLFVJtR3T556I19v3M8k9nptryDfb1W3dHuPPkrqDapEmTMGrUKEyfPh2XXnppxvUjRoxA586drYCa1qVLF7Rv397aXrRoEfbs2YPWrVtn3F6qVCmcd955GDlypJXZ5o+Uh5olovv27cvIlpOPzOj7hHJfIgqOzydKbr5HQRu3p+P4cc989DiT59H4RZXx34FT6HMNn1NEWcH3J6Lo4HOJyL8DhyTOkQt5c5/Ccd0MLROF8kv4RsVHCheQ8s80HDpyCgcPnfA6Lk3DCRw/bjdMa1RD/s2NVZuAvxedzChY3Ljd+35kC+c1K6FBtd27d+P222/Hzz//7JWlJjZs2OCTbVajRg3s2rULhw8ftm6vWrUqcpiFxZ77bNxo5DQ6SHZb//79fa6Xfm0FCgQYqeHH2LFjQ74vEQXH5xMlpyt8rpk2ezXqFlyUkL2RZrMDxqh9KnZiLMoVO5SQ/SBKJXx/IooOPpeIvC1YVwpAS6Sl78eoURNC+pqTx84HUFxtH5WeI6Ww/+AJfPDlNAB2stGc2TNwfNsOr68tW/QibN1bEMMmS4pcPuu6ydP/Q4njK6P6c6WKQ4cOuT+odurUKWuwQO/evdGwYUOf2yV4JoMLTIULF84Ixvm7Xd9n+/btAb/vE088gT59+nhlqlWuXBnt2rVDkSJFQopYypvCxRdfjNy5w8jVJCIffD5RUnvL3ixV9BR27E1DgWI10KFD1YTszv6Dx4G31XbT5m3QtFZCdoMoJfD9iSg6+Fwi8u/Y1DTgJ6BiuULo0KFDSF/z1vicWOGZPF+lYnEs2ggcS8+FnMVaed3vnBZn4/zG3hlorWbnxE+Tgd2HVEBNFC97Gjp0qBuNHyfl6IpGVwfVPv/8c6sM8+677/Z7u0z8PHDggNd1MrxAFC9e3O/t+j5yWyB58+a1PpzkRT6cF/pw709EgfH5RMmuVsU07NgrvSlyIHfuxMwAOpZub+fOJc+phOwGUUrh+xNRdPC5RORN90IrWjD0Y8ciBe3tYoVVGeiJ9DRMW+Q9meDQsVw+x4GNasIKqpm2782J3M6pBmQJ5/UqYdM/hw0bZvVTK1++vDWQQD6mTZuGBx54wNquVKmSNXDAtGrVKitgJqWicvvatWutgQXO+1SsWDHOPw0REWVntSomfvqnHqkujrF1DREREZFr7fNUFxb1Lb4LqLDRraqo1VNNGTfH+375/EwU7X6x73UcVBAdCQuqyaTO//77D/Pmzcv4OPPMM/Hcc89Z2x07dsTw4cOtMlEzENepUydru0GDBihatCgmT7bDrTt37sSUKVOsryUiIop3UC2R0z/1iqdzm4iIiIjcZa+n6K5IgfCmf2ZsG18nU0BlvuNHDwEPXQNceIbv19aoANx2mfd1DKpFR8LKP6WE0ylPnjxWoEwy1bp27WoNFejXrx/uuOMOTJ06Fe+//z4mTFBN/GRAQd++fa3bpJS0ZMmSeOihh6yAmnPAARERUTSlG6WWZlBt9341Ij2vnxXCWDMDaYcZVCMiIiJyf6aaUdKZmcJGUC13LqB0MWD7HvX5BU2BO1T+UUAlHC3ktyawwiKVJHT6ZzAycGD06NHo1auXNcigfv36GDp0KJo0aZJxHwmoSV+1nj17Ys+ePVYW24ABAxK630RElPr2OwYClS8J5Mmtyi6lBLRqufjv0+EjqreGsxSUiIiIiNxl70HfPmnhlH9KK7RvnwZWbgLaNgWqlMn864sX8s1Ue2MocG9nIJ9v23lKxqDaxIkTvT6XjLORI0cG/RqZ5GlO8yQiIoo1fSBkKlcCWLc1cUE1r0w1mZZORERERK6072D45Z9eQbVcwIXN1EeoChqZbtrDH6pjyKdvCv1xyCU91YiIiJKV9K4w5UgDypdIbF81r55qzFQjIiIicv2xZDiDCsyeahJUC1dOI/pT0igFnfZv+I9FNgbViIiIspCpdl4joNXpKlMtkRNADxqBNA4qICIiInKfhauAhrcAf8xUnxeLcPpnngiCarlyercu0fLmDv+xyMagGhERUYRBteanAZMHALly2QcnmxMUVDtsBNU4qICIiIjIfYaOBxatUQGuDi2AtnbL+LAHFYSrRX17u6wxNzJfAgZspRIG1YiIiCJN2S/oe3CSqEy1Q0c5qICIiIjIzXQ1QZ9rgN9eAYoVjrD8M+epsL93o5rApHeAld8AJYva1ydian0qYVCNiIgoTHv9BNUK5FOXRwMMCUhPB4b8DqzZHJt94qACIiIiInc74jlGyx9BIMss/5QqiUic3xioUcG7fFQmiVLkGFQjIiKKsPzT7IOhm7+mn/T/NS99DfT4H3DFU7HZJzM7jZlqRERERO6jW3Tkzxv+1zqnf2aF2V9NB/ooMgyqERERRRhUMzPV9MHJiXT/X/P8F+pywcrY7JPZR42DCoiIiIjcRwewIulj5l3+Gb2g2oHDWXus7I5BNSIioiiMQQ+WqXbiBHD8RBynfzJTjYiIiCilgmpmplqgyohQXdHS/zEkhY9BNSIioiiUfwbLVPt7sb0tfSy00TOB654Dtu+J7qCCo8eREg4cAhr3BPq8n+g9ISIiIkpsUK2gp39vsB6+obrsHOCezmqbmWpZw6AaERFRFAYV5MwZeOXw95n+D4IueRT4bjzw9o9Z3yczOy1VemPMWqrKZeV3RERERJSdg2o5jOjNkWP2Ymok0tKAK1upbWaqZQ2DakRERJGWf4bYU+33Gb79zvYf8p4MGs2eaqkSVNuyy/dnIyIiIsqOgwpM0ahKKOjp0cZMtaxhUI2IiCia0z/TfbPa5i73zSibucS+rkSRrO/ToRQOqnHwAhEREWX3TDVTk1qnsrwvevABM9WyhkE1IiKiGE7/3LXfd2VRAm/rttnXRSMTK5WDasc8vzMiIiKi7BxU+3fIcfS9fAbOa5T1oJo+jt29n8dZWcGgGhERUQynf+qUenNikwTRNm63P49GJtahI2kpF1TbutveZgkoERERZfegWp1KQPOaW6KyLxVLqf2QCfWrN0flIbMlBtWIiIjCIAceOsATyvRPHVQrVdQ7iLZpp/F5FNLuzcdIlemfOlNNsASUiIiIkl20yj+jQYZs1auithevTfTeJC8G1YiIiCKY/CmKFAgvU61APjsAtmlHlDPVUrj8UzBTjYiIiJLd4WPRGVQQLfWrqcvFaxK9J8mLQTUiIqIISj+luaus8Dkz1QIF1eT+BfLaAbCNRlCNPdX8Y6YaERERpRI3ZaqJyqV9W25QeBhUIyIiyuKQApEzk/JPCaqd8vSUfWRgdMs/5XHNx0iFoJo0zN22J7olskRERESJcvKkGr7kpqCa7vm7/1Ci9yR55Ur0DhARESVlUM3op+aVqRYkqLZzn9oe9TeQI0f0srCkz1v6SXtQgRywyYGb+T2Sjfyu5GfQWP5JREREyczseeu6oJrneJXCl8SH20RERPGnV/LMfmpmT7VgmWqmaAaM/GVxJfuwArP0U7D8k4iIiJKZWUngtqCaPl6l8DGoRkREFEFQTR+EhNNT7YqW/h8zqwGjg0cCH7h9MAxodgew1RGkSrqgGss/iYiIKInpYxlZiM3tkppBvejL8s/IMahGREQUBp0eXzjMTLWC+YCvnvT/mFkNGOmgXP48x5Ejh2rcdtQTVLv3bWDOMuCFL5FUmKlGREREKVnt4OjLm0iFGVTLMgbViIiIwqAPOpzlnKFkqhUqAFzS3L6tQqnoBIx0UC5frvSMcgLnsIJkGV7w0yTgvF7A34u9r2dPNSIiIkpm+wK0EEkkln9mnUuSDomIiJKDPujQK3vh9lSrXMa+rVZFYNOOyANGko1mTf70fH2e3OlIO6mCbM4gmlvKDDLT9Vl1OXWh9/XMVCMiIqJktu+g+zLVWP6ZdcxUIyIiikJPtZwBMtWWrlOX5Uuqy0ql7dvqVLIPsszBBaEG1E7vATS+Ddi9X12XN0imWjIE1fYeCHwbe6oRERFRMnNzphqnf0aOQTUiIqIoln+amWoS7JqzXG23buybqXb2aWr6k0zqXLUpvP2QTK7lG4Bl64GfJ6vr8uYOHFTLkwRBNf27MhX1rOay/JOIiIiSmRsz1XRQTRYv0x3VFhQaBtWIiIgiKf8MMKjAPCCZv0KVZ9aoYPdPMzPVqpQFGlTz3HdlePsx+h97+9NR6jJvrhPI6wmqHT4GnDiRXJlq2/f4Xle9vLpk+ScRERElM1dmquUPPk2eMsegGhERURSmf/rLVNvrWZEsXdS+zgyqVSgJNKqptv9dHd5+/DHT97p8udNRvJCa/rlrn/fBUTIE1WSfAwbVeKBHREREKZCp5jyGTCRZjNXHsHv8tOFYtBqY66eSgGwMqhEREUWh/DMjU+1k8Ky2yqXVfdPSVICtdDHvAFwoNm4HFq5Sj6GDciJPrnSULGpnfSXbJKddnt5wpsY17cw7IiIioqTPVHNR+accS0pFhVi63vs2qXhoeCtwxu3B+95mdwyqERERRWH6p79MNX9DDQoVAAY/BnzyMFCssPRBU9dLX7VwSz+b1wPOrGtfLz3VdFbc9r3emWrHwnh8N2WqFSukLpmpRkRERCnRU81FmWqiYXX/VRPbjLYc67fFd5+SCYNqRERE0Zj+6SdTLVCp6E3tgZ6Xqe2MoNqx8INqlzQHqpezr5fpn6WKqvLPHXu9M9XCCdolym7PKminc4F6VYBxbwIF8qnr2FONiIiIktXxE8Cqze7LVBOne4JqUgVh2rzT3t6yK777lEySoMMKERFR8kz/lIMmGVaQM2fg+5qcmWrjZqv+Z+d7poU6SSr+2Flqu31z76mhkqlWyij/TNZMtctaAHderrY37VCXzFQjIiKiZNX5Kfv4rbgnC9+tmWpy/Jo/j3dQbZOxTd6YqUZERBSGQNlnEkTTur8YvFTUpKd1SlBNAmEXPQS0fiDwWPN/lgK79wPFCwNn1bUb+Tsz1Zw91ZIhU033VCtRxL5OZ6odZqYaERERJSGZBD/qb/vz8iXhKqfXUJeL1gAjpgHFOgIVrwa+HGvfRy9yki8G1YiIiEIkgS6dMRVo+qf4bjywcmPgUtFAmWorNtrXHwlQDvrfOrufWq5cQDWj/DNXzpNemWr6+4tjJ5A0mWrmCm6BvOqS5Z9ERESUjGTAlMltQbWaFdTxqBzjfjsOOHkS2LYb+H6CfZ+NDKoFxKAaERFRiMxySmf2me6ppk1dGGZQ7ZgKhGWWWaaDbQU9379cCfu2/YfzZDT2l2mi0xcZj3csuTPVWP5JREREyZKZZlriWRB1a1BNFmnrV1Pbs5ba1+fI4X9oAXljUI2IiChEOkgmATRdtukvU03sORC4VDRQpprZBDZQppq+Pl8e3wOePYfyolB+dSQnpZ+//Z2cmWolCtvX5c9rj3n/Y0Zi9ouIiIgoFJ//AZTp7L2wqasMtJLG4qHb+qot36Aun74JmPquPVSBi5uBMahGREQUooweaQWAtLTgmWpWUC2UQQVGT7UN2zPPVNMZZzoYJxp4VhfPqbUpI4AnWV/6wCjY47mF9EzTAUOvTDVPUE1c+lj894uIiIgoVKNmqAns0ptMW7LW+z7mgqjb+qppsnh7TgPgwwfV5+xtGxinfxIREYUoWDmnv0y1kAYV5PYfVAs1U03ISuK8FSewZ+1mFM7f1LpO+mGYpEGuHBDpzC+3keELOjhp/n51+ScRERGRWwz5XWVv3Xul/2NFczq7manW/WK4kl6g1fRxJnvbZs6FMVIiIiJ30uWc/jLPcgTJVAu1p5p8TWY90HTGmZmpVqww0LLhKSt7Ltj3evpTuL/0s4h3FqCMdCciIiJyCzlG6/E/4L53vPvhCr2gunKTb6bahLeAL/rClfSgK2dQTS/GsvwzMAbViIiIQhQsSOYsBw07qHbcexBCZoMKzEw1U+5c3gE3M53/sz/cO7BADykobvRTMwcyuLUHCREREWUvOrteOI+r9LGfDqrt2W/3zG1a2/d40S3MY0czmKYrBg679PjRDRhUIyIiClEo5ZxhDyoweqrpxw9W/ukvU83JzKQ7q569vXMfMGwqkmZIgf7dXdHSN8BGRERElAi7DwQeBKWP5STwJh+69LNCKaCoZ0K7GzkXa33KP5mpFhCDakRERCEKZfCAGcDSByBBBxWYmWqHs56p5gzinVXX+7aPR8LVmWrmkALtf3eqy73GQSwRERFRIni16/Acry1bD9S8Hlix0b5N+qot8QTV6lWBqzkXazOCap5MNfZUC4xBNSIioiyWKPpjDh0IaVDBsdDKPzMy1UIIqknqfo0K3reNnwOsNA743J6pJop5Vnb3HfIdwEBERESUqPJPvdj57BDv4QRi1WY7U831QTXHcaXuaasvOf0zMAbViIiIQrR5p7osXzL0Ay6ZChosAGZmqoVS/hlOplrD6sC+g/b17c9Sl4N+g2tLKfxlqhUtqC5PnfL+HRERERG5oafaiXTf+8ki5vptartGeSR1ppoE1biw6R+DakRERGEG1cqVCP1rJMAVrCmt2VPNK1MtUE+1Y5n3VNOZcY1rAhefCZQprvqS3XyJun7MLCRVplq+vECe3L4lF2Lav0C9G4E/ZsRhJ4mIiCjb85epVsSzAOjMVNu0w+6p5maZ9VQLtuCb3eVK9A4QERElg1F/Az9OCj1TTQs2pEDkyWVnYemebVnNVNP7d2Zd1RR3/fdqKqgEoNzamyyz0lrJVpOx9RJUK1scOH4CKFQAuOghtXp66WPAqYlx3WUiIiLK5oMKdFsO56KfngC60RNUq+jyoFqg6Z/6Usjxls5cIxsz1YiIiELQ81V7u3wmmWp3dLK3MxtqECjjLNOeakEy1Z7rAXzwIHBze/W5ZHlJtpxeRZXeZMmUqSYkkCZkLH3t7kDJK9TBHXt8EBERUTyZATS92Ll1l//yz02eKocKYSzIJkLOnEBOIzqkF2/lel0twGEF/jGoRkREFALzQCNQptqkd4APHwRevt2+zt/KpSlQv7VAfc9CyVSrXAa4+wpVNmkq4smaM/usJcP0T/0zCWkCLP1Jjh0HFq+J3/4RERER+fRU8yx2btvje7+1W+1J8G4v/3Qek5rHmboEVP8s5I1BNSIiohA0qG5vVynr/z7nNwbuusI7MKR7aQQigwz89VybtwKYvTSyTLVAdKaaPIYEpdxCfkdzlgXPVNNBtcVr47dfRERERKEE1bbuthdhe3f1LpOUKebJUDZpHluaZZ/msALyxaAaERFRCPSBxPf9VH+yzHRoEVoPDQmoNajm/zbpxRFJplpmAwyE2b8t0Z773N4OmKlWWl0uMrLTzIO7UP5PiIiIiKI9qODgYbsKYOevwFv3AR3Pse+TDFlqTuZxZsF83lUF5I1BNSIiohDoIJQuoczMV0+qEsxhL2R+36HPZn7Q5pOpFkFQLVcue7XRTX3VRkz37Z3mpLMD/11tX7f/sL0tgwte+TpWe0hERETkZ1DBMbtvmgSfdFXAUzfa93F7PzVNhmb5C6o1ra0upy6M/z4lAwbViIiIQnDgcGiDBzSZYinDAs6sl/l961cDZn8MVHWUlUrvsGhmqrm1r5ru1fHYdWpaabDyz22e8gpzuIH2xCfAig2x2ksiIiIi30EFG7er7Yql7ZYe5jGdTDBPBuYgAj2cQFzQVF2OmxP/fUoGDKoRERGFkalWOMRMtXCdUQdY/R1wdRv7ug2eg7SoBtVcNgH05EnVyFdIZl8guvzTNH2R73VXPavKMIiIiIji0VNt4w7flh/6eEukn0RSCNRv98Iz7OMuDivwxaAaERFRGJlqsQqqCVndfPd+OytrvZ+gmpQZRDqowMxU25vJVNJ4kQNRKd2UgQ2V/ATONH+3ffaH73ULVgK3vx7dfSQiIiISJ05496W1MtV2BC/zPJGOpFazojo2leM1loD6YlCNiIgohGyqg0fCK/+MVNkSwBdP+C//lF4XuqdapJlqOijYqS9cYaSnn1qjmkDOnIHvly8vULqY93X6/0Rr2VBdfjsOOOCSTDwiIiJKzdJPn0w1xwLgLZeoy77dkdRk0Vdnq41nCagPBtWIiIhCzFJzTtCMFZ2pJuWfZtNYMy0/0ky12pXgKiOmqcvrLsj8vlU8vxeT7l0iLj3b3tbBRyIiIqJYDCnQi4Ob/JR/ikGPAJt/As5pgKSng2rsq+aLQTUiIqIQg2o5c0Q2dTNcutTx8FHvZvy6n1pWMtVevt27hCHR9h60SwtCDTaaenWxt4sVAnLkSI1SCyIiInJ/ptqStcDYWf6DapKBXy5JJn+aZIqp07meaoAFq+K+O67HoBoREVEYQwrMzKhYkcBdmeK+JaBm9pU5lSkc+T2TNsVhI0iXKOH0iHMG1SSAds8V3r8TCXwKBtWIiIgolkMKnAuEzqBasvLXP1i3P5GqCbOKghhUIyIicsWQgkDZauYE0CNGACrS4J6Z4eaGCU7HPNlyeXKFH1Tr3AqoZWS4yWPIwAPBoBoRERHFI6imOXuqJZvbO6rLl4yqBs08TuMxljcG1YiIiELMVIv1kAJT5dK+E0B1plpWSlAlGKcDa1Jemmjh/ExmT7UnuwOfP+493EAy1RhUIyIiolgH1a5oCVQo5X18Va4EktqHDwLLv7IHLJjMCgmzxy8xqEZERJSp/QnIVNNZWfe8BVz5lEq115lqkfZTc5aAJlv5p/RM0x7qBhRy/H+ULMKgGhEREcV+UEHxwkCDavb1ZYoBuUPIunczWaisVcl/NYSZqaarDEhJ8v92IiKi+JV/xjNTTZd/imFTVRloOAGoYArkVSutbshUC6f8s0V9oGhBoEYFdTCrvXUv8O9q4KJmDKoRERFR7AcVyHHIzn2pU/qZGbMygJlq3hhUIyIiCnVQQTzLPx39wyRIFO1MNTf0VAun/LNoIWDNd97DFkTvq+1tBtWIiIgo1uWfElQzj0dSZUhBIJK9JiWgElBjppo3BtWIiIhcOKjAGVSTrLKMAFTu7Fn+KYoZGWr+MKhGREREMQ+qFfJe5Ez1oJquKpCg2nEG1bywpxoREZELBxXUNqZa6qyyqPdUS7Lyz1AwqEZERESxDqpJn9f8xvGYObQgVelhBSz/9MagGhERUajln3HMVCtX0vvzQ9HMVHPJ9M+TJ+3VzqxMNDUxqEZERESxIsdjeqHVXOQsn+STP0OhF0BZ/umNQTUiIiIXDioQzerEJlOtQD7PYyY4qGaudGY1UKgxqEZERESxohc4JWvLPB4rWRQpT083ZaaaNwbViIiIMrE/AT3VxNg3AmSqpUj5p7nSGcvyz2FTgJ6vAkdcUO5KREREyUsHlGQx0BxUID3WUp0+VjvOhUsvDKoRERGFOqggzplqMlnq4jNj0FPNJeWfOkho9umIRVDtyqeBwaOAD4ZH53sQERFR9mS24jCPx6THWqpjTzX/GFQjIiJy4aACrUBeO1NNB9WiNv0z0UG1Y3Y5QY4csS//3LIrOt+DiIiIsqdjAco/ZSE01bGnmn8MqhEREblwUIFP/7MjdhAqWtM/dQZeqkz+zCyopnuBEBEREUUi0NCo7BBUY081/xhUIyIicumgglhlqtWvqi5HTAdOnQJOJGjFMVo94kIOqnluIyIiIsrqoAJ9XJaohdeElX8yU80Lg2pERERJkKkmpZrR6ql23YXqcRevAa59DijaEfhrIfD9BKDDY8DWOJVJ6sy7aE3+9BdUk6BhtPu2ERERUfZkDiow22ikpSH7DCpgUM0Lg2pEREQuzlTTpZpv/hC9/ShaCLj+QrUtgTQpLb3vHaBbf+D3GUD3F+37ym2/TAEOeAKLyVb+qQN3guWfREREFK3yTzNTLTvgoAL/GFQjIiIKQjKd9h9OXKaazuKygltToxfcu+ty78+LFLS3/5xtb9/zFtDlaeCRgUjK8k+zb1x2Kf88IlmNCR5CQURElGpOnrSPLyTAdGM7tX1eI2QLHFTgH4NqREREQcgqpBxEicIJyFRbstbe3rQjesG9ZnWBRjXtz0sX9X+/z0ery4G/IinLPw8esW9L9/w/pgIpOflgGLBhm+/vtOGtwBl3RK9X3rs/A416AEPHR+fxiIiIkpGZoSXHLg2qA1t+Bsa/iWyBgwr8Y1CNiIgohH5qZn+zeLqjk+910SpDbWwG1Yp533ZNP+Cf/+zPyxRXlyOnAZ2fBP5elBzln2ZQLZVWVqX33b1vA0984n39/JXAyk0qGCvbWSV9U3q/ByxcBVz/QtYfj4iIKFnpDHvz2KVsCSBXNmkvwUw1/xhUIyIiCiGoJoGsHAl417yoGfDqXd7XRasMtWQRe7ugI2D4w0SgufF9K5VWJYWd+gLD/wI+jELmmm7wG8vyT6+gWoqsrEqga+I8tf3rNO/bZi21t6cuzPr3Gj/HztSUS71NRESU3ZjBpOw4/Ej/zBxU4I1BNSIiIpcOKdDTpC5r4X1dtPalZgV7e/eB4PeVgNQ+I2vvoNGrLFLj5qjL2hURu6DaYf8rzMls2257u2XDwEG1KQuy/r1+nOT9+e79WX9MIiKiZKTbVkgZZCIWWhONmWr+ZcM/BSIiovAz1RIxpECrXcl7cmW0gmo9Otjb2/cEv++u/WpYgnY4ixOvJEg3dILavv4iRE12yFTbsivwz+TMVJNBG+GaOBd47nP12DL51bRjb/iPR0RElAr04lw021YkE/ZUc2FQ7cSJE3j++edRrVo1FC5cGBdccAHmzp2bcfvq1avRsWNHlChRAq1atcKYMWN8HuPNN99E/fr1UaFCBdx55504epTjroiIKPqZaokMqslBzGlV7c+jNTBBesQ92T20YIlkKB0y3mLNAFsk/pgJ7NoHlCsBXHgGoiY79FTbutt7kIb5f7JojdrOmUPdb7YRZAt1Fb7tg8CzQ4C8FwM796ky4apl1e0MqhERUXaljyOi2bYiGcs/U+V4KiWCai+//DKGDx+OkSNHYu3atWjfvj06dOiAvXv3Yv/+/bjkkkvQrFkzLFy4EL169UK3bt0wb56niQiAjz/+GAMHDsSgQYMwceJEbNq0CT169Ejkj0RERClmz4HETf40Naxub0ezFFUHoTLLVJP+ZxJg0cwAWyS+GmtnqeX07EMsgmpmqWQqZqqZQbV5K1TPs/IlgW4XqOte+Sa8x3Zmpun/Iwl+hvJ3QkRElOrln9k1U03/3Pr3QAkOqp08eRJvvfUWPvzwQzRs2NDKRnv00UetjLW///4bP/30E8qVK4d+/fqhYsWKVkDt7rvvxjvvvJPx9RKU++ijj3DuueeiTp06GDJkiBWgkww3IiKiaFi3zW7U75agWjSz5nQqv78MpKIFvT/fsD06PdXkYGyEp8F+94sRVc6g2tjZ9m2psrIaKKimSz/PrAv0uFRtL14b3mN/PNL78wvOAF69EyhVVH3OTDUiIkJ2z1TLhkMKdIVDNBZWU03CYqyHDx/Gc889h6ZNm2Zcl5aWhvz58yN37twYMWIEOnfubF2ndenSxcpmE4sWLcKePXvQunXrjNtLlSqF8847zwqsSWabP1IeapaI7tunlt2PHz9ufWRG3yeU+xJRcHw+UTJYvUnWn3KiUul0HD+euNGHp1VJy3jbzptL3rOi83xKs9bXcmZk5Jk+ffQEGlY/hRb35MKeA2nYsE0iVSpqdejIKRyPcPzT5p0SDMqNXDlPoUHVEz4/S1bkSFM/z9Hj6Thy5CTGzZbfmTqWOHLsJI4f90TbktimHepnFEeO2f8PM5fIdTnQtFY6yhSTv9Xc2LQj9P+nZRuACXO9zxR6XHoCOXOcQonC6rG37Ers8yBe+P5EFB18LlEqOXhYHYvlyR35MVAyP6cK5FXHH3sPpMbxVDDh/H4TFlQrWLAg7rvvvozPT506hZ9//tkq4ZTMsyeeeALVqxvL8gBq1KiBXbt2WQG5DRs2oGrVqsjhGLsh99m4cWPA7yvZbf379/e5Xvq1FSgQ+tL/2LGeuhUiyjI+n8jNZv17NoBy2LdtIUaNCjPtJ4p27s+HNLRD/rwnMO7PUVF7Pi1fXlPy4PzetmzRdOTasws50Q5AfsyYsxJAHeu23fuOYtSo0YjE+p2FAFyI/LmP4/fff4/oMQI+9roGAGph2bKVGPzdOhw8Yk9BWLtuM0aNmoVkN3XOOQDKWNt79h3BqFGq5+zfC2ShsRhO7JmFf2fvAHAZ9h5Mw8/DRyNf7swPfr+bXhdAPa/rli3+B6MObcPu7fI3UhPzFq7EqKJLkF3w/YkoOvhcolQwf62ULZyLo4f3Y9Qoz7SlbPScWrtSGvw2wYrVWzFq1EykskOHjJH3mXBFNfDTTz9tlYKmp6djwoQJyJcvnxU8K1RIDrptUhoqdu/e7fd2fZ/t2436FAcJ1vXp08crU61y5cpo164dihQpElLEUv6AL774Yiujjogix+cTJYMnf1FvlR0ubIj2Z0nAJnEKV0hH0YJpuOAMY2xnFp9Pq47mACbbnzepdQrzVqjMrvYXtcDp1YESP+TCzgNAkZK1Mu6XjrxWH9RIzFySBnwBlCyWO+LHCGTyxhzAHKBqtZooWaWG120lS5eP+veLN5nm2fNT+/AtLWe+jJ/p4e/V9e3aNsM5DU6h4OBTOHgkDY3ObI9aFTN/7OFWppu3i9uchbPrn8LfW3Ng5DygbIWa6NDBe9EzFfH9iSg6+FyiVJI2Iw34GShVolDCjicS+Zzaly8NH44DChQpm/THU5nRFY1JE1R75JFHcOutt1rDBq677jqrfFN6rB044F2LIsMLRPHixf3eru8jtwWSN29e68NJ/iDD+aMM9/5EFBifT+Rmmz39q6qVy4VE/5le42k+H83nk3OCVbM6aVbDe1GskDyWPRhh5347O1yCNbly5YbRpSFkhzx9wIoUTIv6c1/3OTl5KieWeBILJaldGvifSM+B3LkTOqMpS+57G/hohN0vThw5Zv8O9aTTYoVzIU8eoEIpYPkGmQKaG0P+AOYsA756EihX0v/j7/MsyhYvrKa9ipLF1N+97q938EhO5M4dxckSLsf3J6Lo4HOJUkH6KXWZN0/ijycS8Zwq4clB2n8o8T9/rIXzu03Yb2Lr1q2YPn26tS0ZYlK2KZM7JWPsk08+QaVKlXwGDqxatcoKmEnfNbldJobKwALnfWSwARERUVbJW0xGcMHTqD3V6EEF2mlV1XUSiKrgCb4UzOc7SVN+N5FO09x3UF0WieLABX+DChZ6DiOa1UmN6Z8yMVUH1No29R1UcMAzPEIHQfVwjVe/A177Dhg3B+g7KPDj7z3oO5RDB9P0cAz9PYiIiLKbQ57Fq/y+OTrZgj4W2M9jAXcE1aREU1IWjxzx/GV6SL+0okWLomPHjhg+fLjVa00bNmwYOnXqZG03aNDAut/kyXbNys6dOzFlyhTra4kodcnJ/J+zgB17Er0nlOok+KPfhor7dhxICToIZa5Cbh8GbP0ZyJfXO6i23fGci3T6k86IKuKYLhr1oNoqR1Atyad/6t/38q+AH/rZP+eJE8Cy9bJy7B1Ua+lplff7DPsxJs8P/Ph6WEUx429db/NAmoiIsrudnorAkpl3jUpJejFUL45SgoNq9evXx5lnnokbb7zRyi6THmmDBg2yAmnXX389unbtag0t6NevnzV4YOjQoXj//ffRu3dvteM5cqBv37644447rIy3ZcuW4ZZbbrECas4BB0SUHNZsBn6ZYgcxAuk1ALj4YeD+d+O1Z5Rd7fYEGfLlsQNMqcZZySdBlKKFgFLF7OsKeoI02/f6X7E1yfP3t+nAVk/ZbKIy1Qb+qkofUyVTTYaM6UFjkjUpf5Pa0eNA3Rvtz3VQ7ZLmvo+zw/F/6C9TTQfQzNV4/Zg6cEeUDOTv9fsJ9nOHiCgrsntQTR8fyOLokQgXVlNRwoJqEhT74YcfrMECMu2zWrVq+Prrr60pnHXr1rWuHz16NGbPno2GDRtiwIABVmCtSZMmGY8hAbW77roLPXv2RJs2bVChQgUMHjw4UT8SEWXRba8BXZ4Gpi8KfJ/0dOCDYWr723Fx2zXKpnTpp/SYSlXOTDUzS0krkNf/yqTu4WUaPAro+ATQ+oHA33N/DDPV9AGvVroYULlM8meqHTYOXvPnsXvH+ft/0IGws0+zyzfNwFmgAIPOVCvsCaAJ3TOP5Z+UjB58D+jWH3jkw0TvCRGlgp2ehalSKdoSJDP6uE0WVYt1tBcvs7uEDiooXbp00CCYZJzJ0IJgZJKnOc2TiJLXFk9my8pNwLmesiWn1ZvtbXOVaON2oExx3/5QRFmxa1/qB9Wczxl/QTVd/unkL8Ay+Hd1uXR95uWfZvAmWpxzE06vAeTJnfyZajorUIJcMlxCLiUgKuWfZq87If3wRK5cwEXNgJ+M6a76pMDfsAKdqVa3iu9t+v+KmWqUTD4dpS7f+Ql4u1ei94aIkp3O9s62mWr5vbPkR0wD+lyTyD1yh9Qe2UBESUU33Hb2bTItWuOdkSIZF3OXA5WuBto/Evt9pOxj7wHg2/Fqu0Q2ylTz1ztOl386OZ+rBw8D0/7N/HtmlH/GIFPtkWuBq863P8+Ty87qSuZ+YLqfmmQN6uwxXQJ6/QuBv669nxJQZxmvkDIOHXTsdSVw66XA10/Zt7P8k5I5u1O/phMRRaX8M5tmqsminnncGMoxX3bAoBoRuYaseGQWVFu81je77dPf1PaEuTHcOcp2pGeflDIKZqp5DzJo4+nEsHmn9/2e/yLMQQUx6KlWtgTw43NA1bLq886tgPpVVSBq3VbffU62TLUCxv+FDhbqgQz+tD/L3tYlnP76qunST/k9yf/x4MeA6y/y/VqWf1KymLcieJCNiCjSoFp2Lf+UYwTzOPGvfzPvhZ0dMKhGRMmVqbba+3M5QdYZFEJe2KW0afbSGO0kZRtfjLa3UzmoZq44ysGSv+wxM6h2TRs7YLXZMYzgh0mhfc9YZqpp094HvuwL3N4RKFYYaFJLXT9pHpI+Uy1Q/zh/qpQFXugJPHGD/TvwF1TTjyUHy7p81KSDarL4IRnCR48BE+cmd0ktpbaZS7w/T+aeikTkDtm9/NN5TCzJDWu3JHJv3IFBNSJyX1Btb+iZas6gmgTULugDnHknsGBljHaUsk0vNe2yFsgWQTXJHPMXUNHlhqL7xUB5Tz8uZ9ZXnUqJz1TTKpQCurezf56mtdXlKqMvY7JnqskgglA8eSPw0u1AxVLq8zH/+N5n/TZ1Wam0/8cwX2clW00yOds+CDzxSYg/AFGczfzP+3MGgIkoq/RgIPM9MbuRthqmaUEGzGUXDKoRUdJkqsnkzyWeoFqtivZ0Rv114q0f7WDaLGarUYTMv50xrwPXtEW2KP/0V/opTp60t2WISKCgmjNz6sQJ1US/Tnfv0tB4ZKo56VXlULK7kiVTbeizwPi3Qn+MezvbzdvnLfe+bcN2daknpTrJsAc98EH6qn08Qm2/+X3o358okZlqx9MTtSdElCpkOJDIzoPRcjp68U5jXzUG1YjIHSRgpt+oJKg2eT4wxDNFUFuzRQXQpDn3GXXsPkA660W89JW9LVNEiSLxjyfD4boLgYvPREozM9UCBdXuvgK4ug3wx6sqa61CSe9AjHOCr7miO+AnNXL9mcHxzVRz0sMmnFmIyZypVrUc0Lap9/1+fSnwY7RqBFx7gSqTf+A97z4o63VQLUCmWqAJoNm5BIbcS57nKzZ69x5kphoRZZW0PxC5HYGl7MQ54GoaM9UYVCMidw0pEGu3Aq0fAHr8z15p3rIT+NzT46peFbtB6O4DwF5P1ovTKgbVKItlQ2fVQ8rLHUJQrWgh4Pt+9iTJhtXV5fyVKhtNSIBm627foFq6keWW0Ey1oqmXqeZPp3OD3/6/O1XGmSxcrPQEHczyz0CZama5izmsQGctErlxYaR2JTW8RLCnGhFllU4AcAaWshPnzz5/JRctGFQjIlcwSzj1KpB5YNzxCbt8rH41++Rfyj/3eqbWaec1UpfMVKNISHBIB3ObZ4OgWiiZak51KgNFC6ppev+utrNG9UGVZJPq4Et+Iwgkze3l98tMtcgz1czfp6Z/36H8/8ngAj1oYpNRvrtxR/CeauawAjMjkUE1SrS/FvoOMTIXRnT/H/PYgogoEgyq+f7sJ0/ai6XZFYNqROS6oJppznLgv7XA7GX2dQ2qAcWNoJpZ/nlPZ+Dl2wNPuCPKzMbtKmiQM4fd3D6VhdJTzUma/+ssPn3yqgMtMhVKlwRKUE1+j9q2PSqwpk9u2VMtgkw1o/xTm/g20LKhKs8NRZli6lL63Wk62BisnFMH1XRvSzOgR5QI0xcBrXqpoRkmc2FE9wLM7pkURJT1VjW6bUK27qlmHNfphb79RgZ7dsSgGhG5OqgmWTDfjPM+qeva2h7nbGWqeVZHfnsFeL+3feKnJ/QQheOfpXaJo78ARiqvOJb2BFtCoSdPzljiHVQrV8K7TNDsvyX3MYPg8ZyeVcITLNq1H8ndU81PptrZ9YGp76nLUOhyOLNcV/9e9O/JH/3/pXtVCclWJEoUncEuvVjNTAmd5S7Bf13izvJPIopGllp2z1R79U51+dA19jnXfuPYLjtiUI2IXB1UW7cVGPSb2u7RAVjwqSo9ywiqHbDLP3UpWUFPIORgNl81ocis3aIu61ZBtmCutgbrp+XUXAfVFgcOqv0+wzuItnWXfeAlB2KS8ZaI8k+zQX+y0EEvKbvNqoxMNWPSsixQCP3a6o8+eDZL6wO9dhPFmvRN/WOmbwmzvM5IwFiGqjSuyUw1IooOBtWUFg2AfaOA1+62Bxj97TkWzK4YVCMi1w0qMMmJ+uadQNniwIcPAtXKq+vNTLXNnpP5MsW9g2pSLpWMJ8+UWLo8sLSnsX2qMw8Mg/XTCpSptnitCpSZQTUJfIvBv3sPEtm+1w6y6QBNvAcVyEFxMq6o6oBBxTD+jwKR11MdfND9UKQnntCl9f4U9pOpxqAaJYpkyZrv8VK6LxasUpe1KgIF8xs91YwTYiKicJl9GbNz+ac+hpOFC30sd+cbwIoNyLYYVCMiV8jsxOz2jvZqs9n7afVmuyyqiifLRg6ihRxs84SPwqV78ekgTKqLNKgmJYTS8F6eZ7OWegfVBvaxe3YtNw6ypBw0Y/JnnINq0vdD9/9Kxr5qm3RQrVTWH0uXf340Apj1nwp86uBEsEw1nYG4wRO8EHyNpUQ/JzT9d7nQE1RrVMP75JeZakSUFcxU82W28fjH0z4lO2JQjYhcIdiJmZSI3dHJ+zqdjaa/Tk7k8+X17TnEElCKNKhWKpsE1XKk2dvhBmx0Cag0BTeDajKAQLJEzN5G+vmYMfkzjkMKNN2EPxkngOpJndEIqtX1ZBKKc++zSz+lh2DeIIMH/GUXsqcaJTp70xlUW+Mp4a9dSV1mlH8msKeavEZKOTwRJX9QTTK04tm+ws3MbOFCceyT6zb8cyAi1wfVOp7j2+vJ+cJdrZy9nTOnnZHCYQUULp3FlF2CaubET10WGMmwAjOopqf0OsnzMVGZamYT/mTLVJODVl3aViEKQbVWp3uXs+jfR7DSz0BBNWaqUawFauOgg2rlS3r39MkolfY8V/SgArN0K977f/bdQIfH1LRSIkpOuoQ8u5d+mg4bxwCnsnHLHQbViMgV/J2YNa2t3rge6eZ7m85U06QMzaSnNjKoRhGXfwaZgphKJMNz/ffA5p+AXGEeKDYPFlSr7nt/q/yTmWphk35nuu9kec/vNyskc+epG+3Pl63PfPKnOKO2HVxr2VBtM6hGsdTrHaBiV1VK7qQDzbddpi7HzVHZsDpjTfcfTOSggvR0laWmffZH/PeBiKKbqcbST/8VQYey8TkXg2pE5ApH/ZyYDXoE2PIz0KpRCEG1cv5vZ1CNwpXdMtVEpTJAOU+2Rzia1VFlENLb6N/V3kG1htXdm6mmJ2kmCz1YIW9uu8w9q57vaWcpPjNEXTapFfxr2jcH1nynXpe/fFJdx6AaxYpkPXw5Rg0rmrvc93adkdb+LFVuLn+LP0y0g226R6QeVJCI8s/bXwda3GN//tfC+O8DEUUHg2q+ZCicv+3shkE1InIFfWKmM8xE0YKBMycko0ZOMP2Vf5pBtfN6sdyCQnfkqJ0RUbpYovfG/eT5KgMA9ATJkMo/XZCplgzln/J3eGEfoOV9wPY9vq+P0aDLfVdtUv+Pz/cIfn8JoMoChuyHLrGX0o/sXPJBsbN+mz092HmyJn9zZp/BHh3s4Rs6azaj/FNP/0xAUG3I796fL1kHHEjC6cNEZL+G6JJy8s5OO5iNExkYVCMiVwXVKpQM3r8nUF81Z/mnDqpJyVSnJ6K2m5Tipi9WK5HSoyecSZjZmQ6uiJw57MCV2Qzfa1BBIjPVCidP+ef7w4Dxc4Bp/wJvfO87hCUa9BRQ8WR334zfUP7fJZhqTkQjipb5KwMPHdq51y7nlNfrWy5Rrz/SVy39pAoS66CxGwYVaPJ8meMn646I3I+Zar7632pvs/yTiMglQTXdcDiUKTIFgwTV9hsH4JKVsnxDVHaTUtyYf9TlBU1VVg5lLr8RVCtTXA0KEf6mSCa8p1rR5MlU09lp4ttxsc1Uk9K5h/30rgw1mMoSUIqFBWZQ7Yj/0k8p05fXGjl2kKFGWtum9mtRRvlnAnqq+WNORCYid2ak7T3gez2Dar7u6Qy0bqy2Wf5JRJRg+qRMTu4evQ54+qbMTyCDZarpxtsamwNTZqScaOgEtW2enFHowZUqjim9ruuplkSZanowgSnamWrXX6gyCgc/5j8IGgyDahRrC1bZ286TNeeET3FHJ3u7cyt7W5d/Sm/Cxj2BSx9FXDjLolvUV5cMqhG521XPANWuBdZtDVD+yemfGWQBuvlp/jOKsxMG1YjIVSeQcqL2vzuB5zLp7WMG1aTcrJDjBN3ZZP7zP9QULqJAJJtx9Wb1N3h5y0TvTfLQPdVEs7ret53tOdDS2FMt/OEt9ar4z86Nhs7nAf99CZznZxhMKAfSuq+lZCASRdv8Ffa282QtI6hmlOlfejbw1ZPAd88APS61r9eZajIdVLLf/pgJ7NgDHD4KfPQr8L1nMSXanPt8c3t1+c/S2Hw/IoqOv/5VU7d/meJ9PTPV/CvgOQ5kphoRUYLpTAdz+EBmdN8055ACMfxF4NoL1KQ6GXYgB+CvfMPAGgWmS4TrVI5+mV0qMzOWznIE1b5+Sk3me/E2o/zTk6lWOMoBolDowSfSd8nZQNytCw1mwCvamWpZVbuSulxoZBQRRYP05lm+MXD5p0wcdmaqSaD3houBbhfYpZ9mT7V5RpDuzR+A2jcAd70J3PBCbHoBOYP3V7exB4P0/yz634+Isk7OE3Z7JoRLAN7EoJp/BTzHzOypRkTkkqCaeYIeaqaav+ba5zYEvn1G3SYlTuKpT91/Ik2Js2qzuqxZIdF7klzM56yUb5tqVgT+eM0up5UTYynBSnSmmujxP7iabqqup6k6swLdQJd8zFiS6D2hVLN4jT1ROFhPNXO4USD+SrVe/tp+DDlR3uyZJBqroNr09+2ejqLfZypbjojcRSYO69LtifNURqszqMbyT/9JDgcZVCMiSr6gmn4Rd/ZTc7rtMnv7z9mR7B2lgiNH1cQ4kxw43fWGCrCs2Og/METBmYGeQIEyPclXGv9KSUXCeqoZQbVkKf80g2puG55xZl3fMj2iaE/+FO/+7J0R6a/8MxBd/hnMll2Iuh2e95vTawAtGniXgIol60J/LGbZE8WHGQyXc5PJ8317qjFTLUCm2lFkW1kOqu3f78mPJCKKc1BNDlTFuZ6D1UAa11LDD8RSxwADyj6u7gdUuhpY48lIExu2Ax+NUBmMX49V1zGoFh7zOauDZ06VS6vgm5Q0bt2duEw1PaggGejyT3OfdaDNLXTpnf4/JYr2kALz9aX3e/b2xu2+5Z+B6PJPf/TrUCyCavrk3MyQlZ6x2pK1oT3OLS8DFbsCMxZHeQeJMjFwOPDUIN+hG6nMOcjo9xn2Nss//SvgWVyNRcZvSgbV0h3LJHPnzkXNmjVx9Gg2DksSUcJ6qj1xA7D+e6Crp09JMHd6poItXmuvNFH2IROcRk5Xf2ejjAOkf1d7nwBVKQtcdX5CdjFpmWUQgfqk5coFNK7pfV0iMtXCnXDphvJPc5/9TQRNpDLF1OV2lrFRlMlAATMbUphlWJt2hh5UM48rZPCH2YdVpt/GLKi213dwUtkSwINXhx5UkxLYz0erwPU1/ZFUzPJdSj4T5gJ3vwW8+JUa4pSMpH/hiRNZ64Vo9lVj+ad/9TwDleYuB74ag2wp5KDazp07UapUKXzwwQc45QlX9+3bF7fffjsqV66MAgUKWB/58+dHrly5cOyYy5ZTiShppn+GSkqhKpUJ7b5yEC0HtseOA38tjGwfKXn9OMneXr9NXcqBlnmwVKwQ8Pv/vPveUOb0QWawTDVxRh3vz4PdNx5lCm4/KNZZaWZAwG1BtdI6qOYoqybKCjnN0OWf5gRh3fJBSvl1ILdCCEG106ra22fVsx/HPBmMRYaFLv80M9VE9fLe70VOUiZ/9bNqMqm58LNtd/JkDH36G1CsIzBpXqL3hCI16m97O9kmPMtz68qngJrXA70GRJappgP6UuGiA+THmakWsCLoye6qxUb5EPpcIrtnqp177rmYMGECrr/+eowZMwb//vsvHn30USvIdujQIevj8OHDKFy4MPLkSaLlYCJKyvLPcOTIAXQ6V23/Oi0234Pc6/sJ9rZMgZWDxTa9gQE/2dePeR2oXy0hu5fUJFAdSibYGbWN++VOXNbY5HfsIKqb6QCaGVQzf9duCqrJxK+DSXbSRe4lpZ0yfS9nDqCysXCmjw/0UBkJzJtZYIE0r+e9wGaeEOty/3UBAlxRKf907KPukxgoO04WgeRDJpM27ul9nKSnnrrdba+poTR3vJHoPaFI7drv3gWdYCSjteV9wLCp9rCBSH7uGhXsTNZ//lOXLP8M7LkewIJPgQubIVsKK6hWsmRJ/PDDD1bGWqdOnfD555+jaNGiSHN0znV+TkSU6KCaOMfTe235hth9D3Jn6adzOuFljwN//WtnKuz8VWUwUNYy1YIxM9US0U/NmSEX7kmCHKjvO4i4l3+a/aDcdmIjv0u9fywBpWiY9R/w8jf2Sa0ZVNZDTpZ5eqPWqRTa8I6iheyMNCnvz2mcEOvpoV+OAXq/i5gE1ZyBv7LFg/ciDNaign1hKV4ksO3Wfp7ByOuDZKrp9yY55pfs1lDpMnPpE6YnXE9b5Cj/ZFDNb/JCxRAGxyC7B9VGjBiRUQa6YMECVKtWDfPmqdCvLgclInJzUE33/9nGk79s5afJ6rJ2Je/r5W9t2AvAnE+Sayqk24Tao7BBNfsgNxH91DT9GqNfc0IljcKLXqaysuJd/tnUk+V33YVwFQlolPYEDFgCStFw1l3AB8PUds0KwI3t7FJtXU653DOpuY4niyQUk94B5n+qypTMLBNzuu47RuZyOPbsBy59FLjppdDKP/X3DBRU0xP0pGx19GtA764qwKh7RLmRlKzqHmrmaWEiX+spikE1ly3oBKP7LZ5WRR3bpZ8Mb9KumY12QVO1/etf6pLTPynLQbXvvvsO8+fPR4cOHfD0009bQbb+/ftj6dKl1u3bt2/Htm3bsHXrVpxkZ0oiinRQQSyDap7VYWZUZM+G1ze1A479CfTqorLSfuwPXNFKTaWk2GeqSUDt9OqJz1TTmS9SSinlv3JCnBm5rz7BiFemq1n++ecbKgD8yLVwHb6uUqxIIKlgfmDOx95BKt0XVTLVwvk7beQZliJlpf6CaiLcPAHpxVa8k+rPKdluEnQfPROYv8L/9E8zU03KI/0F6Q96rjuvEdDuLOCt+4DzG7k3eC2vidI/rfuL6vOtRlmr2b+OkjeoJsfokqn9+ncq+9+tpO9gh8fUtmRN1faUd6/ZEvpjSBBOv05I2xjJwJIej/J3zfJPynJQ7Y8//sCDDz6Iffv2WYMJ6tSpYw0peOGFF6zPmzdvjrPPPhstWrRA8eLFcfx4EoW0iSgpBxVEnKkWYHWYUpPOBpCG1pLxMOB+YOZA4LJzEr1nqUE37g2nBDSR2QtmOdkD7wIfDM/8a+TkVzPLMeNV/imr7RIAduNwhYxhBQyqURaZ0z1F5dLef2MSpFq5ERj+l8qSvDqEyd/+BMpUs75HmEGrpY4MmEnzgUseBZrcZj+WM6gmiwr6WMdftpruT2gGpNz8PHv7R3X57Th1+YMxGGif8dpJyWW3p9xaH6Pf+zbwyEDgyqfhWvpvUT+3dem1Hj4QVlAtp+qHqLOxJYDO6Z8USFh/ErfccgtKlCiBkSNHom7dutb0z5o1a2L16tUoVszzak9E5NLyT31QKqvActAqK+DxIqVc/61TK+VsOxlf+qRFZwdQYso/dcPwT0ba2U2J4HyNkR5FMgk2V5AjIvPEMD2MIGK0p3+6UUb5pwtP9im5mCe+kh1ySXP7vVuCy5IxOm6Ouq5+VaCBJ/M1XHddDkxZoDLByjqCauu3A6XCOKU57CgjH2EMQlq71X9PNTkGkNdAyfiR542eBurMVAsUVHt0IDByOjBlgDumVUvpp/bOj8DTg+3PdXYhJX/551dj1facZXCt1Z4hJnpiqX5+6KzRUDiz0eS5J8eRkiXK8k8KJOw4a/fu3a1stc2bN+PGG29Ejx49GFAjoqQIqklTbTlBlYMDOTCNZ1BNVvZ+nwF82Rfo3i5+3zc7kgP8P2cDl7dUq4m6FIVBtdh46BpV9tO5Veb37X6xajYu/zeJ4sw0m74IKNsFuO4C4L3e3lkzP0y0BxvEu7eMv+mfbpRxss+TZ8oiPXVP/qZWfQMUKmAH2KqUAVZsBKZ5BsxUykJDbOlNKD02JTDnzDiRdgG6h2Ek2XXOoTjCX+CrqKcEfq+f4Sd+g2qex5DjCB0gkObpeqp5IsxbDrz6HTDT+Jl7v2f/H8pxVjjBDHIPCR6ZGdrh9iBNlDnLvSf96onZYWWqpXuXiZsBbZZ/UpaDai+9pLpvHj16FM899xw++OADq+RTJoLq20ySxUZE5Kagml4dlqlAcgJYzbE6HEtyICze+pFBtVjasQdocQ+wchPw4YPAHZ2MTDVHRgJFx/UXqbLOWp7eJcHkyws8nOC+YHKCrrNezB5p7w/zDqrJqvwdrwcuy4wlaU2rD97jVW4aKX3CwbJ6yip94ivlkjqgplUt6wmqeabwZWXKnBwLmNOe3++tSttEnw+A1o1DPz5wBtXmGif1+vVGB9D8BtWMLC8zw0aYC39mCaxmBj0S4cWvgB+NUk+T9Cxt/YDqGafLWSk5SF/BwaOC/527kWR3y2uEkEW+x66zh55kNVPNGVRj+SdF3FNNeqTJR1pamjX1c+/evVi8eDH27NmTcZv5QUQUDv2GHeusjESfACbLal8ykNXEV74GKlwFPPaRuu7211VATUjjaPl/1gdBuqceRZecoMqUumQ6yAzldWbjdnXpk6l2LLpBhAN+Tox1wC8ZMtX084rln5RV+sTX3zRmyToxg+AVSkbv+97TGTg8GjizrnpOdn029DJv53u6c9BB8UIqsBZKppoE0+U9TTJknZlqzhJSIY3jE0X2dcJc/7dJMENKa/XPuMbFje3J11OfAne96X3dz54p6qKA5+9SAtGdn7SnviaaBNRkX6Rn4c/Pq9cR/VoSTlDNHFTgbHHA8k/KclDt2WeftT5y586Nn3/+2Zr2KVNAR48ejd27d+Puu+/OuI98EBGFSlYydVDN2dA3ZsMKEnQCyKBa9Lz7M/DEJ6p57Dd/qpOZifPs25dvBF76Wm1XLhPbybKUXEIJVOleST07xKb8U7I3ynUBanf3PRE/mkRBNZZ/UrQz1UoU9r2tqieoplUsFd3vLVm0kl0lgaDZywIHjJwyy+AJdDJftJB3UE36OsrUQnlP08ygmryHyQKGnMw3qaWu2384sQEM+dlkevYQz7RF7eU71L7W9GQvr97MRrLJQv4OZSq2eL4HcFN7ta17GepjdnmuShaYDA1ZuAquIH2LRb0qdu9ifU7xyxTgpwBZlSFnqu21jwtiWVVDKR5U0z777DPrcvny5bj55pvxzz//oHr16pg2zejMSUQUBl2eJ29SssIUS7pBeqKyKhhUiw4JQgz81ftvSIJr0q/LPMCSZs565ZxIC+WAWE4cRKH8sQmq/btarXpv2eX9d+ssMXV7BqA+4ZC+ShIUcAYIiUK1cUfgxTUp/zSVj2KmWsb3KKf6rYnvPdli4Q4qEDeG0OJBT0DW2WZjZwOj//G+jxlUk+nV3z0D/PEqcG7DxJd/6gCo9Cq95VL7+hoV7IBGDU8J7b+r03DwaC6+NiSBuStU+bEEl5+4Acgf4L3ywfftbXkPc4Mla+2gmmZmvXbrD+wxhi+Emqmm+/FOnm/36PUX+KfsLeyg2lVXXYWhQ4fijjvuwMmTJ5EnTx488MAD6Ny5c2z2kIhS3rbdaRlvXLGejKnTuKUsMBEp62ZQ7Y2halIWhe+vhWpqoz7okeDEd+PVdp3K6m9J/n/1JKgOLRK3r+Q+gbK/zJO+Q0e9S138lWZmhRlIkz6P/kpMJaDmr3TMjUE13TvSGSAkCpVkk4iWp2ceVItVVntLT8BqlaeNQCSZai3q2+WqgTjLPxetVpfm1zmHKV3TFriwGVA4f+KDanoisnMhNE8u7wCbeGZITtzwwWW4uj9r5txukifbX8p3c+b0zvA3hxF9MdreXrMFrspUO62K/9cJCZaZgwxCzVS7opV6vsrj62C7G6bukruEfagmGWqPPvoofvjhB+Rw+5EeESWFrXvi10heZ6p98htQrCMwzHMQH8/VXTl4lky5hz9Uk7LYxDd8g35TlzdfYq9EPjpQXZ53umqcbwZQZHIckRaoFFj3SzFPlp0r9dHKVNu0095et9X/9D+3l36aCxXa7hAyAYicFq8B5q9UgeSrzs88qOav71o0jxFCbRERKKj2y/MqQCYTv4OVf0pZXfcXgF+n+S4AmQEqk+7zGKugmmTpfvobsMV4jXLSGXY6404zB6tIEMYMavwxM43Zai43ab66lGEdzvcgKQVt5SfgvXar+8o/NWdG2T//hZGpltPOiu3Rwfs5F+tWNZR8woqKLVu2DF27dsW7776L+++/H/Xr1/f7ce65CZzvTERJRw8N0CnW8eipJgeE8uZ45dOIu7PuUicQ5iqfNHvtNyT++5JsJAAp09L0auFtl9kNq+VASFZX37gHaFrb/hrp66IPjoiCBavME2Rd/imZauc0CD+oJiePHw4Hvh3nf7jBJk+pm1jvGYqg6X5O9avB9XRwQAunITSRJs8T0f4s/wEzmfZpZrIXLxzbzEs5QT9yNPyWDtJj7PQaQJPawOrvAk/71plq0o/q6z+BKQvU522a2PcJlLmvs8N0tli0vfUDcNtrwHn3B76P7uemA3zyXqz7cGnyurnjV2D7MPWieex4GltguJi8Z01dqLZbN/F9r5TA9scP+36dGzLVZN/9BdWcGWWzlmb+WHpIia6EEFe09L4Pg2rkFHKnDin5fO655/Dpp5+iRYsWOPPMMwNO+WQGGxGFY8sudeRYrkR8S5USRU7ch021P+/UV5UpSsPXfrcmcs/cbeh44Nrn7M8l4CAZAdKwWvpT1aqosgPkJP8MI6jWqEZCdpdcLFC2hDw3dZBI90oqkBcY9gJQ9srwgmpzlwP3vGVnvwx9BmjTNLRMtd9nqssrjXIbt3Ke+O/kwAKK4Pmog2q6p5mTZLBJAEdnSMlUzVguvEnWarfngOEvhpepJhNEQ+mDqINqTjUrAO/0Ahat8Q7mm2Jd/jl2lj2MQP5v/AX3nJlqHz0EPHOzGqjgb39zpJ3CyVNpVnm4BB7JfaS1gS7fr13RN/NQFr79neInsgxZ27BdZXhLyaYekOGvJ6r02f1tusoIDRS0dpZ/6rJwCfZnDFNhUI0cQo5+3XTTTfj222+tgJqoUKECqlat6vejcuXKoT4sERH+Xqze2SQoEmu6tCPRRhizXXTfL3OFjHy98o335zKVUQ6K+lyjykxG/c9elTQz1RrXjO9+kvsFOpjWfdTMTDU5AZTXjWsvCK+nmh7AorNxvxjjfbvZ3NnZU235BvsEPdnsYvknRdBgfOUm9Vy7PEixiw4m6WmdsVDKyGz59a/M2zPo4Hvz09RlsP036WwaZ8ZdpdLA/VepIFWg16lYl3+amT4rN9rbf8wALuwDLFvv21NNgi3+Amr6toJ51Qsney66l247YPbz0++DelG6mJ9gtjlYJxEGjQSqXIOM8wgzqO18DkmmZMcngC8d78fBBhWIXLmAjufYnzNTjSIOqv3888+4/vrrMWaM+iucNWsWxo8fjylTpmDJkiU4dMgFYWoiSjoHjuTC+LlpftOrY7kKHU/+BiLICYQ/MnWJfMlB/LwV9uTGq9sAd1+hPm93FvDLC0DtSvb9q3umjgkpxSEynVUvhPJPx6ACXQYTaqaalCn7y+zw97lZ/imZIbqcxvw7ThYs/6Rw6VJoydIq5OjRZYr1dHB//RZnLAl+f/2acePFwNZfgIe6hfZ9mtUF5n8KrP8+cFAvkIygWhaOF+56A7jiSf/HJ+Zr3MLV9s956WPA+DnAHa/bAT1nT7VACniCauy56F46gCZBKR2Y0oM0dNaav8nZZi/SRLj9df8B4WDMQQuhZKo5z1EYVKOIyz8vu+wyNG3aFN27d8fChQuxbds2q8eaBNOWLl2KLVu2WCWh0mvt6quvRlqsR/gRUUqYtbocTqSnWaV8dUN8M0y28k/zAHXBYKCR0XPESQ5gnD2KSKXsi4uaAWPfyPz+8hb060tqKAQnf5LTfVcCH49QB+C6D4szqOYcVKBPtuX5LBPSuvUHPuwDXHme/+/hzMhwngCbWSZm+adktcn3lr/hQJkfblOhlB0YYfknhUs/V/xlwfgLJsXTi18BbZsGzhrTrxMSbAg3E76RJ4taMvT044TSQUeXtEW6CCfBk49GqG15/atRHhg7G2jbRAU1zdcu/fqop0Ja2/PtPqWhBjqZqZY8mWrS8kAzg2rC3/Mg0UE1U6hBtePp4WWq6X6PstBVMF98AvyUXMJqfiYln8OHD8fnn3+Oli1b4pdffsHo0aOxZs0a7Nq1C88++ywGDBiAq666KnZ7TEQp5e8VKhXD37SvWJCsE3lDjCezMW/dymqSUKBgnzObJdVNXQBU6waMNMphgwXVOoUxB0fu+/gNgU+GKPuS7MUlnwPjPT3PnKVczkEF5iQ+GTpw88uqvLNLkEEn+mREH3w7S7XME+KNO1Tp94KVwHNf2GVgZj8bN5v8jr3NTDUKlw60ZDZ84LQ4LLwJmdgp5Zzy3iGZWXradLD396z0CQt36EJG1myETf/NcnPZ/8c+Bi7vC9z1pm8gRQfV5noyxTX5vYQT6DSDajMWq2w3BuDdGVTTpZ/OkutAkjGoJtUPgXqrBspUk9/LwsHA7I95XElZCKpNmjQJJ06cQOHChfHTTz+hd+/e2LPHnjddoEABXHzxxZg4cSLuvvvuUB+WiLIx6VUyZ41KxegSINsjFpyryRPnApc9DqwKUJKZVfqgW1agJaXeX/NhXULhXBVMdX0+UOPYZVhDIHv225PRLmPWGUVJvaoqwL30S/s6s3/MYcfJsj6Rlf4x2+zDn4DlTPq5XLm0/6CambkmJyVy8nrOvcAHw9R1TWohaUhj6E8etqcZEsUiU+21u1W5/w/9Yrs/MrFzxofA657TmYc/9L/gdeKE/bzOSlDt2ZsR1uKiLsELtRTdad0279evAT+pbZlC6jwO+XuxKhH9yzMV0uwrFU75pw6qSQ/Zzk8Bn4wEvhob2f5TjINqxsJzv1tUhcD3QZ5zie6pZjqtqu91j1+vLmUyvD7+l56m0ssxaKaan6nxElhLlsUucmH556lTp/DKK69g8eLFuOeee1C+fHkrqPbrr7/Gfg+JKGWNmZWGYydyoXq5U2hcK37LPtJXzRwO8M5PwKi/gdaNgUevi11QTQ6EZXVLmo//PNm+XRrsP/Wp2tbNf1PV2i3AB8OBPlervnJms3ZpiGxObdJG/6NWDuVgyd/tRFlRp7IKdE9f5Oip5iiF0eWfv/3tfT/5uz3TT482fWIqGWcyyS9Yppp48wf1PauVA566UfUNTCatTleXM/9Twxx44kGh2h1iUE0yuke/hrh54Crg1W9VVurS9d69GGWC4NX9jDLxLATVbrtMvb81DTGQHm5/x2CZav4WBcx+kJLR02uAet3TAYpVm4HFa8LrLaWDavpYR+jekeQOh/wE1cqV9G25IX3/duwFzmukFjzdlKkm1SBOL90O9O4KlC2hBlu1fwQY84+aciutZwJlqjnLP4myHFST/mi///475s+fj9deew1PPPGENQVUJn0Gur9MCyUiCuaXqeodq3Ork0hL87MkFIdMNckekwPEWPb6MINq4pLmQN9P1PbRserkUw7c9cGsrAqH0lcl2cjBe7Vr1fbomcD8ld63z1nuGzQbNgW49rnwSz+JwqFPiPVKvZSFBBpUoE8mNTNrTRs32844k6BasJ5qVcqqnmqDR6nP7+0M9LwMSUd6YkpQRF5HJQDBASEU7Uy1eJNMFelrKEE1cwFoxx7g3rdVQE2yz5vVAc6pH/n3kfd7CVCEyuzvGAmzh6O/ab16QeC6C4Fvx9mvZZIpfm5DFbjQr4MtPcH0zNQsuwfjFlUNmDFHLuqplkmLlGnvA7/PABpUAy56yF1BNX89iWUxWwJqWv2qKqhmPqdN0orBX/knUTBhnbY1btwYX331FX744QdrUEHPnj3x7bff+nx888034TwsEWVD0gtk1N8qO61zqwCNDWLEbP4tK1G67DNQ6aWUH778NbBlZ3SCak1rq9X2ZV/Z2Ry679KNLwEVrgI2R/i93OrfVUC5LvbnzoCakH5SJskevPJp7yaxRLGgs9H0FDE5SdBT8fSgAt1TTa/Ut2yotrc7gmrydZc8an+eEVQ7ZPdwkUwufSIiB/iavA7c3hFJSU5cdNaKMyuPKBo91RKhrGcRbqtxAt72QdW2QEiJ6PQPgGJx3PeMUvTj/qd3ZiZY+bqUeer/j/63qsxZTUo/5XkuC4NCAoqhDn9qf/oa9L0h3foaydZ3ZsyRO8s//ZFJ6/dfZQffEln+afZFmzIgOuXTzFSjSET05yKDCP7880+cjOSVnIhImtzOlVLHNBQvcARnnxbfoFoVI6gmJ7a6DMsseTC17KUyy6SZbzSCakL6wsiBiVa0oP1mLqviujF/qvh5ijoBCMYMtMlJuQQYTRKMJIoF6RkjJPNEDm3M3mr6xMHsr/LItWoKmL9MNXn+6oNyoQeTyGuNbixuBp3MHjDN6yX39F/9u9InZ0TJnKkmynkyXMysln9X29uhlj/GIqgmMntf9ccsPX/CkzWvteqlXr/ke8iCgPSh0nQwTMpVP3gQWGCUcmZGAhT9bjmJA78DHz2krmNQzf3ln8HohaZEZqqZg8Aah1k+bX6tv55qzFSjcEQcg61WrRratWsX6ZcTUTanm96eUX1r3EsdA62s+stUk0CbLnP4YjQweX50gmpOzhV6/ftJFRPmqsueHezrGtcEhj4LPH2Tb1BN+lvtckwRdGMWA6UGOUnUJLB22DFcxHkALiWauox8227vx3L2CZIpgpouAdUntfKaoINz4Uwucyt9MiZDaIhCpbOl3BhU02VjEiwXBxxZmCUSHFSLpATUDHr7m4DY/WLgzzdUWfyV56necjdcZC9syevi3VeoYS/hkux8ec2Tx5BA5aR54T8GJbb8U8vtgqCauQCms8ozo4/FMwuqMVONwsE/FyJKiIWeld7qpeM/U11Kt0IJqj3+EVDMUYrV+oHwv9+mHZmfMJglYOKvf5EyjhxVQTJx8yX29dI36pq2wINX231e9MmVTBwL5f+MKBrMJuNyYmEOKZByJx0Qloy24S+qCWAy8ESXf67YAFzYB/hzlh1Ua1QT+GegOhHVJyk6Q+2xj+yyGbMXk/RXS4mgGjPVKEUz1WToiCkhmWp5shZUcw5JGfQIcMEZauFq8gDgyyeBVp4eb/L693Yv4Kun/E9DjIR8n5vbq23p2UbJVf7pE1RLDx6kjSXd+1T2JVeu6JZ/MlONwsGgGhHFzI8TgY9H+L9Nl09ULeWnS26MtQjQUHifI6j2P8/wgKzSASUp7QpEsrZMKzZ693BJZhIgk4MXOTmRCYFS7ibb119oH2DrYMLCVepyxhJ1+ea9np417ydo5ylbkKwJHViTgJo+UDeDbRVKqSlol7f0zniVDJYbXgTGzwEuflhNuBVNatlTQXV5t2RfSsnT0Anqcyk1lft0ba0O4KVnUTKTYKPQvz+isHqqFXJxTzVPppo0aE90UE0CXbofa1aCapJtO/FttcA15jVg44/hDUzIirM9GbybU+Q4JzuXf+oS5JteAnK0VX2B41Xaq6fv6r6o4QSlM81UY1CNwsCgGhHFhKxW3fwKcOcbwAbHm6u8AevhAJVLxD+oJgeSf3/ge32gQQXhkhPlr8cCb/+gpghN8wTVZGpWIP4m5emvS3YTPeUdbZuqkwHJ3ln8OVCyqG9QUUpA5W9HZ6pJEO6hbkAto/8cUSzog3JZrQ/lQF33Stu00349EzpTzWzwXcNT4rl8oz1JT7RurC6/fRrYNsy7v1pS/w5Z/kkpmqk2/K/EB9XMElDdpzGSoNqXfYHWTewAgrmIkIhedZS85Z9yXP/lGPW5DNp6/ovo7Ze8H+uKD23OMjWF91CY+xxK+ScHFVAk+OdCRDEhb4L6zW6lccIpdIlfWtopFCmQmJSG+tWCB9XMFHZ94huqX/8Cur8IPPg+8P1Eu1eYWeYVKMPDLX3Vlq4D7ntbHbRk1QRPUK1NE/tndfZH00G1+wcA1z+vMnrkpMGZwUcUjyyrUA7U9cATWZE3e8r4C6rV9fRKm7sc+Hik2u7dVZVZCSlbSYWegSz/pHBJUEgHsYu5PFNNjguWrve+PVGDRTJrth6MDnoX8nPcES8MqrmP/lsKtTeZWf7pPM43h/Vk1Wk3ARW7Ams2q8+fHQI0u0MN1dDH7eEEhDPtqcbyT4oAg2pEFBPmdDtn424dVJOSqByefkXx5m9ogJR/6mCa2XPkrfvCe+zR/9jbn4xUmWtS3ljJmDoaikT2VTv3PuD9YcAD7/m/XXpIXdNPrRZm1k9NZ51Jplog0n9K+268ujyjjl3iQhTPJvt6UEGwA/XKZezXMzMgr1/vqhr90epWVpevfacCxtKoW8qa9WOkWmAy1KCavN6+9BXQbwiwc699MkPZL0tNspiLeMqk3UQHf+T4QALoOgDYrI7qC6Z7LiYsUy0L5Z+JDKrpTF8JqsW7DxdFPlTLlNsTdJJj3CVr/ZeGRsParepyzCz1/vy/b9TnEuB+84fwyz8zeqoxU42iKIp/8kREtn3BgmouKPXwtwIlfRTkDbtQATX1U9+vvOegOlTjPZMuzamXLYOUfpoHyeYB8uxlKgNGrwbGi5zc6smbo2f6v88Hw4EfJqqD8sGPBX4s6Z0nZQFSIlOrYuD7nVYl9N53RLEu/9QnF8EO1CUAIB/OXoz6BMDMVHNO9bzvytTs16IDk+ZEtmAWrASeHKS2+3+uFlr+HRL+AgQlf1DNWmRz4UmsPMflJFxeE2YusQNCsz5O7H7pvlDJGlTTGYByfCB/A6mQqZvs9N+SOQgjGHPRU/dJ1kJ9jMyYAVcJ1P06zftvXipDzPfdaGR56p5qzFSjcLjw7YuIUj1TzQ39U5yry3pFSmecmPsYzn5K/7hljvIQcW6DzL+2cAF7Ww52JaC2fAPi6q43gFJX2J/v3AfU6Q5MNAKFOuCnJ3YGM3eFupQJiMFW9PWqtYlBNUpY+aefQQXBSkBNcqAuwYFKpX0z1fRj9rgUKR+YDMU8z+uDfm2Q119dLk/ZgxuOB4KRv00dAJr5n28WaqJkNsEwEMkqCnfKYyxI0EX/n7MENEkz1YwFXz1kKtqZajozVP/N6Gmxj13n/fcbysK1xp5qFAv8cyGihJV/Fi+U2Jx/M4il+6LooJq+lNXzfHl9D0oz6x/mdE6D0MtMzJ5vi9YgbmRF8CM/01olsHfd88CJE/bPL72hxLptoZ00S1AtGH+r1OEcJBFFKyAkk3clC9O8LpBA5ZsVS3mv4teoYG9LBluxFM3KCLf8U5e4S+aeDJBxlt5T6tPHA24NqpnvzTpTTU+rTqRIBxWYWaSJzFQTpYra2fGUePpvSf9thVr+6S+odjJKh/dmawXJHBs7S21fdyHw8/Mqk1Tah7x8e/QC0sxUo0gwqEZEMS//XO1pLurTUy3BB9F9b7AnTErwzHwDv7CP95vvpWf7XzlzGj9HXdYxMlNkol8oDfe/6KsO3j9+GGjgCaq9+b1qyqoDWvEKhIoVXwNv32evJI+a4bl+o31fyVQL1g9Fr0BLD6lg/JX9VDQyfYhiTa969/0EGDEttEy1QEE1s/TTuaJvZrClmnAGFSxarXpOiub11Ie/1yFKbbodhJvL/8qWcF+mWqQ91fRzUzLw4jnt0x89OXUHg2pJmakmLQx0lrFzgEe0JkDrVixC2pLofaxZAWh3FrD3N2D+p0CTTBZuIyn/TMUWDRQ7DKoRUUyYJ0YbtnsHhexMNSTUY9cDP/QDvu9nBNUOqEwsfaCqM8VGvuydXXH6rcDwqd6Pt3Ij8PMUtd3BCMJJjyCZ7pcZyeba9BNwe0eg3ZnqOmny/9zn/jPIom27MelTVuhqVgQe6Ao83E1dN+g3dWkOJ5Dfk/l1wfrlhOPDB8O7P1FW+Zv0GWz6Z6Dyz0An3U92V0EnHahORTqz75//gIc/ALbtDnzfoRPs171uF9iZw8xUy17kfdPtwWadqaYX1NwUVAt3+qdeOJTnaqJ72GVkqnl6uJJLeqqFmKkmAbVAPX91C4VgJEj2/BdAi7uB2Uszz1TTi7RyfKqzoiPB8k+KBf65EFFMmM27ZdVHAmvaNk8QpnjhxJZ/ygFB1zaqn5eZqWaOAteZF3Lwqd+I2z+imrJ2fsq+n4z6rnWD+rnlIKPfLcDlLYEXeoZ34KpX/eQk08zuWhaH3mpmcGzBYHu7Zwd1+dvfwMbtwBxP6acWrK9aJP1yJJ3/LqOvG1E8+OsBUyBKmWrihduA/b/bpd2pSP8+Nu8E3vgeePhDu5G0LFjc+QYwaZ73lOT7u6jXTB1U28+gWraywFM2Fko2d6LonmqaK8o/w+ip9scMoElPFejWZXq1KyHhSjKoltSZas4SUFMo2co9/gc8MxiYsUS9V2QWVJP3FX08mZWpu87yT2dLFz2FmuWfFA4G1YgoJpwnRv+tAz4YBlz5FPDuz8GzPBLB7KkmAwK0X18KnrWie1BMX+y9qi2PN/xF4MkbI9sfOWAwfz/RSqUPRpdgNKujSla1elVViawceHw+2h5SoAXrqxZOUO1cTw+1m9qFv+9EWbXZT7PsaJV/alk5EUgG5zdWr5mdzlWffzkGuOJJYMoCoPuLwMcjgHaPeLcFOKOOd38nZqplLzIBVkhfJLcy+526LVMtlKDaq9+pASAS6NYTyZufhoRj+WdyZ6oJs3eoHDuGesx64BAw3LPgoo+9/bUS8Qqq7YpO/0UzU0324/QeaiDX4FFqH5ipRpHgnwsRxYSzL84jA4F73waGTbUb999ySZCO/3EWKFPNbJbvL2tFjxE3z5V16WZWvWWUia3chJjb7jmwLV3M97bbLrNLQHWavu4bpzPVHvoAaHCLXfIlAcdwgmrDXwCGPAa8ckdWfxKi8PXuqv72zVX6TDPVSocXVEt1EjSUgNoVLb2vl5LxkdPV9rHj3tnM+rW3sCeoxp5q2Yc0zV/uKf9sVAPJk6nmggXBjGybIOWf/YYAHR9X5djan7PV5Vl1kXC6/FMGQEjWkrS6oOTKVNPtXPTCqLRUCaX8c7xjorwMv3r60+A91bZEKaimg4aygP7TZGDxGjWQq+erQJ/37XMAZqpROBhUI6KYl3+awafOrYC/PwCmDMi8X1E8mT3VzKCa2S/C3/7u2u+7mvb63dHZJ+k1NP6tOAbV9gQOql3dRh1oSXaJ/KyyOnlJc+9MNRmqIAcn/T8H/loIFLlM9cwI9SCoVDHglkuz1iuDKFIXnwlsGybB/tAz1QL1gcquQTVNSupNutekLp2TQIDOitCvvcxUy36kZ6lkQMvkVz0MwO2ZatKewA3Te/XzxRwK5RwEIu/F0rbBfE7phvJneQaDuCFTbdJ8YMjvaiiTtJigxAbVdGlxuM6obR8nZ1b++btn8JXp2/GhlX9mdaiJGTS8523v297+0d53ZqpROPjnQkQxLf/UfXK0K88Dzq7vvqk6ZqaaLv+UrAuzH5p+Qxe6PFKvouk3/hvbRfeAW08BlZ50wVako0H3NdEHuiY5UDJ7vLWoD9SqaE8D3WOsVkrPJOk7pzNSorGySBQvuo9iKH+3+fKGVxaaXYNqM4zyeFn9NwMB+j0io6caM9WyXemnm/upOYPkzlLQRNHv04H6kf0wUV02rO5b6imLBfrYIpH0Pshrrg4SLlmb0F3K1vRCRziZaqYbLjYmQAdZHJESSx1Uu6atff2qTeojUCac2VMtK8yfT7JlWzcG9owEBj3i3aKBmWoUDgbViCjLfpkC3PSS6k2g6ROj02u4r2wis55qOlPNOdXoombqslcXoEZ572Cavgx3ymVmJGtMDjblIET3IIp1dmGgAxYzqHbflXa5jhwc/T7TO/vAXKWUA5PMMn6I3MLMSI3k+Syr22afmeyovCPwYPZ9kpMt/VojJ2B6gUWfVDOoln18P9HOynazCqXs7XCnbca6dFL6kU37Vw0h0NNJzWCbVAfM+NA7iCYZRaFMJI+1Vo2ApV+qqecXnmH336UEZ6pF8P7V/ix1zKzfP4OVf0rgdO1W9X0u9/Tf1MbN8V8NIvSxeVaDavK3L4Fy2d9X7wLGvanOAXpeBtxwkX0/ty3+k7sxqEZEWdbladWQ+sux9nX6pKlJLe/7VnVpWVSRAkamWoB+CtLva+ZA4J1e3kE4M2Mt2kE1WTWrWSE+JaD6Z5HyFn/MAy05gJKm5BJolAOdYL1Q5PZUb9BOqcPsoxbJwbt5Ap5dyWJAoN+dBNwzFiGM++hMNTmpMhdoKDVNng+M+Ue9z97ZCa5mvn+5Jeirg2o79wIt71NDCF77znc/nZmgbhlSoElvVjnmqFdFfc6gWuLoaohIMtUaVPc9lg5k1N/qsnUT336Fuuef5i8TM6vln+KfgcDaocAj13oHz842nhss/6Rw8M+FiLJki1ESaU7u0eWf5zYIrQeRG3uqOYNqcgIofUjkANssFzUvzZPEaKlZMb5BtUCBQfNASw6C5fegV/WCHQi7pVyGKBRmT79QgmpvGwNFhC6Lzs7kJGXs60ANz4KASfo7vfOT9wmYc/ubccgWpNXAWz+o/lfZiRwrPDnIHoLj7+/Eba46X10+dA1cl6mmzVhib+s+ajoD1AyquaGfmhODaol14gSQfjL8TDXJ9JK/p6du9A54ycJ6utGf2PSLZ2BZp3N8W8SMm6325efJauiV7stratMEWSY9fJ1tCoT5WsTyTwoHg2pElCXTHb1y5M1QJvhIOYJwrkI5SyrdQgfDpNdPRvlnkDdUHXiSDC3p8xCr8k8Rr0w15zQ+p5dvV71vvnrSvu7ylkDVsipwJtMTZ3+s0v/lcznYkmlQkwfEdr+JEpmp9kBXYPcI+3MG1ZQz69nDTJy+GO37WmOe6EsPyexAAmoyba7hrchWZi0Fpi5UCzX6ZNzthjwOjPqfymxxU1BNeppq5pAlZ19bM3jhrCBwAwbVEsss0Q8nU02eD1LBoYNpZhaZnv5ukvL/6YvU9hWtfINqkpn2v2+Bq54Bzrvf972gRBG7FUss6NYuIgcrLCgMLj29JaJkscwzSUo3/Bz0G/DCl94ZTfKmK2UJ33tGbbuRv0EFwVapzJPtl76KU1DNOHhORPmnlO7O+9T3AGfNUJV5oEtk5n6ieqhJs3a3nIAQhSp/BOWf5nCSWB7wJ3OAUoa7OJuQmydUkiH4zM1qoUJPIk51MpkxO1q+wR54U9Gl2etO8rd66dlwjYzyTyOTx5yc6cxUMwcdVXdhG466nqDaxh0qS6mLJzOQ4sPsFRhJTzVz4Vz+5uTvT/qhlfT8nZqBNjlelNJKqVxZ75ker48nJTNt8O++5xdal/NiuzhfuxJwRh21j4GOhYn8YVCNiLJk1WbvXjnLN/oeiL7QE7i9o3qzcqtQyj9N+kBVZ7fFqqeaiHdPtUh+BrPnjPRIIUpWJz0lMOGWc096B5izDLi6TUx2K+kDlPL67wyqObNSSvspaUtl2eXndNJT/Nh/MHJSBSDTyc3Xq9Vb/PRU8xyrbNuT+dTiRJIMpzLFVcmfZCltH6ZK9Ci+mWoS7MrqEAsJjklQzZzc6a/Xnxw3mgsr0s9MBl85J4Carr0AMW9dMOsj9byS5xdRqPjnQkRZYr75SVDNLEXQfXJkEp6bA2qBMtWCrYZt2WVvy0GrnlCkV49j0VNNApiBelSIY8eBy/sCfT+JTfknUXZwzPP8D7cMRgZ39L6aQzkCZaqZZTVazw7en+vXz+ySqSZN5rNzUM05JZZCJ9mxMjzp7ivUNG5dLaCHfOjghV4ATIbn1Jv32Nv9gww/ohhO/oxgSIFTCU/mtr9+aAecZcnGAnXzer4Db5xaN0bMyXs4J39SuBhUI6KoZaq9/LWa5mVy9ktwK52RIllqknmWWaaaHv+usw10xoEz1T0aKpdW+yJBMymNCDZNbcQ09f+wwUipD4WkuuufmynvRAoDZFkj/RX9NYBudxawcDDQt7v/k6jt2STYZJbumYN+Ut1mz6KUv0bhFLqb2gMfPAi8+wBQMJ/3gp8zeHFNW/cOKdBuuBgY96bafu8XoNV96riHYk8HYc2FkEjpvmq7/fRU073+dLBXsuIev15Vs1zV2vu+fW9Qw7BubAf06AAMeyHrWXREscKgGhFFTIYSrDXKDTIrk3Qz2U+d6q2zB4IF1WQUuKwQ65KLw0djl6kmBxHVymVeArp2q71d7Vqg/s3A12ODP7bst/SEk/vpMhJmqlF2dk0bVcJ8T+dE70lqlX+afZzk9bZhDZXFnNlEw1Qj2cY6g8NsLN/hMe/7yevx9xOArUZWdKSkPcGClXBfphqDalGjp2zr4wCzzE70uwX4si8w6hW42gVnAB3PUdt//Qt8OSbRe5S9+hxGYxJvoEw1eU17+EPfBfeX7wA+fhioU0mVn2pSHv7VU8AXfYFPH1WDDYjcikE1IorY+u32CO5AkqUngWSkSKmqeUIXrPxT7n+Vp5Hu4jXqUk4QYxVEDGVYgT4oEvL/Iv2LXvwq+OO++zPw5CDgxpfsFUbzRJgouylUAPjvC+D93onekxQr/zRO1soE6JWkM9XkNVgWbVLR5U8CJS8HVhiv1+KPmd4noV+NBbr1B1rel/Xvee1zQOOe7gms6YxrBtWiR/pYiYseUiWgukeWPiaR9/Xu7ZKjT9lnj9vZTpJ9T7Gn+1ue5hkYkRW6YkMHz83BLNJ31Fn2qUnpqdkqJhaL1ESxkiSnu0TkRoGaiUq69su3A18/haSiD+J0Q99gmWpCmuqaZRZyABCrcjHdVy1YppoeEvH2fcDUd9X2pgDlop/+BrR5APjsD/W5Xh2UoRIseaPsjs+B6DAD9JJt++a9QINqKmsmULaNBAEko2GZI+iUKkZ5Jn5+PNL3tumL7O1fptiv+Vnth6UXXGYuQcLJFEq9OCSZKRQdOlihM7ySrVrAGZR59361fchTBUCxtcQTVKsXhaDa6dXV5Wzjb9J5PCrTnv0xv7+/nmpEbsWgGhFFpZ+aqWlt4PEbgOsvQlLRb+B6dS2zoJq8+esMMjPlPRZ06dS6bZmfOMlKX8Pq9uAFaV5s+mshcMcbwKT59jS+OZ8Aiz9nyRsRRT9TTYJl0l/twauBfz8DypYInNl8eg217ZasqmgyB834C9yawRC9WCPGzc7a99V9jNwQqJT3HMmklkUsTv+MnmeNQPX4ufagFWeJdbLQ+82eavHNVItGUK1FfXU58z/vXpHm4B89EMyp1enqskktoH7VrO8LUbYJqn355Zdo2LAhChUqhFatWmHWrFkZt61evRodO3ZEiRIlrNvGjPEtrH/zzTdRv359VKhQAXfeeSeOHuWSBlG8M9WcJRzyZpiMSjtS1oOVf+rbv+9nf751d+z2TafT+xtRLiSzQ09elaCaDBvQTcLNFHzp0dOql90/Tfe2kBPZ03gAQ0RRJO8FksHbxVMqH4pGnqDa/BQMquksaGc/NX9BtYWr7O0ZWcww0/21lq5Hwi1crS7lPYcZodHzcDc7IKGzHHUv1mSU1xNU02WsFDtyPJhR/hmF48BGNdX/n5Sz6+NSZ9ahfk1y6tUFGPs68Nd7nMBJySWhQbXhw4fjkUcewQcffID169ejR48euPTSS7Ft2zbs378fl1xyCZo1a4aFCxeiV69e6NatG+bNm5fx9R9//DEGDhyIQYMGYeLEidi0aZP1GEQU30y1KmW8r29cE0lJ92/Qk8kyy1QTZ9QBPnlYZVj07BC7fQs2olxMnKeGDkgZpxxIy8lKeU82yKaddkDuhhd8v1ZWA3lyQ0TRVq4ksPWX8PrTNfYsyiwwgkqpYuN2e9vfAAIpz9x3EPj1L+9FmqwE1aQ3nR6ks8wFQTVdgto0SRff3Fxqfd+V3lnr1csjaeXxLGoei2JvRSk77vQEcNUzqduzMRIbtquKBjnmjcagAskylGNjMWOx98AUf5m4zq+96EzvydFEySChg2lfffVV9O/fH+efr5YwJSD2zTff4LfffkNaWhrKlSuHfv36WdsSUJs/fz7eeecdDBkyBCdPnsTLL7+MwYMH49xzz7W+Xq6vWbOmleFWvbqn9omIYp6ppqdOiW+fTo5GuKGUf+YOcZXsto5qKlEsm6rqfm+7/GSqSXr9hX3UtgTHdIadlNZIP57124CerwKDR9lf07sr8PaPapsZakQUK+EOq8nIVFuBlKMb9JuLHaYjx9QQA2cWmznZOdLSTyHvBxJMkInSiaKz8c5tmLh9SFXOKoGkDqrpTLVj0Xk8WZBscIud+TZnOdD8tOg8drLTWWq1KmZeoRGqs09TPSJlQUAGZIg9IQTViJJVQjPVli1bhhYtWnhdlzdvXuzduxcjRoxA586drYCa1qVLF/z666/W9qJFi7Bnzx60bt064/ZSpUrhvPPOw8iRfrq/ElHMMtVuuURdFisEXHshkla4PdWcXxvLbC+dqeav/NMcGHF1G98Je5KdZgbUZGT9hWfYn+sVRSKiRNM91SQAtdMziTlVyAKHvwDbNW2B8xurbTOgJkMdgpVKhcL8WuljlJUAXTQCG7qs99wGiduPVCWtHwrm8+3Fmszln+FkqknvQRnANHqm721/zvYuJV0doCdwdhTN0k8zqCbe+wV45EPgmz+9FxJ6xLCygyjbZapt377dO/10wwZMmjQJzz77LL799lufbLMaNWpg165dOHz4sHXfqlWrIodjCVTus3GjUcDtID3XzL5r+/apWqrjx49bH5nR9wnlvkSpTA6Od+1TRz3nNzqOJZ8DJYvIcyP0x3Db86l4oTSvl8WcOU7i+HE/jW8SQI0fz41d+0/h6NETXtkfUxeq/a5U+hQ+feRExv9B4QI5/a6d5Ml5EiWLnMz4WTudI69/8fpJKFbc9nwiikT+PBIMyIXVW9IwZ9kJtGlidLpO8ufT8g3yeqxWazbtkJ8rDY1rnsJXfU/g1e9yYPJ875WccxqkY9GanFZg7OjR42Fn/dktA+xu9YvXnECVMon5nQ6bmoaTJ3OhQbVTKFvcfq+i6GlUIyemL1Z/KLUqyu84Mf/XWX0u5bBWKXPh2PFTOB6oq73DJyNzYtL8HNYQpg3fH8+Y0C7Pn97vyvGOvfK5fEM6jh83mstmY4tWq9el2hWj9zs5o7b9uvP6UO/b2p91Eg90ke8VlW+VLfF4Lz7C+f0mNKhmkp5qnTp1wuWXX47mzZtbwTMZXmAqXFilauzevdvv7fo+zmCdSUpGpeTUSYYgFChQIOT9HTt2bMj3JUpFk5ZUAtAMlUrsx5SJ47P0WG55Pq1fLc3hzsn4fOeOrRg1ys+SZwIcPSEHPZ1w8mQafho+BgXz2geZ34+X7sQ10LTSKowZbXe63rVNUgFUPUjHpitRtdQ+fDGlAS6o+Te2rNiNtvWbolzRg5g/cxnmJ+Snolhwy/OJKFJlCjbHapTH0JFLcGjTqpR5Pk2be7Y0TLC2DxxWJ/jHDu/CqFFTkbZP+iic53X/AsfllVmlFQ8YMh11ygefhiOtAHYfzIcSheyRz0s3S2TBnhQxfMwSnNyRmN/pJyPOksYEaFB2GUaN+i8h+5DqCuWQlEeV4rhj7TiM2uUY/50kz6XV24sAaIt9+49i1KjRIX3N0lVy/Kaa/PYdsAxdzlI15BMWV8LmXc2s7fPqbsCUpZUweeZ6nF6URz5i0mx53SmBo7vnYdSo6IwIVlM/r/B7W53i8zB2jAsaPKYAHu/F1qFDh5IrqDZ37lxcdtll1qTP9957z7pOJn4eOGAUX8tKw35V91S8eHG/t+v7yG2BPPHEE+jTp49XplrlypXRrl07FCkiL+CZRyzlD/jiiy9G7txJOqeaKAq+nadW1LtfUgAdOkSWx+2259Npm4Hnh9mfV6xQNuKfLRbyfXQKR46loWil9mh3lr363H+keinv1qEqOrSx56HP3pkDv85R221aVMP9XU7irUeRETjseJm+JztGpwK3PZ+IIvXP9hyYsRJIz98AHTrUS5nn02M/+h52Vypf3HqfaXsUePIH79t6XnM6BngG3z/63flY8+1xq1dmIP0/z4EXv8qJQQ+fwE3t1XtE7llpwHf2fXIXqZ+Q36k0Qr/uA/XzP3RLDTSt5anzpaiavDEHxnrW1m665gJXDCGK5LlklSR+BaTlzBvycdib4+xMzykr6qPjhXVx55s5UchTEtvnmnTUr1oOU14DTuapgg4dKiK7+3FSGpZuVs/L6zs3QtNajaL22O+fSse97/j2UWnVohE6nOcZVUsR4fFefOiKxqQIqskfRNeuXfHMM89YwS7dQ61SpUrWwAHTqlWrrIBZ/vz5rdvXrl1rDSwwS0DlPu3btw/4/aRnm3w4yR9kOH+U4d6fKNVs8Uwua1IrJ3KH2tHf5c+nWpXUBC09KS1P7hzInTuhrSe9lCgiJUNAx7658MvzQOfzgMnzgbnL1e3nNcoF89eohxuIMsWz/v9EycEtzyeiSOkJoEvX58CgUTlw8DDwULfETCmO1vNJfgbdh9RUqIB6n5FvceulwJDf7dvqV/P+vu8Ny43X7g78PV78Sl3e/XYu9Oyotg87Gr2v2JSY94KJM9R7a9WywFn1crsi2JOK+lwNfDlG9VfNo7v9u0Q4z6WCVssL6YOWFvLXmEM5pHz8mv65vHrRXtwsZ8ZUydVb3HV8lwgytOShD9W2TK+P9vPy1g7Ave/YQzO6tgZGTpe+vt7HqhQ5Hu/FVji/24S+mkydOtUq9/zoo4/w0EMPeQ0lkKy14cOH45TKH7UMGzbMKhEVDRo0QNGiRTF58uSM23fu3IkpU6ZYX0tE0bVnP1CnO9DlaeDbcbB6VohYTryMN4nP17MTvaI2BSlazCmrL30NLF0HtH5AfS7ZC5VV1UOGogV9Bx0QEbldFc9r2dL1wL1vA48MBP5aqCaCGoeFPv6YAdz3NvDYR0C6O9phZhgzSw0KkJPLy1v6HyQw+DHg1ERg6LPAuDd9p3QO/FX3SAvObEGlAwr6/WBZdKq7wvbLFHXZuVVigqPZRcXSwLZhwPu9kdQiGVSgn0tn1vW9TQZPtTodqOGZiLpuq/teI+Jt2x41mEuOfeXvJdrPS1mkNo+nX70LWPw5UMozRIsolSQ0qNa7d2/cdNNNVqbaiRMnMj4k+0yu27RpE/r162cNHhg6dCjef/9962usHc+RA3379sUdd9yB6dOnW5NEb7nlFiug5hxwQERZ98tUaeyqDoyvf96+XoYTpJL6xvSjcKZ/xkOFkvb2P/8BA362P5cTJucBkRlUS7X/JyJKXZVKq0sJIOkg2nn3A01uA379y//XnDwJdH0WeH8Y8Oq3wDhP6btbfOdpPXpFS+DrJ+3rG/o5ZJVpoDKl2enAYTVNL5LvqydDSzBh+x7EPSNmxHS1faV32zgiv3SSnQSI5bkdyJh/gOmL1PY+T1Dt4W6+9zurnmSFqgVIeWyZsrshcAvumJOM1NNuAlYkKMhtBtxl0TVvnth+LxZKUKpLWFBN+qHNnj0bH3/8cUbqov547rnnrIEDo0ePtu7TsGFDDBgwwAqsNWnSJOMxJKB21113oWfPnmjTpg0qVKiAwYMHJ+pHIkppP9tJoV5SKVPNOVLcbUE1PclK+8Do/3btBb7398pUY1CNiJKEZOXmDHCE+uHwwD27Dho92ZeshWtIEEtnat3UXp3cb/4JeK4H8ODV4T3WiGmh3U+CEUePAePnqs8fv17aNajt0XGevzNtkQqQyuJOy4bx/d6UnPIYWZqBhn9KllX7R4Bz7wX2HgD2HVTXn30acKEjKN22qbqUrCwpQRb+yrHjpcf/VN+4B99P3D7s2h+/40O3VX4QRVvC/sRlcqdZ2umPZJyNHDky6H2kD5s5eICIok9S6qV0xZ+SKRZUq6+GZrnyICDQSaY+YQrUk0Sw/JOIkkXOnED5kv4zSeR6f8yAmi4ddYsvRqvAQLM6QNPa6rpyJYGnbwr/sYJl10jAQGf13PIKMHWhyvQrmA+oUQFodxYwb4W6vns7xM3KTXa2kLOklShY+acuAR06AVixEeh/q52Vv9AYYvv9ROCIp39gkYLAN08DE+YC1z6nrmtR376vlIBK5cXqzXawLVEk+zRRdCl5PI4P3XY8TRRt2btDIxGFZOI84NhxoKwjU8rZMyEVnGb0VMvhsr4v5jrExWfa21Lq4K8fsbn75tACIiK3qxhkymUoJ6duCarJ6/YnnvXh2ztmfVFl6+7AmTtmdvI3f6pST/36L4GI6uW8Bw3Fy8696pJtCChU5jHN9xOAm18Gnv8CmL3U/3P84xH2duECKrO/2wVAx3OAM+rIkAL7dulrKCSolmiJrIjQmWqxPD7s1lZd9r0hdt+DyA0YVCOiTOkD85anq4OTVFbTmLAuJy9uIn12hGQcDOyTeQmuNOuVzADppSOZH0REyaJYIXv7ns72dqB+YD5BtXVwBRmwICf/MnXwugvD//pBj6jfxYS3VbaHBOmk7C2cbBB9/7Il4vveJllzUpanT95TLbOdYkeyLnXASaaZatMX29tSPqnN8gTb8uXxfh6MeBmY/TGQL69vUC2R5Z+hVCCkQqbaV08Cy79S0+qJUhmDakSUqU077Ub5V7ayrz+/MVKOeTAm5QFuItlpU98FZnzgPelTGu76I2U2MwcC3/eL2y4SEUWFWb4uGcTDX7Qn1gUr/9RZFxt3AAeMyZqJ8vlodXlNG1WWFq6elwG7RgDnNbKH1cjP5s9BT2BRXvM/fwI4vYb6/LJz1KXONo9XUO2Vb4BiHYEfJqrP2YaAIslW09Pmxf0DgFF/q+0pC3y/JpTnWI0EZ6pJr0M3ZarFsqeaHIfWqhS7xydyCwbViChTmzwH8DI16aFuwAs9gfFvAaNfRUrbcwCuI9mCMo7cDP6ZvUeIiFJBofze26WLhZapJgsOOnt3mQsWRnQ/0usvivwxdA+pip6pqBu3+88KO3RUbZ/fSA1EGPem6tsm2W6BgmorN6r2DjqrbmSIgxBCoYNp0gtLsPyTIh1WYOrWX03NlJ5qEpR6srt9W+kQsiF1ptqaLUiInZ4MsXBJlqpM0o20N/LlfYFBIxPTU40o1TGoRkRhZapJD7Unb1TNXc10+lQiq/xy8P/Jw3C1AfcDrRsDd3RK9J4QEcU2qFammN0PTDfj95epJvetW9kdfdXkJFj3L6tTKXp95vxlqh0+avfdlMEEQgKRMmFUByT1BGmZlCpZfF+PBWrdAJxzL/D+L0DrB4DLn1SBtqyS72E2khecQk3hCLRgKAH0AT+r7VanA5c099/CI9h0YR2g9/daEs+g2r4wsmk7PqGerzojNRzy/JbJwbe/br9O6OB6oBYiRBQ6BtWIKKxMtexAepBtHw5cZAwDcKNeXYCJ70RWUkRElCxBNWk8XqWsytCVCX/rtwXOVJOAUt0q7uirJtkhOgtMB7ayolLpwBNAzemn0r8t0O9UDxe6/12gu6ekds4y4L53gPST6oTbX1lduGTKqDyeiZlqFOmwgr7dVfsL6RUr3vUE1S44A6hnDJgq7wmYBaOfi9I6IxEVCTpDTOw9aAeh/5gRPBNNyl7XbgVG/xP+9zSz8vTr4qpNdp9eIsoaBtWIKFN6Ncvf9M9UpcttiIgo/nS2lQ4GSUBNZ3stWhM4qOamTLVtu+0gV6BAV1Yz1SQb7NJH1ZRuIUEzafIe6H3tqvPV9pDfA//O//o36/s6c4nvdcxUo0gz1SQrX9pfXHuB932k1FlaYmjOQG6gYJ0ehKKfo4nKVJNBHuL654FLHwO+G595HzYzgB6qf1fb27/PVMHz1Vu8y2GJKHIMqhFRpvRKmjmNjYiIKF7ln6JBdTuoJieF1/YHGtyieizd85b7gmrb96pLXboa7aDaxyOARj2AP2aq34EzGOnP4MeAIY8Bt17qfb1kA37i6b02d3nW93Xmf77X6f8XolDoXnzi3Abqsv1Z3vdpWN07MNTFEzQONVst0OCTeAXVJFNOehkO/yvwgoHOeg006TgYyXyT5/Mc4znd532g0xPAPs+xfbVy4e0/EfliUI2IgpLVMV2+UpRlhkRElKCgWqMadmnhzr3A0AnA4jXA9xPs+0qwzQyqpQeYjhwPeqhCNEo//Q0quPMN3/s0qhn8MSTj75ZLVXBNpopqvbsCzeqo7cVrs95r6h9PUK3dWXawg60KKBxn1VOXUt5ZqIB3YF0r6ekHNu09YOzr3v3VgtGB7oRkqnmC7XrR+g7jeRxoMq/Zey2cfX78Y+CM21XPRdNvf9sl5boknIgiF2CuChGRd5aa8ySHiIgoVgrk9c6iMk+yZyz2ztZ4617gwffV9vRFQK2K6msku0PKI5vURkLok99oZarpflF6+IG/8s7X7gr98czscwm21aygSu7kBHz15tCavgfqGaWzjL54Qv2ftA8x2EGkvXIHMOYf4IkbvP/GpYzY7EsmypVUH6HSQzt0Nmkip3/KwkCmQTXjWHyzZ3hYKN743t6WoQ4SQBs7S01JvqApcEWr0B+LiAJjphoRBaXfyCWgljNnoveGiIiyW19LvaCjg2orN6mG3ToLrPfVwE3t1ecPXg3kymWXi01eoJqAJ4I+YY9WpprOypGeSjqD3HT3FcAZnmyzcH/Hp1dX7/F1PFl+yzdEvp+zlqpLCW6WLQF0Po/ZMBQ+GULwyp1AUUfrkeEvqCDwG/dE/th6Aqi/oR+BfDgcuPpZ4Igj6ytcu/b7XqfLWLfuyjxTbcI872y3UMmQByn9fugaldn32RPAleeF/zhE5ItBNSIKKVONpZ9ERBQvUsap6YBM8cJA1bLe5YU64PbxQ2o64F2Xq8/Prq8uH3gXKNEJ+G9t/PZdl5xmZKpFaciPvA/rQNju/d6N/89tCLzQM/zHnDxABSd0xop+r4+kGbpzSEHz0yJ/DKJAWjUCjowB+lwT+WNIwFe89JX9WpJZKxTp2/jjJOD7icgSHRCT4QvyfJZeiTrDNJRMtWXrgdNuBhasBL4YDZx7LzB/hX17oKCflHdLCfnr9wCVymTtZyAibyz/JKKg9OoYe6EQEVEimBlVdauoLDWdDaWDannzqOmAmg6+iaPHgf6fA98+E/t9XbQaOPc+4MGuRk81T4ZZVslUTynZlICaPLZcii0/q4ywSJzXSH04A5iHjmZ9SEFzT2YhUbQFmnAbKj1JWJx9t8psPZ4ODH7Ut2+bmLIg8/LLg4fVlN/Mpsfr8s/7rgR+fUk95/RjSlBNFhSsUu7vVJmrbK/f5v0Y8vxv+6AKtp1IB5rcBsz/VPVKe/pT4Jmbgfu7eH9Nk1rB94uIIsegGhEFpVfHmKlGRETxYgbITDKEQPos6aCa7rfmJA24TToAFWsSUJL3zR8m2dM6o5WpJkoUVj/L6i12Np9k8EW7l52zsXmoZJ90ppou1yVyGwnOm3+zf/2rtj8dBbx5r+/99euNWOanNFqyx2QS7y2XAAMfCi2oJpmmesG6fEkgT25V1i39DOXzRwcGfxxnX7nWD6hpouLZId6vgVLmWb9a8Mcjosix/JOIQir/ZKYaERHFS/XywJLPgW3DvK+XSYC6r1qwATo6oKXt9pxsxpru3yYn2Zt2RLenmtAlnys32j+/nIxHS/4sBtWkR5Vk2+TMATRN0IAIosxUK+c/GG2WX973NtD8LjXwZM0W+/qvxgLn3w+8/YN93QtfqozYj4yJupkF1Uoa5dvSI05PN5YAnp7wG8ilZ6vBA0KGDpzTwA6oabe/bk8E/vn5rGf3EVFgfHoRUUiZakUCZAMQERHFQr2qvgEpyVQzBQqqOTPVZBql2actVnQwSkqyFq2J7vRPoQMBX45Vl2ZfNTcE1XSWmpzIczgBuZUEsf77AqjgCb63berdB3HDNuD9Yarf2rfjvINqkk0m5aAycViyypw9z657LvD3ldegXX6CanqQgJi9DFi/PfOM1T9eBV68DfihHzDsBe/bJah98qT3UAYiih0G1YgopClF0seFiIgokUINqjmzq+VENtBkvWjy14ssmplqBfOpyznLYtMnKSOodiyyr5+zXF2y9JPcTsqy//4A+Os94NFr7V5lkm1a+RrvgLwOqo17UwXjpDxTbPG8pphZYt+NV9lt/sj1EnA3p/k6g2qSqeacSir7eGM7+3MZOFAwP9C3uxrKIj/Ly7er7NBvnwZuvdS+L4NqRLHHnmpEFNRiz0p7baOpKxERUSJYJ5P57OmUgYJq0tx78GPAnW8Ax0+o6yRzrJznZDjW5Z+xCqqZAxgkS6V3V0RV/jyBfw6nNZtVVs26bSpQ8Mi1dtlajfLR3S+iWJDSSfmY7emZtn2vmqppGvATcPKUXTZaowJQoaQaLiCTPCX7bPFa34ElLRoELv3Ml8c3kzMjU20pcIEnc04sHAw0rKEm/LZpAnzzJ/D49b6P/fgN6kOXzw/6TW0zqEYUewyqEVFQC1ery9M9vR6IiIgSRYJl0mRcZ2oFCqoJyda4pg1wwwvA8L9UUO3CZrHdP2eGl2R5R7MMUoJoElSUn61mRURdqOWf0/4FLnvcO0PnpnbAtj3RH85AFGv671Uy1eat8L5NeqWJ8xurYJVZdt2pL9Criz3pV1uwKkBQba//0k8hgwTy5la9jL8Zp6579mYVUNN6dFAfmZHya01nxhFR7LD8k4gCOnoMWLpObTf0M2KciIgokSWghYME1YSUSDXwvH/pHmex5MzwinaWSNVywAu3xSagJgrky7z8c+5y4KKHfBujL11v96SKZh85olgr7SnFlKzWSfN9b5dy5pEvq6C+Myj27s/q0hwY4gzMCclokyBcoJYq0udNl3NLlYh8bpZ8hsMM5EtWHRHFFoNqRBTQf+uA9JNA0YK+TZ+JiIgSHVRz9iXyp0E1uyQr3j3Voj1IINZ0+WewTDXpGSW3O6cnWkG1PdEveSWKtXx57azXP2ery2+eBhZ/DrzTC5j4NlDYGNjlL9Pswa6qn5nQmbTOfmq6B1ugAL8uARX3XZm14Ln0f7u3M3DX5ZE/BhGFhuWfRBTQwlV26adenSMiIkqkip6JfeLiZmEE1daobJFYvp85g1GBGpa7VSjln7qETco93/nJvn7SPGArM9UoiUtADxwGdnj+vhvXBE6rqj6c/AXzW50O1PEE/OevBE6cAHIZZ9r6cYUZoDPpAR+SyfbUjVn4YQBccIb6IKLYY6YaUYzt2KPKKJPNig1A/8/VNvupERGRW1x8pmr0LSegtSqFntkm5YoyBTSe5Z+x/n6xCqr5m2LqnAouve3mfmIHEr7+Ezjm6T/FTDVK1hJQIb3N6gR5bfEXLJdBArUqqoDZkWPAEk/7FH9BtZ+e8/+417QF7ukMDH02+bJcibIzBtWIYmjLTjWWW5r5JpvGt6kx4oJBNSIicgvpK7bsK2Ds66GXdulSRZ1JFSvOXmT3XomkDKr56wnlDBRKCVyT2sDSL4H3HrBvl+ullx1RMjGHa8hxr5ll5nSRnwxZCYLlyAE0ra0+1xNFnUG1ZnXUwkCg59/7vYF2Z4W//0SUOAyqEcWQTP+R1SoZOZ9MpDzGXG0/nUMKiIjIRSqXAYr6afYdSNni8Qmq6fdO6cf0+/+Ah7shKXuqycCBF75Q21t3AW/9YJd96ky1EoW9g4ejXwN6dgB+6B/vvSbKOjO7Uko/g7n0bOCPV+3PixS0tyVoJuYs9x9UKxVCH0giSi4MqhHF0KYddsmJ9FZItv3WOPmTiIiSmZ7CqRuFx4oum5Qg3iVnqwl+ycScGvj0YGDiXOCVb4A+7wOdn1LTEXd6MtWc5WmSXTPoUaBt0/juM1E0mH0AG3umcAYifRnbNwe+elI913972b7tDJ2p5lhQZ1CNKHUl2Vs9UXLZaASndh9Inh4jyzZ4f17MMeGLiIgomWRkqsU4qKYb/JvBqWTiHOJw/Qv2YIipC4EnB9nln2amGlGykz5qWsuGoX3NDRerD1OzunYJdXo6kDOn+ny7ZzIug2pEqYeZakQxtGmnva3LJpLBmi329owPE7knRERESZSp5in/LJCkQbXzGwE9OgAD7gdqVAA27wRmGb2hXvtOtbUQbKROqUT3Obv2AuAMTwlnJGTAQcF86rVg6Xr7+oyyaT5viFIOg2pEcSqj1OUSyUBPNerWFmh+WqL3hoiIKDpBNXOxK5blnwXyISlJc/ZPHwV6dQFubu9922UtvIcRyJRDolTR8nRg44/A109l7XEkM00P+Fq0xr7+4GF1WYhDPIhSDoNqRPHKVEuioFrGSnuSnhQQERGZJOtKLHe0N4hZ+aen4X8yM0vgJLvmhZ5AntyqKftnj/uWihIluwql1ATPrKpdyff1Rk8GTtbScCIKjD3ViGIoWTPVDnqCapK+TkRElOzqVlaXfy8GNmwDKpWJ/veQJv4n0lNnUUpn9wnpq9akNrD6W5Whxiw1osyDais2pmbAnYi8MVONKEZOnlS9SJKxpxqDakRElIonuUKmWMaCPmlO5p5q/oY7mM3VJZOHATWi4GpVVJfL1qfOEBMiCoxBNaIYkSk/6SeTO1MtFVbaiYiIzPez2cti2zpByiLzpkA2itlQPSfPGIhC1rSWupz5H7BjT2r0WySiwPgWSRQjzmbIeupPMtAnBsxUIyKiVDHshdi+tx0yMlFSod+Y2VsqGn2miLKLelXVBFEpCR/+l7qO5Z9EqYtvkURx6KcW6/LP36YD7R4G1m+LzuOx/JOIiFJNmyb2e5xZqhkt+jFTofTTKUcKBAmJ4umc+t591Vj+SZS6GFQjilOmWizLPzs+AYydBdw/IMpBNY79JiKiFCFTK2V6pW7REMtMtVTR/ix1ef9Vid4TouRSpay61AveDKoRpS4G1YhinKlWvXz8eqotWhPd8s9UXG0nIqLsSUoyyxRT29t2R//xU/G9U0pmF30GXNI80XtClFwqeyYMr9+eukF3IlIYVCOKkY2eoNrpNYIH1WRK6Nzlqu+CPig/EkZZSnq6vX3sOKKC5Z9ERJSKSnuCasOmAhs9J7tRL/9MoffOfHmB+tVSo0ccUTxVLu0/Uy2Vgu5EpDCoRhSjLLVPRqrt06vbPdVOnfK978cjgDNuB/q8rw7w692kPnSQTcj2DS8AgzyP6S94J/Yfjs7+s/yTiIhSURVP9siLXwH1bwEORul9UzAThYic5Z8btqvj+CPH1Od8fSBKPQyqEcXAIwPtbZ2pdvS4/8bI/T5Tl+/9AnR9Vq1ord0KbN1l3+fLMcA3fwK3v+779QtX2du79vkOSIgEp38SEVEqev1u1R8sdy5g30Hf/qdZwUwUItIqlQaKFlQBtX/+s69nUI0o9TCoRhQD81bY2zUq2GUTB/ysiDf0ZLKJvxfb2zuMaaESZPNHMt8G/Ox93ayliFqmGk8MiIgoldSqBLzTyy4D3X8o+plqqVT+SUSRyZEDaH6a2p4w174+f56E7RIRxQiDakQxULyQvd24pp3x5S+oJtPI/DF7sJnlKWYJqWSwjflHrbi3Ol1dJ1NAQ/HFaKDvJ74lqfK53k+WfxIRUSoqUkBdSrZatKTioAIiity5DdTlz1PUZa6cQK5cCd0lIooBBtWIYkD6J4hp7wN5cgOF8gcOqu054D/I5hVUO2Jsex5DykR7DVDb/W4BnrhBbX84HJi3PPN9vPll4OWvgVF/e19/9Jjdz03S1omIiFJN4QLR7UVqln+yvIuIxDVt1eWcZeqSWaxEqYlBNaIok2meeniAnvwTTlAtY7CBEVTbe9B3+9Vv1Qp7i/rAo9cCHVoAXVsD6SdV77UTxqADJ3Nl3uzzYD6+lKzq/SYiIkrJoFqUyj8XrQbeH6a2malGREIm5+oSUMFexUSpiUE1oijbvgc4ka56KZQrEX5QrW4V355qm3f6Br0mzVeXj1xrp5K/ez9QrJDqq/bHzMwz6cT4ud4loPsO2Scc8jMQERGlmmiXf/YdZL+3XntBdB6TiJLfrZd4t4QhotTDU2aiGATVRInCdrArWE81Z1CtdFF1udMIqi3bYG9LyWdaG3vqZ8uG9m3lSgJXna+2O/UFuvVXmXPSe238HPt+ZsPUKQvU7dreA94nHERERKkm2uWfurxrYB+gVaPoPCYRJT8zyF63ciL3hIhihUE1oijbtV9dlihiXxcoU00CXmZppyhZ1Lv886dJwCZPOakwg2M1KwBlPdlwmplm/v0E4M43gJteAi7sY/dku+8d76/p/R6wZad3phr7qRERUaqKZqbarn12ltp1F2b98YgodRQrDDx2HVCqKHDflYneGyKKBQbViKJMB8NK+gmqPfWpd6mlBNkksKZTwoc8Zt9XhhPIgXrXZwN/r5+f972uTRM1XUgb9Ju9Lb1jzNLPG9sBZ9QBdu8HHhmo+rDpIF+gqaRERETJLpo91eavVJfVy/O9k4h8vXInsH04UKtSoveEiGKBQ32JokyXbUr5p6YDZTKxc+I8oG1T715pcvu8T9X2F6PtoNrnnu1A6lf1va5OZWD2x8C6rcAHw9X3mLdC3SYZb2avNpkaOmyqKlv5aixQprg9KIHln0RElOpBNZ2dnRXzPe+x7JdERESU/TBTjShKJAMtPd0u/9RlnOLwMd+ea2LtVnVZtax9ndl/beCvavvx64E7Ovl+T92zzalRTaDjucCo/wFzBwH1PMMP/vrX/v5nnwbUqAA09ATRxJvfA9s9QbeihUL+0YmIiJKKXjga8rt3D9NILFhlv/cSERFR9sJMNaIoBdTO66UCUi3q+5Z/StaYJhloPkG1cr5BtWn/2qvpfbsDR48BFzUD2jYBbn8duOGi0PdPJoKKnq8Cb9yjtksXU5dmUE08OlBdMlONiIhSVesm9vaMJUCHFlkv/2SmGhERUfbDoBpRFKzZorLAxLL1vuWfaWn29rbd9vbaLb6ZarpUVOt+sQqsycfVbdR1v7wQ3v6Z00NnL/MOqpUvCdzZCfhohPfXSCkoERFRKjq9hpqeLe/dWemrJr1IF61W2wyqERERZT8s/ySKAulJ5mRO5XzvAXt7W2bln46gmmSnZdXzPeztJWu9g2oS8Bv4EJA+Hhj0iJpQ9Mi1wL2ds/59iYiI3Kq4Z/ErK0G1peuBo8fVgpgMKiAiIqLshZlqRFEwd7m6bFBNZYXVqgh0bW3fLhM2X7tLTdj0ylTzBNWq+OmppkkmWVZJJtor36hBCf+tU9fJaG9TjhxAz8uy/r2IiIiSgZ7Uuf9w1vupSeabvI8SERFR9sKgGlEUrN+uLm9qD9zUTgWsnEMEdDmlmam2LsigAq28kfEWqZw5gbPqqaDa4aPqOt37jYiIKDsqnD9rmWqrNwMzl6jtOpWit19ERESUPBhUI4qCHZ7JYRJMKxcgs6yMp9xSZ6pJH5YN2zMPqpWLQlBNlDYy086sC7Q6PTqPS0RElIykV2mkQTVZpKp/M3DEM927Yuno7hsRERElByaqE0U5qBaIM1Nt004g/SSQO5d3iaezp1q+vNHZR91DTUjPNHN4AhERUXaTlaDaj5PsgJqoEIVWDURERJR8GFQjioKdnqBaySKB72Nmqp06ZfdTq1zGuw+LBNmcB/zRoDPeqpUDupwXvcclIiJKRvo9dl8EQbXJ870/r1AqOvtEREREyYXln0RxylTTmWIn0oE9B4C1W3xLP51qVojePl57ATB1IXDX5b793oiIiLKbSHuqHT8BjJvjfR0z1YiIiLInnloTZZEcXO89mHmmWt48QNGC6r6SraYz1YIF1c4+LXr7WbIo8O0z0Xs8IiKi7Fj++fdi36+pyEw1IiKibIlBNaIsuvdte7t44eD3lb5qVlBtT/CgWtfWwJhZwFM3RnlniYiIyDuodji8rxs9U11edyHQpBZw8AhQqUz094+IiIjcj0E1oiz6ZKS9nTNn8PtKX7XlG1Sm2rwV6rqq5XzvN/RZ4OhxIH+UhhQQERGRN92yYd1W4ORJ7/6mwYz+R122OxO45dLY7R8RERG5HwcVEGXBseP2FM0v+2Z+fz0B9NNRwD//qe36VX3vJwf2DKgRERHFjmSZFcqv+qLO+X979wFdVZWocfxLICSUQIBQE7qACIhI7yiCBXAQdGwMogi2hw9lngI6iGJB1CeKAzaQp8sBlLHBSAlvKAIPHIoIMqIQWgLSIQkhJCTnrXMONzeXBMyF23Lu/7fWcZ/OPqxs7+XLPnv/Yu/bdUB66l33WKkF/XuPlHi7tH67vd2nXWDrCwAAQg891YDLsCPVnsnT/FJ+b+/fP981WcHCdXY5uLfU4Sr/1hEAABRWJkrqda309Wpp+Q9S2yul5kOl02ekzKxI9W7gef7zs6TUI+5tZvwEAAD0VAMug/lba9OVdd091n7v9c+Cxg32T70AAMDva3elXf6YLCXvtwM107p/F/5Q/3mve/21hwNVQwAAEMroqQZcBtcX7GZFvMJ5sdc/XeoysDEAAEHTsqFd/rjTsxfaiYyIQr9E27zTXv+/v9LLHAAA2OipBvigp1qxQ7U4z3HTysX4p14AAOD3XX0uVNu2Rzp4zL0/+YCUfdb9NbnnKPexa5sUr3c6AABwPkI14DL829VTra73PdViy/KlHACAYDJn4I4tJ+WcldZuc+/Py4tQ6vEK1vq+Q/as3abEavZYbAAAACZCNeAS5eVdwuufBXqqmV/iAQBA8Ji/3HL1VjMnKyho79FYq1z8vXvfP98MZO0AAECoI1QDLpH5m+vMLCmqtNSotvc91cpG+61qAACgmK5uZJcbfvHcv+9oRatcdC5UmzBUapwY6NoBAIBQRqgGXOZ4auYX7NLFnPKjiv1Lb0t2jn/qBQAAis/VU80lroK7p5r5WmjSBnv7pvaBrxsAAAhthGpAgMZTM5Uq5V4/ne37OgEAgEubAdSl29V2+f3OWqrYt7TSTklVKkptmwalegAAIIQRqgGX2VPtSi9CtYKyCNUAAAi6Fg08t7u2dK/n5tkzCvVu4/mLMQAAABOhGnCZoVpxJyk43+kzPq0OAAC4BJUqSPVrurevbSxVjjU8zrn+2sDXCwAAhD5CNYS93NzghGrmOC0AACD4rkjwfB20WV3jgr3XAAAAXAjVENb+tlSKvUX6epV31+35TTqaJkVGSk3reHdtlxZ2eVs3764DAAD+UbqU50zdNaq4t2tWufShHgAAgLMRqiGs3fui/RrmkFe8u+6rVe7fXJcv6921X74ovf249OF/eXcdAADwj6E32WXz+lJEhBRb4LN9y0z7l2gAAADnK11oDxCGznr5CuiKzXbZr5P3f1a1OGnkQO+vAwAA/nHn9eY4alKrRvb2n/rk6eMlkWpWz1B8nD1ZAQAAwPn4vRvC1sFj7vXMLOnTpOJf+2tK0TOGAQCAkqlPO/drnz1aGXr1rpX63zcYABUAAFwYoRrC1oZfPLcHv1S86/LypJ377fXGBQY2BgAAztG01nHFVwp2LQAAQCgLqVDtyy+/VHR0tHbv3m1tZ2VlacSIEapdu7aaN2+uN998s9A1ixcvVpcuXVSlShX169cv/1rgfIbhOePn+aFacR04ao/DZg5qXL+mz6oHAAAAAABKkJAI1Q4cOKBhw4Zp9OjRys7Ozt//wAMP6NChQ1q+fLk++OADvfPOO1bp8sMPP+juu+/WqFGjtGXLFl1zzTW68cYblZ6eHqQnQagyX9esNVCa+LG0+HupTG9p/Ez7WMPadhkdJe0/Io14Xdpx7vXOouw5aJd1qkulGZUQAAAAAICwFBKh2tq1a5WRkaHVq1fn70tOTtbChQv10UcfqUmTJurcubOmT5+ul19+Wca5LkdTpkzRyJEjdccddyghIUETJ05UfHy8vvjiiyA+DULR/DXSweN2kHbTU/YrnC4f/Nme1etMjtT+YemDBdIfnr3wvU6esktzQGMAAAAAABCeQiJUGzBggObOnatatWrl7/vHP/6h7t27q3Llyvn7rr/+eh07dkxbt261tufPn29d6xIREaHbbrtN33zzTYCfACV5ds/2V0q1q9rrqUfscttuKecCYxOnnQvVYsv6upYAAAAAAKCkCImX18ww7HwpKSlq0MBzasXSpUurXr16Sk1NVaNGjayA7fxzGjZsqHnz5l3wzzpz5oy1uKSlpVllTk6Otfwe1znFOReh4+hJMz8uVWh/Qryh6KizSogvpZTDnhlzryfytPDVXC1YG6EtyRHKzZPG3pOnE+nmz2tplS+bp5yci6R1+F20J8B3aE+A79CeAN+gLQG+RZsKDG/+fkMiVCuKGZjVqFGj0P7Y2Fjr2PHjx63tChUqFHn8Ql555RU9//zzhfYvWbJE5cqVK3b9kpKSin0ugu/HbVdLaqCqFU4rLy9CV9c9rKMZMerXepe+/faAInLaSTo3uNo5322JVPvhx7QtNT5/3+HUbbJfPm6pjBOp+vbbjQF/FieiPQG+Q3sCfIf2BPgGbQnwLdqUf2VmZpb8UM2czdMcZ+185iQE5jHXa6HmOXFxcYWOX8jYsWP15JNPevRUq1Onjvr06aOKFSsWK7E0f4B79+6tqKioS3gyBMPsH+xeamP/VEaPDzQHVHNN29naWhbtiNTaHYWvKxiomX4701zXNrFjtaaNauuWW5j+83LQngDfoT0BvkN7AnyDtgT4Fm0qMFxvNJboUC0xMVFLly712Jebm6s9e/ZYkxKYvcrM8GzXrl1q3doMRtwTHJjHLyQ6Otpazmf+QHrzQ+nt+QiutHNBc9WKpRQVVcRroNXc668Mlw6flP77M3u7ZUNpxlP2JAb/uzFS2efGWouLLfpe8B7tCfAd2hPgO7QnwDdoS4Bv0ab8y5u/25CYqKAoffv21cqVK/Nf8zQtW7bMCtJatGhhbffr109ff/11/nFzVlBzu3///kGpM0LXiXOdHuM83xbOV909H4Zu7iDd0Ma9/dKDUtumUu+2Ula2lLTe3h9b/LeFAQAAAACAw4RsTzVzwoGbb75Z999/v1577TUdPnxYDz30kMaNG5c/scGoUaPUq1cvNW/eXJ07d9a0adN06NAhDRo0KNjVR4g5nn7xUC2qQEto0UCqX1NqUEu6uqHUr5M5mYY09zmpwyPSryn2ecz+CQAAAABA+ArZnmqmmTNnqnr16urRo4cefPBBjRw5UsOHD88/br72OXv2bE2ZMsXqvbZ582YtXrzYmqwAcJm/Rvp578VDtUHdpc4t7F5ppUpJlSpIybOlL1+0AzVT5Vjpr6Pc11QsH4DKAwAAAACAkBRyPdXMVzhdYmJi9P7771/0/BtvvNFagKKs2SrdOs69Xatq0eeZAdnqdwrvdwVqLh2vcq+fyvJVLQEAAAAAQEkT0j3VgMs1ZZ7ndo0LTwxbLAXHUTNfEQUAAAAAAOEp5HqqAb5idnpcsdm9/dkE39z3x5nS//0k9e3om/sBAAAAAICSh1ANjrUzVTp0XCoTJZ1cIMVE++a+LRvaCwAAAAAACF+8/gnHWrXFLts19V2gBgAAAAAAYCJUg2Ot3mqXXVoGuyYAAAAAAMBpCNXg/FCtRbBrAgAAAAAAnIZQDY509KT07z32eufmwa4NAAAAAABwGkI1ONKan+zyyrpSfFywawMAAAAAAJyGUA2OnqSgK+OpAQAAAAAAPyBUgyOtPheqMZ4aAAAAAADwB0I1OE5urrR+u73emVANAAAAAAD4AaEaHOe3Y9KZHKlUpNSodrBrAwAAAAAAnIhQDY6TesQua1aRSpUKdm0AAAAAAIATEarBsaFaYrVg1wQAAAAAADgVoRocJ/WwXSYQqgEAAAAAAD8hVINje6olxAe7JgAAAAAAwKkI1eA4W5LtkkkKAAAAAACAvxCqwVHy8qTVW+31Li2CXRsAAAAAAOBUhGpwlO37pBMZUvkY6Zorgl0bAAAAAADgVIRqcJQdqXbZtK5UunSwawMAAAAAAJyKUA0hzzCkrDPFOzd5v102rOXXKgEAAAAAgDBHqIaQ9+Rfpcr9pZ/3/P65yQfssiGTFAAAAAAAAD/iBTmEvCnz7PKZGdLfX5Ays6SH/1vKypbaNrV7p93cQfpDV2b+BAAAAAAAgUGohpB/9dPl0HHp0Tel6V+7932+3C7fmy/d3kNatkmKjJR6tAp8XQEAAAAAQPggVENIO5nhXl+1xV5c+nWS0jKllZvt7Xkr7PKh/vZEBQAAAAAAAP5CqIaQ9veVRe9f8ZbU/VxvtIiensdeuN//9QIAAAAAAOGNiQoQstJOSQ++Vnj/nrnuQM1kjrPm8vE4KT4uMPUDAAAAAADhi55qCFkzvi28787rpLo1PPcN7C4Z58ZWAwAAAAAACARCNYSU1MPSmRypXg3p7b/b+yaNkB64RfpqlTS4d7BrCAAAAAAAQKiGEJKRKbUZIR08Lt3UXtr9m1S1ovT4IKlstDS8X7BrCAAAAAAAYGNMNYSMmQvtQM206Hu7fPhWO1ADAAAAAAAIJYRqCAm5udKUeYX3P3ZbMGoDAAAAAABwcYRqCAlffiftOmC/7pnyudSvk/TGo1KtqsGuGQAAAAAAQGGMqQa/+HGntOEXqWtL6YoEKSLC3n/6jLRhuzT8dalVI6nnNdKxdOmZD+3jj/xBSqgmzX8lqNUHAAAAAAC4KEI1XJbDJ+ylcqy7V5lhSLc8LaUesbcT4qW+nez1T5OkU1n2+s97pbnLPO9nzvIJAAAAAAAQ6gjVcMnMHmdtH7LXa8dL2z+WKpST1my1A7VSkVJkpL3+/vzC13e8SqpZRVq/XUo5LLVvJjWoFfDHAAAAAAAA8BqhGi7Z+I/c6/uPSDO+lQZ2l7qOtPd1u1r69FnpyiFSeqbUvL70zJ/snmsdmknRZezz8vKkr1ZJ1zYOznMAAAAAAAB4i1ANxZK0XjqZYY+BFh9nh2QrfvA8Z/kP0l+/cm8/2Nfuwfavd6Xss1LLhkXf2+zNZoZxAAAAAAAAJQWhGn7X9K+lR9+0180JB14ZLlWp6B4brXyMvW72NjPVqCx9N1VqnGhvN60bpIoDAAAAAAD4CaEaLirlkPTn6e5tcxKCMe+7tyeNsF/lvO4J9753/tMdqAEAAAAAADhRZLArgND2xmdSZpbUpYW049PCx4fcKNWt4d5uUke6rVtAqwgAAAAAABBw9FTDRble6Rxzj1S/puexfp2kWlWl7Bz7tVCzF9vTd0ulSgWlqgAAAAAAAAFDqIYLOnVa2v2bvd7xqsJh2f+MtcsyUdLYe+1zB/cOfD0BAAAAAAACjVANF7R9n13GV7Jn/DT9Y5L095XS1MelcjHuc196MDh1BAAAAAAACAZCNYfJy5MOHLV7j1U7F4Rdqh922GWzeu59t3S0FwAAAAAAgHDGRAUO89AbUuId0nvfXN59zPHR3vnSXr++tU+qBgAAAAAA4BiEag7jmolz17mx0C7VwnXSpl+l8jHSyIE+qRoAAAAAAIBjEKo5jGuGzl0HLq+X2sSP7fVHB0hVK/mmbgAAAAAAAE5BqOYwDXwQqi3bJK3dJsWUkZ68w2dVAwAAAAAAcAxCNYdpUMsu9x2Szp69tHtMnm2Xw/tJNav6rm4AAAAAAABOQajmMLWq2uOg5eZJc5cV/Wrntt1Sbm7R189bLi3+l70+6nb/1hUAAAAAAKCkIlRzmMhI6ck/2utTvyh8fN4KqflQaeB4KS9P2n9E+jXFPpZ1Rnpgsr1+axepYe0AVhwAAAAAAKAEIVRzoBH97PJf26WjJz2P/c8iu/xmtTRsspRwu3TNg9JvR6WF30vpmfZYarP/Evh6AwAAAAAAlBSEag6UWF1qXt/uibZ0g3u/ub3mJ/f2rHMBW2aW9PkK6T/esrcfGyCViwlwpQEAAAAAAEoQQjWHuqm9XbrGRzP9dkw6nm6/Ivr5BKlrS/exx9+2XwW9qr40YWjg6wsAAAAAAFCSEKo51I0FQjVzcgJT8n67rFtdur2n9N1UadYY9zWx5aQvXpAqlAtChQEAAAAAAEoQQjWH6tZSKhtt9z7busvel3zALgtOQHBbN8/1pnUDXFEAAAAAAIASiFDNoWKipeta2+ujp0nTvpI+WGBvNyoQqlUsL/3XXVJCvPTcfcGpKwAAAAAAQElTOtgVgP/8+U7p27VS0np7cWnTxPO8yQ/bCwAAAAAAAIqHUM3Bel5jT0pgzvppeqi//Ypnn3bBrhkAAAAAAEDJRqjmYBERntvvjg5WTQAAAAAAAJyFMdUczjXzJwAAAAAAAHyHUM3hOjSzy/Ixwa4JAAAAAACAcxCqOdwn46RB3aXlU4JdEwAAAAAAAOdgTDWHuyJRmvdCsGsBAAAAAADgLPRUAwAAAAAAALxEqAYAAAAAAAB4iVANAAAAAAAA8BKhGgAAAAAAAOAlQjUAAAAAAADAS4RqAAAAAAAAgJcI1QAAAAAAAAAvEaoBAAAAAAAAXiJUAwAAAAAAALxEqAYAAAAAAAB4iVANAAAAAAAA8BKhGgAAAAAAAOAlQjUAAAAAAADAS4RqAAAAAAAAgJcI1QAAAAAAAAAvEaoBAAAAAAAAXiJUAwAAAAAAALxEqAYAAAAAAAB4qbTCnGEYVpmWllas83NycpSZmWmdHxUV5efaAc5GewJ8h/YE+A7tCfAN2hLgW7SpwHDlQ6686GLCPlRLT0+3yjp16gS7KgAAAAAAAAiRvKhSpUoXPSfCKE705mB5eXnav3+/YmNjFRERUazE0gzg9u3bp4oVKwakjoBT0Z4A36E9Ab5DewJ8g7YE+BZtKjDMmMwM1GrXrq3IyIuPmhb2PdXMv6DExESvrzN/gPkhBnyD9gT4Du0J8B3aE+AbtCXAt2hT/vd7PdRcmKgAAAAAAAAA8BKhGgAAAAAAAOAlQjUvRUdH67nnnrNKAJeH9gT4Du0J8B3aE+AbtCXAt2hToSfsJyoAAAAAAAAAvEVPNQAAAAAAAMBLhGoAAAAAAACAlwjVAAAAAAAAAC+Fdaj2ySefqEWLFqpQoYK6du2q9evX5x/btWuX+vXrpypVqljHlixZ4nGtORTdG2+8ocaNG6tixYq6+eab9euvv3qcs2HDBt1www2qXLmyVW7cuDFgzwY4tV1t375ddevW1axZswL2TIATP5/Ma837N2vWTJ9++mnAng1wWnv64osv1LJlS5UrV84qP//884A9G+CktuSSlZWlVq1aaejQoX5/JsCpbWru3LmKiIgotEyaNCmgzxgWjDD11VdfGTVq1DBWrFhhHDt2zJgxY4YRHx9vHDx40EhLSzOaNGlijB8/3khJSTHmzJljxMXFGZs2bcq/fsqUKUbTpk2tfUeOHDFefPFF44orrjAyMzOt4/v27TOqV69uTJs2zUhNTTWmTp1q/XnmfsCp/NmusrOzjeeff96oWbOmUa1aNeOjjz4K6rMCJfXzyfxMio2Nte5r3n/58uVWu0pKSgriUwMlsz2Z7ce83+LFi43jx48bCxYssNrX2rVrg/jUQMlrSwWNGjXKuv6+++4L8FMCzmlTZv4wbNgwIycnx2PJzc0N4lM7U9iGap07dzbeffddj329evUyZs6caf1jvXv37kZeXl7+sbFjxxpDhw7N365du7axaNGi/G3z3EaNGhn//Oc/re3nnnvOGDJkiMf97777bmPChAl+fCrAue3q8OHDRseOHa1/qPTo0YNQDY7l78+ncePGGXfddZfH/SdOnFjoMwtwAn+3pwEDBhiTJ0/2uP/w4cONMWPG+PGpAOe1JRfznDZt2lhtiFANTubvNmUGcuY18L+wff3zl19+UceOHT32RUdH6+TJk5o/f74GDBhgdY90GThwoL755htr3Tzn4MGDHteb57quN7nuUVDBewBO5M92VbVqVa1Zs0YdOnQI4BMBzvt8Ml+h7tSpU5H3B5zG3+1p0KBBuvPOOz3uX7ZsWUVFRfn5yQBntSXToUOH9Nhjj1lDEpjHACfzd5s6fPiwqlWrFrDnCWdhG6qZP2Tmu/ouKSkpWrFihTp37mytN2jQwOP8hg0b6tixYzp9+rQqVaqks2fPWmXB8Wl27Nihdu3a5d+vqHukpqb6/dkAJ7Yr1zgAgNP5+/Np3rx5evzxx/OP5+bmas6cOdb9Aafxd3saPHiwNc6ny88//6zPPvvMGgcHcBJ/tyXzDaphw4Zp3Lhxatq0aQCfDHBmmzLvHxkZqQceeEBNmjSxxmUzvwPC98I2VCto37591pefW2+9Ve3bt7d+WM3BAguKjY21yuPHjxe6fuvWrVZyPHr0aCUkJFj7LnQPcz8QDvzRroBw4+92ZA4GPWTIEJ04ccLqHQA4mT/b08KFC63BpM2JP8aOHWvdH3Aqf7Sl6dOnW70877///gA9BeDsNmWGauZEBuYEBgsWLLDCtfvuu0+LFi0K0FOFj7AP1TZt2mS9Tmb+8LpmEzS/FGVkZHicl56ebpXmTJ4FmbNwdOnSxfoAeOmll/L3X+ge5n7A6fzVroBw4u92dPToUfXq1Us7d+7U6tWrVb58eb8+D+Dk9nTddddp8+bNVrj23nvvafbs2X59HsBJbWnbtm16++23rbbDWwkIN/76fJo4caJWrlypO+64w+qpZoZqTz/9tKZOnRqQ5wonYR2qJSUlqWfPnlaia/5PvEyZMtb+xMREawrbgpKTk60fbvM3KAWnwDUT4XfffVcTJkzw+BC40D3ocQOn82e7AsKFv9vR7t27rS9g5v2WLVummjVrBujJAOe0p+zsbC1dutQqY2JiVKdOHd1000169tln9eqrrwb4KYGS25bMUGDv3r1WT0/z88hcXn/9dc2dO9daN8fUBZzIn9/3unXrpvr163vco02bNtZ3QPiYEaa+++47IyYmxpg9e3ahY+aMG+bsggVn23jmmWc8ZqAxrzOnTF+2bFmR9zdn2zh/xpp7773XmhUUcCp/tysXZv+Ek/m7HR09etSaMWrkyJFMqw7H82d7MttPfHy8sWrVKo/9M2bMsGZtA5zEn20pIyPDOHDggMcyevRo449//KO1fubMGT8+GeDMz6dmzZoZGzdu9Nj/6quvGv379/f5s4S7sA3VzKmaR4wYYeTk5Hgs5g9gWlqa0bhxYysYS0lJMebMmWPExcUZmzZtsq7Nzs42atasabz88stFXm/au3evUb16dWPatGlGamqqMXXqVGt73759QX5yoOS2KxdCNTiZv9uR+Q+Vq666ysjKyvI4fvbs2SA/OVDy2tNf/vIXo3nz5sa6deuMkydPGklJSUZCQoLxySefBPnJgZL5Hc/F7IhwfgcFwEn83aaeeuopo3379tbnkvkL1blz5xqVK1e2wjz4VliGaunp6YbZSa+oxdWTLDk52ejbt6/1w9u5c2dj8eLF+df/9NNPF7y+4D/0169fb1x//fVGpUqVjF69ehkbNmwIyvMCTmpXJkI1OFUg2lG7du2KPG62K8BJAtGezDD6hRdeMBo0aGCULVvWaNmypfHxxx8H7ZmBkv4dz4VQDU4WqM+niRMnGvXq1TPKly9vdOzY0ViyZEnQntnJIsz/+PqVUgAAAAAAAMDJwnqiAgAAAAAAAOBSEKoBAAAAAAAAXiJUAwAAAAAAALxEqAYAAAAAAAB4iVANAAAAAAAA8BKhGgAAAAAAAOAlQjUAAAAAAADAS4RqAAAAyJebm6ucnJxgVwMAACDkEaoBAAA42PLlyxUZGamYmJgiF/PYrFmz8s+fPXu2HnnkkfztAQMGaMGCBdZ66dKlVaFCBWuJjo7W0KFDg/JMAAAAoaB0sCsAAAAA/+revbsVrhVl8ODB+eubN2/WmDFjdOrUKa1du9bat2fPHm3cuFGtW7e2tjMyMqzSDOIudE8AAIBwQE81AAAAWFq1aqVJkyZp0KBB2rp1q7X06tVL06ZNU0JCQrCrBwAAEFII1QAAAOBhzpw5ql+/vrUsWbLE41hiYqK1PPHEE0GrHwAAQCggVAMAAICHu+66S7t377aWPn36eBzbsWOHtUyePDlo9QMAAAgFjKkGAADgcCtXrrQmJShKdna2brjhhmLfy3WfqKgon9UPAACgJKKnGgAAQBhMVJCVlVXkcs899xTr9c+zZ88qLy8vCLUHAAAITfRUAwAAQKHXPz/88ENrfcCAAfmzgJratm1rlUeOHFHPnj2DWEsAAIDgIlQDAACA5W9/+5tGjBhh9UqbN2+ete/UqVNKSkrShAkTrEDt+++/t/bPmjVLy5cvD3KNAQAAgofXPwEAAGAxXwVNS0vTqFGjdPDgQSs4W7p0qRWspaamqkePHsGuIgAAQMigpxoAAIDDeTNRwfjx4xUZGano6GglJCTo9ttv14IFC/TBBx9oxYoV+eelp6crIiIiIPUHAAAIRYRqAAAAYTBRwYVe1RwyZEh+OLZ582atW7dOixcvtrbbtWunRx99VG+99ZY1zpr5+ueGDRus+5UpUyZ/3DUAAIBwFGEYhhHsSgAAACA0mDN8mj3VznfmzBmr9xoAAABshGoAAAAAAACAl5ioAAAAAAAAAPASoRoAAAAAAADgJUI1AAAAAAAAwEuEagAAAAAAAICXCNUAAAAAAAAALxGqAQAAAAAAAF4iVAMAAAAAAAC8RKgGAAAAAAAAeIlQDQAAAAAAAJB3/h8ulCN46RjCQwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1500x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 画一下收盘价\n", "plt.figure(figsize=(15, 6))\n", "plt.plot(data.index, data['close'], label=f'{ticker}收盘价')\n", "plt.title(f'{ticker}股价走势')\n", "plt.xlabel('日期')\n", "plt.ylabel('价格')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "047ef1e5-1251-4c15-aa8e-8c3eeed7793e", "metadata": {}, "source": ["## 2. 加入技术指标"]}, {"cell_type": "code", "execution_count": 11, "id": "fc040874-6a78-4900-bfa0-7c86edde3121", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["添加特征后: (1235, 15)\n"]}], "source": ["# ======================\n", "# 2. 加入技术指标\n", "# ======================\n", "\n", "def compute_RSI(series, period=14):\n", "    \"\"\"\n", "    计算 RSI 指标\n", "    \"\"\"\n", "    delta = series.diff()\n", "    gain = (delta.where(delta > 0, 0)).ewm(alpha=1/period, adjust=False).mean()\n", "    loss = (-delta.where(delta < 0, 0)).ewm(alpha=1/period, adjust=False).mean()\n", "    rs = gain / loss\n", "    rsi = 100 - (100 / (1 + rs))\n", "    return rsi\n", "\n", "def add_features(df):\n", "    df = df.copy()\n", "\n", "    # shift_5\n", "    df['close_shift_5'] = df['close'].shift(5)\n", "    df['volume_shift_5'] = df['volume'].shift(5)\n", "\n", "    # momentum_5\n", "    df['momentum_5'] = df['close'] / df['close_shift_5'] - 1\n", "\n", "    # vol_ratio\n", "    df['vol_ratio'] = df['volume'] / df['volume_shift_5'] - 1\n", "\n", "    # RSI_14\n", "    df['RSI_14'] = compute_RSI(df['close'], period=14)\n", "\n", "    # Bollinger Bands\n", "    df['BB_middle'] = df['close'].rolling(window=20).mean()\n", "    df['BB_std'] = df['close'].rolling(window=20).std()\n", "    df['BB_upper'] = df['BB_middle'] + 2 * df['BB_std']\n", "    df['BB_lower'] = df['BB_middle'] - 2 * df['BB_std']\n", "\n", "    # future_ret_1d: 下一日收益 (明天收盘 / 今天收盘 - 1)\n", "    df['future_ret_1d'] = df['close'].shift(-1) / df['close'] - 1\n", "\n", "    # 去掉空值\n", "    df.dropna(inplace=True)\n", "    return df\n", "\n", "data_feat = add_features(data)\n", "print(\"添加特征后:\", data_feat.shape)"]}, {"cell_type": "markdown", "id": "1b5e50f2-5ffe-494a-a400-f960054a98d0", "metadata": {}, "source": ["## 3. 生成分类标签和数据分割"]}, {"cell_type": "code", "execution_count": 12, "id": "baea39cc-4ad5-4179-b3f3-0e3fab9aa4ba", "metadata": {}, "outputs": [], "source": ["# ======================\n", "# 3. 生成分类标签\n", "# ======================\n", "# label = 1 表示下一日上涨，否则为0\n", "# 注意，这一步需要 future_ret_1d 已经shift(-1)，以免未来信息泄漏\n", "data_feat['label'] = (data_feat['future_ret_1d'] > 0).astype(int)\n", "\n", "# 因为 shift(-1)，最后一行没有下一日数据，通常要drop掉\n", "data_feat.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": 13, "id": "3fdda23e-f5f0-488c-b27f-e0a67ff14773", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集范围: 2020-04-07 00:00:00 → 2024-03-11 00:00:00\n", "测试集范围: 2024-03-12 00:00:00 → 2025-03-06 00:00:00\n", "\n", "训练集样本数: 988\n", "测试集样本数: 247\n"]}], "source": ["# ======================\n", "# 4. 分割训练集、测试集（基于时间）\n", "# ======================\n", "train_size = int(len(data_feat) * 0.8)\n", "train_data = data_feat.iloc[:train_size].copy()\n", "test_data = data_feat.iloc[train_size:].copy()\n", "\n", "features = [\n", "    'momentum_5','vol_ratio','RSI_14','BB_upper','BB_middle','BB_lower'\n", "]\n", "target = 'label'  # 改成分类标签\n", "\n", "X_train = train_data[features]\n", "y_train = train_data[target]\n", "X_test = test_data[features]\n", "y_test = test_data[target]\n", "\n", "print(f\"训练集范围: {train_data.index.min()} → {train_data.index.max()}\")\n", "print(f\"测试集范围: {test_data.index.min()} → {test_data.index.max()}\")\n", "print(f\"\\n训练集样本数: {len(train_data)}\")\n", "print(f\"测试集样本数: {len(test_data)}\")"]}, {"cell_type": "markdown", "id": "0ac1fca5-e0ad-4835-9ef2-6d9071dbdf20", "metadata": {}, "source": ["## 5. 训练随机森林分类器"]}, {"cell_type": "code", "execution_count": 14, "id": "3cd0d33f-4f7a-435d-b1a2-94c5f81c1f35", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["随机搜索最优参数: {'n_estimators': 200, 'min_samples_split': 10, 'min_samples_leaf': 5, 'max_depth': 3}\n"]}], "source": ["# ======================\n", "# 5. 训练随机森林分类器 + TimeSeriesSplit\n", "# ======================\n", "tscv = TimeSeriesSplit(n_splits=5)\n", "rfc = RandomForestClassifier(random_state=42)\n", "\n", "param_dist = {\n", "    'n_estimators': [50, 100, 200, 300, 500],\n", "    'max_depth': [3, 5, 10, None],\n", "    'min_samples_split': [2, 5, 10],\n", "    'min_samples_leaf': [1, 2, 5],\n", "}\n", "\n", "random_search = RandomizedSearchCV(\n", "    estimator=rfc,\n", "    param_distributions=param_dist,\n", "    n_iter=10,\n", "    scoring='accuracy',  # 分类的评价指标\n", "    cv=tscv,\n", "    random_state=42,\n", "    n_jobs=-1\n", ")\n", "random_search.fit(X_train, y_train)\n", "\n", "best_clf = random_search.best_estimator_\n", "print(\"随机搜索最优参数:\", random_search.best_params_)"]}, {"cell_type": "code", "execution_count": 15, "id": "69dfe5f7-40b3-4a00-9747-3f1ad71c1dff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集 Accuracy: 0.5982, F1: 0.6886\n", "测试集 Accuracy: 0.5304, F1: 0.6184\n"]}], "source": ["# 评估在训练集、测试集的准确率\n", "y_pred_train = best_clf.predict(X_train)\n", "y_pred_test = best_clf.predict(X_test)\n", "\n", "acc_train = accuracy_score(y_train, y_pred_train)\n", "acc_test = accuracy_score(y_test, y_pred_test)\n", "f1_train = f1_score(y_train, y_pred_train)\n", "f1_test = f1_score(y_test, y_pred_test)\n", "\n", "print(f\"训练集 Accuracy: {acc_train:.4f}, F1: {f1_train:.4f}\")\n", "print(f\"测试集 Accuracy: {acc_test:.4f}, F1: {f1_test:.4f}\")"]}, {"cell_type": "markdown", "id": "e46d1ea3-2af2-4c42-8055-d50a0af94a61", "metadata": {}, "source": ["## 6. 简易回测策略与买入持有对比"]}, {"cell_type": "code", "execution_count": 16, "id": "a6a4eb60-f5eb-4c14-88fa-a318f0fe9b09", "metadata": {}, "outputs": [], "source": ["# ======================\n", "# 6. 简易回测策略\n", "# ======================\n", "# 策略：若模型预测下一日会上涨，则在当日收盘买入并持有1天；否则空仓。\n", "# 注：此处仅是示例。真正交易需考虑滑点、交易时点、手续费等。\n", "\n", "test_data['pred_label'] = y_pred_test  # 预测的明日涨/跌\n", "# 我们已经有 future_ret_1d = (明日收盘/今日收盘 -1),\n", "# 策略收益: 如果pred_label=1，则策略明日收益 = future_ret_1d，否则 = 0\n", "test_data['strategy_ret'] = np.where(test_data['pred_label'] == 1, \n", "                                     test_data['future_ret_1d'], \n", "                                     0.0)\n", "\n", "# 同时我们看看Buy & Hold从测试集第一天开始一直持有的收益\n", "# buy_and_hold_ret = future_ret_1d (每天的涨跌幅)，从测试集起始时假设买入\n", "test_data['bh_ret'] = test_data['future_ret_1d']\n", "\n", "# 计算累计收益（假设初始资金 = 1）\n", "test_data['strategy_cum'] = (1 + test_data['strategy_ret']).cumprod() - 1\n", "test_data['bh_cum'] = (1 + test_data['bh_ret']).cumprod() - 1"]}, {"cell_type": "code", "execution_count": 17, "id": "31ce7f76-64cf-4f6a-ae81-1cef2be46c7b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 回测曲线可视化\n", "plt.figure(figsize=(12,6))\n", "plt.plot(test_data.index, test_data['strategy_cum'], label='RandomForest Strategy')\n", "plt.plot(test_data.index, test_data['bh_cum'], label='Buy & Hold')\n", "plt.title('策略累计收益对比')\n", "plt.xlabel('日期')\n", "plt.ylabel('累计收益')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 18, "id": "cb5b4290-02c4-465c-a123-5af3841bf24e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["随机森林策略:\n", "年化收益率: 83.87%\n", "年化波动率: 58.30%\n", "夏普比率: 1.44\n", "\n", "买入持有策略:\n", "年化收益率: 60.89%\n", "年化波动率: 65.53%\n", "夏普比率: 0.93\n"]}], "source": ["# 计算策略评价指标\n", "strategy_return = test_data['strategy_ret'].mean() * 252  # 年化收益\n", "strategy_vol = test_data['strategy_ret'].std() * np.sqrt(252)  # 年化波动率\n", "strategy_sharpe = strategy_return / strategy_vol if strategy_vol > 0 else 0  # 夏普比率\n", "\n", "bh_return = test_data['bh_ret'].mean() * 252  # 年化收益\n", "bh_vol = test_data['bh_ret'].std() * np.sqrt(252)  # 年化波动率\n", "bh_sharpe = bh_return / bh_vol if bh_vol > 0 else 0  # 夏普比率\n", "\n", "print(\"随机森林策略:\")\n", "print(f\"年化收益率: {strategy_return:.2%}\")\n", "print(f\"年化波动率: {strategy_vol:.2%}\")\n", "print(f\"夏普比率: {strategy_sharpe:.2f}\")\n", "\n", "print(\"\\n买入持有策略:\")\n", "print(f\"年化收益率: {bh_return:.2%}\")\n", "print(f\"年化波动率: {bh_vol:.2%}\")\n", "print(f\"夏普比率: {bh_sharpe:.2f}\")"]}, {"cell_type": "markdown", "id": "83c9a4dd-d92c-4bfe-bca9-8a1f9afd0a25", "metadata": {}, "source": ["## 7. 使用Backtrader进行策略回测"]}, {"cell_type": "code", "execution_count": 26, "id": "09a41b4b-30f9-4ad5-914e-0a1e76a83dff", "metadata": {}, "outputs": [], "source": ["# Backtrader策略类\n", "class MLStrategy(bt.Strategy):\n", "    params = (\n", "        ('model', None),  # 分类器\n", "        ('features', None),  # 特征列表\n", "    )\n", "    \n", "    def __init__(self):\n", "        # 收盘价\n", "        self.close = self.datas[0].close\n", "        \n", "        # 存储特征\n", "        self.feature_values = {}\n", "        \n", "        # momentum_5\n", "        self.momentum_5 = bt.indicators.ROC(self.close, period=5)\n", "\n", "        # vol_ratio\n", "        self.vol_ma = bt.indicators.SMA(self.datas[0].volume, period=5)\n", "        self.vol_ratio = self.datas[0].volume / self.vol_ma - 1\n", "        \n", "        # RSI_14\n", "        self.rsi_14 = bt.indicators.RSI(self.close, period=14)\n", "        \n", "        # Bollinger Bands\n", "        self.bb = bt.indicators.BollingerBands(self.close, period=20)\n", "        \n", "        # 预测结果\n", "        self.pred = None\n", "    \n", "    def next(self):\n", "        # 等待足够数据\n", "        if len(self) < 25:  # 需要足够的历史数据计算特征\n", "            return\n", "\n", "        print()\n", "        # 准备特征\n", "        self.feature_values = {\n", "            'momentum_5': self.momentum_5[0],\n", "            'vol_ratio': self.vol_ratio[0],\n", "            'RSI_14': self.rsi_14[0],\n", "            'BB_upper': self.bb.top[0],\n", "            'BB_middle': self.bb.mid[0],\n", "            'BB_lower': self.bb.bot[0]\n", "        }\n", "        \n", "        # 提取需要的特征\n", "        features_array = [self.feature_values[f] for f in self.p.features]\n", "        features_array = np.array(features_array).reshape(1, -1)\n", "        \n", "        # 预测\n", "        try:\n", "            self.pred = self.p.model.predict(features_array)[0]\n", "        except Exception as e:\n", "            print(f\"预测错误: {e}\")\n", "            return\n", "        \n", "        # 交易逻辑\n", "        if self.pred == 1:  # 预测上涨\n", "            if not self.position:  # 没有持仓，买入\n", "                self.buy()\n", "        else:  # 预测下跌\n", "            if self.position:  # 有持仓，卖出\n", "                self.close()"]}, {"cell_type": "code", "execution_count": 25, "id": "b550ec22-38c3-450e-9a91-5cb9a12cc4a2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["初始资金: 100000.00\n"]}, {"ename": "TypeError", "evalue": "'NoneType' object is not iterable", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[25], line 4\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mback_test\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m run_backtest\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mstrategy\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbuy_and_hold\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m BuyAndHoldStrategy\n\u001b[0;32m----> 4\u001b[0m result, cerebro \u001b[38;5;241m=\u001b[39m \u001b[43mrun_backtest\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m      5\u001b[0m \u001b[43m    \u001b[49m\u001b[43mticker\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mticker\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m      6\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdf\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m      7\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstart_date\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstart_date\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m      8\u001b[0m \u001b[43m    \u001b[49m\u001b[43mend_date\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mend_date\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m      9\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstrategy\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mMLStrategy\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     10\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeframe\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbt\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mTimeFrame\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mDays\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[1;32m     11\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\n\u001b[1;32m     12\u001b[0m \u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Desktop/金融概念学习/quant_learning/notebooks/week2/back_test/backtesting.py:83\u001b[0m, in \u001b[0;36mrun_backtest\u001b[0;34m(ticker, df, start_date, end_date, strategy, initial_cash, strategy_params, print_log, timeframe, compression, market_params)\u001b[0m\n\u001b[1;32m     80\u001b[0m cerebro\u001b[38;5;241m.\u001b[39mbroker\u001b[38;5;241m.\u001b[39mset_slippage_fixed(\u001b[38;5;241m0.05\u001b[39m, slip_open\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m     82\u001b[0m \u001b[38;5;66;03m# 运行回测\u001b[39;00m\n\u001b[0;32m---> 83\u001b[0m results \u001b[38;5;241m=\u001b[39m \u001b[43mcerebro\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     84\u001b[0m strat \u001b[38;5;241m=\u001b[39m results[\u001b[38;5;241m0\u001b[39m]\n\u001b[1;32m     86\u001b[0m final_value \u001b[38;5;241m=\u001b[39m cerebro\u001b[38;5;241m.\u001b[39mbroker\u001b[38;5;241m.\u001b[39mgetvalue()\n", "File \u001b[0;32m~/miniforge3/envs/ta_arm/lib/python3.9/site-packages/backtrader/cerebro.py:1132\u001b[0m, in \u001b[0;36mCerebro.run\u001b[0;34m(self, **kwargs)\u001b[0m\n\u001b[1;32m   1128\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_dooptimize \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mp\u001b[38;5;241m.\u001b[39mmaxcpus \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[1;32m   1129\u001b[0m     \u001b[38;5;66;03m# If no optimmization is wished ... or 1 core is to be used\u001b[39;00m\n\u001b[1;32m   1130\u001b[0m     \u001b[38;5;66;03m# let's skip process \"spawning\"\u001b[39;00m\n\u001b[1;32m   1131\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m iterstrat \u001b[38;5;129;01min\u001b[39;00m iterstrats:\n\u001b[0;32m-> 1132\u001b[0m         runstrat \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrunstrategies\u001b[49m\u001b[43m(\u001b[49m\u001b[43miterstrat\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1133\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrunstrats\u001b[38;5;241m.\u001b[39mappend(runstrat)\n\u001b[1;32m   1134\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_dooptimize:\n", "File \u001b[0;32m~/miniforge3/envs/ta_arm/lib/python3.9/site-packages/backtrader/cerebro.py:1298\u001b[0m, in \u001b[0;36mCerebro.runstrategies\u001b[0;34m(self, iterstrat, predata)\u001b[0m\n\u001b[1;32m   1296\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_runonce_old(runstrats)\n\u001b[1;32m   1297\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1298\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_runonce\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrunstrats\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1299\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1300\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mp\u001b[38;5;241m.\u001b[39moldsync:\n", "File \u001b[0;32m~/miniforge3/envs/ta_arm/lib/python3.9/site-packages/backtrader/cerebro.py:1700\u001b[0m, in \u001b[0;36mCerebro._runonce\u001b[0;34m(self, runstrats)\u001b[0m\n\u001b[1;32m   1697\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_timers(runstrats, dt0, cheat\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[1;32m   1699\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m strat \u001b[38;5;129;01min\u001b[39;00m runstrats:\n\u001b[0;32m-> 1700\u001b[0m     \u001b[43mstrat\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_oncepost\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdt0\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1701\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_event_stop:  \u001b[38;5;66;03m# stop if requested\u001b[39;00m\n\u001b[1;32m   1702\u001b[0m         \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n", "File \u001b[0;32m~/miniforge3/envs/ta_arm/lib/python3.9/site-packages/backtrader/strategy.py:309\u001b[0m, in \u001b[0;36mStrategy._oncepost\u001b[0;34m(self, dt)\u001b[0m\n\u001b[1;32m    307\u001b[0m minperstatus \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getminperstatus()\n\u001b[1;32m    308\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m minperstatus \u001b[38;5;241m<\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[0;32m--> 309\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnext\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    310\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m minperstatus \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m    311\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnextstart()  \u001b[38;5;66;03m# only called for the 1st value\u001b[39;00m\n", "Cell \u001b[0;32mIn[19], line 47\u001b[0m, in \u001b[0;36mMLStrategy.next\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m     37\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfeature_values \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m     38\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmomentum_5\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmomentum_5[\u001b[38;5;241m0\u001b[39m],\n\u001b[1;32m     39\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mvol_ratio\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mvol_ratio[\u001b[38;5;241m0\u001b[39m],\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     43\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mBB_lower\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbb\u001b[38;5;241m.\u001b[39mbot[\u001b[38;5;241m0\u001b[39m]\n\u001b[1;32m     44\u001b[0m }\n\u001b[1;32m     46\u001b[0m \u001b[38;5;66;03m# 提取需要的特征\u001b[39;00m\n\u001b[0;32m---> 47\u001b[0m features_array \u001b[38;5;241m=\u001b[39m [\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfeature_values[f] \u001b[38;5;28;01mfor\u001b[39;00m f \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mp\u001b[38;5;241m.\u001b[39mfeatures]\n\u001b[1;32m     48\u001b[0m features_array \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray(features_array)\u001b[38;5;241m.\u001b[39mreshape(\u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m)\n\u001b[1;32m     50\u001b[0m \u001b[38;5;66;03m# 预测\u001b[39;00m\n", "\u001b[0;31mTypeError\u001b[0m: 'NoneType' object is not iterable"]}], "source": ["# 若想看最优参数的详细回测日志，可再手动调用:\n", "final_result, cerebro = run_backtest(\n", "    ticker=ticker,\n", "    df=test_data,\n", "    start_date=start_date,\n", "    end_date=end_date,\n", "    strategy=MLStrategy,\n", "    initial_cash=100000,\n", "    strategy_params={'model': best_clf, 'target_percent':0.98},\n", "    print_log=True,  # 这次打开日志\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "id": "d856f810-95b7-4234-a5ac-c5bd5e2495bf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "54c70ae6-1b7e-4c4c-91cc-651c87bb4e4a", "metadata": {}, "outputs": [], "source": ["# 运行回测 - 随机森林策略\n", "print('初始资金: %.2f' % cerebro.broker.getvalue())\n", "results = cerebro.run()\n", "strat = results[0]\n", "end_value = cerebro.broker.getvalue()\n", "print('最终资金: %.2f' % end_value)\n", "print('总收益率: %.2f%%' % ((end_value / 100000.0 - 1) * 100))\n", "\n", "# 获取分析结果\n", "sharpe_ratio = strat.analyzers.sharpe.get_analysis()['sharperatio']\n", "annual_return = strat.analyzers.returns.get_analysis()['rnorm100']\n", "max_drawdown = strat.analyzers.drawdown.get_analysis()['max']['drawdown']\n", "\n", "print('\\n随机森林策略分析:')\n", "print('夏普比率: %.2f' % sharpe_ratio if sharpe_ratio else 'N/A')\n", "print('年化收益率: %.2f%%' % annual_return)\n", "print('最大回撤: %.2f%%' % max_drawdown)"]}, {"cell_type": "code", "execution_count": null, "id": "37c80fba-8c37-4657-833a-f3cadd219c40", "metadata": {}, "outputs": [], "source": ["# 运行回测 - 买入持有策略\n", "print('初始资金: %.2f' % cerebro_bh.broker.getvalue())\n", "results_bh = cerebro_bh.run()\n", "strat_bh = results_bh[0]\n", "end_value_bh = cerebro_bh.broker.getvalue()\n", "print('最终资金: %.2f' % end_value_bh)\n", "print('总收益率: %.2f%%' % ((end_value_bh / 100000.0 - 1) * 100))\n", "\n", "# 获取分析结果\n", "sharpe_ratio_bh = strat_bh.analyzers.sharpe.get_analysis()['sharperatio']\n", "annual_return_bh = strat_bh.analyzers.returns.get_analysis()['rnorm100']\n", "max_drawdown_bh = strat_bh.analyzers.drawdown.get_analysis()['max']['drawdown']\n", "\n", "print('\\n买入持有策略分析:')\n", "print('夏普比率: %.2f' % sharpe_ratio_bh if sharpe_ratio_bh else 'N/A')\n", "print('年化收益率: %.2f%%' % annual_return_bh)\n", "print('最大回撤: %.2f%%' % max_drawdown_bh)"]}, {"cell_type": "code", "execution_count": null, "id": "4b5e4b66-9a18-41a4-8af0-1aebf3c2c8b2", "metadata": {}, "outputs": [], "source": ["# 绘制策略收益对比\n", "plt.figure(figsize=(15, 6))\n", "\n", "# 策略1：随机森林策略\n", "cerebro.plot(style='candlestick', barup='green', bardown='red', plotdist=1, start=0, end=None, width=16, height=9, dpi=100, tight=True, use=None, numfigs=1, volume=True, voloverlay=True, novolumeprefix=False)[0][0]\n", "\n", "# 策略2：买入持有策略\n", "cerebro_bh.plot(style='candlestick', barup='green', bardown='red', plotdist=1, start=0, end=None, width=16, height=9, dpi=100, tight=True, use=None, numfigs=1, volume=True, voloverlay=True, novolumeprefix=False)[0][0]"]}, {"cell_type": "code", "execution_count": null, "id": "5fc7f93c-45fb-42c0-813c-3bdf5dee0fc5", "metadata": {}, "outputs": [], "source": ["# ======================\n", "# 输出最后几行结果\n", "# ======================\n", "print(\"\\n测试集结果(尾部5行)：\")\n", "print(test_data[['close','future_ret_1d','pred_label','strategy_ret','strategy_cum','bh_cum']].tail(5))"]}], "metadata": {"kernelspec": {"display_name": "Python (ta_env)", "language": "python", "name": "ta_arm"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}