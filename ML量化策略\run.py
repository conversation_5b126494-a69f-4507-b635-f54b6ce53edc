#!/usr/bin/env python3
"""
MLQuant 主程序入口

提供命令行接口来运行ML量化策略系统。
"""

import argparse
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from mlquant import (
    RandomDataGenerator,
    MLStrategyOptimizer,
    BacktestEngine,
    PerformanceAnalyzer,
    StrategyFileGenerator,
    StrategyManager
)
from mlquant.utils.config import ConfigManager
from mlquant.utils.logger import setup_logger
from mlquant.utils.helpers import print_header


def run_demo():
    """运行演示程序"""
    print_header("MLQuant 演示程序")
    
    # 设置日志
    logger = setup_logger("mlquant_demo")
    logger.info("开始运行演示程序")
    
    try:
        # 导入并运行演示
        from examples.demo import run_ml_strategy_demo
        run_ml_strategy_demo()
        
    except Exception as e:
        logger.error(f"演示程序运行失败: {e}")
        raise


def run_optimization(strategy_type: str, data_days: int = 1000):
    """运行策略优化"""
    print_header(f"运行 {strategy_type} 策略优化")
    
    # 设置日志
    logger = setup_logger("mlquant_optimization")
    logger.info(f"开始优化 {strategy_type} 策略")
    
    try:
        # 生成数据
        generator = RandomDataGenerator()
        data = generator.generate(days=data_days)
        logger.info(f"生成了 {len(data)} 天的数据")
        
        # 初始化优化器
        optimizer = MLStrategyOptimizer()
        
        # 运行优化
        best_strategy = optimizer.optimize(data, strategy_type)
        logger.info(f"找到最优策略: {best_strategy}")
        
        print(f"✅ 策略优化完成: {best_strategy}")
        
    except Exception as e:
        logger.error(f"策略优化失败: {e}")
        raise


def list_strategies():
    """列出已生成的策略"""
    print_header("已生成的策略列表")
    
    manager = StrategyManager()
    strategies_df = manager.list_strategies()
    
    if strategies_df.empty:
        print("暂无已生成的策略")
    else:
        print(strategies_df.to_string(index=False))
        
    manager.print_summary()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MLQuant - 机器学习量化交易系统")
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 演示命令
    demo_parser = subparsers.add_parser('demo', help='运行演示程序')
    
    # 优化命令
    optimize_parser = subparsers.add_parser('optimize', help='运行策略优化')
    optimize_parser.add_argument('strategy', choices=['dual_ma', 'rsi', 'bollinger'],
                               help='策略类型')
    optimize_parser.add_argument('--days', type=int, default=1000,
                               help='数据天数 (默认: 1000)')
    
    # 列表命令
    list_parser = subparsers.add_parser('list', help='列出已生成的策略')
    
    # 配置命令
    config_parser = subparsers.add_parser('config', help='显示配置信息')
    
    args = parser.parse_args()
    
    if args.command == 'demo':
        run_demo()
    elif args.command == 'optimize':
        run_optimization(args.strategy, args.days)
    elif args.command == 'list':
        list_strategies()
    elif args.command == 'config':
        config = ConfigManager()
        print("当前配置:")
        import json
        print(json.dumps(config.config, indent=2, ensure_ascii=False))
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
