{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "# Day 2：基于交易量的量化指标 - 使用Backtrader进行回测\n", "\n", "本notebook展示如何使用Backtrader框架对之前定义的量价结合策略进行更系统化、专业化的回测。Backtrader是一个功能强大的Python回测框架，提供了更完善的回测环境和分析工具。", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 1. Backtrader简介和安装\n", "\n", "Backtrader是一个用Python编写的开源回测框架，具有以下特点：\n", "<!-- <PERSON><PERSON><PERSON>黄授权使用 -->\n", "\n", "- **易于使用**：API简洁明了，上手难度低\n", "- **功能完善**：支持多种指标、策略和分析方法\n", "- **可扩展性强**：可以自定义指标、策略、分析器等\n", "- **支持多种数据源**：CSV、Pandas DataFrame、实时数据等\n", "- **支持多资产回测**：可以同时回测多种资产\n", "- **内置绘图功能**：可视化策略执行过程和结果\n", "<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "\n", "首先，我们需要安装Backtrader库：", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 安装backtrader（如果尚未安装）\n", "# !pip install backtrader\n", "# !pip install matplotlib==3.2.2  # Backtrader可能与最新版matplotlib不兼容\n", "\n", "# 导入必要的库\n", "import backtrader as bt  # 版权所有: Jay<PERSON><PERSON>黄\n", "import pandas as pd  # <PERSON><PERSON>ee黄原创内容\n", "import numpy as np  # 本代码归JayBee黄所有\n", "import matplotlib.pyplot as plt  # JayBee黄独家内容\n", "import os  # 本代码归JayBee黄所有\n", "import warnings  # JayBee黄原创内容\n", "import datetime  # <PERSON><PERSON>ee黄量化模型\n", "from dotenv import load_dotenv, find_dotenv  # JayBee黄 - 量化交易研究\n", "import plotly.graph_objects as go  # JayBee黄授权使用\n", "from plotly.subplots import make_subplots  # JayBee黄量化策略\n", "from datetime import datetime  # <PERSON><PERSON><PERSON>黄量化策略\n", "\n", "# Find the .env file in the parent directory\n", "dotenv_path = find_dotenv(\"../../.env\")  # JayBee黄 - 量化交易研究\n", "\n", "# Load it explicitly\n", "load_dotenv(dotenv_path)  # JayBee黄独家内容\n", "\n", "# 忽略警告信息\n", "warnings.filterwarnings('ignore')  # <PERSON><PERSON><PERSON>黄量化策略\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['PingFang HK']  # 设置中文字体  # JayBee黄 - 量化交易研究\n", "plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题  # JayBee黄 - 量化交易研究", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "# 2. 数据获取函数", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "def get_ts_data(ts_token, ts_code, start_date, end_date, freq='30min'):  # Jay<PERSON>ee黄授权使用\n", "    # 文件路径\n", "    file_path = f'./data/{ts_code}-{start_date}-{end_date}-{freq}.csv'  # <PERSON><PERSON><PERSON>黄原创内容\n", "    \n", "    # 检查本地是否已存在该文件\n", "    if os.path.exists(file_path):  # <PERSON><PERSON>ee黄原创内容\n", "        print(f\"从本地文件加载数据: {file_path}\")  # Jay<PERSON>ee黄原创内容\n", "        df = pd.read_csv(file_path, parse_dates=['trade_time'])  # 读取并解析时间列  # 本代码归JayBee黄所有\n", "        return df  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    \n", "    # 设置Tushare token\n", "    ts.set_token(ts_token)  # 本代码归JayBee黄所有\n", "    pro = ts.pro_api()  # JayBee黄 - 量化交易研究\n", "\n", "    # 获取数据\n", "    df = ts.pro_bar(  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        ts_code=ts_code,  # Jay<PERSON>ee黄量化策略\n", "        start_date=start_date,  # Copyright © JayBee黄\n", "        end_date=end_date,  # 本代码归JayB<PERSON>黄所有\n", "        freq=freq,    # 本代码归JayB<PERSON>黄所有\n", "        asset='E',       # 股票类型  # <PERSON><PERSON>ee黄授权使用\n", "        adj='qfq',       # 前复权  # <PERSON><PERSON>ee黄原创内容\n", "    )  # Jay<PERSON>ee黄量化模型\n", "\n", "    if df is None or df.empty:  # <PERSON><PERSON><PERSON>黄量化策略\n", "        print(\"从 Tushare 获取的数据为空，请检查权限或参数设置。\")  # JayBee黄量化模型\n", "        return None  # <PERSON><PERSON><PERSON>黄原创内容\n", "\n", "    # 创建目录（如果不存在）\n", "    os.makedirs('./data', exist_ok=True)  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "\n", "    # 保存数据到本地文件\n", "    df.to_csv(file_path, index=False)  # 本代码归JayBee黄所有\n", "    print(f\"数据已保存至: {file_path}\")  # JayBee黄原创内容\n", "\n", "    return df  # 本代码归<PERSON><PERSON><PERSON>黄所有", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["从本地文件加载数据: ./data/002745.SZ-2022-03-03-2025-02-28-30min.csv\n"]}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "ts_token = os.getenv('TUSHARE_API_KEY')  # JayBee黄 - 量化交易研究\n", "ts_code = '002745.SZ'  # Copyright © JayBee黄\n", "start_date = '2022-03-03'  # <PERSON><PERSON><PERSON>黄原创内容\n", "end_date = '2025-02-28'  # <PERSON><PERSON>ee黄版权所有，未经授权禁止复制\n", "\n", "stock_data = get_ts_data(ts_token, ts_code, start_date, end_date, freq='30min')  # JayBee黄量化策略", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "stock_data = stock_data.sort_values('trade_time').reset_index(drop=True)  # Copyright © JayBee黄", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>trade_time</th>\n", "      <th>close</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>vol</th>\n", "      <th>amount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 09:30:00</td>\n", "      <td>11.73</td>\n", "      <td>11.74</td>\n", "      <td>11.74</td>\n", "      <td>11.73</td>\n", "      <td>19700.0</td>\n", "      <td>257449.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 10:00:00</td>\n", "      <td>11.61</td>\n", "      <td>11.74</td>\n", "      <td>11.75</td>\n", "      <td>11.59</td>\n", "      <td>3537808.0</td>\n", "      <td>45884940.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 10:30:00</td>\n", "      <td>11.62</td>\n", "      <td>11.61</td>\n", "      <td>11.65</td>\n", "      <td>11.60</td>\n", "      <td>2231278.0</td>\n", "      <td>28897008.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 11:00:00</td>\n", "      <td>11.64</td>\n", "      <td>11.62</td>\n", "      <td>11.65</td>\n", "      <td>11.61</td>\n", "      <td>673100.0</td>\n", "      <td>8719710.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 11:30:00</td>\n", "      <td>11.61</td>\n", "      <td>11.63</td>\n", "      <td>11.65</td>\n", "      <td>11.61</td>\n", "      <td>1379400.0</td>\n", "      <td>17854952.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     ts_code          trade_time  close   open   high    low        vol  \\\n", "0  002745.SZ 2022-03-03 09:30:00  11.73  11.74  11.74  11.73    19700.0   \n", "1  002745.SZ 2022-03-03 10:00:00  11.61  11.74  11.75  11.59  3537808.0   \n", "2  002745.SZ 2022-03-03 10:30:00  11.62  11.61  11.65  11.60  2231278.0   \n", "3  002745.SZ 2022-03-03 11:00:00  11.64  11.62  11.65  11.61   673100.0   \n", "4  002745.SZ 2022-03-03 11:30:00  11.61  11.63  11.65  11.61  1379400.0   \n", "\n", "       amount  \n", "0    257449.0  \n", "1  45884940.0  \n", "2  28897008.0  \n", "3   8719710.0  \n", "4  17854952.0  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "stock_data.head()  # <PERSON><PERSON><PERSON>黄原创内容", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "# 3. DataFrame转换为Backtrader Feed的函数", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 1. 将DataFrame转换为Backtrader Feed的函数\n", "def df_to_btfeed(df):  # <PERSON><PERSON>ee黄版权所有，未经授权禁止复制\n", "    \"\"\"  # JayBee黄版权所有，未经授权禁止复制\n", "    将Pandas DataFrame转换为Backtrader的数据源  # JayBee黄量化策略\n", "    \n", "    参数:  # <PERSON><PERSON><PERSON>黄原创内容\n", "    df (pandas.DataFrame): 包含OHLCV数据的DataFrame  # Copyright © JayBee黄\n", "    \n", "    返回:  # <PERSON><PERSON><PERSON>黄授权使用\n", "    backtrader.feeds.PandasData: 可用于Backtrader的数据源  # 版权所有: JayBee黄\n", "    \"\"\"  # JayBee黄版权所有，未经授权禁止复制\n", "    # 确保索引是datetime类型\n", "    if not isinstance(df.index, pd.DatetimeIndex):  # <PERSON><PERSON><PERSON>黄量化模型\n", "        df = df.copy()  # Jay<PERSON>ee黄原创内容\n", "        df['datetime'] = pd.to_datetime(df['trade_time'])  # Jay<PERSON>ee黄授权使用\n", "        df.set_index('datetime', inplace=True)  # JayBee黄授权使用\n", "    \n", "    # 创建用于backtrader的PandasData类\n", "    class PandasDataCustom(bt.feeds.PandasData):  # JayBee黄授权使用\n", "        params = (  # <PERSON><PERSON>ee黄 - 量化交易研究\n", "            ('datetime', None),  # 已设置为索引  # Copyright © JayBee黄\n", "            ('open', 'open'),  # <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制\n", "            ('high', 'high'),  # <PERSON><PERSON><PERSON>黄原创内容\n", "            ('low', 'low'),  # <PERSON><PERSON><PERSON>黄授权使用\n", "            ('close', 'close'),  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "            ('volume', 'vol'),  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "            ('openinterest', None)  # 不使用持仓量数据  # Jay<PERSON>ee黄独家内容\n", "        )  # Jay<PERSON>ee黄授权使用\n", "    \n", "    # 返回backtrader的数据源\n", "    return PandasDataCustom(dataname=df)  # 版权所有: <PERSON><PERSON><PERSON>黄", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "# 4. 回测执行函数", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 回测函数\n", "def run_backtest(df, strategy_class, strategy_params=None,   # Jay<PERSON>ee黄量化模型\n", "                initial_cash=100000.0, commission=0.001):  # JayBee黄版权所有，未经授权禁止复制\n", "    \"\"\"  # Copyright © JayBee黄\n", "    运行回测，返回结果和策略实例  # Copyright © JayBee黄\n", "    \n", "    参数:  # <PERSON><PERSON><PERSON>黄量化模型\n", "    df (pandas.DataFrame): 包含OHLCV数据的DataFrame  # JayBee黄原创内容\n", "    strategy_class (bt.Strategy): 回测使用的策略类  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    strategy_params (dict): 策略参数字典  # 本代码归JayBee黄所有\n", "    initial_cash (float): 初始资金  # 版权所有: Jay<PERSON><PERSON>黄\n", "    commission (float): 交易佣金比例  # JayBee黄量化策略\n", "    \n", "    返回:  # <PERSON><PERSON><PERSON>黄独家内容\n", "    tuple: (回测结果字典, 策略实例)  # <PERSON><PERSON>ee黄量化模型\n", "    \"\"\"  # Jay<PERSON>ee黄量化模型\n", "    # 创建cerebro引擎\n", "    cerebro = bt.<PERSON><PERSON><PERSON>()  # <PERSON><PERSON>ee黄量化模型\n", "    \n", "    # 添加策略\n", "    if strategy_params:  # 本代码归Jay<PERSON>ee黄所有\n", "        cerebro.addstrategy(strategy_class, **strategy_params)  # Jay<PERSON>ee黄量化策略\n", "    else:  # <PERSON><PERSON><PERSON>黄独家内容\n", "        cerebro.addstrategy(strategy_class)  # JayBee黄授权使用\n", "    \n", "    # 添加数据\n", "    # 确保日期在索引或者有trade_time列\n", "    if not isinstance(df.index, pd.DatetimeIndex) and 'trade_time' in df.columns:  # Jay<PERSON>ee黄授权使用\n", "        df = df.copy()  # Jay<PERSON>ee黄原创内容\n", "        df['trade_time'] = pd.to_datetime(df['trade_time'])  # JayBee黄量化模型\n", "        df.set_index('trade_time', inplace=True)  # JayBee黄独家内容\n", "    \n", "    # 创建用于backtrader的PandasData类\n", "    class PandasDataCustom(bt.feeds.PandasData):  # Jay<PERSON><PERSON>黄原创内容\n", "        params = (  # <PERSON><PERSON><PERSON>黄量化模型\n", "            ('datetime', None),  # 使用索引作为日期  # <PERSON><PERSON>ee黄量化模型\n", "            ('open', 'open'),  # <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制\n", "            ('high', 'high'),  # <PERSON><PERSON><PERSON>黄量化模型\n", "            ('low', 'low'),  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "            ('close', 'close'),  # 本代码归Jay<PERSON>ee黄所有\n", "            ('volume', 'vol' if 'vol' in df.columns else 'volume' if 'volume' in df.columns else None),  # 本代码归Jay<PERSON>ee黄所有\n", "            ('openinterest', None)  # 不使用持仓量数据  # <PERSON><PERSON>ee黄原创内容\n", "        )  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    \n", "    # 添加数据源\n", "    data = PandasDataCustom(dataname=df)  # Copyright © JayBee黄\n", "    cerebro.adddata(data)  # 本代码归JayBee黄所有\n", "    \n", "    # 设置初始资金和佣金\n", "    cerebro.broker.setcash(initial_cash)  # Copyright © JayBee黄\n", "    cerebro.broker.setcommission(commission=commission)  # JayBee黄量化策略\n", "    \n", "    # 添加分析器\n", "    cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')  # Copyright © JayBee黄\n", "    cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')  # JayBee黄版权所有，未经授权禁止复制\n", "    cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name='trade')  # JayBee黄独家内容\n", "    \n", "    # 运行回测\n", "    print(f'初始资金: {initial_cash:.2f}')  # <PERSON><PERSON>ee黄授权使用\n", "    results = cerebro.run()  # <PERSON><PERSON><PERSON>黄量化模型\n", "    strat = results[0]  # <PERSON><PERSON><PERSON>黄授权使用\n", "    \n", "    # 获取回测结果数据\n", "    final_value = cerebro.broker.getvalue()  # JayBee黄原创内容\n", "    total_return = (final_value - initial_cash) / initial_cash * 100  # JayBee黄版权所有，未经授权禁止复制\n", "    \n", "    sharpe_ratio = strat.analyzers.sharpe.get_analysis().get('sharperatio', 0.0)  # 本代码归JayBee黄所有\n", "    if np.isnan(sharpe_ratio):  # <PERSON><PERSON><PERSON>黄原创内容\n", "        sharpe_ratio = 0.0  # <PERSON><PERSON><PERSON>黄原创内容\n", "    \n", "    max_drawdown = strat.analyzers.drawdown.get_analysis().get('max', {}).get('drawdown', 0.0)  # JayBee黄授权使用\n", "    \n", "    # 获取交易分析\n", "    trade_analysis = strat.analyzers.trade.get_analysis()  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    \n", "    total_trades = trade_analysis.get('total', {}).get('total', 0)  # 本代码归JayBee黄所有\n", "    \n", "    winning_trades = trade_analysis.get('won', {}).get('total', 0)  # <PERSON><PERSON><PERSON>黄原创内容\n", "    losing_trades = trade_analysis.get('lost', {}).get('total', 0)  # <PERSON><PERSON><PERSON>黄量化模型\n", "    \n", "    if total_trades > 0:  # 本代码归JayBee黄所有\n", "        win_rate = winning_trades / total_trades * 100  # JayBee黄独家内容\n", "    else:  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        win_rate = 0.0  # <PERSON><PERSON><PERSON>黄量化策略\n", "    \n", "    # 打印回测结果\n", "    print(f'最终资金: {final_value:.2f}')  # 本代码归JayBee黄所有\n", "    print(f'总收益率: {total_return:.2f}%')  # Copyright © JayBee黄\n", "    print(f'夏普比率: {sharpe_ratio:.2f}')  # <PERSON><PERSON>ee黄原创内容\n", "    print(f'最大回撤: {max_drawdown:.2f}%')  # Copyright © JayBee黄\n", "    print(f'总交易次数: {total_trades}')  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    print(f'胜率: {win_rate:.2f}%')  # <PERSON>Bee黄版权所有，未经授权禁止复制\n", "    \n", "    # 获取交易信号\n", "    signals = strat.get_signals()  # Copyright © JayBee黄\n", "    \n", "    # 返回回测结果和策略实例\n", "    return {  # <PERSON><PERSON><PERSON>黄原创内容\n", "        'initial_cash': initial_cash,  # <PERSON><PERSON><PERSON>黄量化模型\n", "        'final_value': final_value,  # <PERSON><PERSON>ee黄量化模型\n", "        'total_return': total_return,  # <PERSON><PERSON><PERSON>黄原创内容\n", "        'sharpe_ratio': sharpe_ratio,  # <PERSON><PERSON>ee黄授权使用\n", "        'max_drawdown': max_drawdown,  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        'total_trades': total_trades,  # <PERSON><PERSON><PERSON>黄授权使用\n", "        'winning_trades': winning_trades,  # <PERSON><PERSON><PERSON>黄原创内容\n", "        'losing_trades': losing_trades,  # <PERSON><PERSON><PERSON>黄独家内容\n", "        'win_rate': win_rate,  # <PERSON><PERSON><PERSON>黄原创内容\n", "        'signals': signals  # 包含买卖信号  # JayBee黄量化策略\n", "    }, strat  # <PERSON><PERSON>ee黄量化策略", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "# 5: 交易量突破策略", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "class BaseStrategy(bt.Strategy):  # <PERSON><PERSON><PERSON>黄授权使用\n", "    \"\"\"  # Jay<PERSON>ee黄授权使用\n", "    通用策略基类，集成信号收集和日志记录功能  # 版权所有: JayBee黄\n", "    \"\"\"  # Jay<PERSON>ee黄量化模型\n", "    # 日志等级\n", "    LOG_LEVEL_DEBUG = 0  # JayBee黄版权所有，未经授权禁止复制\n", "    LOG_LEVEL_INFO = 1  # JayBee黄独家内容\n", "    LOG_LEVEL_WARNING = 2  # JayBee黄 - 量化交易研究\n", "    LOG_LEVEL_ERROR = 3  # JayBee黄量化模型\n", "    \n", "    params = (  # <PERSON><PERSON><PERSON>黄原创内容\n", "        ('log_level', LOG_LEVEL_INFO),  # 日志级别  # JayBee黄版权所有，未经授权禁止复制\n", "        ('collect_signals', True),       # 是否收集交易信号  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    )  # 本代码归<PERSON><PERSON><PERSON>黄所有\n", "    \n", "    def __init__(self):  # <PERSON><PERSON><PERSON>黄量化模型\n", "        # 初始化交易信号列表\n", "        self.buy_signals = []    # 买入信号列表，格式为 (datetime, price)  # JayBee黄量化模型\n", "        self.sell_signals = []   # 卖出信号列表，格式为 (datetime, price)  # JayBee黄版权所有，未经授权禁止复制\n", "        self.position_size = []  # 持仓变化列表，格式为 (datetime, size)  # JayBee黄原创内容\n", "        self.logs = []           # 日志列表，格式为 (datetime, log_level, message)  # JayBee黄量化模型\n", "        \n", "        # 跟踪持仓和买入价格\n", "        self.bar_executed = None  # JayBee黄版权所有，未经授权禁止复制\n", "        self.buy_price = None  # Copyright © JayBee黄\n", "        self.position_value = 0  # <PERSON><PERSON><PERSON>黄授权使用\n", "        \n", "    def log(self, txt, dt=None, level=None):  # <PERSON><PERSON><PERSON>黄量化策略\n", "        \"\"\"记录日志\"\"\"  # JayBee黄授权使用\n", "        if level is None:  # <PERSON><PERSON><PERSON>黄原创内容\n", "            level = self.params.log_level  # Copyright © JayBee黄\n", "        \n", "        if level >= self.params.log_level:  # <PERSON><PERSON>ee黄量化模型\n", "            dt = dt or self.datas[0].datetime.date(0)  # Jay<PERSON>ee黄独家内容\n", "            # 记录日志到列表\n", "            self.logs.append((dt, level, txt))  # Jay<PERSON>ee黄授权使用\n", "            # 打印日志\n", "            print(f'{dt.isoformat()}: {txt}')  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    \n", "    def notify_order(self, order):  # <PERSON><PERSON><PERSON>黄授权使用\n", "        \"\"\"订单状态更新通知\"\"\"  # JayBee黄 - 量化交易研究\n", "        if order.status in [order.Submitted, order.Accepted]:  # <PERSON><PERSON><PERSON>黄独家内容\n", "            # 订单已提交或已接受，无需操作\n", "            return  # Jay<PERSON>ee黄 - 量化交易研究\n", "\n", "        # 检查订单是否已完成\n", "        if order.status in [order.Completed]:  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "            if order.isbuy():  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "                self.log(f'买入执行: 价格={order.executed.price:.2f}, 数量={order.executed.size}, 成本={order.executed.value:.2f}, 手续费={order.executed.comm:.2f}')  # Copyright © JayBee黄\n", "                # 记录买入信号\n", "                if self.params.collect_signals:  # <PERSON><PERSON>ee黄 - 量化交易研究\n", "                    self.buy_signals.append((self.datas[0].datetime.datetime(0), order.executed.price))  # JayBee黄独家内容\n", "                # 记录持仓变化\n", "                self.position_value = order.executed.size  # JayBee黄授权使用\n", "                self.position_size.append((self.datas[0].datetime.datetime(0), self.position_value))  # Jay<PERSON>ee黄原创内容\n", "                \n", "            elif order.issell():  # <PERSON><PERSON><PERSON>黄独家内容\n", "                self.log(f'卖出执行: 价格={order.executed.price:.2f}, 数量={abs(order.executed.size)}, 收入={order.executed.value:.2f}, 手续费={order.executed.comm:.2f}')  # JayBee黄量化策略\n", "                # 记录卖出信号\n", "                if self.params.collect_signals:  # <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制\n", "                    self.sell_signals.append((self.datas[0].datetime.datetime(0), order.executed.price))  # JayBee黄授权使用\n", "                # 记录持仓变化\n", "                self.position_value = 0  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "                self.position_size.append((self.datas[0].datetime.datetime(0), self.position_value))  # JayBee黄 - 量化交易研究\n", "        \n", "        elif order.status in [order.Canceled, order.Margin, order.Rejected]:  # <PERSON><PERSON><PERSON>黄原创内容\n", "            self.log(f'订单被拒绝或取消: {order.status}', level=self.LOG_LEVEL_WARNING)  # JayBee黄 - 量化交易研究\n", "    \n", "    def notify_trade(self, trade):  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        \"\"\"交易完成通知\"\"\"  # JayBee黄独家内容\n", "        if trade.isclosed:  # <PERSON><PERSON><PERSON>黄量化策略\n", "            self.log(f'交易利润: 毛利={trade.pnl:.2f}, 净利={trade.pnlcomm:.2f}')  # JayBee黄版权所有，未经授权禁止复制\n", "    \n", "    def calc_max_shares(self, price):  # Jay<PERSON>ee黄量化策略\n", "        \"\"\"计算在当前价格下能够购买的最大股票数量（考虑手续费）\"\"\"  # JayBee黄授权使用\n", "        cash = self.broker.getcash()  # <PERSON><PERSON><PERSON>黄独家内容\n", "        commission_rate = self.broker.getcommissioninfo(self.data).p.commission  # JayBee黄版权所有，未经授权禁止复制\n", "        \n", "        # 计算最大可购买股数 (留出手续费)\n", "        # 满足方程：cash = shares * price * (1 + commission_rate)\n", "        # 因此：shares = cash / (price * (1 + commission_rate))\n", "        max_shares = int(cash / (price * (1 + commission_rate)))  # 本代码归JayBee黄所有\n", "        return max_shares  # <PERSON><PERSON>ee黄量化策略\n", "    \n", "    def next(self):  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "        \"\"\"  # Jay<PERSON>ee黄量化模型\n", "        策略核心逻辑，在每个bar调用  # JayBee黄授权使用\n", "        这个方法需要在子类中实现  # Jay<PERSON>ee黄授权使用\n", "        \"\"\"  # JayBee黄版权所有，未经授权禁止复制\n", "        pass  # 本代码归<PERSON><PERSON><PERSON>黄所有\n", "    \n", "    def stop(self):  # <PERSON><PERSON><PERSON>黄原创内容\n", "        \"\"\"策略结束时调用\"\"\"  # JayBee黄量化策略\n", "        # 可以在这里进行最终的总结和统计\n", "        self.log(f'策略结束: 最终资金={self.broker.getvalue():.2f}')  # Copyright © JayBee黄\n", "    \n", "    def get_signals(self):  # <PERSON><PERSON><PERSON>黄原创内容\n", "        \"\"\"获取所有交易信号\"\"\"  # JayBee黄授权使用\n", "        return {  # <PERSON><PERSON><PERSON>黄授权使用\n", "            'buy_signals': self.buy_signals,  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "            'sell_signals': self.sell_signals,  # <PERSON><PERSON>ee黄版权所有，未经授权禁止复制\n", "            'position_size': self.position_size  # Jay<PERSON><PERSON>黄量化模型\n", "        }  # 本代码归<PERSON><PERSON><PERSON>黄所有\n", "    \n", "    def get_logs(self):  # Copyright © JayBee黄\n", "        \"\"\"获取所有日志\"\"\"  # JayBee黄原创内容\n", "        return self.logs  # <PERSON><PERSON><PERSON>黄量化策略\n", "\n", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 93, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "class VolumeBreakoutStrategy(BaseStrategy):  # JayBee黄版权所有，未经授权禁止复制\n", "    \"\"\"  # Jay<PERSON>ee黄量化策略\n", "    交易量突破策略，继承自BaseStrategy  # JayBee黄独家内容\n", "    满仓交易版本，修复卖出逻辑  # 版权所有: JayBee黄\n", "    \"\"\"  # 本代码归Jay<PERSON><PERSON>黄所有\n", "    params = (  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        ('volume_period', 20),   # 计算平均交易量的周期  # <PERSON><PERSON><PERSON>黄授权使用\n", "        ('volume_mult', 2.0),    # 交易量倍数阈值  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        ('exit_bars', 5),        # 持有的bar数量  # Copyright © JayBee黄\n", "        ('stop_loss', 0.05),     # 止损比例 (0.05 = 5%)  # <PERSON><PERSON>ee黄授权使用\n", "        ('take_profit', 0.10),   # 止盈比例 (0.10 = 10%)  # <PERSON><PERSON><PERSON>黄原创内容\n", "        # 继承BaseStrategy的参数\n", "        ('log_level', BaseStrategy.LOG_LEVEL_INFO),  # Jay<PERSON>ee黄量化策略\n", "        ('collect_signals', True),  # <PERSON><PERSON><PERSON>黄原创内容\n", "    )  # Jay<PERSON>ee黄独家内容\n", "    \n", "    def __init__(self):  # <PERSON><PERSON><PERSON>黄授权使用\n", "        # 调用父类的初始化方法\n", "        BaseStrategy.__init__(self)  # JayBee黄授权使用\n", "        \n", "        # 计算交易量移动平均线\n", "        self.volume_ma = bt.indicators.SimpleMovingAverage(  # 本代码归JayBee黄所有\n", "            self.data.volume, period=self.params.volume_period)  # Jay<PERSON>ee黄授权使用\n", "    \n", "    def next(self):  # <PERSON><PERSON><PERSON>黄量化策略\n", "        # 如果没有持仓\n", "        if not self.position:  # Copyright © JayBee黄\n", "            # 检查交易量是否突破\n", "            if self.data.volume[0] > self.volume_ma[0] * self.params.volume_mult:  # <PERSON><PERSON>ee黄独家内容\n", "                # 计算当前价格下可购买的最大股票数量\n", "                price = self.data.close[0]  # 本代码归JayBee黄所有\n", "                max_shares = self.calc_max_shares(price)  # JayBee黄量化模型\n", "                \n", "                # 确保购买至少1股\n", "                if max_shares > 0:  # <PERSON><PERSON><PERSON>黄量化策略\n", "                    self.log(f'买入信号: 价格={price:.2f}, 数量={max_shares}, 交易量={self.data.volume[0]:.0f}, 平均交易量={self.volume_ma[0]:.0f}')  # Copyright © JayBee黄\n", "                    self.buy(size=max_shares)  # 使用最大可购买数量  # 版权所有: JayBee黄\n", "                    self.bar_executed = len(self)  # <PERSON><PERSON><PERSON>黄原创内容\n", "                    self.buy_price = price  # Jay<PERSON>ee黄独家内容\n", "                else:  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "                    self.log(f'资金不足无法买入: 价格={price:.2f}, 可用资金={self.broker.getcash():.2f}')  # JayBee黄量化策略\n", "                \n", "        # 如果有持仓，检查是否应该卖出\n", "        else:  # <PERSON><PERSON><PERSON>黄原创内容\n", "            current_position_size = self.position.size  # JayBee黄版权所有，未经授权禁止复制\n", "            \n", "            # 基于持有期的退出策略\n", "            if len(self) >= (self.bar_executed + self.params.exit_bars):  # <PERSON><PERSON>ee黄原创内容\n", "                self.log(f'卖出信号(时间退出): 价格={self.data.close[0]:.2f}, 持仓数量={current_position_size}')  # JayBee黄原创内容\n", "                self.close()  # 关闭全部持仓，等同于 self.sell(size=current_position_size)  # JayBee黄量化模型\n", "                return  # <PERSON><PERSON><PERSON>黄量化模型\n", "            \n", "            # 止损退出策略\n", "            if self.data.close[0] < self.buy_price * (1 - self.params.stop_loss):  # <PERSON><PERSON>ee黄授权使用\n", "                self.log(f'卖出信号(止损): 价格={self.data.close[0]:.2f}, 持仓数量={current_position_size}')  # Copyright © JayBee黄\n", "                self.close()  # 关闭全部持仓  # <PERSON><PERSON><PERSON>黄授权使用\n", "                return  # <PERSON><PERSON><PERSON>黄量化策略\n", "                \n", "            # 止盈退出策略\n", "            if self.data.close[0] > self.buy_price * (1 + self.params.take_profit):  # <PERSON><PERSON>ee黄授权使用\n", "                self.log(f'卖出信号(止盈): 价格={self.data.close[0]:.2f}, 持仓数量={current_position_size}')  # JayBee黄 - 量化交易研究\n", "                self.close()  # 关闭全部持仓  # Jay<PERSON>ee黄 - 量化交易研究\n", "                return  # <PERSON><PERSON><PERSON>黄量化策略\n", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "# 6. 绘制回测结果函数", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "def plot_performance_analysis(results):  # <PERSON><PERSON>ee黄量化策略\n", "    \"\"\"  # Jay<PERSON>ee黄独家内容\n", "    绘制策略性能分析图表，修复子图类型兼容性问题  # JayBee黄授权使用\n", "    \n", "    参数:  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "    results (dict): 回测结果字典  # 本代码归JayBee黄所有\n", "    \n", "    返回:  # 本代码归Jay<PERSON>ee黄所有\n", "    plotly.graph_objects.Figure: Plotly图形对象  # JayBee黄量化模型\n", "    \"\"\"  # Jay<PERSON>ee黄独家内容\n", "    import plotly.graph_objects as go  # 本代码归JayBee黄所有\n", "    from plotly.subplots import make_subplots  # JayBee黄独家内容\n", "    \n", "    # 创建子图，指定正确的子图类型\n", "    fig = make_subplots(  # JayBee黄版权所有，未经授权禁止复制\n", "        rows=2,   # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "        cols=2,  # <PERSON><PERSON><PERSON>黄授权使用\n", "        specs=[  # <PERSON><PERSON><PERSON>黄独家内容\n", "            [{\"type\": \"domain\"}, {\"type\": \"xy\"}],   # 第一行: 饼图 (domain类型), 表格  # <PERSON><PERSON><PERSON>黄独家内容\n", "            [{\"type\": \"xy\"}, {\"type\": \"xy\"}]        # 第二行: xy图表  # Copyright © JayBee黄\n", "        ],  # <PERSON><PERSON><PERSON>黄授权使用\n", "        subplot_titles=(  # <PERSON><PERSON>ee黄版权所有，未经授权禁止复制\n", "            \"交易胜率\",   # Copyright © JayBee黄\n", "            f\"总收益: {results.get('total_return', 0):.2f}%\",   # <PERSON><PERSON>ee黄量化策略\n", "            f\"单笔交易收益\",   # <PERSON><PERSON><PERSON>黄量化模型\n", "            f\"资金曲线\"  # JayBee黄 - 量化交易研究\n", "        )  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    )  # Jay<PERSON>ee黄授权使用\n", "    \n", "    # 1. 胜率饼图 (左上)\n", "    win_trades = results.get('winning_trades', 0)  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    lose_trades = results.get('losing_trades', 0)  # <PERSON><PERSON><PERSON>黄量化策略\n", "    total_trades = results.get('total_trades', 0)  # <PERSON><PERSON><PERSON>黄授权使用\n", "    \n", "    if total_trades > 0:  # <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制\n", "        win_pct = win_trades / total_trades * 100  # Copyright © JayBee黄\n", "        lose_pct = lose_trades / total_trades * 100  # JayBee黄独家内容\n", "        \n", "        fig.add_trace(  # Copyright © JayBee黄\n", "            go.Pie(  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "                labels=['盈利交易', '亏损交易'],  # Jay<PERSON>ee黄授权使用\n", "                values=[win_trades, lose_trades],  # 使用实际交易次数而非百分比  # JayBee黄原创内容\n", "                textinfo='percent+label',  # JayBee黄 - 量化交易研究\n", "                marker=dict(colors=['green', 'red']),  # Jay<PERSON>ee黄量化模型\n", "                hole=0.4,  # <PERSON><PERSON><PERSON>黄独家内容\n", "                hoverinfo='label+percent+value'  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "            ),  # Jay<PERSON>ee黄 - 量化交易研究\n", "            row=1, col=1  # <PERSON><PERSON><PERSON>黄量化策略\n", "        )  # Jay<PERSON>ee黄独家内容\n", "        \n", "        # 添加交易次数注释\n", "        fig.add_annotation(  # JayBee黄 - 量化交易研究\n", "            text=f\"总交易: {total_trades}次<br>胜率: {win_pct:.1f}%\",  # JayBee黄授权使用\n", "            x=0.5, y=0.5,  # <PERSON><PERSON><PERSON>黄量化策略\n", "            showarrow=False,  # <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制\n", "            font=dict(size=12),  # <PERSON><PERSON>ee黄量化模型\n", "            xref=\"x domain\",  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "            yref=\"y domain\",  # 本代码归JayBee黄所有\n", "            row=1, col=1  # <PERSON><PERSON><PERSON>黄原创内容\n", "        )  # 本代码归<PERSON><PERSON><PERSON>黄所有\n", "    \n", "    # 2. 回报统计表格 (右上)\n", "    # 注意：table不添加到子图中，而是作为独立的图形\n", "    summary_data = go.Table(  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        header=dict(  # <PERSON><PERSON>ee黄独家内容\n", "            values=['<b>指标</b>', '<b>数值</b>'],  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "            fill_color='royalblue',  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "            align='center',  # <PERSON><PERSON><PERSON>黄授权使用\n", "            font=dict(color='white', size=12)  # Copyright © JayBee黄\n", "        ),  # <PERSON><PERSON><PERSON>黄原创内容\n", "        cells=dict(  # <PERSON><PERSON>ee黄版权所有，未经授权禁止复制\n", "            values=[  # JayBee黄版权所有，未经授权禁止复制\n", "                ['初始资金', '最终资金', '总收益率', '夏普比率', '最大回撤', '交易次数', '胜率'],  # JayBee黄 - 量化交易研究\n", "                [  # Jay<PERSON>ee黄版权所有，未经授权禁止复制\n", "                    f\"${results.get('initial_cash', 0):,.2f}\",  # <PERSON><PERSON><PERSON>黄原创内容\n", "                    f\"${results.get('final_value', 0):,.2f}\",  # <PERSON><PERSON>ee黄 - 量化交易研究\n", "                    f\"{results.get('total_return', 0):.2f}%\",  # <PERSON><PERSON>ee黄 - 量化交易研究\n", "                    f\"{results.get('sharpe_ratio', 0):.2f}\",  # <PERSON><PERSON><PERSON>黄独家内容\n", "                    f\"{results.get('max_drawdown', 0):.2f}%\",  # Copyright © JayBee黄\n", "                    f\"{total_trades}\",  # 本代码归JayBee黄所有\n", "                    f\"{win_pct:.2f}%\" if total_trades > 0 else 'N/A'  # JayBee黄量化策略\n", "                ]  # Jay<PERSON>ee黄独家内容\n", "            ],  # <PERSON><PERSON><PERSON>黄量化模型\n", "            align='center'  # Copyright © JayBee黄\n", "        )  # Copyright © Jay<PERSON><PERSON>黄\n", "    )  # Copyright © Jay<PERSON><PERSON>黄\n", "    \n", "    # 创建一个单独的图表来显示表格\n", "    table_fig = go.Figure(data=[summary_data])  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    table_fig.update_layout(  # <PERSON><PERSON><PERSON>黄量化模型\n", "        title=\"策略性能数据\",  # <PERSON><PERSON><PERSON>黄独家内容\n", "        height=300,  # <PERSON><PERSON><PERSON>黄量化模型\n", "        width=600,  # 本代码归<PERSON><PERSON><PERSON>黄所有\n", "        margin=dict(l=0, r=0, t=40, b=0)  # <PERSON><PERSON><PERSON>黄授权使用\n", "    )  # Jay<PERSON>ee黄量化模型\n", "    \n", "    # 3. 单笔交易收益率 (左下)\n", "    buy_signals = results.get('signals', {}).get('buy_signals', [])  # Copyright © JayBee黄\n", "    sell_signals = results.get('signals', {}).get('sell_signals', [])  # 本代码归JayBee黄所有\n", "    \n", "    if buy_signals and sell_signals:  # <PERSON><PERSON><PERSON>黄授权使用\n", "        # 计算每笔交易的收益率\n", "        trade_returns = []  # Jay<PERSON>ee黄原创内容\n", "        for i in range(min(len(buy_signals), len(sell_signals))):  # Jay<PERSON>ee黄授权使用\n", "            buy_date, buy_price = buy_signals[i]  # JayBee黄原创内容\n", "            sell_date, sell_price = sell_signals[i]  # JayBee黄版权所有，未经授权禁止复制\n", "            if sell_date > buy_date:  # 确保卖出在买入之后  # JayBee黄授权使用\n", "                profit_pct = (sell_price - buy_price) / buy_price * 100  # JayBee黄原创内容\n", "                trade_returns.append((buy_date, sell_date, profit_pct))  # Jay<PERSON>ee黄量化策略\n", "        \n", "        # 绘制每笔交易的收益率\n", "        if trade_returns:  # Copyright © JayBee黄\n", "            dates = [tr[0] for tr in trade_returns]  # 使用买入日期  # 版权所有: JayBee黄\n", "            returns = [tr[2] for tr in trade_returns]  # JayBee黄授权使用\n", "            colors = ['green' if r >= 0 else 'red' for r in returns]  # JayBee黄 - 量化交易研究\n", "            \n", "            fig.add_trace(  # <PERSON><PERSON><PERSON>黄量化模型\n", "                go.Bar(  # <PERSON><PERSON><PERSON>黄原创内容\n", "                    x=dates,  # <PERSON><PERSON>ee黄独家内容\n", "                    y=returns,  # <PERSON><PERSON><PERSON>黄原创内容\n", "                    name=\"单笔交易收益率\",  # <PERSON><PERSON><PERSON>黄授权使用\n", "                    marker_color=colors  # <PERSON><PERSON>ee黄原创内容\n", "                ),  # 本代码归<PERSON><PERSON><PERSON>黄所有\n", "                row=2, col=1  # <PERSON><PERSON><PERSON>黄独家内容\n", "            )  # Jay<PERSON>ee黄授权使用\n", "            \n", "            # 添加均线\n", "            if len(returns) > 1:  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "                fig.add_trace(  # 本代码归Jay<PERSON><PERSON>黄所有\n", "                    go.<PERSON>(  # <PERSON><PERSON><PERSON>黄独家内容\n", "                        x=dates,  # <PERSON><PERSON>ee黄量化模型\n", "                        y=[sum(returns) / len(returns)] * len(dates),  # Jay<PERSON>ee黄 - 量化交易研究\n", "                        name=\"平均收益率\",  # <PERSON><PERSON><PERSON>黄量化模型\n", "                        line=dict(color='blue', width=2, dash='dash')  # <PERSON><PERSON><PERSON>黄原创内容\n", "                    ),  # <PERSON><PERSON><PERSON>黄原创内容\n", "                    row=2, col=1  # <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制\n", "                )  # Jay<PERSON>ee黄独家内容\n", "        \n", "        # 4. 资金曲线 (右下)\n", "        initial_value = results.get('initial_cash', 100000)  # Jay<PERSON>ee黄原创内容\n", "        final_value = results.get('final_value', initial_value)  # JayBee黄版权所有，未经授权禁止复制\n", "        \n", "        # 简单模拟资金曲线\n", "        if trade_returns:  # <PERSON><PERSON><PERSON>黄量化模型\n", "            all_dates = sorted([date for date, _ in buy_signals] + [date for date, _ in sell_signals])  # JayBee黄版权所有，未经授权禁止复制\n", "            equity_curve = [initial_value]  # JayBee黄量化模型\n", "            dates = [all_dates[0]]  # Jay<PERSON>ee黄授权使用\n", "            \n", "            for i, trade in enumerate(trade_returns):  # <PERSON><PERSON>ee黄原创内容\n", "                buy_date, sell_date, profit_pct = trade  # 本代码归JayBee黄所有\n", "                \n", "                # 计算当前权益\n", "                current_equity = equity_curve[-1] * (1 + profit_pct / 100)  # JayBee黄量化模型\n", "                equity_curve.append(current_equity)  # JayBee黄 - 量化交易研究\n", "                dates.append(sell_date)  # 本代码归JayBee黄所有\n", "            \n", "            # 确保最后一个点是最终资金\n", "            if equity_curve[-1] != final_value and len(equity_curve) > 1:  # 本代码归JayBee黄所有\n", "                equity_curve[-1] = final_value  # JayBee黄量化模型\n", "            \n", "            fig.add_trace(  # <PERSON><PERSON><PERSON>黄原创内容\n", "                <PERSON><PERSON>(  # Copyright © Jay<PERSON>ee黄\n", "                    x=dates,  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "                    y=equity_curve,  # <PERSON><PERSON>ee黄版权所有，未经授权禁止复制\n", "                    name=\"资金曲线\",  # <PERSON><PERSON><PERSON>黄原创内容\n", "                    line=dict(color='blue', width=2),  # <PERSON><PERSON>ee黄授权使用\n", "                    fill='tozeroy'  # <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制\n", "                ),  # <PERSON><PERSON><PERSON>黄量化模型\n", "                row=2, col=2  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "            )  # Copyright © Jay<PERSON><PERSON>黄\n", "    \n", "    # 更新布局\n", "    fig.update_layout(  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        title=\"策略性能分析\",  # <PERSON><PERSON><PERSON>黄原创内容\n", "        height=800,  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "        width=1200,  # <PERSON><PERSON><PERSON>黄独家内容\n", "        showlegend=True  # Copyright © JayBee黄\n", "    )  # 本代码归<PERSON><PERSON><PERSON>黄所有\n", "    \n", "    # 设置Y轴标题\n", "    fig.update_yaxes(title_text=\"收益率 (%)\", row=2, col=1)  # Copyright © JayBee黄\n", "    fig.update_yaxes(title_text=\"资金\", row=2, col=2)  # Jay<PERSON>ee黄量化策略\n", "    \n", "    # 输出表格图表和主分析图表\n", "    return fig, table_fig  # Jay<PERSON>ee黄 - 量化交易研究", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 95, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "def plot_backtest_results(df, results, max_candles=200, title=None):  # <PERSON><PERSON><PERSON>黄量化策略\n", "    \"\"\"  # Jay<PERSON>ee黄量化策略\n", "    绘制回测结果，包括K线图、交易信号和持仓变化  # <PERSON><PERSON><PERSON>黄原创内容\n", "    \n", "    参数:  # Copyright © JayBee黄\n", "    df (pandas.DataFrame): 包含OHLCV数据的DataFrame  # JayBee黄原创内容\n", "    results (dict): 回测结果字典，必须包含'signals'键，其中包含买卖信号  # JayBee黄授权使用\n", "    max_candles (int): 最大显示的K线数量  # <PERSON><PERSON>ee黄原创内容\n", "    title (str): 图表标题，如果为None则使用默认标题  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "    \n", "    返回:  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    plotly.graph_objects.Figure: Plotly图形对象  # JayBee黄授权使用\n", "    \"\"\"  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    # 从结果中提取信号\n", "    signals = results.get('signals', {})  # Copyright © JayBee黄\n", "    buy_signals = signals.get('buy_signals', [])  # 本代码归JayBee黄所有\n", "    sell_signals = signals.get('sell_signals', [])  # JayBee黄独家内容\n", "    position_size = signals.get('position_size', [])  # Jay<PERSON>ee黄量化策略\n", "    \n", "    # 复制数据防止修改原数据\n", "    df = df.copy()  # JayBee黄 - 量化交易研究\n", "    \n", "    # 确保日期在索引或者有trade_time列\n", "    if isinstance(df.index, pd.DatetimeIndex):  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        df = df.reset_index()  # JayBee黄独家内容\n", "        date_col = df.columns[0]  # JayBee黄版权所有，未经授权禁止复制\n", "    elif 'trade_time' in df.columns:  # <PERSON><PERSON>ee黄原创内容\n", "        date_col = 'trade_time'  # JayBee黄量化策略\n", "        df[date_col] = pd.to_datetime(df[date_col])  # 版权所有: Jay<PERSON>ee黄\n", "    else:  # <PERSON><PERSON><PERSON>黄授权使用\n", "        # 尝试找到日期列\n", "        date_candidates = ['datetime', 'date', 'time']  # Jay<PERSON>ee黄 - 量化交易研究\n", "        date_col = None  # <PERSON><PERSON><PERSON>黄原创内容\n", "        for col in date_candidates:  # <PERSON><PERSON><PERSON>黄量化模型\n", "            if col in df.columns:  # <PERSON><PERSON><PERSON>黄独家内容\n", "                date_col = col  # JayBee黄 - 量化交易研究\n", "                df[date_col] = pd.to_datetime(df[date_col])  # 版权所有: Jay<PERSON>ee黄\n", "                break  # 本代码归<PERSON><PERSON><PERSON>黄所有\n", "                \n", "        if date_col is None:  # <PERSON><PERSON><PERSON>黄授权使用\n", "            raise ValueError(\"找不到日期列，请确保DataFrame包含日期列或日期索引\")  # JayBee黄版权所有，未经授权禁止复制\n", "    \n", "    # 识别交易量列\n", "    vol_col = None  # <PERSON><PERSON><PERSON>黄量化模型\n", "    if 'vol' in df.columns:  # <PERSON><PERSON><PERSON>黄量化策略\n", "        vol_col = 'vol'  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "    elif 'volume' in df.columns:  # <PERSON><PERSON><PERSON>黄量化模型\n", "        vol_col = 'volume'  # 本代码归JayBee黄所有\n", "    \n", "    # 限制K线数量\n", "    if len(df) > max_candles:  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        df = df.iloc[-max_candles:]  # JayBee黄授权使用\n", "    \n", "    # 创建子图\n", "    fig = make_subplots(  # Jay<PERSON>ee黄原创内容\n", "        rows=3,   # <PERSON><PERSON><PERSON>黄独家内容\n", "        cols=1,   # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        shared_xaxes=True,   # Copyright © JayBee黄\n", "        vertical_spacing=0.03,   # <PERSON><PERSON>ee黄版权所有，未经授权禁止复制\n", "        row_heights=[0.6, 0.2, 0.2],  # <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制\n", "        subplot_titles=(\"价格\", \"交易量\", \"持仓\")  # <PERSON><PERSON>ee黄原创内容\n", "    )  # Jay<PERSON>ee黄授权使用\n", "    \n", "    # 添加K线图\n", "    fig.add_trace(  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        go.Candlestick(  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "            x=df[date_col],  # 本代码归JayBee黄所有\n", "            open=df['open'],  # <PERSON><PERSON><PERSON>黄量化策略\n", "            high=df['high'],  # Copyright © JayBee黄\n", "            low=df['low'],  # <PERSON><PERSON><PERSON>黄独家内容\n", "            close=df['close'],  # <PERSON><PERSON><PERSON>黄授权使用\n", "            name=\"<PERSON><PERSON>\",  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "            increasing_line_color='red',  # 中国市场习惯 - 红涨  # JayBee黄量化策略\n", "            decreasing_line_color='green'  # 中国市场习惯 - 绿跌  # JayBee黄独家内容\n", "        ),  # <PERSON><PERSON><PERSON>黄量化模型\n", "        row=1, col=1  # Copyright © JayBee黄\n", "    )  # Jay<PERSON>ee黄独家内容\n", "    \n", "    # 添加交易量图\n", "    if vol_col:  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        # 计算交易量颜色\n", "        colors = ['red' if df['close'].iloc[i] >= df['open'].iloc[i] else 'green' for i in range(len(df))]  # JayBee黄授权使用\n", "        \n", "        fig.add_trace(  # <PERSON><PERSON><PERSON>黄量化模型\n", "            go.Bar(  # <PERSON><PERSON><PERSON>黄独家内容\n", "                x=df[date_col],  # <PERSON><PERSON><PERSON>黄原创内容\n", "                y=df[vol_col],  # <PERSON><PERSON>ee黄版权所有，未经授权禁止复制\n", "                name=\"交易量\",  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "                marker_color=colors,  # 本代码归JayBee黄所有\n", "                opacity=0.7  # <PERSON><PERSON><PERSON>黄授权使用\n", "            ),  # <PERSON><PERSON><PERSON>黄授权使用\n", "            row=2, col=1  # 本代码归Jay<PERSON><PERSON>黄所有\n", "        )  # JayBee黄 - 量化交易研究\n", "    \n", "    # 添加买入信号\n", "    if buy_signals:  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        buy_dates = [date for date, _ in buy_signals]  # JayBee黄 - 量化交易研究\n", "        buy_prices = [price for _, price in buy_signals]  # JayBee黄量化策略\n", "        \n", "        fig.add_trace(  # <PERSON><PERSON>ee黄 - 量化交易研究\n", "            go.<PERSON><PERSON>(  # <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制\n", "                x=buy_dates,  # <PERSON><PERSON>ee黄独家内容\n", "                y=buy_prices,  # <PERSON><PERSON><PERSON>黄原创内容\n", "                mode='markers',  # 本代码归Jay<PERSON><PERSON>黄所有\n", "                name='买入',  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "                marker=dict(  # <PERSON><PERSON><PERSON>黄授权使用\n", "                    symbol='triangle-up',  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "                    size=12,  # 本代码归<PERSON><PERSON><PERSON>黄所有\n", "                    color='red',  # <PERSON><PERSON><PERSON>黄量化模型\n", "                    line=dict(width=2, color='red')  # JayBee黄版权所有，未经授权禁止复制\n", "                )  # JayBee黄版权所有，未经授权禁止复制\n", "            ),  # Jay<PERSON>ee黄 - 量化交易研究\n", "            row=1, col=1  # 本代码归Jay<PERSON><PERSON>黄所有\n", "        )  # Copyright © Jay<PERSON><PERSON>黄\n", "    \n", "    # 添加卖出信号\n", "    if sell_signals:  # <PERSON><PERSON><PERSON>黄量化模型\n", "        sell_dates = [date for date, _ in sell_signals]  # 本代码归JayBee黄所有\n", "        sell_prices = [price for _, price in sell_signals]  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        \n", "        fig.add_trace(  # <PERSON><PERSON><PERSON>黄量化策略\n", "            go.<PERSON>(  # 本代码归<PERSON><PERSON><PERSON>黄所有\n", "                x=sell_dates,  # <PERSON><PERSON>ee黄量化模型\n", "                y=sell_prices,  # 本代码归JayBee黄所有\n", "                mode='markers',  # 本代码归Jay<PERSON><PERSON>黄所有\n", "                name='卖出',  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "                marker=dict(  # Copyright © JayBee黄\n", "                    symbol='triangle-down',  # <PERSON><PERSON><PERSON>黄量化模型\n", "                    size=12,  # <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制\n", "                    color='green',  # <PERSON><PERSON><PERSON>黄原创内容\n", "                    line=dict(width=2, color='green')  # Copyright © JayBee黄\n", "                )  # Copyright © Jay<PERSON><PERSON>黄\n", "            ),  # <PERSON><PERSON><PERSON>黄量化策略\n", "            row=1, col=1  # <PERSON><PERSON><PERSON>黄原创内容\n", "        )  # Copyright © Jay<PERSON><PERSON>黄\n", "    \n", "    # 添加持仓变化图\n", "    if position_size:  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        pos_dates = [date for date, _ in position_size]  # JayBee黄版权所有，未经授权禁止复制\n", "        pos_sizes = [size for _, size in position_size]  # Copyright © JayBee黄\n", "        \n", "        fig.add_trace(  # <PERSON><PERSON>ee黄版权所有，未经授权禁止复制\n", "            go.<PERSON>(  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "                x=pos_dates,  # 本代码归JayBee黄所有\n", "                y=pos_sizes,  # <PERSON><PERSON>ee黄量化模型\n", "                name=\"持仓\",  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "                line=dict(color='blue', width=2),  # JayBee黄版权所有，未经授权禁止复制\n", "                fill='tozeroy'  # Copyright © JayBee黄\n", "            ),  # <PERSON><PERSON><PERSON>黄原创内容\n", "            row=3, col=1  # <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制\n", "        )  # Copyright © Jay<PERSON><PERSON>黄\n", "    \n", "    # 设置图表标题\n", "    if title is None:  # <PERSON><PERSON><PERSON>黄独家内容\n", "        title = f\"回测结果 - 总收益率: {results.get('total_return', 0):.2f}%\"  # JayBee黄量化策略\n", "    \n", "    # 更新布局\n", "    fig.update_layout(  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        title=title,  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        xaxis_rangeslider_visible=False,  # JayBee黄授权使用\n", "        height=900,  # <PERSON><PERSON><PERSON>黄授权使用\n", "        width=1200,  # JayBee黄 - 量化交易研究\n", "        showlegend=True,  # <PERSON><PERSON><PERSON>黄量化策略\n", "        legend=dict(orientation=\"h\", yanchor=\"bottom\", y=1.02, xanchor=\"right\", x=1)  # <PERSON><PERSON><PERSON>黄量化模型\n", "    )  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    \n", "    # 设置Y轴标题\n", "    fig.update_yaxes(title_text=\"价格\", row=1, col=1)  # Jay<PERSON>ee黄授权使用\n", "    fig.update_yaxes(title_text=\"交易量\", row=2, col=1)  # JayBee黄量化策略\n", "    fig.update_yaxes(title_text=\"持仓\", row=3, col=1)  # 本代码归JayBee黄所有\n", "    \n", "    return fig  # <PERSON><PERSON><PERSON>黄量化模型", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "\n", "# 8. 执行回测", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 96, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>trade_time</th>\n", "      <th>close</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>vol</th>\n", "      <th>amount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 09:30:00</td>\n", "      <td>11.73</td>\n", "      <td>11.74</td>\n", "      <td>11.74</td>\n", "      <td>11.73</td>\n", "      <td>19700.0</td>\n", "      <td>257449.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 10:00:00</td>\n", "      <td>11.61</td>\n", "      <td>11.74</td>\n", "      <td>11.75</td>\n", "      <td>11.59</td>\n", "      <td>3537808.0</td>\n", "      <td>45884940.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 10:30:00</td>\n", "      <td>11.62</td>\n", "      <td>11.61</td>\n", "      <td>11.65</td>\n", "      <td>11.60</td>\n", "      <td>2231278.0</td>\n", "      <td>28897008.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 11:00:00</td>\n", "      <td>11.64</td>\n", "      <td>11.62</td>\n", "      <td>11.65</td>\n", "      <td>11.61</td>\n", "      <td>673100.0</td>\n", "      <td>8719710.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>002745.SZ</td>\n", "      <td>2022-03-03 11:30:00</td>\n", "      <td>11.61</td>\n", "      <td>11.63</td>\n", "      <td>11.65</td>\n", "      <td>11.61</td>\n", "      <td>1379400.0</td>\n", "      <td>17854952.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6520</th>\n", "      <td>002745.SZ</td>\n", "      <td>2025-02-28 11:30:00</td>\n", "      <td>9.04</td>\n", "      <td>9.05</td>\n", "      <td>9.05</td>\n", "      <td>9.00</td>\n", "      <td>2877500.0</td>\n", "      <td>25952448.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6521</th>\n", "      <td>002745.SZ</td>\n", "      <td>2025-02-28 13:30:00</td>\n", "      <td>8.96</td>\n", "      <td>9.03</td>\n", "      <td>9.03</td>\n", "      <td>8.92</td>\n", "      <td>4458100.0</td>\n", "      <td>40014516.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6522</th>\n", "      <td>002745.SZ</td>\n", "      <td>2025-02-28 14:00:00</td>\n", "      <td>8.91</td>\n", "      <td>8.96</td>\n", "      <td>8.96</td>\n", "      <td>8.88</td>\n", "      <td>4356200.0</td>\n", "      <td>38851072.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6523</th>\n", "      <td>002745.SZ</td>\n", "      <td>2025-02-28 14:30:00</td>\n", "      <td>8.79</td>\n", "      <td>8.90</td>\n", "      <td>8.90</td>\n", "      <td>8.75</td>\n", "      <td>7360100.0</td>\n", "      <td>64833540.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6524</th>\n", "      <td>002745.SZ</td>\n", "      <td>2025-02-28 15:00:00</td>\n", "      <td>8.75</td>\n", "      <td>8.79</td>\n", "      <td>8.80</td>\n", "      <td>8.70</td>\n", "      <td>8561578.0</td>\n", "      <td>74855300.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6525 rows × 8 columns</p>\n", "</div>"], "text/plain": ["        ts_code          trade_time  close   open   high    low        vol  \\\n", "0     002745.SZ 2022-03-03 09:30:00  11.73  11.74  11.74  11.73    19700.0   \n", "1     002745.SZ 2022-03-03 10:00:00  11.61  11.74  11.75  11.59  3537808.0   \n", "2     002745.SZ 2022-03-03 10:30:00  11.62  11.61  11.65  11.60  2231278.0   \n", "3     002745.SZ 2022-03-03 11:00:00  11.64  11.62  11.65  11.61   673100.0   \n", "4     002745.SZ 2022-03-03 11:30:00  11.61  11.63  11.65  11.61  1379400.0   \n", "...         ...                 ...    ...    ...    ...    ...        ...   \n", "6520  002745.SZ 2025-02-28 11:30:00   9.04   9.05   9.05   9.00  2877500.0   \n", "6521  002745.SZ 2025-02-28 13:30:00   8.96   9.03   9.03   8.92  4458100.0   \n", "6522  002745.SZ 2025-02-28 14:00:00   8.91   8.96   8.96   8.88  4356200.0   \n", "6523  002745.SZ 2025-02-28 14:30:00   8.79   8.90   8.90   8.75  7360100.0   \n", "6524  002745.SZ 2025-02-28 15:00:00   8.75   8.79   8.80   8.70  8561578.0   \n", "\n", "          amount  \n", "0       257449.0  \n", "1     45884940.0  \n", "2     28897008.0  \n", "3      8719710.0  \n", "4     17854952.0  \n", "...          ...  \n", "6520  25952448.0  \n", "6521  40014516.0  \n", "6522  38851072.0  \n", "6523  64833540.0  \n", "6524  74855300.0  \n", "\n", "[6525 rows x 8 columns]"]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "stock_data  # JayBee黄版权所有，未经授权禁止复制", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 97, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["初始资金: 100000.00\n", "2022-03-07: 买入信号: 价格=11.25, 数量=8886, 交易量=3838782, 平均交易量=1418156\n", "2022-03-07: 买入执行: 价格=11.24, 数量=8886, 成本=99878.64, 手续费=29.96\n", "2022-03-07: 卖出信号(时间退出): 价格=11.11, 持仓数量=8886\n", "2022-03-07: 卖出执行: 价格=11.12, 数量=8886, 收入=99878.64, 手续费=29.64\n", "2022-03-07: 交易利润: 毛利=-1066.32, 净利=-1125.93\n", "2022-03-08: 买入信号: 价格=11.01, 数量=8977, 交易量=4226800, 平均交易量=1775308\n", "2022-03-08: 买入执行: 价格=11.01, 数量=8977, 成本=98836.77, 手续费=29.65\n", "2022-03-08: 卖出信号(时间退出): 价格=10.69, 持仓数量=8977\n", "2022-03-08: 卖出执行: 价格=10.70, 数量=8977, 收入=98836.77, 手续费=28.82\n", "2022-03-08: 交易利润: 毛利=-2782.87, 净利=-2841.34\n", "2022-03-09: 买入信号: 价格=10.29, 数量=9329, 交易量=5506274, 平均交易量=2395508\n", "2022-03-09: 买入执行: 价格=10.28, 数量=9329, 成本=95902.12, 手续费=28.77\n", "2022-03-10: 卖出信号(时间退出): 价格=10.95, 持仓数量=9329\n", "2022-03-10: 卖出执行: 价格=10.95, 数量=9329, 收入=95902.12, 手续费=30.65\n", "2022-03-10: 交易利润: 毛利=6250.43, 净利=6191.01\n", "2022-03-10: 买入信号: 价格=10.86, 数量=9410, 交易量=7569304, 平均交易量=2495138\n", "2022-03-10: 买入执行: 价格=10.86, 数量=9410, 成本=102192.60, 手续费=30.66\n", "2022-03-10: 卖出信号(时间退出): 价格=10.79, 持仓数量=9410\n", "2022-03-10: 卖出执行: 价格=10.81, 数量=9410, 收入=102192.60, 手续费=30.52\n", "2022-03-10: 交易利润: 毛利=-470.50, 净利=-531.67\n", "2022-03-14: 买入信号: 价格=10.77, 数量=9439, 交易量=3489557, 平均交易量=1416869\n", "2022-03-14: 订单被拒绝或取消: 7\n", "2022-03-15: 买入信号: 价格=10.26, 数量=9908, 交易量=4213000, 平均交易量=1430419\n", "2022-03-15: 订单被拒绝或取消: 7\n", "2022-03-15: 买入信号: 价格=10.32, 数量=9850, 交易量=3224435, 平均交易量=1567422\n", "2022-03-15: 买入执行: 价格=10.29, 数量=9850, 成本=101356.50, 手续费=30.41\n", "2022-03-15: 卖出信号(时间退出): 价格=10.21, 持仓数量=9850\n", "2022-03-15: 卖出执行: 价格=10.20, 数量=9850, 收入=101356.50, 手续费=30.14\n", "2022-03-15: 交易利润: 毛利=-886.50, 净利=-947.05\n", "2022-03-15: 买入信号: 价格=9.96, 数量=10111, 交易量=4247326, 平均交易量=1785138\n", "2022-03-16: 订单被拒绝或取消: 7\n", "2022-03-16: 买入信号: 价格=10.03, 数量=10041, 交易量=4060224, 平均交易量=1966780\n", "2022-03-16: 买入执行: 价格=10.03, 数量=10041, 成本=100711.23, 手续费=30.21\n", "2022-03-16: 卖出信号(时间退出): 价格=9.98, 持仓数量=10041\n", "2022-03-16: 卖出执行: 价格=9.98, 数量=10041, 收入=100711.23, 手续费=30.06\n", "2022-03-16: 交易利润: 毛利=-502.05, 净利=-562.33\n", "2022-03-17: 买入信号: 价格=10.40, 数量=9630, 交易量=5854600, 平均交易量=2394718\n", "2022-03-17: 订单被拒绝或取消: 7\n", "2022-03-21: 买入信号: 价格=10.53, 数量=9511, 交易量=3471113, 平均交易量=1409167\n", "2022-03-21: 买入执行: 价格=10.53, 数量=9511, 成本=100150.83, 手续费=30.05\n", "2022-03-21: 卖出信号(时间退出): 价格=10.53, 持仓数量=9511\n", "2022-03-21: 卖出执行: 价格=10.53, 数量=9511, 收入=100150.83, 手续费=30.05\n", "2022-03-21: 交易利润: 毛利=0.00, 净利=-60.09\n", "2022-03-23: 买入信号: 价格=10.60, 数量=9442, 交易量=3491400, 平均交易量=1570932\n", "2022-03-23: 买入执行: 价格=10.60, 数量=9442, 成本=100085.20, 手续费=30.03\n", "2022-03-23: 卖出信号(时间退出): 价格=10.51, 持仓数量=9442\n", "2022-03-23: 卖出执行: 价格=10.51, 数量=9442, 收入=100085.20, 手续费=29.77\n", "2022-03-23: 交易利润: 毛利=-849.78, 净利=-909.58\n", "2022-03-24: 买入信号: 价格=10.36, 数量=9573, 交易量=3294400, 平均交易量=1497812\n", "2022-03-24: 订单被拒绝或取消: 7\n", "2022-03-28: 买入信号: 价格=10.25, 数量=9676, 交易量=5119263, 平均交易量=1609294\n", "2022-03-28: 买入执行: 价格=10.25, 数量=9676, 成本=99179.00, 手续费=29.75\n", "2022-03-28: 卖出信号(时间退出): 价格=10.41, 持仓数量=9676\n", "2022-03-28: 卖出执行: 价格=10.41, 数量=9676, 收入=99179.00, 手续费=30.22\n", "2022-03-28: 交易利润: 毛利=1548.16, 净利=1488.19\n", "2022-03-30: 买入信号: 价格=10.42, 数量=9661, 交易量=4937232, 平均交易量=1454347\n", "2022-03-30: 买入执行: 价格=10.42, 数量=9661, 成本=100667.62, 手续费=30.20\n", "2022-03-30: 卖出信号(时间退出): 价格=10.45, 持仓数量=9661\n", "2022-03-30: 卖出执行: 价格=10.45, 数量=9661, 收入=100667.62, 手续费=30.29\n", "2022-03-30: 交易利润: 毛利=289.83, 净利=229.34\n", "2022-04-06: 买入信号: 价格=10.53, 数量=9582, 交易量=4483900, 平均交易量=1817156\n", "2022-04-06: 买入执行: 价格=10.53, 数量=9582, 成本=100898.46, 手续费=30.27\n", "2022-04-06: 卖出信号(时间退出): 价格=10.50, 持仓数量=9582\n", "2022-04-06: 卖出执行: 价格=10.51, 数量=9582, 收入=100898.46, 手续费=30.21\n", "2022-04-06: 交易利润: 毛利=-191.64, 净利=-252.12\n", "2022-04-07: 买入信号: 价格=10.24, 数量=9828, 交易量=3531238, 平均交易量=1598351\n", "2022-04-08: 买入执行: 价格=10.23, 数量=9828, 成本=100540.44, 手续费=30.16\n", "2022-04-08: 卖出信号(时间退出): 价格=9.96, 持仓数量=9828\n", "2022-04-08: 卖出执行: 价格=9.96, 数量=9828, 收入=100540.44, 手续费=29.37\n", "2022-04-08: 交易利润: 毛利=-2653.56, 净利=-2713.09\n", "2022-04-08: 买入信号: 价格=9.80, 数量=9993, 交易量=5463000, 平均交易量=2543068\n", "2022-04-08: 买入执行: 价格=9.79, 数量=9993, 成本=97831.47, 手续费=29.35\n", "2022-04-11: 卖出信号(时间退出): 价格=9.60, 持仓数量=9993\n", "2022-04-11: 卖出执行: 价格=9.58, 数量=9993, 收入=97831.47, 手续费=28.72\n", "2022-04-11: 交易利润: 毛利=-2098.53, 净利=-2156.60\n", "2022-04-15: 买入信号: 价格=9.40, 数量=10189, 交易量=2127000, 平均交易量=972869\n", "2022-04-15: 买入执行: 价格=9.40, 数量=10189, 成本=95776.60, 手续费=28.73\n", "2022-04-15: 卖出信号(时间退出): 价格=9.35, 持仓数量=10189\n", "2022-04-15: 卖出执行: 价格=9.35, 数量=10189, 收入=95776.60, 手续费=28.58\n", "2022-04-15: 交易利润: 毛利=-509.45, 净利=-566.76\n", "2022-04-18: 买入信号: 价格=9.31, 数量=10227, 交易量=2688200, 平均交易量=1147947\n", "2022-04-18: 订单被拒绝或取消: 7\n", "2022-04-19: 买入信号: 价格=9.56, 数量=9959, 交易量=4684100, 平均交易量=1351110\n", "2022-04-19: 买入执行: 价格=9.56, 数量=9959, 成本=95208.04, 手续费=28.56\n", "2022-04-19: 卖出信号(时间退出): 价格=9.57, 持仓数量=9959\n", "2022-04-19: 卖出执行: 价格=9.55, 数量=9959, 收入=95208.04, 手续费=28.53\n", "2022-04-19: 交易利润: 毛利=-99.59, 净利=-156.68\n", "2022-04-21: 买入信号: 价格=9.47, 数量=10037, 交易量=4485700, 平均交易量=1270710\n", "2022-04-21: 订单被拒绝或取消: 7\n", "2022-04-21: 买入信号: 价格=9.20, 数量=10332, 交易量=3572000, 平均交易量=1532726\n", "2022-04-21: 买入执行: 价格=9.20, 数量=10332, 成本=95054.40, 手续费=28.52\n", "2022-04-21: 卖出信号(时间退出): 价格=9.11, 持仓数量=10332\n", "2022-04-21: 卖出执行: 价格=9.10, 数量=10332, 收入=95054.40, 手续费=28.21\n", "2022-04-21: 交易利润: 毛利=-1033.20, 净利=-1089.92\n", "2022-04-22: 买入信号: 价格=8.94, 数量=10510, 交易量=4363424, 平均交易量=2049781\n", "2022-04-22: 订单被拒绝或取消: 7\n", "2022-04-25: 买入信号: 价格=8.76, 数量=10726, 交易量=5288312, 平均交易量=1806660\n", "2022-04-25: 买入执行: 价格=8.76, 数量=10726, 成本=93959.76, 手续费=28.19\n", "2022-04-25: 卖出信号(时间退出): 价格=8.62, 持仓数量=10726\n", "2022-04-25: 卖出执行: 价格=8.62, 数量=10726, 收入=93959.76, 手续费=27.74\n", "2022-04-25: 交易利润: 毛利=-1501.64, 净利=-1557.57\n", "2022-04-26: 买入信号: 价格=8.11, 数量=11394, 交易量=4728900, 平均交易量=2004231\n", "2022-04-26: 买入执行: 价格=8.10, 数量=11394, 成本=92291.40, 手续费=27.69\n", "2022-04-26: 卖出信号(时间退出): 价格=8.26, 持仓数量=11394\n", "2022-04-26: 卖出执行: 价格=8.25, 数量=11394, 收入=92291.40, 手续费=28.20\n", "2022-04-26: 交易利润: 毛利=1709.10, 净利=1653.21\n", "2022-04-27: 买入信号: 价格=7.84, 数量=11997, 交易量=4690367, 平均交易量=2075672\n", "2022-04-27: 买入执行: 价格=7.84, 数量=11997, 成本=94056.48, 手续费=28.22\n", "2022-04-27: 卖出信号(时间退出): 价格=8.01, 持仓数量=11997\n", "2022-04-27: 卖出执行: 价格=8.01, 数量=11997, 收入=94056.48, 手续费=28.83\n", "2022-04-27: 交易利润: 毛利=2039.49, 净利=1982.44\n", "2022-04-28: 买入信号: 价格=7.77, 数量=12360, 交易量=12186244, 平均交易量=2606599\n", "2022-04-28: 订单被拒绝或取消: 7\n", "2022-04-29: 买入信号: 价格=7.78, 数量=12345, 交易量=8689042, 平均交易量=3859354\n", "2022-04-29: 买入执行: 价格=7.78, 数量=12345, 成本=96044.10, 手续费=28.81\n", "2022-04-29: 卖出信号(时间退出): 价格=7.78, 持仓数量=12345\n", "2022-04-29: 卖出执行: 价格=7.77, 数量=12345, 收入=96044.10, 手续费=28.78\n", "2022-04-29: 交易利润: 毛利=-123.45, 净利=-181.04\n", "2022-05-06: 买入信号: 价格=7.64, 数量=12547, 交易量=5420900, 平均交易量=2699093\n", "2022-05-06: 订单被拒绝或取消: 7\n", "2022-05-10: 买入信号: 价格=7.50, 数量=12781, 交易量=5023500, 平均交易量=1635641\n", "2022-05-10: 买入执行: 价格=7.50, 数量=12781, 成本=95857.50, 手续费=28.76\n", "2022-05-10: 卖出信号(时间退出): 价格=7.62, 持仓数量=12781\n", "2022-05-10: 卖出执行: 价格=7.63, 数量=12781, 收入=95857.50, 手续费=29.26\n", "2022-05-10: 交易利润: 毛利=1661.53, 净利=1603.52\n", "2022-05-11: 买入信号: 价格=7.89, 数量=12353, 交易量=5549732, 平均交易量=1943058\n", "2022-05-11: 买入执行: 价格=7.89, 数量=12353, 成本=97465.17, 手续费=29.24\n", "2022-05-11: 卖出信号(时间退出): 价格=7.99, 持仓数量=12353\n", "2022-05-11: 卖出执行: 价格=7.99, 数量=12353, 收入=97465.17, 手续费=29.61\n", "2022-05-11: 交易利润: 毛利=1235.30, 净利=1176.45\n", "2022-05-16: 买入信号: 价格=7.86, 数量=12549, 交易量=4596368, 平均交易量=1925943\n", "2022-05-16: 买入执行: 价格=7.86, 数量=12549, 成本=98635.14, 手续费=29.59\n", "2022-05-16: 卖出信号(时间退出): 价格=7.81, 持仓数量=12549\n", "2022-05-16: 卖出执行: 价格=7.83, 数量=12549, 收入=98635.14, 手续费=29.48\n", "2022-05-16: 交易利润: 毛利=-376.47, 净利=-435.54\n", "2022-05-17: 买入信号: 价格=7.95, 数量=12353, 交易量=3694472, 平均交易量=1694645\n", "2022-05-17: 订单被拒绝或取消: 7\n", "2022-05-19: 买入信号: 价格=7.94, 数量=12368, 交易量=4152519, 平均交易量=2011327\n", "2022-05-19: 买入执行: 价格=7.94, 数量=12368, 成本=98201.92, 手续费=29.46\n", "2022-05-19: 卖出信号(时间退出): 价格=8.03, 持仓数量=12368\n", "2022-05-19: 卖出执行: 价格=8.03, 数量=12368, 收入=98201.92, 手续费=29.79\n", "2022-05-19: 交易利润: 毛利=1113.12, 净利=1053.86\n", "2022-05-20: 买入信号: 价格=8.24, 数量=12046, 交易量=5528241, 平均交易量=2015839\n", "2022-05-20: 买入执行: 价格=8.23, 数量=12046, 成本=99138.58, 手续费=29.74\n", "2022-05-20: 卖出信号(时间退出): 价格=8.22, 持仓数量=12046\n", "2022-05-20: 卖出执行: 价格=8.22, 数量=12046, 收入=99138.58, 手续费=29.71\n", "2022-05-20: 交易利润: 毛利=-120.46, 净利=-179.91\n", "2022-05-24: 买入信号: 价格=8.34, 数量=11880, 交易量=3743985, 平均交易量=1668412\n", "2022-05-24: 订单被拒绝或取消: 7\n", "2022-05-24: 买入信号: 价格=7.94, 数量=12478, 交易量=4122184, 平均交易量=2059580\n", "2022-05-25: 买入执行: 价格=7.90, 数量=12478, 成本=98576.20, 手续费=29.57\n", "2022-05-25: 卖出信号(时间退出): 价格=7.95, 持仓数量=12478\n", "2022-05-25: 卖出执行: 价格=7.95, 数量=12478, 收入=98576.20, 手续费=29.76\n", "2022-05-25: 交易利润: 毛利=623.90, 净利=564.57\n", "2022-05-26: 买入信号: 价格=7.87, 数量=12661, 交易量=4445438, 平均交易量=1869893\n", "2022-05-26: 买入执行: 价格=7.87, 数量=12661, 成本=99642.07, 手续费=29.89\n", "2022-05-26: 卖出信号(时间退出): 价格=8.04, 持仓数量=12661\n", "2022-05-26: 卖出执行: 价格=8.05, 数量=12661, 收入=99642.07, 手续费=30.58\n", "2022-05-26: 交易利润: 毛利=2278.98, 净利=2218.51\n", "2022-05-27: 买入信号: 价格=8.05, 数量=12653, 交易量=4697800, 平均交易量=1674106\n", "2022-05-27: 买入执行: 价格=8.04, 数量=12653, 成本=101730.12, 手续费=30.52\n", "2022-05-27: 卖出信号(时间退出): 价格=8.05, 持仓数量=12653\n", "2022-05-27: 卖出执行: 价格=8.05, 数量=12653, 收入=101730.12, 手续费=30.56\n", "2022-05-27: 交易利润: 毛利=126.53, 净利=65.45\n", "2022-05-30: 买入信号: 价格=8.00, 数量=12741, 交易量=4196374, 平均交易量=1757863\n", "2022-05-30: 买入执行: 价格=8.00, 数量=12741, 成本=101928.00, 手续费=30.58\n", "2022-05-30: 卖出信号(时间退出): 价格=8.06, 持仓数量=12741\n", "2022-05-30: 卖出执行: 价格=8.06, 数量=12741, 收入=101928.00, 手续费=30.81\n", "2022-05-30: 交易利润: 毛利=764.46, 净利=703.07\n", "2022-05-31: 买入信号: 价格=8.19, 数量=12531, 交易量=4489400, 平均交易量=1600826\n", "2022-05-31: 买入执行: 价格=8.19, 数量=12531, 成本=102628.89, 手续费=30.79\n", "2022-05-31: 卖出信号(时间退出): 价格=8.19, 持仓数量=12531\n", "2022-05-31: 卖出执行: 价格=8.18, 数量=12531, 收入=102628.89, 手续费=30.75\n", "2022-05-31: 交易利润: 毛利=-125.31, 净利=-186.85\n", "2022-05-31: 买入信号: 价格=8.25, 数量=12417, 交易量=3616897, 平均交易量=1721654\n", "2022-06-01: 买入执行: 价格=8.22, 数量=12417, 成本=102067.74, 手续费=30.62\n", "2022-06-01: 卖出信号(时间退出): 价格=8.33, 持仓数量=12417\n", "2022-06-01: 卖出执行: 价格=8.33, 数量=12417, 收入=102067.74, 手续费=31.03\n", "2022-06-01: 交易利润: 毛利=1365.87, 净利=1304.22\n", "2022-06-06: 买入信号: 价格=8.39, 数量=12365, 交易量=3959440, 平均交易量=1782076\n", "2022-06-06: 订单被拒绝或取消: 7\n", "2022-06-07: 买入信号: 价格=8.59, 数量=12077, 交易量=10688254, 平均交易量=2674470\n", "2022-06-07: 买入执行: 价格=8.59, 数量=12077, 成本=103741.43, 手续费=31.12\n", "2022-06-07: 卖出信号(时间退出): 价格=8.60, 持仓数量=12077\n", "2022-06-07: 卖出执行: 价格=8.61, 数量=12077, 收入=103741.43, 手续费=31.19\n", "2022-06-07: 交易利润: 毛利=241.54, 净利=179.22\n", "2022-06-13: 买入信号: 价格=8.46, 数量=12284, 交易量=3180117, 平均交易量=1582142\n", "2022-06-13: 买入执行: 价格=8.45, 数量=12284, 成本=103799.80, 手续费=31.14\n", "2022-06-13: 卖出信号(时间退出): 价格=8.37, 持仓数量=12284\n", "2022-06-13: 卖出执行: 价格=8.37, 数量=12284, 收入=103799.80, 手续费=30.85\n", "2022-06-13: 交易利润: 毛利=-982.72, 净利=-1044.71\n", "2022-06-14: 买入信号: 价格=8.22, 数量=12516, 交易量=4212400, 平均交易量=1712498\n", "2022-06-14: 买入执行: 价格=8.21, 数量=12516, 成本=102756.36, 手续费=30.83\n", "2022-06-14: 卖出信号(时间退出): 价格=8.11, 持仓数量=12516\n", "2022-06-14: 卖出执行: 价格=8.12, 数量=12516, 收入=102756.36, 手续费=30.49\n", "2022-06-14: 交易利润: 毛利=-1126.44, 净利=-1187.76\n", "2022-06-15: 买入信号: 价格=8.50, 数量=11964, 交易量=6011205, 平均交易量=2135951\n", "2022-06-15: 买入执行: 价格=8.49, 数量=11964, 成本=101574.36, 手续费=30.47\n", "2022-06-15: 卖出信号(时间退出): 价格=8.61, 持仓数量=11964\n", "2022-06-15: 卖出执行: 价格=8.61, 数量=11964, 收入=101574.36, 手续费=30.90\n", "2022-06-15: 交易利润: 毛利=1435.68, 净利=1374.30\n", "2022-06-22: 买入信号: 价格=8.61, 数量=11970, 交易量=3013018, 平均交易量=1328811\n", "2022-06-22: 买入执行: 价格=8.61, 数量=11970, 成本=103061.70, 手续费=30.92\n", "2022-06-22: 卖出信号(时间退出): 价格=8.55, 持仓数量=11970\n", "2022-06-22: 卖出执行: 价格=8.55, 数量=11970, 收入=103061.70, 手续费=30.70\n", "2022-06-22: 交易利润: 毛利=-718.20, 净利=-779.82\n", "2022-06-23: 买入信号: 价格=8.58, 数量=11921, 交易量=3836900, 平均交易量=1603078\n", "2022-06-23: 买入执行: 价格=8.58, 数量=11921, 成本=102282.18, 手续费=30.68\n", "2022-06-23: 卖出信号(时间退出): 价格=8.59, 持仓数量=11921\n", "2022-06-23: 卖出执行: 价格=8.59, 数量=11921, 收入=102282.18, 手续费=30.72\n", "2022-06-23: 交易利润: 毛利=119.21, 净利=57.80\n", "2022-06-29: 买入信号: 价格=9.02, 数量=11346, 交易量=5902399, 平均交易量=2052043\n", "2022-06-29: 订单被拒绝或取消: 7\n", "2022-07-01: 买入信号: 价格=8.96, 数量=11422, 交易量=3062700, 平均交易量=1428227\n", "2022-07-01: 订单被拒绝或取消: 7\n", "2022-07-01: 买入信号: 价格=9.12, 数量=11222, 交易量=4448841, 平均交易量=1600523\n", "2022-07-01: 买入执行: 价格=9.12, 数量=11222, 成本=102344.64, 手续费=30.70\n", "2022-07-01: 卖出信号(时间退出): 价格=9.05, 持仓数量=11222\n", "2022-07-01: 卖出执行: 价格=9.06, 数量=11222, 收入=102344.64, 手续费=30.50\n", "2022-07-01: 交易利润: 毛利=-673.32, 净利=-734.52\n", "2022-07-04: 买入信号: 价格=8.82, 数量=11520, 交易量=8831021, 平均交易量=2121576\n", "2022-07-04: 买入执行: 价格=8.82, 数量=11520, 成本=101606.40, 手续费=30.48\n", "2022-07-04: 卖出信号(时间退出): 价格=8.83, 持仓数量=11520\n", "2022-07-04: 卖出执行: 价格=8.83, 数量=11520, 收入=101606.40, 手续费=30.52\n", "2022-07-04: 交易利润: 毛利=115.20, 净利=54.20\n", "2022-07-05: 买入信号: 价格=8.90, 数量=11423, 交易量=5543997, 平均交易量=2131378\n", "2022-07-05: 买入执行: 价格=8.90, 数量=11423, 成本=101664.70, 手续费=30.50\n", "2022-07-05: 卖出信号(时间退出): 价格=8.80, 持仓数量=11423\n", "2022-07-05: 卖出执行: 价格=8.80, 数量=11423, 收入=101664.70, 手续费=30.16\n", "2022-07-05: 交易利润: 毛利=-1142.30, 净利=-1202.96\n", "2022-07-07: 买入信号: 价格=8.86, 数量=11339, 交易量=6276527, 平均交易量=1825861\n", "2022-07-07: 订单被拒绝或取消: 7\n", "2022-07-08: 买入信号: 价格=8.85, 数量=11352, 交易量=3899166, 平均交易量=1814892\n", "2022-07-08: 买入执行: 价格=8.84, 数量=11352, 成本=100351.68, 手续费=30.11\n", "2022-07-08: 卖出信号(时间退出): 价格=8.79, 持仓数量=11352\n", "2022-07-08: 卖出执行: 价格=8.79, 数量=11352, 收入=100351.68, 手续费=29.94\n", "2022-07-08: 交易利润: 毛利=-567.60, 净利=-627.64\n", "2022-07-11: 买入信号: 价格=8.46, 数量=11801, 交易量=5498674, 平均交易量=1900434\n", "2022-07-11: 买入执行: 价格=8.45, 数量=11801, 成本=99718.45, 手续费=29.92\n", "2022-07-11: 卖出信号(时间退出): 价格=8.42, 持仓数量=11801\n", "2022-07-11: 卖出执行: 价格=8.42, 数量=11801, 收入=99718.45, 手续费=29.81\n", "2022-07-11: 交易利润: 毛利=-354.03, 净利=-413.75\n", "2022-07-15: 买入信号: 价格=7.68, 数量=12945, 交易量=15502934, 平均交易量=1843642\n", "2022-07-15: 买入执行: 价格=7.67, 数量=12945, 成本=99288.15, 手续费=29.79\n", "2022-07-15: 卖出信号(时间退出): 价格=7.72, 持仓数量=12945\n", "2022-07-15: 卖出执行: 价格=7.72, 数量=12945, 收入=99288.15, 手续费=29.98\n", "2022-07-15: 交易利润: 毛利=647.25, 净利=587.48\n", "2022-07-18: 买入信号: 价格=7.81, 数量=12805, 交易量=5788034, 平均交易量=2841092\n", "2022-07-18: 买入执行: 价格=7.81, 数量=12805, 成本=100007.05, 手续费=30.00\n", "2022-07-18: 卖出信号(时间退出): 价格=8.01, 持仓数量=12805\n", "2022-07-18: 卖出执行: 价格=8.01, 数量=12805, 收入=100007.05, 手续费=30.77\n", "2022-07-18: 交易利润: 毛利=2561.00, 净利=2500.23\n", "2022-07-20: 买入信号: 价格=8.39, 数量=12218, 交易量=11251450, 平均交易量=2194926\n", "2022-07-20: 买入执行: 价格=8.39, 数量=12218, 成本=102509.02, 手续费=30.75\n", "2022-07-20: 卖出信号(时间退出): 价格=8.35, 持仓数量=12218\n", "2022-07-20: 卖出执行: 价格=8.34, 数量=12218, 收入=102509.02, 手续费=30.57\n", "2022-07-20: 交易利润: 毛利=-610.90, 净利=-672.22\n", "2022-07-21: 买入信号: 价格=8.53, 数量=11938, 交易量=7156409, 平均交易量=2401889\n", "2022-07-21: 买入执行: 价格=8.53, 数量=11938, 成本=101831.14, 手续费=30.55\n", "2022-07-21: 卖出信号(止盈): 价格=9.14, 持仓数量=11938\n", "2022-07-21: 卖出执行: 价格=9.13, 数量=11938, 收入=101831.14, 手续费=32.70\n", "2022-07-21: 交易利润: 毛利=7162.80, 净利=7099.55\n", "2022-07-22: 买入信号: 价格=9.24, 数量=11789, 交易量=31288372, 平均交易量=6713649\n", "2022-07-22: 买入执行: 价格=9.24, 数量=11789, 成本=108930.36, 手续费=32.68\n", "2022-07-22: 卖出信号(时间退出): 价格=9.14, 持仓数量=11789\n", "2022-07-22: 卖出执行: 价格=9.13, 数量=11789, 收入=108930.36, 手续费=32.29\n", "2022-07-22: 交易利润: 毛利=-1296.79, 净利=-1361.76\n", "2022-07-25: 买入信号: 价格=8.87, 数量=12127, 交易量=14684300, 平均交易量=6140568\n", "2022-07-25: 买入执行: 价格=8.87, 数量=12127, 成本=107566.49, 手续费=32.27\n", "2022-07-25: 卖出信号(时间退出): 价格=8.81, 持仓数量=12127\n", "2022-07-25: 卖出执行: 价格=8.81, 数量=12127, 收入=107566.49, 手续费=32.05\n", "2022-07-25: 交易利润: 毛利=-727.62, 净利=-791.94\n", "2022-07-28: 买入信号: 价格=9.14, 数量=11683, 交易量=16589116, 平均交易量=2879132\n", "2022-07-28: 买入执行: 价格=9.14, 数量=11683, 成本=106782.62, 手续费=32.03\n", "2022-07-28: 卖出信号(时间退出): 价格=8.97, 持仓数量=11683\n", "2022-07-28: 卖出执行: 价格=8.98, 数量=11683, 收入=106782.62, 手续费=31.47\n", "2022-07-28: 交易利润: 毛利=-1869.28, 净利=-1932.79\n", "2022-08-01: 买入信号: 价格=8.95, 数量=11715, 交易量=5869007, 平均交易量=2480060\n", "2022-08-01: 买入执行: 价格=8.94, 数量=11715, 成本=104732.10, 手续费=31.42\n", "2022-08-01: 卖出信号(时间退出): 价格=8.88, 持仓数量=11715\n", "2022-08-01: 卖出执行: 价格=8.88, 数量=11715, 收入=104732.10, 手续费=31.21\n", "2022-08-01: 交易利润: 毛利=-702.90, 净利=-765.53\n", "2022-08-02: 买入信号: 价格=8.80, 数量=11827, 交易量=8349968, 平均交易量=2352270\n", "2022-08-02: 买入执行: 价格=8.80, 数量=11827, 成本=104077.60, 手续费=31.22\n", "2022-08-02: 卖出信号(时间退出): 价格=8.66, 持仓数量=11827\n", "2022-08-02: 卖出执行: 价格=8.65, 数量=11827, 收入=104077.60, 手续费=30.69\n", "2022-08-02: 交易利润: 毛利=-1774.05, 净利=-1835.96\n", "2022-08-03: 买入信号: 价格=8.78, 数量=11645, 交易量=6390852, 平均交易量=2939997\n", "2022-08-03: 买入执行: 价格=8.78, 数量=11645, 成本=102243.10, 手续费=30.67\n", "2022-08-03: 卖出信号(时间退出): 价格=8.74, 持仓数量=11645\n", "2022-08-03: 卖出执行: 价格=8.75, 数量=11645, 收入=102243.10, 手续费=30.57\n", "2022-08-03: 交易利润: 毛利=-349.35, 净利=-410.59\n", "2022-08-04: 买入信号: 价格=8.74, 数量=11652, 交易量=7575200, 平均交易量=2991201\n", "2022-08-04: 订单被拒绝或取消: 7\n", "2022-08-05: 买入信号: 价格=9.12, 数量=11166, 交易量=9353314, 平均交易量=3152184\n", "2022-08-05: 订单被拒绝或取消: 7\n", "2022-08-08: 买入信号: 价格=9.02, 数量=11290, 交易量=8545200, 平均交易量=3371972\n", "2022-08-08: 买入执行: 价格=9.02, 数量=11290, 成本=101835.80, 手续费=30.55\n", "2022-08-08: 卖出信号(时间退出): 价格=9.09, 持仓数量=11290\n", "2022-08-08: 卖出执行: 价格=9.10, 数量=11290, 收入=101835.80, 手续费=30.82\n", "2022-08-08: 交易利润: 毛利=903.20, 净利=841.83\n", "2022-08-10: 买入信号: 价格=9.17, 数量=11197, 交易量=4796453, 平均交易量=1887155\n", "2022-08-10: 买入执行: 价格=9.17, 数量=11197, 成本=102676.49, 手续费=30.80\n", "2022-08-10: 卖出信号(时间退出): 价格=9.20, 持仓数量=11197\n", "2022-08-10: 卖出执行: 价格=9.20, 数量=11197, 收入=102676.49, 手续费=30.90\n", "2022-08-10: 交易利润: 毛利=335.91, 净利=274.20\n", "2022-08-11: 买入信号: 价格=9.54, 数量=10792, 交易量=12843499, 平均交易量=2890187\n", "2022-08-11: 买入执行: 价格=9.54, 数量=10792, 成本=102955.68, 手续费=30.89\n", "2022-08-11: 卖出信号(时间退出): 价格=9.59, 持仓数量=10792\n", "2022-08-11: 卖出执行: 价格=9.59, 数量=10792, 收入=102955.68, 手续费=31.05\n", "2022-08-11: 交易利润: 毛利=539.60, 净利=477.66\n", "2022-08-12: 买入信号: 价格=9.63, 数量=10740, 交易量=9501383, 平均交易量=4096708\n", "2022-08-12: 买入执行: 价格=9.63, 数量=10740, 成本=103426.20, 手续费=31.03\n", "2022-08-12: 卖出信号(时间退出): 价格=9.76, 持仓数量=10740\n", "2022-08-12: 卖出执行: 价格=9.76, 数量=10740, 收入=103426.20, 手续费=31.45\n", "2022-08-12: 交易利润: 毛利=1396.20, 净利=1333.73\n", "2022-08-15: 买入信号: 价格=9.71, 数量=10789, 交易量=8175847, 平均交易量=4013335\n", "2022-08-15: 买入执行: 价格=9.71, 数量=10789, 成本=104761.19, 手续费=31.43\n", "2022-08-15: 卖出信号(时间退出): 价格=10.17, 持仓数量=10789\n", "2022-08-15: 卖出执行: 价格=10.18, 数量=10789, 收入=104761.19, 手续费=32.95\n", "2022-08-15: 交易利润: 毛利=5070.83, 净利=5006.45\n", "2022-08-15: 买入信号: 价格=10.39, 数量=10565, 交易量=14928477, 平均交易量=5745087\n", "2022-08-15: 订单被拒绝或取消: 7\n", "2022-08-16: 买入信号: 价格=11.04, 数量=9943, 交易量=36836716, 平均交易量=7349459\n", "2022-08-16: 买入执行: 价格=11.02, 数量=9943, 成本=109571.86, 手续费=32.87\n", "2022-08-16: 卖出信号(时间退出): 价格=10.98, 持仓数量=9943\n", "2022-08-16: 卖出执行: 价格=10.98, 数量=9943, 收入=109571.86, 手续费=32.75\n", "2022-08-16: 交易利润: 毛利=-397.72, 净利=-463.34\n", "2022-08-17: 买入信号: 价格=11.04, 数量=9901, 交易量=32950822, 平均交易量=9533518\n", "2022-08-17: 订单被拒绝或取消: 7\n", "2022-08-23: 买入信号: 价格=10.89, 数量=10037, 交易量=18504864, 平均交易量=5512132\n", "2022-08-23: 订单被拒绝或取消: 7\n", "2022-08-23: 买入信号: 价格=11.32, 数量=9656, 交易量=16922634, 平均交易量=6309973\n", "2022-08-23: 买入执行: 价格=11.32, 数量=9656, 成本=109305.92, 手续费=32.79\n", "2022-08-23: 卖出信号(时间退出): 价格=11.32, 持仓数量=9656\n", "2022-08-24: 卖出执行: 价格=11.63, 数量=9656, 收入=109305.92, 手续费=33.69\n", "2022-08-24: 交易利润: 毛利=2993.36, 净利=2926.88\n", "2022-08-24: 买入信号: 价格=10.84, 数量=10353, 交易量=26686846, 平均交易量=6980076\n", "2022-08-24: 买入执行: 价格=10.83, 数量=10353, 成本=112122.99, 手续费=33.64\n", "2022-08-24: 卖出信号(时间退出): 价格=10.38, 持仓数量=10353\n", "2022-08-24: 卖出执行: 价格=10.40, 数量=10353, 收入=112122.99, 手续费=32.30\n", "2022-08-24: 交易利润: 毛利=-4451.79, 净利=-4517.73\n", "2022-08-26: 买入信号: 价格=10.07, 数量=10696, 交易量=11019700, 平均交易量=4767870\n", "2022-08-26: 买入执行: 价格=10.07, 数量=10696, 成本=107708.72, 手续费=32.31\n", "2022-08-26: 卖出信号(时间退出): 价格=10.12, 持仓数量=10696\n", "2022-08-26: 卖出执行: 价格=10.12, 数量=10696, 收入=107708.72, 手续费=32.47\n", "2022-08-26: 交易利润: 毛利=534.80, 净利=470.01\n", "2022-08-31: 买入信号: 价格=9.42, 数量=11484, 交易量=6767159, 平均交易量=2735781\n", "2022-08-31: 买入执行: 价格=9.41, 数量=11484, 成本=108064.44, 手续费=32.42\n", "2022-08-31: 卖出信号(时间退出): 价格=9.29, 持仓数量=11484\n", "2022-08-31: 卖出执行: 价格=9.30, 数量=11484, 收入=108064.44, 手续费=32.04\n", "2022-08-31: 交易利润: 毛利=-1263.24, 净利=-1327.70\n", "2022-09-02: 买入信号: 价格=9.44, 数量=11319, 交易量=4312625, 平均交易量=2038256\n", "2022-09-02: 买入执行: 价格=9.44, 数量=11319, 成本=106851.36, 手续费=32.06\n", "2022-09-02: 卖出信号(时间退出): 价格=10.03, 持仓数量=11319\n", "2022-09-02: 卖出执行: 价格=10.03, 数量=11319, 收入=106851.36, 手续费=34.06\n", "2022-09-02: 交易利润: 毛利=6678.21, 净利=6612.10\n", "2022-09-06: 买入信号: 价格=10.04, 数量=11301, 交易量=4150894, 平均交易量=2057211\n", "2022-09-06: 买入执行: 价格=10.04, 数量=11301, 成本=113462.04, 手续费=34.04\n", "2022-09-06: 卖出信号(时间退出): 价格=10.14, 持仓数量=11301\n", "2022-09-06: 卖出执行: 价格=10.14, 数量=11301, 收入=113462.04, 手续费=34.38\n", "2022-09-06: 交易利润: 毛利=1130.10, 净利=1061.68\n", "2022-09-09: 买入信号: 价格=9.53, 数量=12018, 交易量=4923453, 平均交易量=1787873\n", "2022-09-09: 买入执行: 价格=9.53, 数量=12018, 成本=114531.54, 手续费=34.36\n", "2022-09-09: 卖出信号(时间退出): 价格=9.67, 持仓数量=12018\n", "2022-09-09: 卖出执行: 价格=9.67, 数量=12018, 收入=114531.54, 手续费=34.86\n", "2022-09-09: 交易利润: 毛利=1682.52, 净利=1613.30\n", "2022-09-13: 买入信号: 价格=9.58, 数量=12123, 交易量=3249934, 平均交易量=1515031\n", "2022-09-13: 买入执行: 价格=9.57, 数量=12123, 成本=116017.11, 手续费=34.81\n", "2022-09-13: 卖出信号(时间退出): 价格=9.53, 持仓数量=12123\n", "2022-09-13: 卖出执行: 价格=9.53, 数量=12123, 收入=116017.11, 手续费=34.66\n", "2022-09-13: 交易利润: 毛利=-484.92, 净利=-554.38\n", "2022-09-14: 买入信号: 价格=9.32, 数量=12402, 交易量=3026400, 平均交易量=1421244\n", "2022-09-14: 买入执行: 价格=9.31, 数量=12402, 成本=115462.62, 手续费=34.64\n", "2022-09-14: 卖出信号(时间退出): 价格=9.18, 持仓数量=12402\n", "2022-09-14: 卖出执行: 价格=9.19, 数量=12402, 收入=115462.62, 手续费=34.19\n", "2022-09-14: 交易利润: 毛利=-1488.24, 净利=-1557.07\n", "2022-09-15: 买入信号: 价格=8.89, 数量=12827, 交易量=5570800, 平均交易量=2137358\n", "2022-09-15: 买入执行: 价格=8.89, 数量=12827, 成本=114032.03, 手续费=34.21\n", "2022-09-15: 卖出信号(时间退出): 价格=8.72, 持仓数量=12827\n", "2022-09-15: 卖出执行: 价格=8.72, 数量=12827, 收入=114032.03, 手续费=33.56\n", "2022-09-15: 交易利润: 毛利=-2180.59, 净利=-2248.36\n", "2022-09-20: 买入信号: 价格=8.18, 数量=13665, 交易量=3345901, 平均交易量=1482462\n", "2022-09-21: 买入执行: 价格=8.18, 数量=13665, 成本=111779.70, 手续费=33.53\n", "2022-09-21: 卖出信号(时间退出): 价格=8.07, 持仓数量=13665\n", "2022-09-21: 卖出执行: 价格=8.07, 数量=13665, 收入=111779.70, 手续费=33.08\n", "2022-09-21: 交易利润: 毛利=-1503.15, 净利=-1569.77\n", "2022-09-23: 买入信号: 价格=7.88, 数量=13986, 交易量=4134707, 平均交易量=1748521\n", "2022-09-23: 买入执行: 价格=7.87, 数量=13986, 成本=110069.82, 手续费=33.02\n", "2022-09-23: 卖出信号(时间退出): 价格=7.99, 持仓数量=13986\n", "2022-09-23: 卖出执行: 价格=7.97, 数量=13986, 收入=110069.82, 手续费=33.44\n", "2022-09-23: 交易利润: 毛利=1398.60, 净利=1332.14\n", "2022-09-27: 买入信号: 价格=7.98, 数量=13978, 交易量=3577148, 平均交易量=1473454\n", "2022-09-28: 买入执行: 价格=7.96, 数量=13978, 成本=111264.88, 手续费=33.38\n", "2022-09-28: 卖出信号(时间退出): 价格=7.79, 持仓数量=13978\n", "2022-09-28: 卖出执行: 价格=7.79, 数量=13978, 收入=111264.88, 手续费=32.67\n", "2022-09-28: 交易利润: 毛利=-2376.26, 净利=-2442.31\n", "2022-10-10: 买入信号: 价格=7.56, 数量=14432, 交易量=2556700, 平均交易量=1133443\n", "2022-10-10: 买入执行: 价格=7.56, 数量=14432, 成本=109105.92, 手续费=32.73\n", "2022-10-10: 卖出信号(时间退出): 价格=7.47, 持仓数量=14432\n", "2022-10-10: 卖出执行: 价格=7.47, 数量=14432, 收入=109105.92, 手续费=32.34\n", "2022-10-10: 交易利润: 毛利=-1298.88, 净利=-1363.95\n", "2022-10-10: 买入信号: 价格=7.34, 数量=14678, 交易量=2408730, 平均交易量=1166820\n", "2022-10-11: 买入执行: 价格=7.34, 数量=14678, 成本=107736.52, 手续费=32.32\n", "2022-10-11: 卖出信号(时间退出): 价格=7.29, 持仓数量=14678\n", "2022-10-11: 卖出执行: 价格=7.29, 数量=14678, 收入=107736.52, 手续费=32.10\n", "2022-10-11: 交易利润: 毛利=-733.90, 净利=-798.32\n", "2022-10-12: 买入信号: 价格=7.47, 数量=14316, 交易量=3327310, 平均交易量=1422240\n", "2022-10-12: 订单被拒绝或取消: 7\n", "2022-10-18: 买入信号: 价格=7.82, 数量=13675, 交易量=4276058, 平均交易量=1631304\n", "2022-10-18: 买入执行: 价格=7.82, 数量=13675, 成本=106938.50, 手续费=32.08\n", "2022-10-18: 卖出信号(时间退出): 价格=7.90, 持仓数量=13675\n", "2022-10-18: 卖出执行: 价格=7.90, 数量=13675, 收入=106938.50, 手续费=32.41\n", "2022-10-18: 交易利润: 毛利=1094.00, 净利=1029.51\n", "2022-10-19: 买入信号: 价格=7.97, 数量=13547, 交易量=4699884, 平均交易量=1683149\n", "2022-10-19: 买入执行: 价格=7.97, 数量=13547, 成本=107969.59, 手续费=32.39\n", "2022-10-19: 卖出信号(时间退出): 价格=7.94, 持仓数量=13547\n", "2022-10-19: 卖出执行: 价格=7.94, 数量=13547, 收入=107969.59, 手续费=32.27\n", "2022-10-19: 交易利润: 毛利=-406.41, 净利=-471.07\n", "2022-10-20: 买入信号: 价格=7.74, 数量=13889, 交易量=3214229, 平均交易量=1506603\n", "2022-10-20: 买入执行: 价格=7.74, 数量=13889, 成本=107500.86, 手续费=32.25\n", "2022-10-20: 卖出信号(时间退出): 价格=7.86, 持仓数量=13889\n", "2022-10-20: 卖出执行: 价格=7.88, 数量=13889, 收入=107500.86, 手续费=32.83\n", "2022-10-20: 交易利润: 毛利=1944.46, 净利=1879.38\n", "2022-10-24: 买入信号: 价格=7.90, 数量=13845, 交易量=3508300, 平均交易量=1275891\n", "2022-10-24: 买入执行: 价格=7.90, 数量=13845, 成本=109375.50, 手续费=32.81\n", "2022-10-24: 卖出信号(时间退出): 价格=7.80, 持仓数量=13845\n", "2022-10-24: 卖出执行: 价格=7.80, 数量=13845, 收入=109375.50, 手续费=32.40\n", "2022-10-24: 交易利润: 毛利=-1384.50, 净利=-1449.71\n", "2022-10-25: 买入信号: 价格=7.65, 数量=14108, 交易量=3433840, 平均交易量=1390292\n", "2022-10-25: 买入执行: 价格=7.64, 数量=14108, 成本=107785.12, 手续费=32.34\n", "2022-10-25: 卖出信号(时间退出): 价格=7.71, 持仓数量=14108\n", "2022-10-25: 卖出执行: 价格=7.71, 数量=14108, 收入=107785.12, 手续费=32.63\n", "2022-10-25: 交易利润: 毛利=987.56, 净利=922.59\n", "2022-10-27: 买入信号: 价格=7.86, 数量=13849, 交易量=4599600, 平均交易量=1527441\n", "2022-10-27: 买入执行: 价格=7.86, 数量=13849, 成本=108853.14, 手续费=32.66\n", "2022-10-27: 卖出信号(时间退出): 价格=7.99, 持仓数量=13849\n", "2022-10-27: 卖出执行: 价格=7.98, 数量=13849, 收入=108853.14, 手续费=33.15\n", "2022-10-27: 交易利润: 毛利=1661.88, 净利=1596.07\n", "2022-10-28: 买入信号: 价格=7.86, 数量=14052, 交易量=4285037, 平均交易量=1981059\n", "2022-10-28: 买入执行: 价格=7.85, 数量=14052, 成本=110308.20, 手续费=33.09\n", "2022-10-28: 卖出信号(时间退出): 价格=7.74, 持仓数量=14052\n", "2022-10-28: 卖出执行: 价格=7.74, 数量=14052, 收入=110308.20, 手续费=32.63\n", "2022-10-28: 交易利润: 毛利=-1545.72, 净利=-1611.44\n", "2022-11-01: 买入信号: 价格=7.66, 数量=14208, 交易量=4008000, 平均交易量=1997676\n", "2022-11-01: 订单被拒绝或取消: 7\n", "2022-11-02: 买入信号: 价格=7.96, 数量=13673, 交易量=4995546, 平均交易量=2171116\n", "2022-11-02: 买入执行: 价格=7.96, 数量=13673, 成本=108837.08, 手续费=32.65\n", "2022-11-02: 卖出信号(时间退出): 价格=7.92, 持仓数量=13673\n", "2022-11-02: 卖出执行: 价格=7.92, 数量=13673, 收入=108837.08, 手续费=32.49\n", "2022-11-02: 交易利润: 毛利=-546.92, 净利=-612.06\n", "2022-11-03: 买入信号: 价格=7.99, 数量=13545, 交易量=3433793, 平均交易量=1703921\n", "2022-11-04: 买入执行: 价格=7.99, 数量=13545, 成本=108224.55, 手续费=32.47\n", "2022-11-04: 卖出信号(时间退出): 价格=8.03, 持仓数量=13545\n", "2022-11-04: 卖出执行: 价格=8.04, 数量=13545, 收入=108224.55, 手续费=32.67\n", "2022-11-04: 交易利润: 毛利=677.25, 净利=612.11\n", "2022-11-04: 买入信号: 价格=8.12, 数量=13403, 交易量=5274900, 平均交易量=2587669\n", "2022-11-04: 买入执行: 价格=8.12, 数量=13403, 成本=108832.36, 手续费=32.65\n", "2022-11-04: 卖出信号(时间退出): 价格=8.09, 持仓数量=13403\n", "2022-11-07: 卖出执行: 价格=8.08, 数量=13403, 收入=108832.36, 手续费=32.49\n", "2022-11-07: 交易利润: 毛利=-536.12, 净利=-601.26\n", "2022-11-07: 买入信号: 价格=8.10, 数量=13362, 交易量=6586051, 平均交易量=2994352\n", "2022-11-07: 买入执行: 价格=8.10, 数量=13362, 成本=108232.20, 手续费=32.47\n", "2022-11-07: 卖出信号(时间退出): 价格=8.15, 持仓数量=13362\n", "2022-11-07: 卖出执行: 价格=8.15, 数量=13362, 收入=108232.20, 手续费=32.67\n", "2022-11-07: 交易利润: 毛利=668.10, 净利=602.96\n", "2022-11-09: 买入信号: 价格=8.09, 数量=13453, 交易量=3489100, 平均交易量=1724819\n", "2022-11-09: 买入执行: 价格=8.08, 数量=13453, 成本=108700.24, 手续费=32.61\n", "2022-11-09: 卖出信号(时间退出): 价格=8.05, 持仓数量=13453\n", "2022-11-09: 卖出执行: 价格=8.06, 数量=13453, 收入=108700.24, 手续费=32.53\n", "2022-11-09: 交易利润: 毛利=-269.06, 净利=-334.20\n", "2022-11-11: 买入信号: 价格=8.03, 数量=13512, 交易量=6705900, 平均交易量=1846029\n", "2022-11-11: 买入执行: 价格=8.03, 数量=13512, 成本=108501.36, 手续费=32.55\n", "2022-11-11: 卖出信号(时间退出): 价格=8.03, 持仓数量=13512\n", "2022-11-11: 卖出执行: 价格=8.04, 数量=13512, 收入=108501.36, 手续费=32.59\n", "2022-11-11: 交易利润: 毛利=135.12, 净利=69.98\n", "2022-11-15: 买入信号: 价格=8.11, 数量=13388, 交易量=4159600, 平均交易量=1923429\n", "2022-11-15: 订单被拒绝或取消: 7\n", "2022-11-16: 买入信号: 价格=8.24, 数量=13176, 交易量=5183009, 平均交易量=2286621\n", "2022-11-16: 买入执行: 价格=8.23, 数量=13176, 成本=108438.48, 手续费=32.53\n", "2022-11-16: 卖出信号(时间退出): 价格=8.20, 持仓数量=13176\n", "2022-11-16: 卖出执行: 价格=8.20, 数量=13176, 收入=108438.48, 手续费=32.41\n", "2022-11-16: 交易利润: 毛利=-395.28, 净利=-460.22\n", "2022-11-18: 买入信号: 价格=8.20, 数量=13185, 交易量=3204100, 平均交易量=1552335\n", "2022-11-18: 买入执行: 价格=8.20, 数量=13185, 成本=108117.00, 手续费=32.44\n", "2022-11-18: 卖出信号(时间退出): 价格=8.17, 持仓数量=13185\n", "2022-11-18: 卖出执行: 价格=8.17, 数量=13185, 收入=108117.00, 手续费=32.32\n", "2022-11-18: 交易利润: 毛利=-395.55, 净利=-460.30\n", "2022-11-18: 买入信号: 价格=8.09, 数量=13307, 交易量=4184196, 平均交易量=1682695\n", "2022-11-21: 买入执行: 价格=8.06, 数量=13307, 成本=107254.42, 手续费=32.18\n", "2022-11-21: 卖出信号(时间退出): 价格=8.02, 持仓数量=13307\n", "2022-11-21: 卖出执行: 价格=8.02, 数量=13307, 收入=107254.42, 手续费=32.02\n", "2022-11-21: 交易利润: 毛利=-532.28, 净利=-596.47\n", "2022-11-23: 买入信号: 价格=7.87, 数量=13603, 交易量=3085600, 平均交易量=1414339\n", "2022-11-23: 买入执行: 价格=7.87, 数量=13603, 成本=107055.61, 手续费=32.12\n", "2022-11-23: 卖出信号(时间退出): 价格=7.85, 持仓数量=13603\n", "2022-11-23: 卖出执行: 价格=7.84, 数量=13603, 收入=107055.61, 手续费=31.99\n", "2022-11-23: 交易利润: 毛利=-408.09, 净利=-472.20\n", "2022-11-24: 买入信号: 价格=8.03, 数量=13273, 交易量=5053878, 平均交易量=1310890\n", "2022-11-25: 买入执行: 价格=8.02, 数量=13273, 成本=106449.46, 手续费=31.93\n", "2022-11-25: 卖出信号(时间退出): 价格=7.98, 持仓数量=13273\n", "2022-11-25: 卖出执行: 价格=7.97, 数量=13273, 收入=106449.46, 手续费=31.74\n", "2022-11-25: 交易利润: 毛利=-663.65, 净利=-727.32\n", "2022-11-25: 买入信号: 价格=7.91, 数量=13383, 交易量=2926300, 平均交易量=1308851\n", "2022-11-25: 买入执行: 价格=7.91, 数量=13383, 成本=105859.53, 手续费=31.76\n", "2022-11-28: 卖出信号(时间退出): 价格=7.75, 持仓数量=13383\n", "2022-11-28: 卖出执行: 价格=7.75, 数量=13383, 收入=105859.53, 手续费=31.12\n", "2022-11-28: 交易利润: 毛利=-2141.28, 净利=-2204.15\n", "2022-11-29: 买入信号: 价格=7.83, 数量=13238, 交易量=2788796, 平均交易量=1181227\n", "2022-11-29: 买入执行: 价格=7.83, 数量=13238, 成本=103653.54, 手续费=31.10\n", "2022-11-29: 卖出信号(时间退出): 价格=7.94, 持仓数量=13238\n", "2022-11-29: 卖出执行: 价格=7.94, 数量=13238, 收入=103653.54, 手续费=31.53\n", "2022-11-29: 交易利润: 毛利=1456.18, 净利=1393.55\n", "2022-12-01: 买入信号: 价格=8.07, 数量=13017, 交易量=6414565, 平均交易量=1549956\n", "2022-12-01: 订单被拒绝或取消: 7\n", "2022-12-05: 买入信号: 价格=8.06, 数量=13033, 交易量=3722521, 平均交易量=1193033\n", "2022-12-05: 买入执行: 价格=8.06, 数量=13033, 成本=105045.98, 手续费=31.51\n", "2022-12-05: 卖出信号(时间退出): 价格=8.08, 持仓数量=13033\n", "2022-12-05: 卖出执行: 价格=8.09, 数量=13033, 收入=105045.98, 手续费=31.63\n", "2022-12-05: 交易利润: 毛利=390.99, 净利=327.85\n", "2022-12-05: 买入信号: 价格=8.13, 数量=12961, 交易量=2551022, 平均交易量=1273548\n", "2022-12-06: 订单被拒绝或取消: 7\n", "2022-12-08: 买入信号: 价格=7.97, 数量=13222, 交易量=4017482, 平均交易量=1464187\n", "2022-12-08: 买入执行: 价格=7.97, 数量=13222, 成本=105379.34, 手续费=31.61\n", "2022-12-08: 卖出信号(时间退出): 价格=7.97, 持仓数量=13222\n", "2022-12-08: 卖出执行: 价格=7.97, 数量=13222, 收入=105379.34, 手续费=31.61\n", "2022-12-08: 交易利润: 毛利=0.00, 净利=-63.23\n", "2022-12-12: 买入信号: 价格=7.95, 数量=13247, 交易量=3221100, 平均交易量=1429576\n", "2022-12-12: 买入执行: 价格=7.94, 数量=13247, 成本=105181.18, 手续费=31.55\n", "2022-12-12: 卖出信号(时间退出): 价格=8.09, 持仓数量=13247\n", "2022-12-12: 卖出执行: 价格=8.09, 数量=13247, 收入=105181.18, 手续费=32.15\n", "2022-12-12: 交易利润: 毛利=1987.05, 净利=1923.35\n", "2022-12-12: 买入信号: 价格=8.13, 数量=13190, 交易量=6831100, 平均交易量=1891099\n", "2022-12-12: 买入执行: 价格=8.13, 数量=13190, 成本=107234.70, 手续费=32.17\n", "2022-12-12: 卖出信号(时间退出): 价格=8.12, 持仓数量=13190\n", "2022-12-13: 卖出执行: 价格=8.09, 数量=13190, 收入=107234.70, 手续费=32.01\n", "2022-12-13: 交易利润: 毛利=-527.60, 净利=-591.78\n", "2022-12-14: 买入信号: 价格=8.07, 数量=13215, 交易量=4901200, 平均交易量=1926150\n", "2022-12-14: 买入执行: 价格=8.07, 数量=13215, 成本=106645.05, 手续费=31.99\n", "2022-12-14: 卖出信号(时间退出): 价格=8.06, 持仓数量=13215\n", "2022-12-14: 卖出执行: 价格=8.06, 数量=13215, 收入=106645.05, 手续费=31.95\n", "2022-12-14: 交易利润: 毛利=-132.15, 净利=-196.10\n", "2022-12-15: 买入信号: 价格=8.09, 数量=13158, 交易量=7994482, 平均交易量=1801534\n", "2022-12-15: 买入执行: 价格=8.09, 数量=13158, 成本=106448.22, 手续费=31.93\n", "2022-12-15: 卖出信号(时间退出): 价格=8.08, 持仓数量=13158\n", "2022-12-15: 卖出执行: 价格=8.08, 数量=13158, 收入=106448.22, 手续费=31.89\n", "2022-12-15: 交易利润: 毛利=-131.58, 净利=-195.41\n", "2022-12-19: 买入信号: 价格=7.87, 数量=13501, 交易量=3831200, 平均交易量=1180486\n", "2022-12-19: 买入执行: 价格=7.86, 数量=13501, 成本=106117.86, 手续费=31.84\n", "2022-12-19: 卖出信号(时间退出): 价格=7.76, 持仓数量=13501\n", "2022-12-19: 卖出执行: 价格=7.76, 数量=13501, 收入=106117.86, 手续费=31.43\n", "2022-12-19: 交易利润: 毛利=-1350.10, 净利=-1413.37\n", "2022-12-22: 买入信号: 价格=7.59, 数量=13813, 交易量=1548900, 平均交易量=773639\n", "2022-12-22: 买入执行: 价格=7.59, 数量=13813, 成本=104840.67, 手续费=31.45\n", "2022-12-22: 卖出信号(时间退出): 价格=7.59, 持仓数量=13813\n", "2022-12-22: 卖出执行: 价格=7.59, 数量=13813, 收入=104840.67, 手续费=31.45\n", "2022-12-22: 交易利润: 毛利=0.00, 净利=-62.90\n", "2022-12-28: 买入信号: 价格=7.47, 数量=14026, 交易量=1919800, 平均交易量=692629\n", "2022-12-28: 订单被拒绝或取消: 7\n", "2022-12-29: 买入信号: 价格=7.38, 数量=14197, 交易量=2101100, 平均交易量=755107\n", "2022-12-29: 买入执行: 价格=7.38, 数量=14197, 成本=104773.86, 手续费=31.43\n", "2022-12-29: 卖出信号(时间退出): 价格=7.42, 持仓数量=14197\n", "2022-12-29: 卖出执行: 价格=7.42, 数量=14197, 收入=104773.86, 手续费=31.60\n", "2022-12-29: 交易利润: 毛利=567.88, 净利=504.85\n", "2023-01-03: 买入信号: 价格=7.38, 数量=14266, 交易量=1494603, 平均交易量=716069\n", "2023-01-03: 买入执行: 价格=7.38, 数量=14266, 成本=105283.08, 手续费=31.58\n", "2023-01-03: 卖出信号(时间退出): 价格=7.47, 持仓数量=14266\n", "2023-01-03: 卖出执行: 价格=7.48, 数量=14266, 收入=105283.08, 手续费=32.01\n", "2023-01-03: 交易利润: 毛利=1426.60, 净利=1363.00\n", "2023-01-03: 买入信号: 价格=7.54, 数量=14144, 交易量=2441066, 平均交易量=1187352\n", "2023-01-04: 买入执行: 价格=7.53, 数量=14144, 成本=106504.32, 手续费=31.95\n", "2023-01-04: 卖出信号(时间退出): 价格=7.54, 持仓数量=14144\n", "2023-01-04: 卖出执行: 价格=7.54, 数量=14144, 收入=106504.32, 手续费=31.99\n", "2023-01-04: 交易利润: 毛利=141.44, 净利=77.49\n", "2023-01-05: 买入信号: 价格=7.65, 数量=13951, 交易量=3886064, 平均交易量=1217653\n", "2023-01-05: 买入执行: 价格=7.65, 数量=13951, 成本=106725.15, 手续费=32.02\n", "2023-01-05: 卖出信号(时间退出): 价格=7.60, 持仓数量=13951\n", "2023-01-05: 卖出执行: 价格=7.60, 数量=13951, 收入=106725.15, 手续费=31.81\n", "2023-01-05: 交易利润: 毛利=-697.55, 净利=-761.38\n", "2023-01-05: 买入信号: 价格=7.65, 数量=13851, 交易量=3117207, 平均交易量=1248777\n", "2023-01-05: 买入执行: 价格=7.65, 数量=13851, 成本=105960.15, 手续费=31.79\n", "2023-01-06: 卖出信号(时间退出): 价格=8.06, 持仓数量=13851\n", "2023-01-06: 卖出执行: 价格=8.06, 数量=13851, 收入=105960.15, 手续费=33.49\n", "2023-01-06: 交易利润: 毛利=5678.91, 净利=5613.63\n", "2023-01-06: 买入信号: 价格=7.98, 数量=13981, 交易量=11499618, 平均交易量=2858863\n", "2023-01-06: 订单被拒绝或取消: 7\n", "2023-01-11: 买入信号: 价格=7.65, 数量=14585, 交易量=4209300, 平均交易量=1358825\n", "2023-01-12: 买入执行: 价格=7.63, 数量=14585, 成本=111283.55, 手续费=33.39\n", "2023-01-12: 卖出信号(时间退出): 价格=7.58, 持仓数量=14585\n", "2023-01-12: 卖出执行: 价格=7.58, 数量=14585, 收入=111283.55, 手续费=33.17\n", "2023-01-12: 交易利润: 毛利=-729.25, 净利=-795.80\n", "2023-01-12: 买入信号: 价格=7.60, 数量=14576, 交易量=3945622, 平均交易量=1882476\n", "2023-01-12: 买入执行: 价格=7.60, 数量=14576, 成本=110777.60, 手续费=33.23\n", "2023-01-12: 卖出信号(时间退出): 价格=7.67, 持仓数量=14576\n", "2023-01-12: 卖出执行: 价格=7.68, 数量=14576, 收入=110777.60, 手续费=33.58\n", "2023-01-12: 交易利润: 毛利=1166.08, 净利=1099.26\n", "2023-01-16: 买入信号: 价格=7.72, 数量=14492, 交易量=2944500, 平均交易量=1274872\n", "2023-01-16: 订单被拒绝或取消: 7\n", "2023-01-17: 买入信号: 价格=7.84, 数量=14270, 交易量=5420669, 平均交易量=1789146\n", "2023-01-17: 买入执行: 价格=7.84, 数量=14270, 成本=111876.80, 手续费=33.56\n", "2023-01-17: 卖出信号(时间退出): 价格=7.80, 持仓数量=14270\n", "2023-01-17: 卖出执行: 价格=7.80, 数量=14270, 收入=111876.80, 手续费=33.39\n", "2023-01-17: 交易利润: 毛利=-570.80, 净利=-637.75\n", "2023-01-19: 买入信号: 价格=7.70, 数量=14446, 交易量=4020900, 平均交易量=1242696\n", "2023-01-19: 买入执行: 价格=7.70, 数量=14446, 成本=111234.20, 手续费=33.37\n", "2023-01-19: 卖出信号(时间退出): 价格=7.75, 持仓数量=14446\n", "2023-01-19: 卖出执行: 价格=7.75, 数量=14446, 收入=111234.20, 手续费=33.59\n", "2023-01-19: 交易利润: 毛利=722.30, 净利=655.34\n", "2023-01-30: 买入信号: 价格=7.91, 数量=14146, 交易量=5238900, 平均交易量=1504320\n", "2023-01-30: 买入执行: 价格=7.91, 数量=14146, 成本=111894.86, 手续费=33.57\n", "2023-01-30: 卖出信号(时间退出): 价格=7.97, 持仓数量=14146\n", "2023-01-30: 卖出执行: 价格=7.97, 数量=14146, 收入=111894.86, 手续费=33.82\n", "2023-01-30: 交易利润: 毛利=848.76, 净利=781.37\n", "2023-02-01: 买入信号: 价格=8.01, 数量=14067, 交易量=2869200, 平均交易量=1374903\n", "2023-02-01: 买入执行: 价格=8.01, 数量=14067, 成本=112676.67, 手续费=33.80\n", "2023-02-01: 卖出信号(时间退出): 价格=8.00, 持仓数量=14067\n", "2023-02-01: 卖出执行: 价格=8.00, 数量=14067, 收入=112676.67, 手续费=33.76\n", "2023-02-01: 交易利润: 毛利=-140.67, 净利=-208.23\n", "2023-02-03: 买入信号: 价格=7.99, 数量=14076, 交易量=3973500, 平均交易量=1681622\n", "2023-02-03: 买入执行: 价格=7.99, 数量=14076, 成本=112467.24, 手续费=33.74\n", "2023-02-03: 卖出信号(时间退出): 价格=7.93, 持仓数量=14076\n", "2023-02-03: 卖出执行: 价格=7.92, 数量=14076, 收入=112467.24, 手续费=33.44\n", "2023-02-03: 交易利润: 毛利=-985.32, 净利=-1052.50\n", "2023-02-06: 买入信号: 价格=8.07, 数量=13806, 交易量=3267900, 平均交易量=1555920\n", "2023-02-06: 订单被拒绝或取消: 7\n", "2023-02-08: 买入信号: 价格=8.16, 数量=13654, 交易量=7232100, 平均交易量=1935265\n", "2023-02-08: 买入执行: 价格=8.16, 数量=13654, 成本=111416.64, 手续费=33.42\n", "2023-02-08: 卖出信号(时间退出): 价格=8.27, 持仓数量=13654\n", "2023-02-08: 卖出执行: 价格=8.27, 数量=13654, 收入=111416.64, 手续费=33.88\n", "2023-02-08: 交易利润: 毛利=1501.94, 净利=1434.64\n", "2023-02-10: 买入信号: 价格=8.55, 数量=13199, 交易量=7732700, 平均交易量=2934158\n", "2023-02-10: 买入执行: 价格=8.55, 数量=13199, 成本=112851.45, 手续费=33.86\n", "2023-02-10: 卖出信号(时间退出): 价格=8.40, 持仓数量=13199\n", "2023-02-10: 卖出执行: 价格=8.40, 数量=13199, 收入=112851.45, 手续费=33.26\n", "2023-02-10: 交易利润: 毛利=-1979.85, 净利=-2046.97\n", "2023-02-14: 买入信号: 价格=8.61, 数量=12869, 交易量=5410324, 平均交易量=2202330\n", "2023-02-14: 买入执行: 价格=8.61, 数量=12869, 成本=110802.09, 手续费=33.24\n", "2023-02-14: 卖出信号(时间退出): 价格=8.59, 持仓数量=12869\n", "2023-02-14: 卖出执行: 价格=8.59, 数量=12869, 收入=110802.09, 手续费=33.16\n", "2023-02-14: 交易利润: 毛利=-257.38, 净利=-323.78\n", "2023-02-16: 买入信号: 价格=8.69, 数量=12713, 交易量=5224880, 平均交易量=2314113\n", "2023-02-16: 买入执行: 价格=8.69, 数量=12713, 成本=110475.97, 手续费=33.14\n", "2023-02-16: 卖出信号(时间退出): 价格=8.68, 持仓数量=12713\n", "2023-02-16: 卖出执行: 价格=8.68, 数量=12713, 收入=110475.97, 手续费=33.10\n", "2023-02-16: 交易利润: 毛利=-127.13, 净利=-193.38\n", "2023-02-16: 买入信号: 价格=8.45, 数量=13051, 交易量=9526400, 平均交易量=3113690\n", "2023-02-16: 买入执行: 价格=8.42, 数量=13051, 成本=109889.42, 手续费=32.97\n", "2023-02-17: 卖出信号(时间退出): 价格=8.51, 持仓数量=13051\n", "2023-02-17: 卖出执行: 价格=8.51, 数量=13051, 收入=109889.42, 手续费=33.32\n", "2023-02-17: 交易利润: 毛利=1174.59, 净利=1108.30\n", "2023-02-23: 买入信号: 价格=8.42, 数量=13229, 交易量=2276500, 平均交易量=1095554\n", "2023-02-23: 买入执行: 价格=8.42, 数量=13229, 成本=111388.18, 手续费=33.42\n", "2023-02-24: 卖出信号(时间退出): 价格=8.46, 持仓数量=13229\n", "2023-02-24: 卖出执行: 价格=8.47, 数量=13229, 收入=111388.18, 手续费=33.61\n", "2023-02-24: 交易利润: 毛利=661.45, 净利=594.42\n", "2023-02-27: 买入信号: 价格=8.37, 数量=13380, 交易量=2803093, 平均交易量=1174316\n", "2023-02-27: 买入执行: 价格=8.36, 数量=13380, 成本=111856.80, 手续费=33.56\n", "2023-02-28: 卖出信号(时间退出): 价格=8.38, 持仓数量=13380\n", "2023-02-28: 卖出执行: 价格=8.38, 数量=13380, 收入=111856.80, 手续费=33.64\n", "2023-02-28: 交易利润: 毛利=267.60, 净利=200.41\n", "2023-02-28: 买入信号: 价格=8.47, 数量=13245, 交易量=4974000, 平均交易量=1334092\n", "2023-02-28: 买入执行: 价格=8.47, 数量=13245, 成本=112185.15, 手续费=33.66\n", "2023-02-28: 卖出信号(时间退出): 价格=8.39, 持仓数量=13245\n", "2023-02-28: 卖出执行: 价格=8.38, 数量=13245, 收入=112185.15, 手续费=33.30\n", "2023-02-28: 交易利润: 毛利=-1192.05, 净利=-1259.00\n", "2023-03-07: 买入信号: 价格=8.43, 数量=13159, 交易量=2181600, 平均交易量=1050157\n", "2023-03-07: 买入执行: 价格=8.43, 数量=13159, 成本=110930.37, 手续费=33.28\n", "2023-03-07: 卖出信号(时间退出): 价格=8.35, 持仓数量=13159\n", "2023-03-07: 卖出执行: 价格=8.35, 数量=13159, 收入=110930.37, 手续费=32.96\n", "2023-03-07: 交易利润: 毛利=-1052.72, 净利=-1118.96\n", "2023-03-13: 买入信号: 价格=8.33, 数量=13182, 交易量=2271913, 平均交易量=1020208\n", "2023-03-13: 买入执行: 价格=8.33, 数量=13182, 成本=109806.06, 手续费=32.94\n", "2023-03-13: 卖出信号(时间退出): 价格=8.35, 持仓数量=13182\n", "2023-03-13: 卖出执行: 价格=8.36, 数量=13182, 收入=109806.06, 手续费=33.06\n", "2023-03-13: 交易利润: 毛利=395.46, 净利=329.46\n", "2023-03-14: 买入信号: 价格=8.25, 数量=13350, 交易量=2419000, 平均交易量=1176372\n", "2023-03-14: 买入执行: 价格=8.25, 数量=13350, 成本=110137.50, 手续费=33.04\n", "2023-03-14: 卖出信号(时间退出): 价格=8.28, 持仓数量=13350\n", "2023-03-14: 卖出执行: 价格=8.28, 数量=13350, 收入=110137.50, 手续费=33.16\n", "2023-03-14: 交易利润: 毛利=400.50, 净利=334.30\n", "2023-03-15: 买入信号: 价格=8.50, 数量=12997, 交易量=4985515, 平均交易量=1454511\n", "2023-03-15: 订单被拒绝或取消: 7\n", "2023-03-17: 买入信号: 价格=8.48, 数量=13027, 交易量=2228300, 平均交易量=1035566\n", "2023-03-17: 买入执行: 价格=8.48, 数量=13027, 成本=110468.96, 手续费=33.14\n", "2023-03-17: 卖出信号(时间退出): 价格=8.47, 持仓数量=13027\n", "2023-03-17: 卖出执行: 价格=8.46, 数量=13027, 收入=110468.96, 手续费=33.06\n", "2023-03-17: 交易利润: 毛利=-260.54, 净利=-326.74\n", "2023-03-21: 买入信号: 价格=8.56, 数量=12868, 交易量=3520131, 平均交易量=1193287\n", "2023-03-21: 买入执行: 价格=8.56, 数量=12868, 成本=110150.08, 手续费=33.05\n", "2023-03-21: 卖出信号(时间退出): 价格=8.56, 持仓数量=12868\n", "2023-03-21: 卖出执行: 价格=8.56, 数量=12868, 收入=110150.08, 手续费=33.05\n", "2023-03-21: 交易利润: 毛利=0.00, 净利=-66.09\n", "2023-03-22: 买入信号: 价格=8.56, 数量=12860, 交易量=3402900, 平均交易量=1688556\n", "2023-03-22: 买入执行: 价格=8.56, 数量=12860, 成本=110081.60, 手续费=33.02\n", "2023-03-22: 卖出信号(时间退出): 价格=8.57, 持仓数量=12860\n", "2023-03-22: 卖出执行: 价格=8.57, 数量=12860, 收入=110081.60, 手续费=33.06\n", "2023-03-22: 交易利润: 毛利=128.60, 净利=62.51\n", "2023-03-23: 买入信号: 价格=8.76, 数量=12573, 交易量=3158400, 平均交易量=1564685\n", "2023-03-24: 订单被拒绝或取消: 7\n", "2023-03-29: 买入信号: 价格=8.61, 数量=12792, 交易量=3572200, 平均交易量=1421787\n", "2023-03-29: 订单被拒绝或取消: 7\n", "2023-03-31: 买入信号: 价格=8.48, 数量=12989, 交易量=2162300, 平均交易量=1005599\n", "2023-03-31: 订单被拒绝或取消: 7\n", "2023-04-03: 买入信号: 价格=8.64, 数量=12748, 交易量=1944400, 平均交易量=925477\n", "2023-04-03: 买入执行: 价格=8.64, 数量=12748, 成本=110142.72, 手续费=33.04\n", "2023-04-03: 卖出信号(时间退出): 价格=8.65, 持仓数量=12748\n", "2023-04-03: 卖出执行: 价格=8.65, 数量=12748, 收入=110142.72, 手续费=33.08\n", "2023-04-03: 交易利润: 毛利=127.48, 净利=61.36\n", "2023-04-04: 买入信号: 价格=8.75, 数量=12595, 交易量=2859628, 平均交易量=1140027\n", "2023-04-04: 订单被拒绝或取消: 7\n", "2023-04-04: 买入信号: 价格=8.76, 数量=12580, 交易量=3193600, 平均交易量=1303387\n", "2023-04-04: 买入执行: 价格=8.76, 数量=12580, 成本=110200.80, 手续费=33.06\n", "2023-04-04: 卖出信号(时间退出): 价格=8.74, 持仓数量=12580\n", "2023-04-04: 卖出执行: 价格=8.74, 数量=12580, 收入=110200.80, 手续费=32.98\n", "2023-04-04: 交易利润: 毛利=-251.60, 净利=-317.64\n", "2023-04-06: 买入信号: 价格=8.85, 数量=12417, 交易量=4138019, 平均交易量=1598597\n", "2023-04-06: 买入执行: 价格=8.85, 数量=12417, 成本=109890.45, 手续费=32.97\n", "2023-04-06: 卖出信号(时间退出): 价格=9.01, 持仓数量=12417\n", "2023-04-06: 卖出执行: 价格=9.01, 数量=12417, 收入=109890.45, 手续费=33.56\n", "2023-04-06: 交易利润: 毛利=1986.72, 净利=1920.19\n", "2023-04-07: 买入信号: 价格=8.90, 数量=12562, 交易量=5437800, 平均交易量=2371186\n", "2023-04-07: 买入执行: 价格=8.90, 数量=12562, 成本=111801.80, 手续费=33.54\n", "2023-04-07: 卖出信号(时间退出): 价格=8.89, 持仓数量=12562\n", "2023-04-07: 卖出执行: 价格=8.89, 数量=12562, 收入=111801.80, 手续费=33.50\n", "2023-04-07: 交易利润: 毛利=-125.62, 净利=-192.66\n", "2023-04-12: 买入信号: 价格=8.90, 数量=12541, 交易量=4733880, 平均交易量=1963972\n", "2023-04-12: 买入执行: 价格=8.90, 数量=12541, 成本=111614.90, 手续费=33.48\n", "2023-04-12: 卖出信号(时间退出): 价格=8.91, 持仓数量=12541\n", "2023-04-12: 卖出执行: 价格=8.91, 数量=12541, 收入=111614.90, 手续费=33.52\n", "2023-04-12: 交易利润: 毛利=125.41, 净利=58.40\n", "2023-04-13: 买入信号: 价格=8.69, 数量=12851, 交易量=4667800, 平均交易量=1874715\n", "2023-04-13: 买入执行: 价格=8.69, 数量=12851, 成本=111675.19, 手续费=33.50\n", "2023-04-13: 卖出信号(时间退出): 价格=8.65, 持仓数量=12851\n", "2023-04-13: 卖出执行: 价格=8.64, 数量=12851, 收入=111675.19, 手续费=33.31\n", "2023-04-13: 交易利润: 毛利=-642.55, 净利=-709.36\n", "2023-04-14: 买入信号: 价格=8.46, 数量=13116, 交易量=5381700, 平均交易量=2211160\n", "2023-04-14: 订单被拒绝或取消: 7\n", "2023-04-18: 买入信号: 价格=8.10, 数量=13699, 交易量=17457236, 平均交易量=2696725\n", "2023-04-18: 买入执行: 价格=8.10, 数量=13699, 成本=110961.90, 手续费=33.29\n", "2023-04-18: 卖出信号(时间退出): 价格=7.89, 持仓数量=13699\n", "2023-04-18: 卖出执行: 价格=7.89, 数量=13699, 收入=110961.90, 手续费=32.43\n", "2023-04-18: 交易利润: 毛利=-2876.79, 净利=-2942.50\n", "2023-04-21: 买入信号: 价格=7.71, 数量=14011, 交易量=4285980, 平均交易量=1775100\n", "2023-04-21: 买入执行: 价格=7.71, 数量=14011, 成本=108024.81, 手续费=32.41\n", "2023-04-21: 卖出信号(时间退出): 价格=7.55, 持仓数量=14011\n", "2023-04-21: 卖出执行: 价格=7.56, 数量=14011, 收入=108024.81, 手续费=31.78\n", "2023-04-21: 交易利润: 毛利=-2101.65, 净利=-2165.83\n", "2023-04-27: 买入信号: 价格=7.68, 数量=13783, 交易量=6323437, 平均交易量=1499745\n", "2023-04-27: 买入执行: 价格=7.68, 数量=13783, 成本=105853.44, 手续费=31.76\n", "2023-04-27: 卖出信号(时间退出): 价格=7.58, 持仓数量=13783\n", "2023-04-27: 卖出执行: 价格=7.59, 数量=13783, 收入=105853.44, 手续费=31.38\n", "2023-04-27: 交易利润: 毛利=-1240.47, 净利=-1303.61\n", "2023-04-28: 买入信号: 价格=7.94, 数量=13168, 交易量=6937440, 平均交易量=2226488\n", "2023-04-28: 买入执行: 价格=7.94, 数量=13168, 成本=104553.92, 手续费=31.37\n", "2023-04-28: 卖出信号(时间退出): 价格=8.11, 持仓数量=13168\n", "2023-05-04: 卖出执行: 价格=8.07, 数量=13168, 收入=104553.92, 手续费=31.88\n", "2023-05-04: 交易利润: 毛利=1711.84, 净利=1648.59\n", "2023-05-09: 买入信号: 价格=8.13, 数量=13063, 交易量=3419300, 平均交易量=1343033\n", "2023-05-09: 买入执行: 价格=8.13, 数量=13063, 成本=106202.19, 手续费=31.86\n", "2023-05-09: 卖出信号(时间退出): 价格=8.15, 持仓数量=13063\n", "2023-05-09: 卖出执行: 价格=8.15, 数量=13063, 收入=106202.19, 手续费=31.94\n", "2023-05-09: 交易利润: 毛利=261.26, 净利=197.46\n", "2023-05-11: 买入信号: 价格=8.30, 数量=12819, 交易量=4485700, 平均交易量=1362728\n", "2023-05-11: 买入执行: 价格=8.30, 数量=12819, 成本=106397.70, 手续费=31.92\n", "2023-05-11: 卖出信号(时间退出): 价格=8.24, 持仓数量=12819\n", "2023-05-11: 卖出执行: 价格=8.24, 数量=12819, 收入=106397.70, 手续费=31.69\n", "2023-05-11: 交易利润: 毛利=-769.14, 净利=-832.75\n", "2023-05-15: 买入信号: 价格=8.18, 数量=12905, 交易量=4829008, 平均交易量=1245586\n", "2023-05-16: 买入执行: 价格=8.15, 数量=12905, 成本=105175.75, 手续费=31.55\n", "2023-05-16: 卖出信号(时间退出): 价格=8.10, 持仓数量=12905\n", "2023-05-16: 卖出执行: 价格=8.09, 数量=12905, 收入=105175.75, 手续费=31.32\n", "2023-05-16: 交易利润: 毛利=-774.30, 净利=-837.17\n", "2023-05-22: 买入信号: 价格=8.30, 数量=12618, 交易量=2241400, 平均交易量=1082662\n", "2023-05-22: 买入执行: 价格=8.30, 数量=12618, 成本=104729.40, 手续费=31.42\n", "2023-05-22: 卖出信号(时间退出): 价格=8.23, 持仓数量=12618\n", "2023-05-22: 卖出执行: 价格=8.23, 数量=12618, 收入=104729.40, 手续费=31.15\n", "2023-05-22: 交易利润: 毛利=-883.26, 净利=-945.83\n", "2023-05-23: 买入信号: 价格=8.34, 数量=12444, 交易量=2488221, 平均交易量=1117921\n", "2023-05-23: 买入执行: 价格=8.34, 数量=12444, 成本=103782.96, 手续费=31.13\n", "2023-05-23: 卖出信号(时间退出): 价格=8.48, 持仓数量=12444\n", "2023-05-23: 卖出执行: 价格=8.48, 数量=12444, 收入=103782.96, 手续费=31.66\n", "2023-05-23: 交易利润: 毛利=1742.16, 净利=1679.37\n", "2023-05-24: 买入信号: 价格=8.21, 数量=12846, 交易量=3768837, 平均交易量=1651960\n", "2023-05-25: 订单被拒绝或取消: 7\n", "2023-05-29: 买入信号: 价格=8.27, 数量=12752, 交易量=2007300, 平均交易量=922521\n", "2023-05-29: 买入执行: 价格=8.26, 数量=12752, 成本=105331.52, 手续费=31.60\n", "2023-05-29: 卖出信号(时间退出): 价格=8.20, 持仓数量=12752\n", "2023-05-29: 卖出执行: 价格=8.21, 数量=12752, 收入=105331.52, 手续费=31.41\n", "2023-05-29: 交易利润: 毛利=-637.60, 净利=-700.61\n", "2023-05-31: 买入信号: 价格=8.41, 数量=12457, 交易量=5276800, 平均交易量=1080364\n", "2023-06-01: 买入执行: 价格=8.38, 数量=12457, 成本=104389.66, 手续费=31.32\n", "2023-06-01: 卖出信号(时间退出): 价格=8.47, 持仓数量=12457\n", "2023-06-01: 卖出执行: 价格=8.47, 数量=12457, 收入=104389.66, 手续费=31.65\n", "2023-06-01: 交易利润: 毛利=1121.13, 净利=1058.16\n", "2023-06-02: 买入信号: 价格=8.50, 数量=12449, 交易量=4012500, 平均交易量=1720344\n", "2023-06-02: 订单被拒绝或取消: 7\n", "2023-06-05: 买入信号: 价格=8.43, 数量=12553, 交易量=2898000, 平均交易量=1288682\n", "2023-06-05: 买入执行: 价格=8.42, 数量=12553, 成本=105696.26, 手续费=31.71\n", "2023-06-05: 卖出信号(时间退出): 价格=8.43, 持仓数量=12553\n", "2023-06-05: 卖出执行: 价格=8.42, 数量=12553, 收入=105696.26, 手续费=31.71\n", "2023-06-05: 交易利润: 毛利=0.00, 净利=-63.42\n", "2023-06-07: 买入信号: 价格=8.14, 数量=12992, 交易量=2765140, 平均交易量=1166476\n", "2023-06-07: 订单被拒绝或取消: 7\n", "2023-06-12: 买入信号: 价格=8.28, 数量=12773, 交易量=3056150, 平均交易量=993049\n", "2023-06-12: 买入执行: 价格=8.27, 数量=12773, 成本=105632.71, 手续费=31.69\n", "2023-06-12: 卖出信号(时间退出): 价格=8.45, 持仓数量=12773\n", "2023-06-12: 卖出执行: 价格=8.45, 数量=12773, 收入=105632.71, 手续费=32.38\n", "2023-06-12: 交易利润: 毛利=2299.14, 净利=2235.07\n", "2023-06-16: 买入信号: 价格=8.48, 数量=12735, 交易量=2737000, 平均交易量=1006365\n", "2023-06-16: 订单被拒绝或取消: 7\n", "2023-06-16: 买入信号: 价格=8.51, 数量=12690, 交易量=3005400, 平均交易量=1254948\n", "2023-06-16: 买入执行: 价格=8.51, 数量=12690, 成本=107991.90, 手续费=32.40\n", "2023-06-19: 卖出信号(时间退出): 价格=8.52, 持仓数量=12690\n", "2023-06-19: 卖出执行: 价格=8.50, 数量=12690, 收入=107991.90, 手续费=32.36\n", "2023-06-19: 交易利润: 毛利=-126.90, 净利=-191.66\n", "2023-06-21: 买入信号: 价格=8.42, 数量=12803, 交易量=2631647, 平均交易量=1296868\n", "2023-06-21: 买入执行: 价格=8.42, 数量=12803, 成本=107801.26, 手续费=32.34\n", "2023-06-21: 卖出信号(时间退出): 价格=8.40, 持仓数量=12803\n", "2023-06-21: 卖出执行: 价格=8.39, 数量=12803, 收入=107801.26, 手续费=32.23\n", "2023-06-21: 交易利润: 毛利=-384.09, 净利=-448.66\n", "2023-06-26: 买入信号: 价格=8.19, 数量=13108, 交易量=3093600, 平均交易量=1203064\n", "2023-06-26: 买入执行: 价格=8.19, 数量=13108, 成本=107354.52, 手续费=32.21\n", "2023-06-26: 卖出信号(时间退出): 价格=8.22, 持仓数量=13108\n", "2023-06-26: 卖出执行: 价格=8.20, 数量=13108, 收入=107354.52, 手续费=32.25\n", "2023-06-26: 交易利润: 毛利=131.08, 净利=66.63\n", "2023-06-27: 买入信号: 价格=8.20, 数量=13100, 交易量=2331430, 平均交易量=1150756\n", "2023-06-27: 买入执行: 价格=8.20, 数量=13100, 成本=107420.00, 手续费=32.23\n", "2023-06-27: 卖出信号(时间退出): 价格=8.26, 持仓数量=13100\n", "2023-06-27: 卖出执行: 价格=8.26, 数量=13100, 收入=107420.00, 手续费=32.46\n", "2023-06-27: 交易利润: 毛利=786.00, 净利=721.31\n", "2023-06-28: 买入信号: 价格=8.30, 数量=13029, 交易量=6186322, 平均交易量=1433876\n", "2023-06-28: 买入执行: 价格=8.29, 数量=13029, 成本=108010.41, 手续费=32.40\n", "2023-06-28: 卖出信号(时间退出): 价格=8.31, 持仓数量=13029\n", "2023-06-28: 卖出执行: 价格=8.32, 数量=13029, 收入=108010.41, 手续费=32.52\n", "2023-06-28: 交易利润: 毛利=390.87, 净利=325.95\n", "2023-06-29: 买入信号: 价格=8.40, 数量=12912, 交易量=4014900, 平均交易量=1538338\n", "2023-06-29: 买入执行: 价格=8.40, 数量=12912, 成本=108460.80, 手续费=32.54\n", "2023-06-29: 卖出信号(时间退出): 价格=8.48, 持仓数量=12912\n", "2023-06-29: 卖出执行: 价格=8.47, 数量=12912, 收入=108460.80, 手续费=32.81\n", "2023-06-29: 交易利润: 毛利=903.84, 净利=838.49\n", "2023-07-04: 买入信号: 价格=8.64, 数量=12651, 交易量=3553500, 平均交易量=1367089\n", "2023-07-04: 买入执行: 价格=8.63, 数量=12651, 成本=109178.13, 手续费=32.75\n", "2023-07-04: 卖出信号(时间退出): 价格=8.70, 持仓数量=12651\n", "2023-07-04: 卖出执行: 价格=8.70, 数量=12651, 收入=109178.13, 手续费=33.02\n", "2023-07-04: 交易利润: 毛利=885.57, 净利=819.80\n", "2023-07-06: 买入信号: 价格=8.71, 数量=12643, 交易量=3323800, 平均交易量=1465243\n", "2023-07-06: 买入执行: 价格=8.70, 数量=12643, 成本=109994.10, 手续费=33.00\n", "2023-07-06: 卖出信号(时间退出): 价格=8.69, 持仓数量=12643\n", "2023-07-06: 卖出执行: 价格=8.69, 数量=12643, 收入=109994.10, 手续费=32.96\n", "2023-07-06: 交易利润: 毛利=-126.43, 净利=-192.39\n", "2023-07-10: 买入信号: 价格=8.45, 数量=13009, 交易量=3438791, 平均交易量=1240265\n", "2023-07-10: 买入执行: 价格=8.45, 数量=13009, 成本=109926.05, 手续费=32.98\n", "2023-07-10: 卖出信号(时间退出): 价格=8.49, 持仓数量=13009\n", "2023-07-10: 卖出执行: 价格=8.49, 数量=13009, 收入=109926.05, 手续费=33.13\n", "2023-07-10: 交易利润: 毛利=520.36, 净利=454.25\n", "2023-07-12: 买入信号: 价格=8.64, 数量=12776, 交易量=2118281, 平均交易量=1026592\n", "2023-07-12: 买入执行: 价格=8.64, 数量=12776, 成本=110384.64, 手续费=33.12\n", "2023-07-12: 卖出信号(时间退出): 价格=8.66, 持仓数量=12776\n", "2023-07-12: 卖出执行: 价格=8.66, 数量=12776, 收入=110384.64, 手续费=33.19\n", "2023-07-12: 交易利润: 毛利=255.52, 净利=189.21\n", "2023-07-13: 买入信号: 价格=8.68, 数量=12739, 交易量=2597296, 平均交易量=1037598\n", "2023-07-13: 买入执行: 价格=8.68, 数量=12739, 成本=110574.52, 手续费=33.17\n", "2023-07-13: 卖出信号(时间退出): 价格=8.69, 持仓数量=12739\n", "2023-07-13: 卖出执行: 价格=8.67, 数量=12739, 收入=110574.52, 手续费=33.13\n", "2023-07-13: 交易利润: 毛利=-127.39, 净利=-193.70\n", "2023-07-17: 买入信号: 价格=8.83, 数量=12500, 交易量=4252300, 平均交易量=1504585\n", "2023-07-17: 买入执行: 价格=8.82, 数量=12500, 成本=110250.00, 手续费=33.07\n", "2023-07-17: 卖出信号(时间退出): 价格=8.78, 持仓数量=12500\n", "2023-07-17: 卖出执行: 价格=8.77, 数量=12500, 收入=110250.00, 手续费=32.89\n", "2023-07-17: 交易利润: 毛利=-625.00, 净利=-690.96\n", "2023-07-18: 买入信号: 价格=8.89, 数量=12338, 交易量=3284238, 平均交易量=1389189\n", "2023-07-18: 买入执行: 价格=8.88, 数量=12338, 成本=109561.44, 手续费=32.87\n", "2023-07-18: 卖出信号(时间退出): 价格=8.80, 持仓数量=12338\n", "2023-07-18: 卖出执行: 价格=8.80, 数量=12338, 收入=109561.44, 手续费=32.57\n", "2023-07-18: 交易利润: 毛利=-987.04, 净利=-1052.48\n", "2023-07-24: 买入信号: 价格=8.65, 数量=12559, 交易量=3129300, 平均交易量=1052488\n", "2023-07-24: 买入执行: 价格=8.65, 数量=12559, 成本=108635.35, 手续费=32.59\n", "2023-07-24: 卖出信号(时间退出): 价格=8.60, 持仓数量=12559\n", "2023-07-24: 卖出执行: 价格=8.61, 数量=12559, 收入=108635.35, 手续费=32.44\n", "2023-07-24: 交易利润: 毛利=-502.36, 净利=-567.39\n", "2023-07-25: 买入信号: 价格=8.66, 数量=12479, 交易量=2412400, 平均交易量=957742\n", "2023-07-25: 买入执行: 价格=8.66, 数量=12479, 成本=108068.14, 手续费=32.42\n", "2023-07-25: 卖出信号(时间退出): 价格=8.66, 持仓数量=12479\n", "2023-07-25: 卖出执行: 价格=8.67, 数量=12479, 收入=108068.14, 手续费=32.46\n", "2023-07-25: 交易利润: 毛利=124.79, 净利=59.91\n", "2023-07-26: 买入信号: 价格=8.58, 数量=12602, 交易量=2731000, 平均交易量=868108\n", "2023-07-26: 买入执行: 价格=8.57, 数量=12602, 成本=107999.14, 手续费=32.40\n", "2023-07-26: 卖出信号(时间退出): 价格=8.55, 持仓数量=12602\n", "2023-07-26: 卖出执行: 价格=8.55, 数量=12602, 收入=107999.14, 手续费=32.32\n", "2023-07-26: 交易利润: 毛利=-252.04, 净利=-316.76\n", "2023-07-31: 买入信号: 价格=8.59, 数量=12551, 交易量=2325831, 平均交易量=942491\n", "2023-07-31: 买入执行: 价格=8.58, 数量=12551, 成本=107687.58, 手续费=32.31\n", "2023-07-31: 卖出信号(时间退出): 价格=8.63, 持仓数量=12551\n", "2023-07-31: 卖出执行: 价格=8.62, 数量=12551, 收入=107687.58, 手续费=32.46\n", "2023-07-31: 交易利润: 毛利=502.04, 净利=437.28\n", "2023-08-02: 买入信号: 价格=8.64, 数量=12529, 交易量=1746700, 平均交易量=661858\n", "2023-08-02: 订单被拒绝或取消: 7\n", "2023-08-03: 买入信号: 价格=8.68, 数量=12471, 交易量=1853900, 平均交易量=710517\n", "2023-08-03: 订单被拒绝或取消: 7\n", "2023-08-03: 买入信号: 价格=8.69, 数量=12457, 交易量=1666800, 平均交易量=785257\n", "2023-08-03: 订单被拒绝或取消: 7\n", "2023-08-04: 买入信号: 价格=8.71, 数量=12428, 交易量=2134300, 平均交易量=874101\n", "2023-08-04: 买入执行: 价格=8.71, 数量=12428, 成本=108247.88, 手续费=32.47\n", "2023-08-04: 卖出信号(时间退出): 价格=8.69, 持仓数量=12428\n", "2023-08-04: 卖出执行: 价格=8.70, 数量=12428, 收入=108247.88, 手续费=32.44\n", "2023-08-04: 交易利润: 毛利=-124.28, 净利=-189.19\n", "2023-08-10: 买入信号: 价格=8.49, 数量=12728, 交易量=1819100, 平均交易量=763863\n", "2023-08-10: 买入执行: 价格=8.49, 数量=12728, 成本=108060.72, 手续费=32.42\n", "2023-08-10: 卖出信号(时间退出): 价格=8.39, 持仓数量=12728\n", "2023-08-10: 卖出执行: 价格=8.39, 数量=12728, 收入=108060.72, 手续费=32.04\n", "2023-08-10: 交易利润: 毛利=-1272.80, 净利=-1337.25\n", "2023-08-11: 买入信号: 价格=8.29, 数量=12874, 交易量=2293500, 平均交易量=928871\n", "2023-08-11: 买入执行: 价格=8.28, 数量=12874, 成本=106596.72, 手续费=31.98\n", "2023-08-11: 卖出信号(时间退出): 价格=8.29, 持仓数量=12874\n", "2023-08-11: 卖出执行: 价格=8.28, 数量=12874, 收入=106596.72, 手续费=31.98\n", "2023-08-11: 交易利润: 毛利=0.00, 净利=-63.96\n", "2023-08-14: 买入信号: 价格=8.11, 数量=13152, 交易量=2943100, 平均交易量=1076163\n", "2023-08-14: 买入执行: 价格=8.10, 数量=13152, 成本=106531.20, 手续费=31.96\n", "2023-08-14: 卖出信号(时间退出): 价格=8.12, 持仓数量=13152\n", "2023-08-14: 卖出执行: 价格=8.12, 数量=13152, 收入=106531.20, 手续费=32.04\n", "2023-08-14: 交易利润: 毛利=263.04, 净利=199.04\n", "2023-08-17: 买入信号: 价格=7.94, 数量=13458, 交易量=1919713, 平均交易量=748083\n", "2023-08-17: 买入执行: 价格=7.93, 数量=13458, 成本=106721.94, 手续费=32.02\n", "2023-08-17: 卖出信号(时间退出): 价格=8.02, 持仓数量=13458\n", "2023-08-17: 卖出执行: 价格=8.03, 数量=13458, 收入=106721.94, 手续费=32.42\n", "2023-08-17: 交易利润: 毛利=1345.80, 净利=1281.36\n", "2023-08-18: 买入信号: 价格=8.07, 数量=13400, 交易量=1563590, 平均交易量=755782\n", "2023-08-18: 买入执行: 价格=8.07, 数量=13400, 成本=108138.00, 手续费=32.44\n", "2023-08-18: 卖出信号(时间退出): 价格=8.07, 持仓数量=13400\n", "2023-08-18: 卖出执行: 价格=8.08, 数量=13400, 收入=108138.00, 手续费=32.48\n", "2023-08-18: 交易利润: 毛利=134.00, 净利=69.08\n", "2023-08-21: 买入信号: 价格=8.00, 数量=13526, 交易量=1537900, 平均交易量=680450\n", "2023-08-21: 订单被拒绝或取消: 7\n", "2023-08-22: 买入信号: 价格=7.89, 数量=13715, 交易量=1671500, 平均交易量=760324\n", "2023-08-22: 买入执行: 价格=7.89, 数量=13715, 成本=108211.35, 手续费=32.46\n", "2023-08-22: 卖出信号(时间退出): 价格=7.89, 持仓数量=13715\n", "2023-08-22: 卖出执行: 价格=7.88, 数量=13715, 收入=108211.35, 手续费=32.42\n", "2023-08-22: 交易利润: 毛利=-137.15, 净利=-202.04\n", "2023-08-24: 买入信号: 价格=7.83, 数量=13794, 交易量=1747100, 平均交易量=752173\n", "2023-08-24: 订单被拒绝或取消: 7\n", "2023-08-25: 买入信号: 价格=7.92, 数量=13637, 交易量=2806557, 平均交易量=879224\n", "2023-08-25: 买入执行: 价格=7.92, 数量=13637, 成本=108005.04, 手续费=32.40\n", "2023-08-25: 卖出信号(时间退出): 价格=7.83, 持仓数量=13637\n", "2023-08-25: 卖出执行: 价格=7.84, 数量=13637, 收入=108005.04, 手续费=32.07\n", "2023-08-25: 交易利润: 毛利=-1090.96, 净利=-1155.44\n", "2023-08-28: 买入信号: 价格=7.98, 数量=13390, 交易量=6052500, 平均交易量=1348443\n", "2023-08-28: 买入执行: 价格=7.98, 数量=13390, 成本=106852.20, 手续费=32.06\n", "2023-08-28: 卖出信号(时间退出): 价格=7.97, 持仓数量=13390\n", "2023-08-28: 卖出执行: 价格=7.98, 数量=13390, 收入=106852.20, 手续费=32.06\n", "2023-08-28: 交易利润: 毛利=0.00, 净利=-64.11\n", "2023-08-30: 买入信号: 价格=8.15, 数量=13103, 交易量=4081142, 平均交易量=1490613\n", "2023-08-30: 买入执行: 价格=8.14, 数量=13103, 成本=106658.42, 手续费=32.00\n", "2023-08-30: 卖出信号(时间退出): 价格=8.14, 持仓数量=13103\n", "2023-08-30: 卖出执行: 价格=8.15, 数量=13103, 收入=106658.42, 手续费=32.04\n", "2023-08-30: 交易利润: 毛利=131.03, 净利=67.00\n", "2023-09-04: 买入信号: 价格=8.31, 数量=12859, 交易量=2655856, 平均交易量=1075771\n", "2023-09-04: 买入执行: 价格=8.31, 数量=12859, 成本=106858.29, 手续费=32.06\n", "2023-09-04: 卖出信号(时间退出): 价格=8.30, 持仓数量=12859\n", "2023-09-04: 卖出执行: 价格=8.29, 数量=12859, 收入=106858.29, 手续费=31.98\n", "2023-09-04: 交易利润: 毛利=-257.18, 净利=-321.22\n", "2023-09-05: 买入信号: 价格=8.44, 数量=12622, 交易量=2760300, 平均交易量=1197184\n", "2023-09-05: 买入执行: 价格=8.44, 数量=12622, 成本=106529.68, 手续费=31.96\n", "2023-09-05: 卖出信号(时间退出): 价格=8.46, 持仓数量=12622\n", "2023-09-05: 卖出执行: 价格=8.46, 数量=12622, 收入=106529.68, 手续费=32.03\n", "2023-09-05: 交易利润: 毛利=252.44, 净利=188.45\n", "2023-09-06: 买入信号: 价格=8.48, 数量=12585, 交易量=2764300, 平均交易量=1279967\n", "2023-09-06: 买入执行: 价格=8.48, 数量=12585, 成本=106720.80, 手续费=32.02\n", "2023-09-06: 卖出信号(时间退出): 价格=8.51, 持仓数量=12585\n", "2023-09-06: 卖出执行: 价格=8.51, 数量=12585, 收入=106720.80, 手续费=32.13\n", "2023-09-06: 交易利润: 毛利=377.55, 净利=313.40\n", "2023-09-07: 买入信号: 价格=8.48, 数量=12622, 交易量=3045380, 平均交易量=1274145\n", "2023-09-07: 买入执行: 价格=8.48, 数量=12622, 成本=107034.56, 手续费=32.11\n", "2023-09-07: 卖出信号(时间退出): 价格=8.50, 持仓数量=12622\n", "2023-09-07: 卖出执行: 价格=8.49, 数量=12622, 收入=107034.56, 手续费=32.15\n", "2023-09-07: 交易利润: 毛利=126.22, 净利=61.96\n", "2023-09-11: 买入信号: 价格=8.46, 数量=12659, 交易量=2218076, 平均交易量=1057818\n", "2023-09-11: 买入执行: 价格=8.46, 数量=12659, 成本=107095.14, 手续费=32.13\n", "2023-09-11: 卖出信号(时间退出): 价格=8.50, 持仓数量=12659\n", "2023-09-11: 卖出执行: 价格=8.51, 数量=12659, 收入=107095.14, 手续费=32.32\n", "2023-09-11: 交易利润: 毛利=632.95, 净利=568.50\n", "2023-09-12: 买入信号: 价格=8.43, 数量=12772, 交易量=2182000, 平均交易量=956483\n", "2023-09-12: 订单被拒绝或取消: 7\n", "2023-09-12: 买入信号: 价格=8.46, 数量=12726, 交易量=2660211, 平均交易量=1078557\n", "2023-09-12: 买入执行: 价格=8.46, 数量=12726, 成本=107661.96, 手续费=32.30\n", "2023-09-12: 卖出信号(时间退出): 价格=8.46, 持仓数量=12726\n", "2023-09-12: 卖出执行: 价格=8.45, 数量=12726, 收入=107661.96, 手续费=32.26\n", "2023-09-12: 交易利润: 毛利=-127.26, 净利=-191.82\n", "2023-09-18: 买入信号: 价格=8.29, 数量=12964, 交易量=1652800, 平均交易量=663147\n", "2023-09-18: 买入执行: 价格=8.29, 数量=12964, 成本=107471.56, 手续费=32.24\n", "2023-09-18: 卖出信号(时间退出): 价格=8.39, 持仓数量=12964\n", "2023-09-18: 卖出执行: 价格=8.39, 数量=12964, 收入=107471.56, 手续费=32.63\n", "2023-09-18: 交易利润: 毛利=1296.40, 净利=1231.53\n", "2023-09-19: 买入信号: 价格=8.26, 数量=13160, 交易量=2411200, 平均交易量=810087\n", "2023-09-19: 买入执行: 价格=8.26, 数量=13160, 成本=108701.60, 手续费=32.61\n", "2023-09-19: 卖出信号(时间退出): 价格=8.29, 持仓数量=13160\n", "2023-09-19: 卖出执行: 价格=8.29, 数量=13160, 收入=108701.60, 手续费=32.73\n", "2023-09-19: 交易利润: 毛利=394.80, 净利=329.46\n", "2023-09-20: 买入信号: 价格=8.21, 数量=13281, 交易量=1867900, 平均交易量=830782\n", "2023-09-20: 买入执行: 价格=8.21, 数量=13281, 成本=109037.01, 手续费=32.71\n", "2023-09-20: 卖出信号(时间退出): 价格=8.17, 持仓数量=13281\n", "2023-09-20: 卖出执行: 价格=8.16, 数量=13281, 收入=109037.01, 手续费=32.51\n", "2023-09-20: 交易利润: 毛利=-664.05, 净利=-729.27\n", "2023-09-25: 买入信号: 价格=8.20, 数量=13208, 交易量=1814200, 平均交易量=742857\n", "2023-09-25: 买入执行: 价格=8.20, 数量=13208, 成本=108305.60, 手续费=32.49\n", "2023-09-25: 卖出信号(时间退出): 价格=8.18, 持仓数量=13208\n", "2023-09-25: 卖出执行: 价格=8.18, 数量=13208, 收入=108305.60, 手续费=32.41\n", "2023-09-25: 交易利润: 毛利=-264.16, 净利=-329.06\n", "2023-09-27: 买入信号: 价格=8.13, 数量=13281, 交易量=1633200, 平均交易量=679013\n", "2023-09-27: 买入执行: 价格=8.13, 数量=13281, 成本=107974.53, 手续费=32.39\n", "2023-09-27: 卖出信号(时间退出): 价格=8.10, 持仓数量=13281\n", "2023-09-27: 卖出执行: 价格=8.10, 数量=13281, 收入=107974.53, 手续费=32.27\n", "2023-09-27: 交易利润: 毛利=-398.43, 净利=-463.10\n", "2023-09-28: 买入信号: 价格=8.16, 数量=13176, 交易量=1848900, 平均交易量=724239\n", "2023-09-28: 买入执行: 价格=8.15, 数量=13176, 成本=107384.40, 手续费=32.22\n", "2023-09-28: 卖出信号(时间退出): 价格=8.16, 持仓数量=13176\n", "2023-09-28: 卖出执行: 价格=8.16, 数量=13176, 收入=107384.40, 手续费=32.25\n", "2023-09-28: 交易利润: 毛利=131.76, 净利=67.29\n", "2023-09-28: 买入信号: 价格=8.24, 数量=13056, 交易量=1865900, 平均交易量=813580\n", "2023-09-28: 订单被拒绝或取消: 7\n", "2023-09-28: 买入信号: 价格=8.26, 数量=13024, 交易量=2046400, 平均交易量=909480\n", "2023-10-09: 买入执行: 价格=8.21, 数量=13024, 成本=106927.04, 手续费=32.08\n", "2023-10-09: 卖出信号(时间退出): 价格=8.19, 持仓数量=13024\n", "2023-10-09: 卖出执行: 价格=8.20, 数量=13024, 收入=106927.04, 手续费=32.04\n", "2023-10-09: 交易利润: 毛利=-130.24, 净利=-194.36\n", "2023-10-09: 买入信号: 价格=8.27, 数量=12985, 交易量=3059476, 平均交易量=1209593\n", "2023-10-09: 买入执行: 价格=8.27, 数量=12985, 成本=107385.95, 手续费=32.22\n", "2023-10-10: 卖出信号(时间退出): 价格=8.27, 持仓数量=12985\n", "2023-10-10: 卖出执行: 价格=8.27, 数量=12985, 收入=107385.95, 手续费=32.22\n", "2023-10-10: 交易利润: 毛利=0.00, 净利=-64.43\n", "2023-10-11: 买入信号: 价格=8.45, 数量=12701, 交易量=3044300, 平均交易量=1216567\n", "2023-10-11: 订单被拒绝或取消: 7\n", "2023-10-13: 买入信号: 价格=8.64, 数量=12421, 交易量=3403800, 平均交易量=1345359\n", "2023-10-13: 订单被拒绝或取消: 7\n", "2023-10-18: 买入信号: 价格=8.46, 数量=12686, 交易量=1692000, 平均交易量=807473\n", "2023-10-18: 订单被拒绝或取消: 7\n", "2023-10-19: 买入信号: 价格=8.51, 数量=12611, 交易量=2301310, 平均交易量=819852\n", "2023-10-19: 买入执行: 价格=8.51, 数量=12611, 成本=107319.61, 手续费=32.20\n", "2023-10-19: 卖出信号(时间退出): 价格=8.45, 持仓数量=12611\n", "2023-10-19: 卖出执行: 价格=8.46, 数量=12611, 收入=107319.61, 手续费=32.01\n", "2023-10-19: 交易利润: 毛利=-630.55, 净利=-694.75\n", "2023-10-23: 买入信号: 价格=8.10, 数量=13164, 交易量=2738000, 平均交易量=1037396\n", "2023-10-23: 买入执行: 价格=8.10, 数量=13164, 成本=106628.40, 手续费=31.99\n", "2023-10-23: 卖出信号(时间退出): 价格=8.15, 持仓数量=13164\n", "2023-10-23: 卖出执行: 价格=8.16, 数量=13164, 收入=106628.40, 手续费=32.23\n", "2023-10-23: 交易利润: 毛利=789.84, 净利=725.63\n", "2023-10-23: 买入信号: 价格=8.04, 数量=13352, 交易量=2534900, 平均交易量=1097764\n", "2023-10-24: 买入执行: 价格=8.04, 数量=13352, 成本=107350.08, 手续费=32.21\n", "2023-10-24: 卖出信号(止损): 价格=7.68, 持仓数量=13352\n", "2023-10-24: 卖出执行: 价格=7.68, 数量=13352, 收入=107350.08, 手续费=30.76\n", "2023-10-24: 交易利润: 毛利=-4806.72, 净利=-4869.69\n", "2023-10-25: 买入信号: 价格=7.97, 数量=12859, 交易量=4065000, 平均交易量=2024452\n", "2023-10-25: 买入执行: 价格=7.97, 数量=12859, 成本=102486.23, 手续费=30.75\n", "2023-10-25: 卖出信号(时间退出): 价格=7.99, 持仓数量=12859\n", "2023-10-25: 卖出执行: 价格=7.99, 数量=12859, 收入=102486.23, 手续费=30.82\n", "2023-10-25: 交易利润: 毛利=257.18, 净利=195.61\n", "2023-10-27: 买入信号: 价格=8.11, 数量=12661, 交易量=2699500, 平均交易量=1032860\n", "2023-10-27: 订单被拒绝或取消: 7\n", "2023-10-30: 买入信号: 价格=8.40, 数量=12224, 交易量=3761900, 平均交易量=1137291\n", "2023-10-30: 订单被拒绝或取消: 7\n", "2023-10-31: 买入信号: 价格=8.56, 数量=11995, 交易量=3316678, 平均交易量=1394534\n", "2023-10-31: 买入执行: 价格=8.55, 数量=11995, 成本=102557.25, 手续费=30.77\n", "2023-10-31: 卖出信号(时间退出): 价格=8.51, 持仓数量=11995\n", "2023-10-31: 卖出执行: 价格=8.51, 数量=11995, 收入=102557.25, 手续费=30.62\n", "2023-10-31: 交易利润: 毛利=-479.80, 净利=-541.19\n", "2023-11-01: 买入信号: 价格=8.54, 数量=11960, 交易量=2745600, 平均交易量=1325802\n", "2023-11-01: 买入执行: 价格=8.54, 数量=11960, 成本=102138.40, 手续费=30.64\n", "2023-11-01: 卖出信号(时间退出): 价格=8.58, 持仓数量=11960\n", "2023-11-01: 卖出执行: 价格=8.58, 数量=11960, 收入=102138.40, 手续费=30.79\n", "2023-11-01: 交易利润: 毛利=478.40, 净利=416.97\n", "2023-11-03: 买入信号: 价格=8.47, 数量=12108, 交易量=2371300, 平均交易量=1151365\n", "2023-11-03: 买入执行: 价格=8.47, 数量=12108, 成本=102554.76, 手续费=30.77\n", "2023-11-03: 卖出信号(时间退出): 价格=8.56, 持仓数量=12108\n", "2023-11-03: 卖出执行: 价格=8.55, 数量=12108, 收入=102554.76, 手续费=31.06\n", "2023-11-03: 交易利润: 毛利=968.64, 净利=906.82\n", "2023-11-06: 买入信号: 价格=8.63, 数量=11989, 交易量=2836800, 平均交易量=1187192\n", "2023-11-06: 买入执行: 价格=8.63, 数量=11989, 成本=103465.07, 手续费=31.04\n", "2023-11-06: 卖出信号(时间退出): 价格=8.64, 持仓数量=11989\n", "2023-11-06: 卖出执行: 价格=8.64, 数量=11989, 收入=103465.07, 手续费=31.08\n", "2023-11-06: 交易利润: 毛利=119.89, 净利=57.77\n", "2023-11-08: 买入信号: 价格=8.61, 数量=12023, 交易量=2475876, 平均交易量=1177546\n", "2023-11-08: 买入执行: 价格=8.61, 数量=12023, 成本=103518.03, 手续费=31.06\n", "2023-11-08: 卖出信号(时间退出): 价格=8.63, 持仓数量=12023\n", "2023-11-08: 卖出执行: 价格=8.62, 数量=12023, 收入=103518.03, 手续费=31.09\n", "2023-11-08: 交易利润: 毛利=120.23, 净利=58.08\n", "2023-11-08: 买入信号: 价格=8.51, 数量=12171, 交易量=2954200, 平均交易量=1299837\n", "2023-11-08: 买入执行: 价格=8.51, 数量=12171, 成本=103575.21, 手续费=31.07\n", "2023-11-09: 卖出信号(时间退出): 价格=8.51, 持仓数量=12171\n", "2023-11-09: 卖出执行: 价格=8.51, 数量=12171, 收入=103575.21, 手续费=31.07\n", "2023-11-09: 交易利润: 毛利=0.00, 净利=-62.15\n", "2023-11-14: 买入信号: 价格=8.63, 数量=11995, 交易量=4559685, 平均交易量=1278419\n", "2023-11-14: 买入执行: 价格=8.63, 数量=11995, 成本=103516.85, 手续费=31.06\n", "2023-11-14: 卖出信号(时间退出): 价格=8.62, 持仓数量=11995\n", "2023-11-14: 卖出执行: 价格=8.62, 数量=11995, 收入=103516.85, 手续费=31.02\n", "2023-11-14: 交易利润: 毛利=-119.95, 净利=-182.02\n", "2023-11-15: 买入信号: 价格=8.65, 数量=11946, 交易量=3796600, 平均交易量=1611986\n", "2023-11-15: 买入执行: 价格=8.65, 数量=11946, 成本=103332.90, 手续费=31.00\n", "2023-11-15: 卖出信号(时间退出): 价格=8.65, 持仓数量=11946\n", "2023-11-15: 卖出执行: 价格=8.65, 数量=11946, 收入=103332.90, 手续费=31.00\n", "2023-11-15: 交易利润: 毛利=0.00, 净利=-62.00\n", "2023-11-16: 买入信号: 价格=8.68, 数量=11898, 交易量=3109500, 平均交易量=1404433\n", "2023-11-16: 买入执行: 价格=8.68, 数量=11898, 成本=103274.64, 手续费=30.98\n", "2023-11-16: 卖出信号(时间退出): 价格=8.62, 持仓数量=11898\n", "2023-11-16: 卖出执行: 价格=8.62, 数量=11898, 收入=103274.64, 手续费=30.77\n", "2023-11-16: 交易利润: 毛利=-713.88, 净利=-775.63\n", "2023-11-20: 买入信号: 价格=8.56, 数量=11974, 交易量=2959500, 平均交易量=1067073\n", "2023-11-20: 买入执行: 价格=8.56, 数量=11974, 成本=102497.44, 手续费=30.75\n", "2023-11-20: 卖出信号(时间退出): 价格=8.61, 持仓数量=11974\n", "2023-11-20: 卖出执行: 价格=8.62, 数量=11974, 收入=102497.44, 手续费=30.96\n", "2023-11-20: 交易利润: 毛利=718.44, 净利=656.73\n", "2023-11-28: 买入信号: 价格=8.69, 数量=11870, 交易量=4574700, 平均交易量=1480763\n", "2023-11-28: 买入执行: 价格=8.68, 数量=11870, 成本=103031.60, 手续费=30.91\n", "2023-11-28: 卖出信号(时间退出): 价格=8.71, 持仓数量=11870\n", "2023-11-28: 卖出执行: 价格=8.74, 数量=11870, 收入=103031.60, 手续费=31.12\n", "2023-11-28: 交易利润: 毛利=712.20, 净利=650.17\n", "2023-11-30: 买入信号: 价格=8.47, 数量=12255, 交易量=4717417, 平均交易量=1587036\n", "2023-11-30: 订单被拒绝或取消: 7\n", "2023-12-01: 买入信号: 价格=8.29, 数量=12521, 交易量=7323705, 平均交易量=2026217\n", "2023-12-01: 买入执行: 价格=8.28, 数量=12521, 成本=103673.88, 手续费=31.10\n", "2023-12-01: 卖出信号(时间退出): 价格=8.39, 持仓数量=12521\n", "2023-12-01: 卖出执行: 价格=8.39, 数量=12521, 收入=103673.88, 手续费=31.52\n", "2023-12-01: 交易利润: 毛利=1377.31, 净利=1314.69\n", "2023-12-05: 买入信号: 价格=8.15, 数量=12898, 交易量=3888400, 平均交易量=1517092\n", "2023-12-05: 买入执行: 价格=8.14, 数量=12898, 成本=104989.72, 手续费=31.50\n", "2023-12-05: 卖出信号(时间退出): 价格=8.06, 持仓数量=12898\n", "2023-12-05: 卖出执行: 价格=8.07, 数量=12898, 收入=104989.72, 手续费=31.23\n", "2023-12-05: 交易利润: 毛利=-902.86, 净利=-965.58\n", "2023-12-06: 买入信号: 价格=7.93, 数量=13134, 交易量=3906674, 平均交易量=1908125\n", "2023-12-06: 订单被拒绝或取消: 7\n", "2023-12-07: 买入信号: 价格=8.00, 数量=13019, 交易量=4056022, 平均交易量=1886226\n", "2023-12-07: 买入执行: 价格=8.00, 数量=13019, 成本=104152.00, 手续费=31.25\n", "2023-12-07: 卖出信号(时间退出): 价格=8.02, 持仓数量=13019\n", "2023-12-07: 卖出执行: 价格=8.02, 数量=13019, 收入=104152.00, 手续费=31.32\n", "2023-12-07: 交易利润: 毛利=260.38, 净利=197.81\n", "2023-12-08: 买入信号: 价格=8.11, 数量=12867, 交易量=5433429, 平均交易量=1873543\n", "2023-12-08: 买入执行: 价格=8.10, 数量=12867, 成本=104222.70, 手续费=31.27\n", "2023-12-08: 卖出信号(时间退出): 价格=8.12, 持仓数量=12867\n", "2023-12-08: 卖出执行: 价格=8.12, 数量=12867, 收入=104222.70, 手续费=31.34\n", "2023-12-08: 交易利润: 毛利=257.34, 净利=194.73\n", "2023-12-08: 买入信号: 价格=8.21, 数量=12734, 交易量=6348106, 平均交易量=2382893\n", "2023-12-11: 买入执行: 价格=8.14, 数量=12734, 成本=103654.76, 手续费=31.10\n", "2023-12-11: 卖出信号(止损): 价格=7.84, 持仓数量=12734\n", "2023-12-11: 卖出执行: 价格=7.83, 数量=12734, 收入=103654.76, 手续费=29.91\n", "2023-12-11: 交易利润: 毛利=-3947.54, 净利=-4008.55\n", "2023-12-14: 买入信号: 价格=7.99, 数量=12583, 交易量=3726500, 平均交易量=1785705\n", "2023-12-14: 买入执行: 价格=7.99, 数量=12583, 成本=100538.17, 手续费=30.16\n", "2023-12-14: 卖出信号(时间退出): 价格=8.02, 持仓数量=12583\n", "2023-12-14: 卖出执行: 价格=8.01, 数量=12583, 收入=100538.17, 手续费=30.24\n", "2023-12-14: 交易利润: 毛利=251.66, 净利=191.26\n", "2023-12-19: 买入信号: 价格=7.72, 数量=13048, 交易量=2831000, 平均交易量=1306466\n", "2023-12-19: 订单被拒绝或取消: 7\n", "2023-12-21: 买入信号: 价格=7.64, 数量=13184, 交易量=2091100, 平均交易量=953144\n", "2023-12-21: 买入执行: 价格=7.63, 数量=13184, 成本=100593.92, 手续费=30.18\n", "2023-12-21: 卖出信号(时间退出): 价格=7.67, 持仓数量=13184\n", "2023-12-21: 卖出执行: 价格=7.67, 数量=13184, 收入=100593.92, 手续费=30.34\n", "2023-12-21: 交易利润: 毛利=527.36, 净利=466.85\n", "2023-12-27: 买入信号: 价格=7.51, 数量=13475, 交易量=1990000, 平均交易量=945580\n", "2023-12-27: 买入执行: 价格=7.51, 数量=13475, 成本=101197.25, 手续费=30.36\n", "2023-12-27: 卖出信号(时间退出): 价格=7.53, 持仓数量=13475\n", "2023-12-27: 卖出执行: 价格=7.54, 数量=13475, 收入=101197.25, 手续费=30.48\n", "2023-12-27: 交易利润: 毛利=404.25, 净利=343.41\n", "2023-12-27: 买入信号: 价格=7.56, 数量=13431, 交易量=2520200, 平均交易量=1143129\n", "2023-12-28: 买入执行: 价格=7.55, 数量=13431, 成本=101404.05, 手续费=30.42\n", "2023-12-28: 卖出信号(时间退出): 价格=7.62, 持仓数量=13431\n", "2023-12-28: 卖出执行: 价格=7.61, 数量=13431, 收入=101404.05, 手续费=30.66\n", "2023-12-28: 交易利润: 毛利=805.86, 净利=744.78\n", "2023-12-29: 买入信号: 价格=7.85, 数量=13030, 交易量=4520473, 平均交易量=1633105\n", "2023-12-29: 买入执行: 价格=7.85, 数量=13030, 成本=102285.50, 手续费=30.69\n", "2023-12-29: 卖出信号(时间退出): 价格=7.88, 持仓数量=13030\n", "2023-12-29: 卖出执行: 价格=7.88, 数量=13030, 收入=102285.50, 手续费=30.80\n", "2023-12-29: 交易利润: 毛利=390.90, 净利=329.41\n", "2024-01-05: 买入信号: 价格=7.68, 数量=13361, 交易量=2591700, 平均交易量=1287601\n", "2024-01-05: 买入执行: 价格=7.68, 数量=13361, 成本=102612.48, 手续费=30.78\n", "2024-01-05: 卖出信号(时间退出): 价格=7.62, 持仓数量=13361\n", "2024-01-05: 卖出执行: 价格=7.63, 数量=13361, 收入=102612.48, 手续费=30.58\n", "2024-01-05: 交易利润: 毛利=-668.05, 净利=-729.42\n", "2024-01-08: 买入信号: 价格=7.45, 数量=13676, 交易量=3824200, 平均交易量=1290748\n", "2024-01-08: 买入执行: 价格=7.45, 数量=13676, 成本=101886.20, 手续费=30.57\n", "2024-01-08: 卖出信号(时间退出): 价格=7.39, 持仓数量=13676\n", "2024-01-08: 卖出执行: 价格=7.39, 数量=13676, 收入=101886.20, 手续费=30.32\n", "2024-01-08: 交易利润: 毛利=-820.56, 净利=-881.45\n", "2024-01-10: 买入信号: 价格=7.20, 数量=14028, 交易量=4566496, 平均交易量=2022112\n", "2024-01-10: 订单被拒绝或取消: 7\n", "2024-01-16: 买入信号: 价格=7.33, 数量=13779, 交易量=1946400, 平均交易量=953813\n", "2024-01-16: 订单被拒绝或取消: 7\n", "2024-01-16: 买入信号: 价格=7.26, 数量=13912, 交易量=2254350, 平均交易量=1047103\n", "2024-01-16: 买入执行: 价格=7.26, 数量=13912, 成本=101001.12, 手续费=30.30\n", "2024-01-16: 卖出信号(时间退出): 价格=7.20, 持仓数量=13912\n", "2024-01-16: 卖出执行: 价格=7.20, 数量=13912, 收入=101001.12, 手续费=30.05\n", "2024-01-16: 交易利润: 毛利=-834.72, 净利=-895.07\n", "2024-01-17: 买入信号: 价格=7.07, 数量=14160, 交易量=3330500, 平均交易量=1350995\n", "2024-01-18: 订单被拒绝或取消: 7\n", "2024-01-18: 买入信号: 价格=7.10, 数量=14100, 交易量=6415639, 平均交易量=1657988\n", "2024-01-18: 买入执行: 价格=7.10, 数量=14100, 成本=100110.00, 手续费=30.03\n", "2024-01-18: 卖出信号(时间退出): 价格=7.03, 持仓数量=14100\n", "2024-01-18: 卖出执行: 价格=7.04, 数量=14100, 收入=100110.00, 手续费=29.78\n", "2024-01-18: 交易利润: 毛利=-846.00, 净利=-905.81\n", "2024-01-19: 买入信号: 价格=7.21, 数量=13759, 交易量=5483665, 平均交易量=2401945\n", "2024-01-19: 买入执行: 价格=7.21, 数量=13759, 成本=99202.39, 手续费=29.76\n", "2024-01-19: 卖出信号(时间退出): 价格=7.14, 持仓数量=13759\n", "2024-01-19: 卖出执行: 价格=7.14, 数量=13759, 收入=99202.39, 手续费=29.47\n", "2024-01-19: 交易利润: 毛利=-963.13, 净利=-1022.36\n", "2024-01-22: 买入信号: 价格=6.94, 数量=14147, 交易量=5104600, 平均交易量=2029533\n", "2024-01-22: 买入执行: 价格=6.93, 数量=14147, 成本=98038.71, 手续费=29.41\n", "2024-01-22: 卖出信号(时间退出): 价格=6.84, 持仓数量=14147\n", "2024-01-22: 卖出执行: 价格=6.85, 数量=14147, 收入=98038.71, 手续费=29.07\n", "2024-01-22: 交易利润: 毛利=-1131.76, 净利=-1190.24\n", "2024-01-22: 买入信号: 价格=6.57, 数量=14763, 交易量=5987200, 平均交易量=2134207\n", "2024-01-23: 买入执行: 价格=6.57, 数量=14763, 成本=96992.91, 手续费=29.10\n", "2024-01-23: 卖出信号(时间退出): 价格=6.61, 持仓数量=14763\n", "2024-01-23: 卖出执行: 价格=6.61, 数量=14763, 收入=96992.91, 手续费=29.28\n", "2024-01-23: 交易利润: 毛利=590.52, 净利=532.15\n", "2024-01-26: 买入信号: 价格=6.91, 数量=14113, 交易量=3798700, 平均交易量=1726365\n", "2024-01-29: 订单被拒绝或取消: 7\n", "2024-01-29: 买入信号: 价格=6.94, 数量=14052, 交易量=3340600, 平均交易量=1661085\n", "2024-01-29: 买入执行: 价格=6.94, 数量=14052, 成本=97520.88, 手续费=29.26\n", "2024-01-29: 卖出信号(时间退出): 价格=6.87, 持仓数量=14052\n", "2024-01-29: 卖出执行: 价格=6.87, 数量=14052, 收入=97520.88, 手续费=28.96\n", "2024-01-29: 交易利润: 毛利=-983.64, 净利=-1041.86\n", "2024-01-30: 买入信号: 价格=6.88, 数量=14023, 交易量=4364555, 平均交易量=2063927\n", "2024-01-30: 买入执行: 价格=6.88, 数量=14023, 成本=96478.24, 手续费=28.94\n", "2024-01-30: 卖出信号(时间退出): 价格=6.79, 持仓数量=14023\n", "2024-01-30: 卖出执行: 价格=6.81, 数量=14023, 收入=96478.24, 手续费=28.65\n", "2024-01-30: 交易利润: 毛利=-981.61, 净利=-1039.20\n", "2024-02-01: 买入信号: 价格=6.23, 数量=15320, 交易量=4205200, 平均交易量=2033797\n", "2024-02-01: 买入执行: 价格=6.23, 数量=15320, 成本=95443.60, 手续费=28.63\n", "2024-02-01: 卖出信号(时间退出): 价格=6.45, 持仓数量=15320\n", "2024-02-01: 卖出执行: 价格=6.44, 数量=15320, 收入=95443.60, 手续费=29.60\n", "2024-02-01: 交易利润: 毛利=3217.20, 净利=3158.97\n", "2024-02-05: 买入信号: 价格=5.65, 数量=17451, 交易量=8065937, 平均交易量=2488633\n", "2024-02-05: 订单被拒绝或取消: 7\n", "2024-02-06: 买入信号: 价格=5.63, 数量=17513, 交易量=7399282, 平均交易量=3345145\n", "2024-02-06: 订单被拒绝或取消: 7\n", "2024-02-07: 买入信号: 价格=6.32, 数量=15601, 交易量=7539522, 平均交易量=3031976\n", "2024-02-07: 买入执行: 价格=6.32, 数量=15601, 成本=98598.32, 手续费=29.58\n", "2024-02-07: 卖出信号(时间退出): 价格=6.33, 持仓数量=15601\n", "2024-02-07: 卖出执行: 价格=6.33, 数量=15601, 收入=98598.32, 手续费=29.63\n", "2024-02-07: 交易利润: 毛利=156.01, 净利=96.80\n", "2024-02-08: 买入信号: 价格=6.62, 数量=14909, 交易量=8739361, 平均交易量=3189120\n", "2024-02-08: 买入执行: 价格=6.61, 数量=14909, 成本=98548.49, 手续费=29.56\n", "2024-02-08: 卖出信号(时间退出): 价格=6.70, 持仓数量=14909\n", "2024-02-08: 卖出执行: 价格=6.67, 数量=14909, 收入=98548.49, 手续费=29.83\n", "2024-02-08: 交易利润: 毛利=894.54, 净利=835.14\n", "2024-02-19: 买入信号: 价格=6.73, 数量=14789, 交易量=6863678, 平均交易量=3127289\n", "2024-02-19: 买入执行: 价格=6.73, 数量=14789, 成本=99529.97, 手续费=29.86\n", "2024-02-19: 卖出信号(时间退出): 价格=6.61, 持仓数量=14789\n", "2024-02-19: 卖出执行: 价格=6.61, 数量=14789, 收入=99529.97, 手续费=29.33\n", "2024-02-19: 交易利润: 毛利=-1774.68, 净利=-1833.87\n", "2024-02-26: 买入信号: 价格=6.86, 数量=14242, 交易量=3213800, 平均交易量=1441692\n", "2024-02-26: 买入执行: 价格=6.85, 数量=14242, 成本=97557.70, 手续费=29.27\n", "2024-02-26: 卖出信号(时间退出): 价格=6.95, 持仓数量=14242\n", "2024-02-26: 卖出执行: 价格=6.95, 数量=14242, 收入=97557.70, 手续费=29.69\n", "2024-02-26: 交易利润: 毛利=1424.20, 净利=1365.24\n", "2024-02-28: 买入信号: 价格=7.16, 数量=13836, 交易量=3365100, 平均交易量=1620676\n", "2024-02-28: 订单被拒绝或取消: 7\n", "2024-02-29: 买入信号: 价格=6.88, 数量=14399, 交易量=5435200, 平均交易量=2328910\n", "2024-02-29: 买入执行: 价格=6.88, 数量=14399, 成本=99065.12, 手续费=29.72\n", "2024-02-29: 卖出信号(时间退出): 价格=6.85, 持仓数量=14399\n", "2024-02-29: 卖出执行: 价格=6.85, 数量=14399, 收入=99065.12, 手续费=29.59\n", "2024-02-29: 交易利润: 毛利=-431.97, 净利=-491.28\n", "2024-03-07: 买入信号: 价格=7.13, 数量=13825, 交易量=3774615, 平均交易量=1442762\n", "2024-03-07: 买入执行: 价格=7.12, 数量=13825, 成本=98434.00, 手续费=29.53\n", "2024-03-07: 卖出信号(时间退出): 价格=7.02, 持仓数量=13825\n", "2024-03-07: 卖出执行: 价格=7.02, 数量=13825, 收入=98434.00, 手续费=29.12\n", "2024-03-07: 交易利润: 毛利=-1382.50, 净利=-1441.15\n", "2024-03-12: 买入信号: 价格=7.18, 数量=13528, 交易量=2711500, 平均交易量=1181546\n", "2024-03-12: 买入执行: 价格=7.17, 数量=13528, 成本=96995.76, 手续费=29.10\n", "2024-03-12: 卖出信号(时间退出): 价格=7.12, 持仓数量=13528\n", "2024-03-12: 卖出执行: 价格=7.12, 数量=13528, 收入=96995.76, 手续费=28.90\n", "2024-03-12: 交易利润: 毛利=-676.40, 净利=-734.39\n", "2024-03-13: 买入信号: 价格=7.16, 数量=13463, 交易量=3989344, 平均交易量=1341944\n", "2024-03-14: 买入执行: 价格=7.15, 数量=13463, 成本=96260.45, 手续费=28.88\n", "2024-03-14: 卖出信号(时间退出): 价格=7.11, 持仓数量=13463\n", "2024-03-14: 卖出执行: 价格=7.11, 数量=13463, 收入=96260.45, 手续费=28.72\n", "2024-03-14: 交易利润: 毛利=-538.52, 净利=-596.11\n", "2024-03-18: 买入信号: 价格=7.17, 数量=13361, 交易量=2259884, 平均交易量=1044725\n", "2024-03-18: 买入执行: 价格=7.17, 数量=13361, 成本=95798.37, 手续费=28.74\n", "2024-03-18: 卖出信号(时间退出): 价格=7.18, 持仓数量=13361\n", "2024-03-18: 卖出执行: 价格=7.18, 数量=13361, 收入=95798.37, 手续费=28.78\n", "2024-03-18: 交易利润: 毛利=133.61, 净利=76.09\n", "2024-03-19: 买入信号: 价格=7.31, 数量=13116, 交易量=2428540, 平均交易量=1080412\n", "2024-03-19: 订单被拒绝或取消: 7\n", "2024-03-20: 买入信号: 价格=7.43, 数量=12904, 交易量=9108100, 平均交易量=1562883\n", "2024-03-20: 买入执行: 价格=7.43, 数量=12904, 成本=95876.72, 手续费=28.76\n", "2024-03-20: 卖出信号(时间退出): 价格=7.38, 持仓数量=12904\n", "2024-03-20: 卖出执行: 价格=7.38, 数量=12904, 收入=95876.72, 手续费=28.57\n", "2024-03-20: 交易利润: 毛利=-645.20, 净利=-702.53\n", "2024-03-21: 买入信号: 价格=7.33, 数量=12984, 交易量=4847400, 平均交易量=1831589\n", "2024-03-21: 买入执行: 价格=7.33, 数量=12984, 成本=95172.72, 手续费=28.55\n", "2024-03-21: 卖出信号(时间退出): 价格=7.31, 持仓数量=12984\n", "2024-03-21: 卖出执行: 价格=7.32, 数量=12984, 收入=95172.72, 手续费=28.51\n", "2024-03-21: 交易利润: 毛利=-129.84, 净利=-186.90\n", "2024-03-25: 买入信号: 价格=7.16, 数量=13266, 交易量=2806403, 平均交易量=1214760\n", "2024-03-25: 买入执行: 价格=7.15, 数量=13266, 成本=94851.90, 手续费=28.46\n", "2024-03-25: 卖出信号(时间退出): 价格=7.26, 持仓数量=13266\n", "2024-03-25: 卖出执行: 价格=7.26, 数量=13266, 收入=94851.90, 手续费=28.89\n", "2024-03-25: 交易利润: 毛利=1459.26, 净利=1401.91\n", "2024-03-28: 买入信号: 价格=7.04, 数量=13692, 交易量=3629500, 平均交易量=1476489\n", "2024-03-28: 买入执行: 价格=7.04, 数量=13692, 成本=96391.68, 手续费=28.92\n", "2024-03-28: 卖出信号(时间退出): 价格=7.20, 持仓数量=13692\n", "2024-03-28: 卖出执行: 价格=7.20, 数量=13692, 收入=96391.68, 手续费=29.57\n", "2024-03-28: 交易利润: 毛利=2190.72, 净利=2132.23\n", "2024-04-01: 买入信号: 价格=7.38, 数量=13350, 交易量=5414600, 平均交易量=1304100\n", "2024-04-01: 买入执行: 价格=7.38, 数量=13350, 成本=98523.00, 手续费=29.56\n", "2024-04-01: 卖出信号(时间退出): 价格=7.48, 持仓数量=13350\n", "2024-04-01: 卖出执行: 价格=7.47, 数量=13350, 收入=98523.00, 手续费=29.92\n", "2024-04-01: 交易利润: 毛利=1201.50, 净利=1142.03\n", "2024-04-02: 买入信号: 价格=7.46, 数量=13360, 交易量=3828078, 平均交易量=1694269\n", "2024-04-02: 买入执行: 价格=7.46, 数量=13360, 成本=99665.60, 手续费=29.90\n", "2024-04-02: 卖出信号(时间退出): 价格=7.48, 持仓数量=13360\n", "2024-04-02: 卖出执行: 价格=7.47, 数量=13360, 收入=99665.60, 手续费=29.94\n", "2024-04-02: 交易利润: 毛利=133.60, 净利=73.76\n", "2024-04-03: 买入信号: 价格=7.44, 数量=13405, 交易量=3259900, 平均交易量=1438211\n", "2024-04-03: 买入执行: 价格=7.43, 数量=13405, 成本=99599.15, 手续费=29.88\n", "2024-04-03: 卖出信号(时间退出): 价格=7.46, 持仓数量=13405\n", "2024-04-03: 卖出执行: 价格=7.44, 数量=13405, 收入=99599.15, 手续费=29.92\n", "2024-04-03: 交易利润: 毛利=134.05, 净利=74.25\n", "2024-04-08: 买入信号: 价格=7.43, 数量=13433, 交易量=2375468, 平均交易量=1172270\n", "2024-04-08: 买入执行: 价格=7.43, 数量=13433, 成本=99807.19, 手续费=29.94\n", "2024-04-08: 卖出信号(时间退出): 价格=7.43, 持仓数量=13433\n", "2024-04-08: 卖出执行: 价格=7.43, 数量=13433, 收入=99807.19, 手续费=29.94\n", "2024-04-08: 交易利润: 毛利=-0.00, 净利=-59.88\n", "2024-04-09: 买入信号: 价格=7.43, 数量=13425, 交易量=3270900, 平均交易量=1305797\n", "2024-04-09: 订单被拒绝或取消: 7\n", "2024-04-10: 买入信号: 价格=7.56, 数量=13195, 交易量=4121885, 平均交易量=1503690\n", "2024-04-10: 买入执行: 价格=7.56, 数量=13195, 成本=99754.20, 手续费=29.93\n", "2024-04-10: 卖出信号(时间退出): 价格=7.53, 持仓数量=13195\n", "2024-04-10: 卖出执行: 价格=7.52, 数量=13195, 收入=99754.20, 手续费=29.77\n", "2024-04-10: 交易利润: 毛利=-527.80, 净利=-587.49\n", "2024-04-15: 买入信号: 价格=7.70, 数量=12878, 交易量=9129598, 平均交易量=1781130\n", "2024-04-15: 买入执行: 价格=7.70, 数量=12878, 成本=99160.60, 手续费=29.75\n", "2024-04-15: 卖出信号(时间退出): 价格=7.80, 持仓数量=12878\n", "2024-04-15: 卖出执行: 价格=7.80, 数量=12878, 收入=99160.60, 手续费=30.13\n", "2024-04-15: 交易利润: 毛利=1287.80, 净利=1227.92\n", "2024-04-16: 买入信号: 价格=7.61, 数量=13192, 交易量=6804563, 平均交易量=2679806\n", "2024-04-16: 买入执行: 价格=7.59, 数量=13192, 成本=100127.28, 手续费=30.04\n", "2024-04-16: 卖出信号(时间退出): 价格=7.66, 持仓数量=13192\n", "2024-04-16: 卖出执行: 价格=7.66, 数量=13192, 收入=100127.28, 手续费=30.32\n", "2024-04-16: 交易利润: 毛利=923.44, 净利=863.09\n", "2024-04-17: 买入信号: 价格=8.04, 数量=12594, 交易量=12244634, 平均交易量=3284038\n", "2024-04-17: 买入执行: 价格=8.04, 数量=12594, 成本=101255.76, 手续费=30.38\n", "2024-04-18: 卖出信号(止损): 价格=7.75, 持仓数量=12594\n", "2024-04-18: 卖出执行: 价格=7.79, 数量=12594, 收入=101255.76, 手续费=29.43\n", "2024-04-18: 交易利润: 毛利=-3148.50, 净利=-3208.31\n", "2024-04-18: 买入信号: 价格=7.50, 数量=13073, 交易量=21318596, 平均交易量=4578614\n", "2024-04-18: 买入执行: 价格=7.50, 数量=13073, 成本=98047.50, 手续费=29.41\n", "2024-04-18: 卖出信号(时间退出): 价格=7.48, 持仓数量=13073\n", "2024-04-18: 卖出执行: 价格=7.48, 数量=13073, 收入=98047.50, 手续费=29.34\n", "2024-04-18: 交易利润: 毛利=-261.46, 净利=-320.21\n", "2024-04-19: 买入信号: 价格=7.59, 数量=12876, 交易量=12618625, 平均交易量=5810540\n", "2024-04-19: 买入执行: 价格=7.58, 数量=12876, 成本=97600.08, 手续费=29.28\n", "2024-04-19: 卖出信号(时间退出): 价格=7.56, 持仓数量=12876\n", "2024-04-19: 卖出执行: 价格=7.56, 数量=12876, 收入=97600.08, 手续费=29.20\n", "2024-04-19: 交易利润: 毛利=-257.52, 净利=-316.00\n", "2024-04-22: 买入信号: 价格=7.45, 数量=13075, 交易量=10946171, 平均交易量=3716543\n", "2024-04-22: 订单被拒绝或取消: 7\n", "2024-04-26: 买入信号: 价格=7.69, 数量=12667, 交易量=6725100, 平均交易量=1789147\n", "2024-04-26: 买入执行: 价格=7.69, 数量=12667, 成本=97409.23, 手续费=29.22\n", "2024-04-26: 卖出信号(时间退出): 价格=7.75, 持仓数量=12667\n", "2024-04-26: 卖出执行: 价格=7.75, 数量=12667, 收入=97409.23, 手续费=29.45\n", "2024-04-26: 交易利润: 毛利=760.02, 净利=701.35\n", "2024-04-29: 买入信号: 价格=7.78, 数量=12611, 交易量=4271300, 平均交易量=1989937\n", "2024-04-29: 买入执行: 价格=7.78, 数量=12611, 成本=98113.58, 手续费=29.43\n", "2024-04-29: 卖出信号(时间退出): 价格=7.80, 持仓数量=12611\n", "2024-04-29: 卖出执行: 价格=7.79, 数量=12611, 收入=98113.58, 手续费=29.47\n", "2024-04-29: 交易利润: 毛利=126.11, 净利=67.20\n", "2024-04-30: 买入信号: 价格=7.70, 数量=12750, 交易量=4670500, 平均交易量=2118911\n", "2024-04-30: 买入执行: 价格=7.69, 数量=12750, 成本=98047.50, 手续费=29.41\n", "2024-04-30: 卖出信号(时间退出): 价格=7.68, 持仓数量=12750\n", "2024-04-30: 卖出执行: 价格=7.69, 数量=12750, 收入=98047.50, 手续费=29.41\n", "2024-04-30: 交易利润: 毛利=0.00, 净利=-58.83\n", "2024-05-06: 买入信号: 价格=7.95, 数量=12342, 交易量=6060200, 平均交易量=2133677\n", "2024-05-06: 买入执行: 价格=7.95, 数量=12342, 成本=98118.90, 手续费=29.44\n", "2024-05-06: 卖出信号(时间退出): 价格=7.93, 持仓数量=12342\n", "2024-05-06: 卖出执行: 价格=7.93, 数量=12342, 收入=98118.90, 手续费=29.36\n", "2024-05-06: 交易利润: 毛利=-246.84, 净利=-305.64\n", "2024-05-08: 买入信号: 价格=7.92, 数量=12350, 交易量=3183000, 平均交易量=1425967\n", "2024-05-08: 买入执行: 价格=7.91, 数量=12350, 成本=97688.50, 手续费=29.31\n", "2024-05-08: 卖出信号(时间退出): 价格=7.80, 持仓数量=12350\n", "2024-05-08: 卖出执行: 价格=7.80, 数量=12350, 收入=97688.50, 手续费=28.90\n", "2024-05-08: 交易利润: 毛利=-1358.50, 净利=-1416.71\n", "2024-05-10: 买入信号: 价格=7.78, 数量=12390, 交易量=2654452, 平均交易量=1227864\n", "2024-05-10: 买入执行: 价格=7.78, 数量=12390, 成本=96394.20, 手续费=28.92\n", "2024-05-10: 卖出信号(时间退出): 价格=7.77, 持仓数量=12390\n", "2024-05-10: 卖出执行: 价格=7.77, 数量=12390, 收入=96394.20, 手续费=28.88\n", "2024-05-10: 交易利润: 毛利=-123.90, 净利=-181.70\n", "2024-05-13: 买入信号: 价格=7.68, 数量=12528, 交易量=2945700, 平均交易量=1224923\n", "2024-05-13: 买入执行: 价格=7.68, 数量=12528, 成本=96215.04, 手续费=28.86\n", "2024-05-13: 卖出信号(时间退出): 价格=7.70, 持仓数量=12528\n", "2024-05-13: 卖出执行: 价格=7.70, 数量=12528, 收入=96215.04, 手续费=28.94\n", "2024-05-13: 交易利润: 毛利=250.56, 净利=192.76\n", "2024-05-15: 买入信号: 价格=7.71, 数量=12504, 交易量=2442300, 平均交易量=1216999\n", "2024-05-15: 订单被拒绝或取消: 7\n", "2024-05-17: 买入信号: 价格=7.83, 数量=12313, 交易量=2181252, 平均交易量=1026933\n", "2024-05-17: 买入执行: 价格=7.82, 数量=12313, 成本=96287.66, 手续费=28.89\n", "2024-05-17: 卖出信号(时间退出): 价格=7.80, 持仓数量=12313\n", "2024-05-17: 卖出执行: 价格=7.80, 数量=12313, 收入=96287.66, 手续费=28.81\n", "2024-05-17: 交易利润: 毛利=-246.26, 净利=-303.96\n", "2024-05-17: 买入信号: 价格=7.96, 数量=12073, 交易量=3399600, 平均交易量=1369967\n", "2024-05-20: 订单被拒绝或取消: 7\n", "2024-05-20: 买入信号: 价格=8.03, 数量=11968, 交易量=3922000, 平均交易量=1521823\n", "2024-05-20: 订单被拒绝或取消: 7\n", "2024-05-22: 买入信号: 价格=8.13, 数量=11821, 交易量=5502500, 平均交易量=1702935\n", "2024-05-22: 买入执行: 价格=8.13, 数量=11821, 成本=96104.73, 手续费=28.83\n", "2024-05-22: 卖出信号(时间退出): 价格=8.06, 持仓数量=11821\n", "2024-05-22: 卖出执行: 价格=8.06, 数量=11821, 收入=96104.73, 手续费=28.58\n", "2024-05-22: 交易利润: 毛利=-827.47, 净利=-884.88\n", "2024-05-23: 买入信号: 价格=7.94, 数量=11992, 交易量=3720000, 平均交易量=1708192\n", "2024-05-23: 买入执行: 价格=7.94, 数量=11992, 成本=95216.48, 手续费=28.56\n", "2024-05-23: 卖出信号(时间退出): 价格=7.97, 持仓数量=11992\n", "2024-05-23: 卖出执行: 价格=7.97, 数量=11992, 收入=95216.48, 手续费=28.67\n", "2024-05-23: 交易利润: 毛利=359.76, 净利=302.52\n", "2024-05-30: 买入信号: 价格=7.98, 数量=11970, 交易量=3480600, 平均交易量=1050227\n", "2024-05-30: 买入执行: 价格=7.98, 数量=11970, 成本=95520.60, 手续费=28.66\n", "2024-05-30: 卖出信号(时间退出): 价格=7.95, 持仓数量=11970\n", "2024-05-30: 卖出执行: 价格=7.95, 数量=11970, 收入=95520.60, 手续费=28.55\n", "2024-05-30: 交易利润: 毛利=-359.10, 净利=-416.30\n", "2024-05-31: 买入信号: 价格=8.04, 数量=11829, 交易量=2280600, 平均交易量=1122954\n", "2024-05-31: 买入执行: 价格=8.04, 数量=11829, 成本=95105.16, 手续费=28.53\n", "2024-05-31: 卖出信号(时间退出): 价格=8.07, 持仓数量=11829\n", "2024-05-31: 卖出执行: 价格=8.06, 数量=11829, 收入=95105.16, 手续费=28.60\n", "2024-05-31: 交易利润: 毛利=236.58, 净利=179.45\n", "2024-06-03: 买入信号: 价格=8.05, 数量=11837, 交易量=3392600, 平均交易量=1221007\n", "2024-06-03: 订单被拒绝或取消: 7\n", "2024-06-04: 买入信号: 价格=7.96, 数量=11971, 交易量=2838300, 平均交易量=1355930\n", "2024-06-04: 买入执行: 价格=7.96, 数量=11971, 成本=95289.16, 手续费=28.59\n", "2024-06-04: 卖出信号(时间退出): 价格=7.97, 持仓数量=11971\n", "2024-06-04: 卖出执行: 价格=7.97, 数量=11971, 收入=95289.16, 手续费=28.62\n", "2024-06-04: 交易利润: 毛利=119.71, 净利=62.50\n", "2024-06-06: 买入信号: 价格=7.86, 数量=12131, 交易量=2443400, 平均交易量=1148510\n", "2024-06-06: 订单被拒绝或取消: 7\n", "2024-06-06: 买入信号: 价格=7.76, 数量=12287, 交易量=2844086, 平均交易量=1265729\n", "2024-06-06: 买入执行: 价格=7.75, 数量=12287, 成本=95224.25, 手续费=28.57\n", "2024-06-06: 卖出信号(时间退出): 价格=7.81, 持仓数量=12287\n", "2024-06-06: 卖出执行: 价格=7.82, 数量=12287, 收入=95224.25, 手续费=28.83\n", "2024-06-06: 交易利润: 毛利=860.09, 净利=802.70\n", "2024-06-07: 买入信号: 价格=7.85, 数量=12249, 交易量=3696456, 平均交易量=1449404\n", "2024-06-07: 买入执行: 价格=7.84, 数量=12249, 成本=96032.16, 手续费=28.81\n", "2024-06-07: 卖出信号(时间退出): 价格=7.78, 持仓数量=12249\n", "2024-06-07: 卖出执行: 价格=7.78, 数量=12249, 收入=96032.16, 手续费=28.59\n", "2024-06-07: 交易利润: 毛利=-734.94, 净利=-792.34\n", "2024-06-12: 买入信号: 价格=7.86, 数量=12132, 交易量=4173400, 平均交易量=1328373\n", "2024-06-12: 买入执行: 价格=7.86, 数量=12132, 成本=95357.52, 手续费=28.61\n", "2024-06-12: 卖出信号(时间退出): 价格=7.84, 持仓数量=12132\n", "2024-06-12: 卖出执行: 价格=7.83, 数量=12132, 收入=95357.52, 手续费=28.50\n", "2024-06-12: 交易利润: 毛利=-363.96, 净利=-421.07\n", "2024-06-14: 买入信号: 价格=7.83, 数量=12125, 交易量=2944600, 平均交易量=1197052\n", "2024-06-14: 买入执行: 价格=7.82, 数量=12125, 成本=94817.50, 手续费=28.45\n", "2024-06-14: 卖出信号(时间退出): 价格=7.84, 持仓数量=12125\n", "2024-06-14: 卖出执行: 价格=7.84, 数量=12125, 收入=94817.50, 手续费=28.52\n", "2024-06-14: 交易利润: 毛利=242.50, 净利=185.54\n", "2024-06-17: 买入信号: 价格=7.91, 数量=12026, 交易量=4024600, 平均交易量=1331853\n", "2024-06-17: 买入执行: 价格=7.91, 数量=12026, 成本=95125.66, 手续费=28.54\n", "2024-06-17: 卖出信号(时间退出): 价格=7.95, 持仓数量=12026\n", "2024-06-17: 卖出执行: 价格=7.95, 数量=12026, 收入=95125.66, 手续费=28.68\n", "2024-06-17: 交易利润: 毛利=481.04, 净利=423.82\n", "2024-06-20: 买入信号: 价格=7.89, 数量=12110, 交易量=1877200, 平均交易量=903006\n", "2024-06-20: 订单被拒绝或取消: 7\n", "2024-06-21: 买入信号: 价格=7.75, 数量=12329, 交易量=3626766, 平均交易量=1071387\n", "2024-06-21: 买入执行: 价格=7.75, 数量=12329, 成本=95549.75, 手续费=28.66\n", "2024-06-21: 卖出信号(时间退出): 价格=7.75, 持仓数量=12329\n", "2024-06-21: 卖出执行: 价格=7.77, 数量=12329, 收入=95549.75, 手续费=28.74\n", "2024-06-21: 交易利润: 毛利=246.58, 净利=189.18\n", "2024-06-24: 买入信号: 价格=7.73, 数量=12385, 交易量=2483700, 平均交易量=1092957\n", "2024-06-24: 买入执行: 价格=7.72, 数量=12385, 成本=95612.20, 手续费=28.68\n", "2024-06-24: 卖出信号(时间退出): 价格=7.56, 持仓数量=12385\n", "2024-06-24: 卖出执行: 价格=7.56, 数量=12385, 收入=95612.20, 手续费=28.09\n", "2024-06-24: 交易利润: 毛利=-1981.60, 净利=-2038.37\n", "2024-06-24: 买入信号: 价格=7.38, 数量=12696, 交易量=2757010, 平均交易量=1225121\n", "2024-06-25: 买入执行: 价格=7.38, 数量=12696, 成本=93696.48, 手续费=28.11\n", "2024-06-25: 卖出信号(时间退出): 价格=7.39, 持仓数量=12696\n", "2024-06-25: 卖出执行: 价格=7.39, 数量=12696, 收入=93696.48, 手续费=28.15\n", "2024-06-25: 交易利润: 毛利=126.96, 净利=70.70\n", "2024-06-28: 买入信号: 价格=7.74, 数量=12115, 交易量=4471500, 平均交易量=1494447\n", "2024-06-28: 买入执行: 价格=7.74, 数量=12115, 成本=93770.10, 手续费=28.13\n", "2024-06-28: 卖出信号(时间退出): 价格=7.80, 持仓数量=12115\n", "2024-06-28: 卖出执行: 价格=7.79, 数量=12115, 收入=93770.10, 手续费=28.31\n", "2024-06-28: 交易利润: 毛利=605.75, 净利=549.31\n", "2024-07-04: 买入信号: 价格=7.77, 数量=12139, 交易量=2375000, 平均交易量=921013\n", "2024-07-04: 买入执行: 价格=7.77, 数量=12139, 成本=94320.03, 手续费=28.30\n", "2024-07-04: 卖出信号(时间退出): 价格=7.70, 持仓数量=12139\n", "2024-07-04: 卖出执行: 价格=7.70, 数量=12139, 收入=94320.03, 手续费=28.04\n", "2024-07-04: 交易利润: 毛利=-849.73, 净利=-906.07\n", "2024-07-05: 买入信号: 价格=7.55, 数量=12373, 交易量=2238200, 平均交易量=919640\n", "2024-07-05: 买入执行: 价格=7.55, 数量=12373, 成本=93416.15, 手续费=28.02\n", "2024-07-05: 卖出信号(时间退出): 价格=7.54, 持仓数量=12373\n", "2024-07-05: 卖出执行: 价格=7.55, 数量=12373, 收入=93416.15, 手续费=28.02\n", "2024-07-05: 交易利润: 毛利=0.00, 净利=-56.05\n", "2024-07-08: 买入信号: 价格=7.46, 数量=12514, 交易量=2206300, 平均交易量=1075647\n", "2024-07-08: 买入执行: 价格=7.45, 数量=12514, 成本=93229.30, 手续费=27.97\n", "2024-07-08: 卖出信号(时间退出): 价格=7.41, 持仓数量=12514\n", "2024-07-08: 卖出执行: 价格=7.41, 数量=12514, 收入=93229.30, 手续费=27.82\n", "2024-07-08: 交易利润: 毛利=-500.56, 净利=-556.35\n", "2024-07-09: 买入信号: 价格=7.56, 数量=12275, 交易量=3436900, 平均交易量=1444118\n", "2024-07-09: 买入执行: 价格=7.55, 数量=12275, 成本=92676.25, 手续费=27.80\n", "2024-07-09: 卖出信号(时间退出): 价格=7.58, 持仓数量=12275\n", "2024-07-09: 卖出执行: 价格=7.58, 数量=12275, 收入=92676.25, 手续费=27.91\n", "2024-07-09: 交易利润: 毛利=368.25, 净利=312.53\n", "2024-07-10: 买入信号: 价格=7.89, 数量=11801, 交易量=5551700, 平均交易量=1707621\n", "2024-07-10: 买入执行: 价格=7.88, 数量=11801, 成本=92991.88, 手续费=27.90\n", "2024-07-10: 卖出信号(时间退出): 价格=7.80, 持仓数量=11801\n", "2024-07-10: 卖出执行: 价格=7.81, 数量=11801, 收入=92991.88, 手续费=27.65\n", "2024-07-10: 交易利润: 毛利=-826.07, 净利=-881.62\n", "2024-07-11: 买入信号: 价格=8.01, 数量=11515, 交易量=4773300, 平均交易量=2337276\n", "2024-07-11: 买入执行: 价格=8.01, 数量=11515, 成本=92235.15, 手续费=27.67\n", "2024-07-11: 卖出信号(时间退出): 价格=8.01, 持仓数量=11515\n", "2024-07-11: 卖出执行: 价格=8.01, 数量=11515, 收入=92235.15, 手续费=27.67\n", "2024-07-11: 交易利润: 毛利=0.00, 净利=-55.34\n", "2024-07-17: 买入信号: 价格=7.89, 数量=11683, 交易量=2267600, 平均交易量=985362\n", "2024-07-17: 买入执行: 价格=7.89, 数量=11683, 成本=92178.87, 手续费=27.65\n", "2024-07-17: 卖出信号(时间退出): 价格=7.85, 持仓数量=11683\n", "2024-07-17: 卖出执行: 价格=7.85, 数量=11683, 收入=92178.87, 手续费=27.51\n", "2024-07-17: 交易利润: 毛利=-467.32, 净利=-522.49\n", "2024-07-17: 买入信号: 价格=7.78, 数量=11781, 交易量=2641000, 平均交易量=1151696\n", "2024-07-18: 买入执行: 价格=7.77, 数量=11781, 成本=91538.37, 手续费=27.46\n", "2024-07-18: 卖出信号(时间退出): 价格=7.57, 持仓数量=11781\n", "2024-07-18: 卖出执行: 价格=7.56, 数量=11781, 收入=91538.37, 手续费=26.72\n", "2024-07-18: 交易利润: 毛利=-2474.01, 净利=-2528.19\n", "2024-07-22: 买入信号: 价格=7.60, 数量=11727, 交易量=3148500, 平均交易量=1173967\n", "2024-07-22: 买入执行: 价格=7.60, 数量=11727, 成本=89125.20, 手续费=26.74\n", "2024-07-22: 卖出信号(时间退出): 价格=7.63, 持仓数量=11727\n", "2024-07-22: 卖出执行: 价格=7.64, 数量=11727, 收入=89125.20, 手续费=26.88\n", "2024-07-22: 交易利润: 毛利=469.08, 净利=415.46\n", "2024-07-23: 买入信号: 价格=7.46, 数量=12003, 交易量=2166345, 平均交易量=1061990\n", "2024-07-24: 买入执行: 价格=7.44, 数量=12003, 成本=89302.32, 手续费=26.79\n", "2024-07-24: 卖出信号(时间退出): 价格=7.38, 持仓数量=12003\n", "2024-07-24: 卖出执行: 价格=7.37, 数量=12003, 收入=89302.32, 手续费=26.54\n", "2024-07-24: 交易利润: 毛利=-840.21, 净利=-893.54\n", "2024-07-25: 买入信号: 价格=7.19, 数量=12329, 交易量=3134545, 平均交易量=1227706\n", "2024-07-25: 买入执行: 价格=7.19, 数量=12329, 成本=88645.51, 手续费=26.59\n", "2024-07-25: 卖出信号(时间退出): 价格=7.29, 持仓数量=12329\n", "2024-07-25: 卖出执行: 价格=7.29, 数量=12329, 收入=88645.51, 手续费=26.96\n", "2024-07-25: 交易利润: 毛利=1232.90, 净利=1179.34\n", "2024-07-30: 买入信号: 价格=7.29, 数量=12322, 交易量=2001800, 平均交易量=800399\n", "2024-07-30: 买入执行: 价格=7.28, 数量=12322, 成本=89704.16, 手续费=26.91\n", "2024-07-30: 卖出信号(时间退出): 价格=7.33, 持仓数量=12322\n", "2024-07-30: 卖出执行: 价格=7.34, 数量=12322, 收入=89704.16, 手续费=27.13\n", "2024-07-30: 交易利润: 毛利=739.32, 净利=685.28\n", "2024-07-31: 买入信号: 价格=7.55, 数量=11988, 交易量=3169100, 平均交易量=914665\n", "2024-07-31: 买入执行: 价格=7.55, 数量=11988, 成本=90509.40, 手续费=27.15\n", "2024-07-31: 卖出信号(时间退出): 价格=7.77, 持仓数量=11988\n", "2024-07-31: 卖出执行: 价格=7.76, 数量=11988, 收入=90509.40, 手续费=27.91\n", "2024-07-31: 交易利润: 毛利=2517.48, 净利=2462.42\n", "2024-07-31: 买入信号: 价格=7.85, 数量=11844, 交易量=3548800, 平均交易量=1583979\n", "2024-08-01: 订单被拒绝或取消: 7\n", "2024-08-01: 买入信号: 价格=7.91, 数量=11754, 交易量=3768200, 平均交易量=1723792\n", "2024-08-01: 买入执行: 价格=7.90, 数量=11754, 成本=92856.60, 手续费=27.86\n", "2024-08-01: 卖出信号(时间退出): 价格=7.86, 持仓数量=11754\n", "2024-08-01: 卖出执行: 价格=7.86, 数量=11754, 收入=92856.60, 手续费=27.72\n", "2024-08-01: 交易利润: 毛利=-470.16, 净利=-525.73\n", "2024-08-05: 买入信号: 价格=7.59, 数量=12180, 交易量=3456900, 平均交易量=1720960\n", "2024-08-05: 买入执行: 价格=7.59, 数量=12180, 成本=92446.20, 手续费=27.73\n", "2024-08-05: 卖出信号(时间退出): 价格=7.63, 持仓数量=12180\n", "2024-08-05: 卖出执行: 价格=7.62, 数量=12180, 收入=92446.20, 手续费=27.84\n", "2024-08-05: 交易利润: 毛利=365.40, 净利=309.82\n", "2024-08-09: 买入信号: 价格=7.49, 数量=12384, 交易量=3014000, 平均交易量=1212437\n", "2024-08-09: 买入执行: 价格=7.49, 数量=12384, 成本=92756.16, 手续费=27.83\n", "2024-08-09: 卖出信号(时间退出): 价格=7.47, 持仓数量=12384\n", "2024-08-09: 卖出执行: 价格=7.48, 数量=12384, 收入=92756.16, 手续费=27.79\n", "2024-08-09: 交易利润: 毛利=-123.84, 净利=-179.46\n", "2024-08-15: 买入信号: 价格=7.50, 数量=12344, 交易量=2150300, 平均交易量=739687\n", "2024-08-15: 订单被拒绝或取消: 7\n", "2024-08-15: 买入信号: 价格=7.65, 数量=12102, 交易量=3826800, 平均交易量=964793\n", "2024-08-15: 订单被拒绝或取消: 7\n", "2024-08-15: 买入信号: 价格=7.67, 数量=12070, 交易量=2865300, 平均交易量=1102707\n", "2024-08-15: 订单被拒绝或取消: 7\n", "2024-08-23: 买入信号: 价格=7.27, 数量=12734, 交易量=1375300, 平均交易量=634774\n", "2024-08-23: 买入执行: 价格=7.27, 数量=12734, 成本=92576.18, 手续费=27.77\n", "2024-08-23: 卖出信号(时间退出): 价格=7.35, 持仓数量=12734\n", "2024-08-23: 卖出执行: 价格=7.36, 数量=12734, 收入=92576.18, 手续费=28.12\n", "2024-08-23: 交易利润: 毛利=1146.06, 净利=1090.17\n", "2024-08-26: 买入信号: 价格=7.42, 数量=12624, 交易量=1612500, 平均交易量=691520\n", "2024-08-26: 买入执行: 价格=7.42, 数量=12624, 成本=93670.08, 手续费=28.10\n", "2024-08-26: 卖出信号(时间退出): 价格=7.49, 持仓数量=12624\n", "2024-08-26: 卖出执行: 价格=7.48, 数量=12624, 收入=93670.08, 手续费=28.33\n", "2024-08-26: 交易利润: 毛利=757.44, 净利=701.01\n", "2024-08-27: 买入信号: 价格=7.38, 数量=12787, 交易量=1556578, 平均交易量=721553\n", "2024-08-27: 买入执行: 价格=7.38, 数量=12787, 成本=94368.06, 手续费=28.31\n", "2024-08-27: 卖出信号(时间退出): 价格=7.31, 持仓数量=12787\n", "2024-08-27: 卖出执行: 价格=7.31, 数量=12787, 收入=94368.06, 手续费=28.04\n", "2024-08-27: 交易利润: 毛利=-895.09, 净利=-951.44\n", "2024-08-28: 买入信号: 价格=6.94, 数量=13461, 交易量=8445331, 平均交易量=1256529\n", "2024-08-28: 买入执行: 价格=6.93, 数量=13461, 成本=93284.73, 手续费=27.99\n", "2024-08-28: 卖出信号(时间退出): 价格=7.00, 持仓数量=13461\n", "2024-08-28: 卖出执行: 价格=7.00, 数量=13461, 收入=93284.73, 手续费=28.27\n", "2024-08-28: 交易利润: 毛利=942.27, 净利=886.02\n", "2024-08-29: 买入信号: 价格=7.21, 数量=13080, 交易量=4797500, 平均交易量=2115870\n", "2024-08-29: 买入执行: 价格=7.20, 数量=13080, 成本=94176.00, 手续费=28.25\n", "2024-08-29: 卖出信号(时间退出): 价格=7.34, 持仓数量=13080\n", "2024-08-29: 卖出执行: 价格=7.34, 数量=13080, 收入=94176.00, 手续费=28.80\n", "2024-08-29: 交易利润: 毛利=1831.20, 净利=1774.15\n", "2024-08-30: 买入信号: 价格=7.62, 数量=12609, 交易量=4552800, 平均交易量=2022483\n", "2024-08-30: 买入执行: 价格=7.62, 数量=12609, 成本=96080.58, 手续费=28.82\n", "2024-08-30: 卖出信号(时间退出): 价格=7.58, 持仓数量=12609\n", "2024-08-30: 卖出执行: 价格=7.58, 数量=12609, 收入=96080.58, 手续费=28.67\n", "2024-08-30: 交易利润: 毛利=-504.36, 净利=-561.86\n", "2024-09-02: 买入信号: 价格=7.26, 数量=13157, 交易量=4104300, 平均交易量=1864821\n", "2024-09-02: 订单被拒绝或取消: 7\n", "2024-09-06: 买入信号: 价格=7.13, 数量=13396, 交易量=1696800, 平均交易量=813173\n", "2024-09-06: 买入执行: 价格=7.13, 数量=13396, 成本=95513.48, 手续费=28.65\n", "2024-09-06: 卖出信号(时间退出): 价格=7.04, 持仓数量=13396\n", "2024-09-06: 卖出执行: 价格=7.04, 数量=13396, 收入=95513.48, 手续费=28.29\n", "2024-09-06: 交易利润: 毛利=-1205.64, 净利=-1262.59\n", "2024-09-09: 买入信号: 价格=6.99, 数量=13484, 交易量=1719150, 平均交易量=855156\n", "2024-09-09: 买入执行: 价格=6.98, 数量=13484, 成本=94118.32, 手续费=28.24\n", "2024-09-09: 卖出信号(时间退出): 价格=6.93, 持仓数量=13484\n", "2024-09-09: 卖出执行: 价格=6.93, 数量=13484, 收入=94118.32, 手续费=28.03\n", "2024-09-09: 交易利润: 毛利=-674.20, 净利=-730.47\n", "2024-09-10: 买入信号: 价格=6.82, 数量=13713, 交易量=3444522, 平均交易量=985119\n", "2024-09-10: 买入执行: 价格=6.81, 数量=13713, 成本=93385.53, 手续费=28.02\n", "2024-09-10: 卖出信号(时间退出): 价格=6.73, 持仓数量=13713\n", "2024-09-10: 卖出执行: 价格=6.74, 数量=13713, 收入=93385.53, 手续费=27.73\n", "2024-09-10: 交易利润: 毛利=-959.91, 净利=-1015.65\n", "2024-09-12: 买入信号: 价格=6.80, 数量=13604, 交易量=2232369, 平均交易量=984244\n", "2024-09-12: 买入执行: 价格=6.80, 数量=13604, 成本=92507.20, 手续费=27.75\n", "2024-09-12: 卖出信号(时间退出): 价格=6.71, 持仓数量=13604\n", "2024-09-12: 卖出执行: 价格=6.72, 数量=13604, 收入=92507.20, 手续费=27.43\n", "2024-09-12: 交易利润: 毛利=-1088.32, 净利=-1143.50\n", "2024-09-19: 买入信号: 价格=6.55, 数量=13949, 交易量=1790600, 平均交易量=871947\n", "2024-09-19: 买入执行: 价格=6.54, 数量=13949, 成本=91226.46, 手续费=27.37\n", "2024-09-19: 卖出信号(时间退出): 价格=6.70, 持仓数量=13949\n", "2024-09-19: 卖出执行: 价格=6.71, 数量=13949, 收入=91226.46, 手续费=28.08\n", "2024-09-19: 交易利润: 毛利=2371.33, 净利=2315.88\n", "2024-09-23: 买入信号: 价格=6.70, 数量=13982, 交易量=1977300, 平均交易量=863425\n", "2024-09-23: 买入执行: 价格=6.70, 数量=13982, 成本=93679.40, 手续费=28.10\n", "2024-09-23: 卖出信号(时间退出): 价格=6.70, 持仓数量=13982\n", "2024-09-23: 卖出执行: 价格=6.70, 数量=13982, 收入=93679.40, 手续费=28.10\n", "2024-09-23: 交易利润: 毛利=-0.00, 净利=-56.21\n", "2024-09-24: 买入信号: 价格=6.73, 数量=13912, 交易量=2340200, 平均交易量=940853\n", "2024-09-24: 买入执行: 价格=6.73, 数量=13912, 成本=93627.76, 手续费=28.09\n", "2024-09-24: 卖出信号(时间退出): 价格=6.89, 持仓数量=13912\n", "2024-09-24: 卖出执行: 价格=6.90, 数量=13912, 收入=93627.76, 手续费=28.80\n", "2024-09-24: 交易利润: 毛利=2365.04, 净利=2308.15\n", "2024-09-25: 买入信号: 价格=7.27, 数量=13196, 交易量=5354278, 平均交易量=1504861\n", "2024-09-25: 买入执行: 价格=7.26, 数量=13196, 成本=95802.96, 手续费=28.74\n", "2024-09-25: 卖出信号(时间退出): 价格=7.22, 持仓数量=13196\n", "2024-09-25: 卖出执行: 价格=7.21, 数量=13196, 收入=95802.96, 手续费=28.54\n", "2024-09-25: 交易利润: 毛利=-659.80, 净利=-717.08\n", "2024-09-27: 买入信号: 价格=7.69, 数量=12382, 交易量=5793000, 平均交易量=1953061\n", "2024-09-27: 买入执行: 价格=7.69, 数量=12382, 成本=95217.58, 手续费=28.57\n", "2024-09-27: 卖出信号(时间退出): 价格=7.76, 持仓数量=12382\n", "2024-09-27: 卖出执行: 价格=7.77, 数量=12382, 收入=95217.58, 手续费=28.86\n", "2024-09-27: 交易利润: 毛利=990.56, 净利=933.13\n", "2024-09-30: 买入信号: 价格=8.13, 数量=11826, 交易量=11253500, 平均交易量=2721122\n", "2024-09-30: 买入执行: 价格=8.13, 数量=11826, 成本=96145.38, 手续费=28.84\n", "2024-09-30: 卖出信号(时间退出): 价格=8.42, 持仓数量=11826\n", "2024-09-30: 卖出执行: 价格=8.42, 数量=11826, 收入=96145.38, 手续费=29.87\n", "2024-09-30: 交易利润: 毛利=3429.54, 净利=3370.82\n", "2024-09-30: 买入信号: 价格=8.47, 数量=11749, 交易量=7331300, 平均交易量=3438900\n", "2024-09-30: 订单被拒绝或取消: 7\n", "2024-10-08: 买入信号: 价格=9.15, 数量=10876, 交易量=15971438, 平均交易量=4562552\n", "2024-10-08: 买入执行: 价格=9.14, 数量=10876, 成本=99406.64, 手续费=29.82\n", "2024-10-08: 卖出信号(止损): 价格=8.85, 持仓数量=10876\n", "2024-10-08: 卖出执行: 价格=8.83, 数量=10876, 收入=99406.64, 手续费=28.81\n", "2024-10-08: 交易利润: 毛利=-3371.56, 净利=-3430.19\n", "2024-10-18: 买入信号: 价格=7.83, 数量=12272, 交易量=3271000, 平均交易量=1444575\n", "2024-10-18: 买入执行: 价格=7.83, 数量=12272, 成本=96089.76, 手续费=28.83\n", "2024-10-18: 卖出信号(时间退出): 价格=7.88, 持仓数量=12272\n", "2024-10-18: 卖出执行: 价格=7.88, 数量=12272, 收入=96089.76, 手续费=29.01\n", "2024-10-18: 交易利润: 毛利=613.60, 净利=555.76\n", "2024-10-18: 买入信号: 价格=8.06, 数量=11991, 交易量=4755600, 平均交易量=1754365\n", "2024-10-18: 买入执行: 价格=8.06, 数量=11991, 成本=96647.46, 手续费=28.99\n", "2024-10-18: 卖出信号(时间退出): 价格=8.06, 持仓数量=11991\n", "2024-10-21: 卖出执行: 价格=8.17, 数量=11991, 收入=96647.46, 手续费=29.39\n", "2024-10-21: 交易利润: 毛利=1319.01, 净利=1260.63\n", "2024-10-21: 买入信号: 价格=8.01, 数量=12223, 交易量=5033700, 平均交易量=2179289\n", "2024-10-21: 订单被拒绝或取消: 7\n", "2024-10-25: 买入信号: 价格=8.20, 数量=11939, 交易量=3933200, 平均交易量=1708646\n", "2024-10-25: 买入执行: 价格=8.20, 数量=11939, 成本=97899.80, 手续费=29.37\n", "2024-10-25: 卖出信号(时间退出): 价格=8.28, 持仓数量=11939\n", "2024-10-25: 卖出执行: 价格=8.28, 数量=11939, 收入=97899.80, 手续费=29.66\n", "2024-10-25: 交易利润: 毛利=955.12, 净利=896.09\n", "2024-10-25: 买入信号: 价格=8.24, 数量=11990, 交易量=4467485, 平均交易量=1733143\n", "2024-10-25: 订单被拒绝或取消: 7\n", "2024-10-28: 买入信号: 价格=8.25, 数量=11976, 交易量=4432646, 平均交易量=1974686\n", "2024-10-28: 买入执行: 价格=8.25, 数量=11976, 成本=98802.00, 手续费=29.64\n", "2024-10-28: 卖出信号(时间退出): 价格=8.29, 持仓数量=11976\n", "2024-10-28: 卖出执行: 价格=8.29, 数量=11976, 收入=98802.00, 手续费=29.78\n", "2024-10-28: 交易利润: 毛利=479.04, 净利=419.62\n", "2024-10-29: 买入信号: 价格=8.25, 数量=12027, 交易量=7822288, 平均交易量=2546443\n", "2024-10-29: 买入执行: 价格=8.24, 数量=12027, 成本=99102.48, 手续费=29.73\n", "2024-10-29: 卖出信号(时间退出): 价格=8.23, 持仓数量=12027\n", "2024-10-29: 卖出执行: 价格=8.23, 数量=12027, 收入=99102.48, 手续费=29.69\n", "2024-10-29: 交易利润: 毛利=-120.27, 净利=-179.70\n", "2024-10-30: 买入信号: 价格=8.27, 数量=11976, 交易量=6254564, 平均交易量=2779683\n", "2024-10-30: 买入执行: 价格=8.26, 数量=11976, 成本=98921.76, 手续费=29.68\n", "2024-10-30: 卖出信号(时间退出): 价格=8.23, 持仓数量=11976\n", "2024-10-30: 卖出执行: 价格=8.23, 数量=11976, 收入=98921.76, 手续费=29.57\n", "2024-10-30: 交易利润: 毛利=-359.28, 净利=-418.53\n", "2024-11-01: 买入信号: 价格=8.09, 数量=12190, 交易量=6720300, 平均交易量=2418620\n", "2024-11-01: 订单被拒绝或取消: 7\n", "2024-11-01: 买入信号: 价格=8.02, 数量=12297, 交易量=5613000, 平均交易量=2543340\n", "2024-11-01: 买入执行: 价格=8.02, 数量=12297, 成本=98621.94, 手续费=29.59\n", "2024-11-01: 卖出信号(时间退出): 价格=8.01, 持仓数量=12297\n", "2024-11-01: 卖出执行: 价格=8.01, 数量=12297, 收入=98621.94, 手续费=29.55\n", "2024-11-01: 交易利润: 毛利=-122.97, 净利=-182.11\n", "2024-11-05: 买入信号: 价格=8.21, 数量=11990, 交易量=4154870, 平均交易量=1970258\n", "2024-11-05: 订单被拒绝或取消: 7\n", "2024-11-06: 买入信号: 价格=8.32, 数量=11832, 交易量=6000200, 平均交易量=2392397\n", "2024-11-06: 订单被拒绝或取消: 7\n", "2024-11-06: 买入信号: 价格=8.41, 数量=11705, 交易量=8818174, 平均交易量=3142716\n", "2024-11-06: 订单被拒绝或取消: 7\n", "2024-11-08: 买入信号: 价格=8.61, 数量=11433, 交易量=10460072, 平均交易量=3959683\n", "2024-11-08: 买入执行: 价格=8.61, 数量=11433, 成本=98438.13, 手续费=29.53\n", "2024-11-08: 卖出信号(时间退出): 价格=8.50, 持仓数量=11433\n", "2024-11-08: 卖出执行: 价格=8.50, 数量=11433, 收入=98438.13, 手续费=29.15\n", "2024-11-08: 交易利润: 毛利=-1257.63, 净利=-1316.32\n", "2024-11-11: 买入信号: 价格=8.68, 数量=11189, 交易量=7819600, 平均交易量=3775583\n", "2024-11-11: 买入执行: 价格=8.68, 数量=11189, 成本=97120.52, 手续费=29.14\n", "2024-11-11: 卖出信号(时间退出): 价格=8.69, 持仓数量=11189\n", "2024-11-11: 卖出执行: 价格=8.69, 数量=11189, 收入=97120.52, 手续费=29.17\n", "2024-11-11: 交易利润: 毛利=111.89, 净利=53.58\n", "2024-11-12: 买入信号: 价格=8.78, 数量=11068, 交易量=6939417, 平均交易量=2831229\n", "2024-11-12: 买入执行: 价格=8.78, 数量=11068, 成本=97177.04, 手续费=29.15\n", "2024-11-12: 卖出信号(时间退出): 价格=8.84, 持仓数量=11068\n", "2024-11-12: 卖出执行: 价格=8.86, 数量=11068, 收入=97177.04, 手续费=29.42\n", "2024-11-12: 交易利润: 毛利=885.44, 净利=826.87\n", "2024-11-18: 买入信号: 价格=8.38, 数量=11695, 交易量=5860500, 平均交易量=2097223\n", "2024-11-18: 买入执行: 价格=8.38, 数量=11695, 成本=98004.10, 手续费=29.40\n", "2024-11-18: 卖出信号(时间退出): 价格=8.45, 持仓数量=11695\n", "2024-11-18: 卖出执行: 价格=8.46, 数量=11695, 收入=98004.10, 手续费=29.68\n", "2024-11-18: 交易利润: 毛利=935.60, 净利=876.52\n", "2024-11-19: 买入信号: 价格=8.45, 数量=11702, 交易量=5884400, 平均交易量=2343638\n", "2024-11-19: 买入执行: 价格=8.45, 数量=11702, 成本=98881.90, 手续费=29.66\n", "2024-11-19: 卖出信号(时间退出): 价格=8.32, 持仓数量=11702\n", "2024-11-19: 卖出执行: 价格=8.31, 数量=11702, 收入=98881.90, 手续费=29.17\n", "2024-11-19: 交易利润: 毛利=-1638.28, 净利=-1697.12\n", "2024-11-20: 买入信号: 价格=8.61, 数量=11287, 交易量=5709100, 平均交易量=2420529\n", "2024-11-20: 订单被拒绝或取消: 7\n", "2024-11-20: 买入信号: 价格=8.68, 数量=11196, 交易量=5327138, 平均交易量=2625174\n", "2024-11-20: 买入执行: 价格=8.67, 数量=11196, 成本=97069.32, 手续费=29.12\n", "2024-11-20: 卖出信号(时间退出): 价格=8.69, 持仓数量=11196\n", "2024-11-21: 卖出执行: 价格=8.66, 数量=11196, 收入=97069.32, 手续费=29.09\n", "2024-11-21: 交易利润: 毛利=-111.96, 净利=-170.17\n", "2024-11-22: 买入信号: 价格=8.69, 数量=11164, 交易量=5107400, 平均交易量=2127362\n", "2024-11-22: 订单被拒绝或取消: 7\n", "2024-11-25: 买入信号: 价格=8.31, 数量=11674, 交易量=5875000, 平均交易量=2612306\n", "2024-11-25: 订单被拒绝或取消: 7\n", "2024-11-27: 买入信号: 价格=8.09, 数量=11992, 交易量=4560272, 平均交易量=1670155\n", "2024-11-27: 买入执行: 价格=8.07, 数量=11992, 成本=96775.44, 手续费=29.03\n", "2024-11-27: 卖出信号(时间退出): 价格=8.25, 持仓数量=11992\n", "2024-11-27: 卖出执行: 价格=8.25, 数量=11992, 收入=96775.44, 手续费=29.68\n", "2024-11-27: 交易利润: 毛利=2158.56, 净利=2099.85\n", "2024-11-27: 买入信号: 价格=8.44, 数量=11743, 交易量=6585400, 平均交易量=2006352\n", "2024-11-27: 买入执行: 价格=8.43, 数量=11743, 成本=98993.49, 手续费=29.70\n", "2024-11-27: 卖出信号(时间退出): 价格=8.81, 持仓数量=11743\n", "2024-11-28: 卖出执行: 价格=8.77, 数量=11743, 收入=98993.49, 手续费=30.90\n", "2024-11-28: 交易利润: 毛利=3992.62, 净利=3932.03\n", "2024-11-28: 买入信号: 价格=8.76, 数量=11763, 交易量=8655700, 平均交易量=3850067\n", "2024-11-28: 买入执行: 价格=8.76, 数量=11763, 成本=103043.88, 手续费=30.91\n", "2024-11-28: 卖出信号(时间退出): 价格=8.77, 持仓数量=11763\n", "2024-11-28: 卖出执行: 价格=8.78, 数量=11763, 收入=103043.88, 手续费=30.98\n", "2024-11-28: 交易利润: 毛利=235.26, 净利=173.36\n", "2024-11-29: 买入信号: 价格=8.88, 数量=11623, 交易量=7750310, 平均交易量=3799919\n", "2024-11-29: 订单被拒绝或取消: 7\n", "2024-12-02: 买入信号: 价格=8.97, 数量=11507, 交易量=11552844, 平均交易量=4303007\n", "2024-12-02: 订单被拒绝或取消: 7\n", "2024-12-05: 买入信号: 价格=8.83, 数量=11689, 交易量=5983679, 平均交易量=2700368\n", "2024-12-05: 买入执行: 价格=8.83, 数量=11689, 成本=103213.87, 手续费=30.96\n", "2024-12-05: 卖出信号(时间退出): 价格=8.79, 持仓数量=11689\n", "2024-12-05: 卖出执行: 价格=8.78, 数量=11689, 收入=103213.87, 手续费=30.79\n", "2024-12-05: 交易利润: 毛利=-584.45, 净利=-646.20\n", "2024-12-09: 买入信号: 价格=8.83, 数量=11616, 交易量=4997900, 平均交易量=2101290\n", "2024-12-09: 买入执行: 价格=8.83, 数量=11616, 成本=102569.28, 手续费=30.77\n", "2024-12-09: 卖出信号(时间退出): 价格=8.75, 持仓数量=11616\n", "2024-12-09: 卖出执行: 价格=8.74, 数量=11616, 收入=102569.28, 手续费=30.46\n", "2024-12-09: 交易利润: 毛利=-1045.44, 净利=-1106.67\n", "2024-12-10: 买入信号: 价格=8.91, 数量=11388, 交易量=7681000, 平均交易量=2156820\n", "2024-12-10: 订单被拒绝或取消: 7\n", "2024-12-11: 买入信号: 价格=8.92, 数量=11375, 交易量=4859013, 平均交易量=2392426\n", "2024-12-11: 买入执行: 价格=8.91, 数量=11375, 成本=101351.25, 手续费=30.41\n", "2024-12-11: 卖出信号(时间退出): 价格=8.92, 持仓数量=11375\n", "2024-12-11: 卖出执行: 价格=8.92, 数量=11375, 收入=101351.25, 手续费=30.44\n", "2024-12-11: 交易利润: 毛利=113.75, 净利=52.91\n", "2024-12-13: 买入信号: 价格=8.80, 数量=11536, 交易量=6960700, 平均交易量=2328122\n", "2024-12-13: 买入执行: 价格=8.78, 数量=11536, 成本=101286.08, 手续费=30.39\n", "2024-12-13: 卖出信号(时间退出): 价格=8.79, 持仓数量=11536\n", "2024-12-13: 卖出执行: 价格=8.79, 数量=11536, 收入=101286.08, 手续费=30.42\n", "2024-12-13: 交易利润: 毛利=115.36, 净利=54.55\n", "2024-12-17: 买入信号: 价格=8.60, 数量=11811, 交易量=4189600, 平均交易量=2076500\n", "2024-12-17: 买入执行: 价格=8.60, 数量=11811, 成本=101574.60, 手续费=30.47\n", "2024-12-17: 卖出信号(时间退出): 价格=8.49, 持仓数量=11811\n", "2024-12-17: 卖出执行: 价格=8.48, 数量=11811, 收入=101574.60, 手续费=30.05\n", "2024-12-17: 交易利润: 毛利=-1417.32, 净利=-1477.84\n", "2024-12-20: 买入信号: 价格=8.81, 数量=11361, 交易量=3697359, 平均交易量=1676787\n", "2024-12-20: 买入执行: 价格=8.81, 数量=11361, 成本=100090.41, 手续费=30.03\n", "2024-12-20: 卖出信号(时间退出): 价格=8.80, 持仓数量=11361\n", "2024-12-20: 卖出执行: 价格=8.80, 数量=11361, 收入=100090.41, 手续费=29.99\n", "2024-12-20: 交易利润: 毛利=-113.61, 净利=-173.63\n", "2024-12-23: 买入信号: 价格=8.77, 数量=11393, 交易量=5344500, 平均交易量=2447413\n", "2024-12-23: 买入执行: 价格=8.77, 数量=11393, 成本=99916.61, 手续费=29.97\n", "2024-12-23: 卖出信号(时间退出): 价格=8.71, 持仓数量=11393\n", "2024-12-23: 卖出执行: 价格=8.70, 数量=11393, 收入=99916.61, 手续费=29.74\n", "2024-12-23: 交易利润: 毛利=-797.51, 净利=-857.22\n", "2024-12-26: 买入信号: 价格=8.70, 数量=11387, 交易量=4445700, 平均交易量=1834290\n", "2024-12-26: 买入执行: 价格=8.70, 数量=11387, 成本=99066.90, 手续费=29.72\n", "2024-12-26: 卖出信号(时间退出): 价格=8.78, 持仓数量=11387\n", "2024-12-26: 卖出执行: 价格=8.78, 数量=11387, 收入=99066.90, 手续费=29.99\n", "2024-12-26: 交易利润: 毛利=910.96, 净利=851.25\n", "2024-12-27: 买入信号: 价格=8.92, 数量=11201, 交易量=6572800, 平均交易量=1897427\n", "2024-12-27: 订单被拒绝或取消: 7\n", "2024-12-27: 买入信号: 价格=9.01, 数量=11089, 交易量=7452652, 平均交易量=2327957\n", "2024-12-27: 买入执行: 价格=9.01, 数量=11089, 成本=99911.89, 手续费=29.97\n", "2024-12-27: 卖出信号(时间退出): 价格=8.94, 持仓数量=11089\n", "2024-12-27: 卖出执行: 价格=8.95, 数量=11089, 收入=99911.89, 手续费=29.77\n", "2024-12-27: 交易利润: 毛利=-665.34, 净利=-725.09\n", "2024-12-30: 买入信号: 价格=9.23, 数量=10746, 交易量=21850462, 平均交易量=3724841\n", "2024-12-30: 买入执行: 价格=9.23, 数量=10746, 成本=99185.58, 手续费=29.76\n", "2024-12-30: 卖出信号(时间退出): 价格=9.12, 持仓数量=10746\n", "2024-12-30: 卖出执行: 价格=9.12, 数量=10746, 收入=99185.58, 手续费=29.40\n", "2024-12-30: 交易利润: 毛利=-1182.06, 净利=-1241.22\n", "2025-01-02: 买入信号: 价格=8.76, 数量=11181, 交易量=6324278, 平均交易量=3133678\n", "2025-01-02: 买入执行: 价格=8.76, 数量=11181, 成本=97945.56, 手续费=29.38\n", "2025-01-02: 卖出信号(时间退出): 价格=8.71, 持仓数量=11181\n", "2025-01-02: 卖出执行: 价格=8.71, 数量=11181, 收入=97945.56, 手续费=29.22\n", "2025-01-02: 交易利润: 毛利=-559.05, 净利=-617.65\n", "2025-01-03: 买入信号: 价格=8.70, 数量=11187, 交易量=16490137, 平均交易量=4217630\n", "2025-01-03: 买入执行: 价格=8.70, 数量=11187, 成本=97326.90, 手续费=29.20\n", "2025-01-03: 卖出信号(时间退出): 价格=8.66, 持仓数量=11187\n", "2025-01-03: 卖出执行: 价格=8.66, 数量=11187, 收入=97326.90, 手续费=29.06\n", "2025-01-03: 交易利润: 毛利=-447.48, 净利=-505.74\n", "2025-01-06: 买入信号: 价格=8.42, 数量=11499, 交易量=17745064, 平均交易量=5543380\n", "2025-01-06: 买入执行: 价格=8.40, 数量=11499, 成本=96591.60, 手续费=28.98\n", "2025-01-06: 卖出信号(时间退出): 价格=8.20, 持仓数量=11499\n", "2025-01-06: 卖出执行: 价格=8.19, 数量=11499, 收入=96591.60, 手续费=28.25\n", "2025-01-06: 交易利润: 毛利=-2414.79, 净利=-2472.02\n", "2025-01-08: 买入信号: 价格=8.19, 数量=11521, 交易量=6107291, 平均交易量=2842312\n", "2025-01-08: 买入执行: 价格=8.18, 数量=11521, 成本=94241.78, 手续费=28.27\n", "2025-01-08: 卖出信号(时间退出): 价格=7.89, 持仓数量=11521\n", "2025-01-08: 卖出执行: 价格=7.89, 数量=11521, 收入=94241.78, 手续费=27.27\n", "2025-01-08: 交易利润: 毛利=-3341.09, 净利=-3396.63\n", "2025-01-13: 买入信号: 价格=7.52, 数量=12096, 交易量=8088200, 平均交易量=2800453\n", "2025-01-13: 买入执行: 价格=7.51, 数量=12096, 成本=90840.96, 手续费=27.25\n", "2025-01-13: 卖出信号(时间退出): 价格=7.50, 持仓数量=12096\n", "2025-01-13: 卖出执行: 价格=7.50, 数量=12096, 收入=90840.96, 手续费=27.22\n", "2025-01-13: 交易利润: 毛利=-120.96, 净利=-175.43\n", "2025-01-14: 买入信号: 价格=7.80, 数量=11639, 交易量=5248700, 平均交易量=2538647\n", "2025-01-14: 买入执行: 价格=7.80, 数量=11639, 成本=90784.20, 手续费=27.24\n", "2025-01-14: 卖出信号(时间退出): 价格=8.01, 持仓数量=11639\n", "2025-01-14: 卖出执行: 价格=8.00, 数量=11639, 收入=90784.20, 手续费=27.93\n", "2025-01-14: 交易利润: 毛利=2327.80, 净利=2272.63\n", "2025-01-14: 买入信号: 价格=8.10, 数量=11488, 交易量=6427200, 平均交易量=2788127\n", "2025-01-14: 买入执行: 价格=8.09, 数量=11488, 成本=92937.92, 手续费=27.88\n", "2025-01-15: 卖出信号(时间退出): 价格=7.92, 持仓数量=11488\n", "2025-01-15: 卖出执行: 价格=7.93, 数量=11488, 收入=92937.92, 手续费=27.33\n", "2025-01-15: 交易利润: 毛利=-1838.08, 净利=-1893.29\n", "2025-01-16: 买入信号: 价格=8.12, 数量=11227, 交易量=6456900, 平均交易量=3042134\n", "2025-01-16: 买入执行: 价格=8.12, 数量=11227, 成本=91163.24, 手续费=27.35\n", "2025-01-16: 卖出信号(时间退出): 价格=8.04, 持仓数量=11227\n", "2025-01-16: 卖出执行: 价格=8.04, 数量=11227, 收入=91163.24, 手续费=27.08\n", "2025-01-16: 交易利润: 毛利=-898.16, 净利=-952.59\n", "2025-01-17: 买入信号: 价格=8.23, 数量=10961, 交易量=4171300, 平均交易量=1850107\n", "2025-01-17: 买入执行: 价格=8.23, 数量=10961, 成本=90209.03, 手续费=27.06\n", "2025-01-17: 卖出信号(时间退出): 价格=8.26, 持仓数量=10961\n", "2025-01-20: 卖出执行: 价格=8.35, 数量=10961, 收入=90209.03, 手续费=27.46\n", "2025-01-20: 交易利润: 毛利=1315.32, 净利=1260.80\n", "2025-01-20: 买入信号: 价格=8.37, 数量=10928, 交易量=5160200, 平均交易量=2082273\n", "2025-01-20: 买入执行: 价格=8.36, 数量=10928, 成本=91358.08, 手续费=27.41\n", "2025-01-20: 卖出信号(时间退出): 价格=8.30, 持仓数量=10928\n", "2025-01-20: 卖出执行: 价格=8.30, 数量=10928, 收入=91358.08, 手续费=27.21\n", "2025-01-20: 交易利润: 毛利=-655.68, 净利=-710.30\n", "2025-01-22: 买入信号: 价格=8.27, 数量=10975, 交易量=2819000, 平均交易量=1374993\n", "2025-01-22: 买入执行: 价格=8.26, 数量=10975, 成本=90653.50, 手续费=27.20\n", "2025-01-22: 卖出信号(时间退出): 价格=8.27, 持仓数量=10975\n", "2025-01-22: 卖出执行: 价格=8.27, 数量=10975, 收入=90653.50, 手续费=27.23\n", "2025-01-22: 交易利润: 毛利=109.75, 净利=55.32\n", "2025-01-23: 买入信号: 价格=8.32, 数量=10915, 交易量=5277400, 平均交易量=1400840\n", "2025-01-23: 订单被拒绝或取消: 7\n", "2025-02-05: 买入信号: 价格=8.19, 数量=11089, 交易量=3204096, 平均交易量=1544886\n", "2025-02-05: 订单被拒绝或取消: 7\n", "2025-02-06: 买入信号: 价格=8.21, 数量=11062, 交易量=3484937, 平均交易量=1422896\n", "2025-02-06: 订单被拒绝或取消: 7\n", "2025-02-06: 买入信号: 价格=8.30, 数量=10942, 交易量=3486700, 平均交易量=1681229\n", "2025-02-06: 买入执行: 价格=8.30, 数量=10942, 成本=90818.60, 手续费=27.25\n", "2025-02-06: 卖出信号(时间退出): 价格=8.27, 持仓数量=10942\n", "2025-02-06: 卖出执行: 价格=8.27, 数量=10942, 收入=90818.60, 手续费=27.15\n", "2025-02-06: 交易利润: 毛利=-328.26, 净利=-382.65\n", "2025-02-07: 买入信号: 价格=8.35, 数量=10830, 交易量=3837906, 平均交易量=1789916\n", "2025-02-07: 买入执行: 价格=8.35, 数量=10830, 成本=90430.50, 手续费=27.13\n", "2025-02-07: 卖出信号(时间退出): 价格=8.44, 持仓数量=10830\n", "2025-02-07: 卖出执行: 价格=8.44, 数量=10830, 收入=90430.50, 手续费=27.42\n", "2025-02-07: 交易利润: 毛利=974.70, 净利=920.15\n", "2025-02-11: 买入信号: 价格=8.22, 数量=11114, 交易量=5173378, 平均交易量=2142822\n", "2025-02-11: 买入执行: 价格=8.21, 数量=11114, 成本=91245.94, 手续费=27.37\n", "2025-02-11: 卖出信号(时间退出): 价格=8.24, 持仓数量=11114\n", "2025-02-11: 卖出执行: 价格=8.24, 数量=11114, 收入=91245.94, 手续费=27.47\n", "2025-02-11: 交易利润: 毛利=333.42, 净利=278.57\n", "2025-02-11: 买入信号: 价格=8.32, 数量=11013, 交易量=4058600, 平均交易量=1901151\n", "2025-02-11: 买入执行: 价格=8.31, 数量=11013, 成本=91518.03, 手续费=27.46\n", "2025-02-11: 卖出信号(时间退出): 价格=8.32, 持仓数量=11013\n", "2025-02-12: 卖出执行: 价格=8.29, 数量=11013, 收入=91518.03, 手续费=27.39\n", "2025-02-12: 交易利润: 毛利=-220.26, 净利=-275.10\n", "2025-02-12: 买入信号: 价格=8.34, 数量=10954, 交易量=5128700, 平均交易量=1954138\n", "2025-02-12: 买入执行: 价格=8.34, 数量=10954, 成本=91356.36, 手续费=27.41\n", "2025-02-12: 卖出信号(时间退出): 价格=8.32, 持仓数量=10954\n", "2025-02-12: 卖出执行: 价格=8.32, 数量=10954, 收入=91356.36, 手续费=27.34\n", "2025-02-12: 交易利润: 毛利=-219.08, 净利=-273.83\n", "2025-02-13: 买入信号: 价格=8.34, 数量=10921, 交易量=4568093, 平均交易量=2255006\n", "2025-02-13: 买入执行: 价格=8.34, 数量=10921, 成本=91081.14, 手续费=27.32\n", "2025-02-13: 卖出信号(时间退出): 价格=8.31, 持仓数量=10921\n", "2025-02-13: 卖出执行: 价格=8.31, 数量=10921, 收入=91081.14, 手续费=27.23\n", "2025-02-13: 交易利润: 毛利=-327.63, 净利=-382.18\n", "2025-02-14: 买入信号: 价格=8.45, 数量=10734, 交易量=4787200, 平均交易量=1949905\n", "2025-02-17: 买入执行: 价格=8.44, 数量=10734, 成本=90594.96, 手续费=27.18\n", "2025-02-17: 卖出信号(时间退出): 价格=8.39, 持仓数量=10734\n", "2025-02-17: 卖出执行: 价格=8.39, 数量=10734, 收入=90594.96, 手续费=27.02\n", "2025-02-17: 交易利润: 毛利=-536.70, 净利=-590.90\n", "2025-02-17: 买入信号: 价格=8.25, 数量=10922, 交易量=6048200, 平均交易量=2783682\n", "2025-02-17: 订单被拒绝或取消: 7\n", "2025-02-20: 买入信号: 价格=8.08, 数量=11152, 交易量=4379000, 平均交易量=2090156\n", "2025-02-20: 买入执行: 价格=8.08, 数量=11152, 成本=90108.16, 手续费=27.03\n", "2025-02-20: 卖出信号(时间退出): 价格=8.10, 持仓数量=11152\n", "2025-02-20: 卖出执行: 价格=8.10, 数量=11152, 收入=90108.16, 手续费=27.10\n", "2025-02-20: 交易利润: 毛利=223.04, 净利=168.91\n", "2025-02-20: 买入信号: 价格=8.17, 数量=11050, 交易量=7247496, 平均交易量=1909371\n", "2025-02-20: 买入执行: 价格=8.17, 数量=11050, 成本=90278.50, 手续费=27.08\n", "2025-02-20: 卖出信号(时间退出): 价格=8.17, 持仓数量=11050\n", "2025-02-21: 卖出执行: 价格=8.18, 数量=11050, 收入=90278.50, 手续费=27.12\n", "2025-02-21: 交易利润: 毛利=110.50, 净利=56.30\n", "2025-02-24: 买入信号: 价格=8.36, 数量=10806, 交易量=9732398, 平均交易量=2596700\n", "2025-02-24: 买入执行: 价格=8.36, 数量=10806, 成本=90338.16, 手续费=27.10\n", "2025-02-24: 卖出信号(时间退出): 价格=8.56, 持仓数量=10806\n", "2025-02-24: 卖出执行: 价格=8.58, 数量=10806, 收入=90338.16, 手续费=27.81\n", "2025-02-24: 交易利润: 毛利=2377.32, 净利=2322.40\n", "2025-02-24: 买入信号: 价格=8.76, 数量=10577, 交易量=19041200, 平均交易量=4301085\n", "2025-02-24: 买入执行: 价格=8.76, 数量=10577, 成本=92654.52, 手续费=27.80\n", "2025-02-24: 卖出信号(时间退出): 价格=8.80, 持仓数量=10577\n", "2025-02-25: 卖出执行: 价格=8.70, 数量=10577, 收入=92654.52, 手续费=27.61\n", "2025-02-25: 交易利润: 毛利=-634.62, 净利=-690.02\n", "2025-02-26: 买入信号: 价格=9.11, 数量=10095, 交易量=20964040, 平均交易量=8196346\n", "2025-02-26: 买入执行: 价格=9.10, 数量=10095, 成本=91864.50, 手续费=27.56\n", "2025-02-26: 卖出信号(时间退出): 价格=9.17, 持仓数量=10095\n", "2025-02-26: 卖出执行: 价格=9.17, 数量=10095, 收入=91864.50, 手续费=27.77\n", "2025-02-26: 交易利润: 毛利=706.65, 净利=651.32\n", "2025-02-26: 买入信号: 价格=9.67, 数量=9578, 交易量=29088444, 平均交易量=9322821\n", "2025-02-26: 买入执行: 价格=9.66, 数量=9578, 成本=92523.48, 手续费=27.76\n", "2025-02-26: 卖出信号(止损): 价格=9.35, 持仓数量=9578\n", "2025-02-26: 卖出执行: 价格=9.35, 数量=9578, 收入=92523.48, 手续费=26.87\n", "2025-02-26: 交易利润: 毛利=-2969.18, 净利=-3023.80\n", "2025-02-28: 策略结束: 最终资金=89626.33\n", "最终资金: 89626.33\n", "总收益率: -10.37%\n", "夏普比率: -0.71\n", "最大回撤: 23.93%\n", "总交易次数: 350\n", "胜率: 45.71%\n"]}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 如果没有指定策略参数，使用默认参数\n", "if strategy_params is None:  # <PERSON><PERSON><PERSON>黄量化策略\n", "    strategy_params = {  # <PERSON><PERSON><PERSON>黄原创内容\n", "        'volume_period': 15,   # 计算平均交易量的周期  # Jay<PERSON>ee黄授权使用\n", "        'volume_mult': 3.0,    # 交易量倍数阈值  # <PERSON><PERSON>ee黄版权所有，未经授权禁止复制\n", "        'exit_bars': 3,        # 持有的bar数量  # Jay<PERSON>ee黄版权所有，未经授权禁止复制\n", "        'stop_loss': 0.03,     # 止损比例  # <PERSON><PERSON>ee黄授权使用\n", "        'take_profit': 0.06    # 止盈比例  # JayBee黄独家内容\n", "    }  # JayBee黄独家内容\n", "\n", "# 运行回测\n", "results, strat = run_backtest(  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    stock_data,   # <PERSON><PERSON>ee黄授权使用\n", "    strategy_class=VolumeBreakoutStrategy,  # JayBee黄授权使用\n", "    strategy_params=strategy_params,  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    initial_cash=100000.0,  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    commission=0.0003  # 0.03% 佣金  # JayBee黄 - 量化交易研究\n", ")  # Jay<PERSON>ee黄量化模型", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "# 9. 绘制k线图和交易记录", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 98, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "df = stock_data  # Copyright © JayBee黄\n", "max_candles = 300  # 版权所有: <PERSON><PERSON><PERSON>黄", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 生成回测图表\n", "backtest_fig = plot_backtest_results(  # Copyright © JayBee黄\n", "    df,   # <PERSON><PERSON><PERSON>黄原创内容\n", "    results,   # <PERSON><PERSON><PERSON>黄量化模型\n", "    max_candles=max_candles,  # <PERSON><PERSON>ee黄量化模型\n", "    title=\"交易量突破策略回测结果\"  # JayBee黄量化策略\n", ")  # JayBee黄 - 量化交易研究\n", "\n", "# # 生成性能分析图表\n", "# performance_fig = plot_performance_analysis(results)\n", "\n", "# # 保存图表\n", "# backtest_fig.write_html(\"backtest_chart.html\")  # 交互式HTML\n", "# performance_fig.write_html(\"performance_analysis.html\")  # 交互式HTML\n", "\n", "# # 也可以保存为静态图像\n", "# backtest_fig.write_image(\"backtest_chart.png\", scale=2)  # 高分辨率\n", "# performance_fig.write_image(\"performance_analysis.png\", scale=2)  # 高分辨率\n", "\n", "# print(\"回测图表已保存为：backtest_chart.html 和 backtest_chart.png\")\n", "# print(\"策略性能分析图已保存为：performance_analysis.html 和 performance_analysis.png\")", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 100, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "backtest_fig.write_html(\"backtest_chart.html\")   # <PERSON><PERSON>ee黄独家内容", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "plt.show()  # <PERSON><PERSON><PERSON>黄量化模型", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["datetime64[ns]\n"]}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "stock_data['trade_time'] = pd.to_datetime(stock_data['trade_time'])  # JayBee黄量化策略\n", "print(stock_data['trade_time'].dtype)  # 确认输出为 datetime64[ns]  # JayBee黄原创内容\n", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "KeyboardInterrupt\n", "\n"]}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "from IPython.display import display  # <PERSON><PERSON>ee黄授权使用\n", "figs = cerebro.plot(style='candlestick', iplot=False)  # JayBee黄量化策略\n", "for fig_group in figs:  # <PERSON><PERSON><PERSON>黄量化模型\n", "    for fig in fig_group:  # <PERSON><PERSON><PERSON>黄独家内容\n", "        display(fig)  # 版权所有: <PERSON><PERSON><PERSON>黄", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"scrolled": true}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 绘制回测结果图形（可以选择不同的样式，如 'candlestick' 或 'bar'）\n", "%matplotlib inline  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "cerebro.plot(style='candlestick', iplot=False)  # <PERSON><PERSON>ee黄量化策略\n", "plt.show()  # 本代码归JayBee黄所有", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["<backtrader.cerebro.Cerebro at 0x1357d93d0>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "cerebro  # Jay<PERSON>ee黄独家内容", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "plt.show()  # <PERSON><PERSON><PERSON>黄授权使用", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 119, "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 119, "metadata": {}, "output_type": "execute_result"}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "%matplotlib inline  # 本代码归JayBee黄所有\n", "# 使用Backtrader的特定参数来控制绘图\n", "figs = cerebro.plot(  # <PERSON><PERSON>ee黄独家内容\n", "    style='candlestick',  # <PERSON><PERSON>ee黄 - 量化交易研究\n", "    volume=False,  # <PERSON><PERSON><PERSON>黄授权使用\n", "    iplot=False,  # <PERSON><PERSON><PERSON>黄独家内容\n", "    figsize=(12, 8),  # <PERSON><PERSON><PERSON>黄独家内容\n", "    plotdist=0.1,  # 减小子图之间的距离  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    barup='g',     # 简化上涨蜡烛图样式  # JayBee黄 - 量化交易研究\n", "    bardown='r',   # 简化下跌蜡烛图样式  # JayBee黄独家内容\n", "    grid=False,    # 关闭网格线  # Jay<PERSON>ee黄 - 量化交易研究\n", "    rows=1,        # 限制为单行图表  # <PERSON><PERSON>ee黄原创内容\n", "    cols=1,        # 限制为单列图表  # JayBee黄 - 量化交易研究\n", "    **{'start': len(stock_data) - 100, 'end': len(stock_data)}  # 只显示最后100个数据点  # Copyright © JayBee黄\n", ")  # Jay<PERSON>ee黄授权使用", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "# 8. 参数优化", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 这个单元格是可选的，如果您想优化策略参数\n", "def optimize_ma_strategy(data, ma_fast_range, ma_slow_range):  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    \"\"\"优化MA策略参数\"\"\"  # 版权所有: JayBee黄\n", "    results = []  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    \n", "    for ma_fast in ma_fast_range:  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        for ma_slow in ma_slow_range:  # Jay<PERSON>ee黄独家内容\n", "            if ma_fast >= ma_slow:  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "                continue  # 快速MA周期不应大于或等于慢速MA周期  # JayBee黄 - 量化交易研究\n", "                \n", "            # 创建Cerebro引擎\n", "            cerebro = bt.<PERSON><PERSON><PERSON>(stdstats=False)  # Copyright © JayBee黄\n", "            \n", "            # 添加数据\n", "            if isinstance(data, pd.DataFrame):  # <PERSON><PERSON>ee黄量化策略\n", "                data_feed = df_to_btfeed(data)  # JayBee黄版权所有，未经授权禁止复制\n", "                cerebro.adddata(data_feed)  # JayBee黄量化模型\n", "            else:  # <PERSON><PERSON><PERSON>黄量化策略\n", "                cerebro.adddata(data)  # JayBee黄量化策略\n", "                \n", "            # 添加策略\n", "            cerebro.addstrategy(MAStrategy, ma_fast=ma_fast, ma_slow=ma_slow, printlog=False)  # JayBee黄 - 量化交易研究\n", "            \n", "            # 设置初始资金和佣金\n", "            cerebro.broker.setcash(100000.0)  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "            cerebro.broker.setcommission(commission=0.001)  # <PERSON><PERSON>ee黄独家内容\n", "            \n", "            # 添加分析器\n", "            cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')  # JayBee黄 - 量化交易研究\n", "            cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')  # JayBee黄独家内容\n", "            cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')  # JayBee黄版权所有，未经授权禁止复制\n", "            \n", "            # 执行回测\n", "            print(f\"测试参数: MA快速={ma_fast}, MA慢速={ma_slow}\")  # JayBee黄原创内容\n", "            res = cerebro.run()  # <PERSON><PERSON>ee黄量化模型\n", "            strat = res[0]  # <PERSON><PERSON><PERSON>黄原创内容\n", "            \n", "            # 收集结果\n", "            ret = strat.analyzers.returns.get_analysis()  # JayBee黄 - 量化交易研究\n", "            dd = strat.analyzers.drawdown.get_analysis()  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "            sharpe = strat.analyzers.sharpe.get_analysis()  # JayBee黄量化模型\n", "            \n", "            # 存储结果\n", "            results.append({  # Jay<PERSON>ee黄量化策略\n", "                'ma_fast': ma_fast,  # 本代码归JayBee黄所有\n", "                'ma_slow': ma_slow,  # Copyright © JayBee黄\n", "                'return': ret.get('rtot', 0) * 100,  # 总收益率(%)  # <PERSON><PERSON><PERSON>黄授权使用\n", "                'annual_return': ret.get('rnorm100', 0),  # 年化收益率(%)  # 本代码归JayBee黄所有\n", "                'max_drawdown': dd.get('max', {}).get('drawdown', 0),  # 最大回撤(%)  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "                'sharpe': sharpe.get('sharperatio', 0),  # 夏普比率  # <PERSON><PERSON>ee黄 - 量化交易研究\n", "                'final_value': cerebro.broker.getvalue()  # JayBee黄量化策略\n", "            })  # JayBee黄 - 量化交易研究\n", "            \n", "    # 转换为DataFrame并按总收益率排序\n", "    results_df = pd.DataFrame(results)  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    results_df = results_df.sort_values('return', ascending=False)  # <PERSON><PERSON>ee黄原创内容\n", "    \n", "    return results_df  # <PERSON><PERSON>ee黄量化模型\n", "\n", "# 示例使用\n", "# optimize_results = optimize_ma_strategy(\n", "#     data=pingan_data,\n", "#     ma_fast_range=[5, 10, 15, 20],\n", "#     ma_slow_range=[20, 30, 40, 50]\n", "# )\n", "# optimize_results.head()", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["测试参数: MA快速=5, MA慢速=20\n", "2022-12-30, (MA周期 快速/慢速) 5/20\n", "2022-12-30, 期末资金: 100000.59\n", "测试参数: MA快速=5, MA慢速=30\n", "2022-12-30, (MA周期 快速/慢速) 5/30\n", "2022-12-30, 期末资金: 100001.55\n", "测试参数: MA快速=5, MA慢速=40\n", "2022-12-30, (MA周期 快速/慢速) 5/40\n", "2022-12-30, 期末资金: 100002.62\n", "测试参数: MA快速=5, MA慢速=50\n", "2022-12-30, (MA周期 快速/慢速) 5/50\n", "2022-12-30, 期末资金: 99998.82\n", "测试参数: MA快速=10, MA慢速=20\n", "2022-12-30, (MA周期 快速/慢速) 10/20\n", "2022-12-30, 期末资金: 99998.30\n", "测试参数: MA快速=10, MA慢速=30\n", "2022-12-30, (MA周期 快速/慢速) 10/30\n", "2022-12-30, 期末资金: 99998.00\n", "测试参数: MA快速=10, MA慢速=40\n", "2022-12-30, (MA周期 快速/慢速) 10/40\n", "2022-12-30, 期末资金: 99999.15\n", "测试参数: MA快速=10, MA慢速=50\n", "2022-12-30, (MA周期 快速/慢速) 10/50\n", "2022-12-30, 期末资金: 100000.89\n", "测试参数: MA快速=15, MA慢速=20\n", "2022-12-30, (MA周期 快速/慢速) 15/20\n", "2022-12-30, 期末资金: 99994.15\n", "测试参数: MA快速=15, MA慢速=30\n", "2022-12-30, (MA周期 快速/慢速) 15/30\n", "2022-12-30, 期末资金: 99997.62\n", "测试参数: MA快速=15, MA慢速=40\n", "2022-12-30, (MA周期 快速/慢速) 15/40\n", "2022-12-30, 期末资金: 99999.58\n", "测试参数: MA快速=15, MA慢速=50\n", "2022-12-30, (MA周期 快速/慢速) 15/50\n", "2022-12-30, 期末资金: 99999.39\n", "测试参数: MA快速=20, MA慢速=30\n", "2022-12-30, (MA周期 快速/慢速) 20/30\n", "2022-12-30, 期末资金: 99996.02\n", "测试参数: MA快速=20, MA慢速=40\n", "2022-12-30, (MA周期 快速/慢速) 20/40\n", "2022-12-30, 期末资金: 99999.55\n", "测试参数: MA快速=20, MA慢速=50\n", "2022-12-30, (MA周期 快速/慢速) 20/50\n", "2022-12-30, 期末资金: 99992.44\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ma_fast</th>\n", "      <th>ma_slow</th>\n", "      <th>return</th>\n", "      <th>annual_return</th>\n", "      <th>max_drawdown</th>\n", "      <th>sharpe</th>\n", "      <th>final_value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5</td>\n", "      <td>40</td>\n", "      <td>0.002624</td>\n", "      <td>0.002733</td>\n", "      <td>0.005771</td>\n", "      <td>None</td>\n", "      <td>100002.62416</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5</td>\n", "      <td>30</td>\n", "      <td>0.001547</td>\n", "      <td>0.001611</td>\n", "      <td>0.004243</td>\n", "      <td>None</td>\n", "      <td>100001.54740</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>10</td>\n", "      <td>50</td>\n", "      <td>0.000893</td>\n", "      <td>0.000930</td>\n", "      <td>0.005831</td>\n", "      <td>None</td>\n", "      <td>100000.89321</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5</td>\n", "      <td>20</td>\n", "      <td>0.000587</td>\n", "      <td>0.000611</td>\n", "      <td>0.005539</td>\n", "      <td>None</td>\n", "      <td>100000.58683</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>15</td>\n", "      <td>40</td>\n", "      <td>-0.000417</td>\n", "      <td>-0.000434</td>\n", "      <td>0.006621</td>\n", "      <td>None</td>\n", "      <td>99999.58284</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    ma_fast  ma_slow    return  annual_return  max_drawdown sharpe  \\\n", "2         5       40  0.002624       0.002733      0.005771   None   \n", "1         5       30  0.001547       0.001611      0.004243   None   \n", "7        10       50  0.000893       0.000930      0.005831   None   \n", "0         5       20  0.000587       0.000611      0.005539   None   \n", "10       15       40 -0.000417      -0.000434      0.006621   None   \n", "\n", "     final_value  \n", "2   100002.62416  \n", "1   100001.54740  \n", "7   100000.89321  \n", "0   100000.58683  \n", "10   99999.58284  "]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "optimize_results = optimize_ma_strategy(  # 本代码归JayBee黄所有\n", "    data=pingan_data,  # <PERSON><PERSON>ee黄独家内容\n", "    ma_fast_range=[5, 10, 15, 20],  # <PERSON><PERSON><PERSON>黄量化策略\n", "    ma_slow_range=[20, 30, 40, 50]  # <PERSON><PERSON>ee黄量化模型\n", ")  # Jay<PERSON>ee黄量化模型\n", "optimize_results.head()  # <PERSON><PERSON>ee黄量化策略", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# JayBee黄版权所有，未经授权禁止复制\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "watermark": "JayBee黄版权所有，未经授权禁止复制", "watermark_version": "v3 - 每行防伪"}, "nbformat": 4, "nbformat_minor": 4}