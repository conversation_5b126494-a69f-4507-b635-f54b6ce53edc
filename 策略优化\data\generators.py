"""
数据生成器模块

提供各种数据生成功能，包括随机数据和模拟数据。
"""

import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

from ..config import get_config
from ..utils.logger import get_logger
from ..utils.exceptions import DataError, ValidationError


class BaseDataGenerator(ABC):
    """数据生成器基类"""
    
    def __init__(self, **kwargs):
        """
        初始化数据生成器
        
        Args:
            **kwargs: 生成器参数
        """
        self.logger = get_logger(f"data.{self.__class__.__name__}")
        self.config = get_config()
        self._validate_params(**kwargs)
    
    @abstractmethod
    def _validate_params(self, **kwargs) -> None:
        """验证参数"""
        pass
    
    @abstractmethod
    def generate(self, **kwargs) -> pd.DataFrame:
        """生成数据"""
        pass
    
    def _create_date_index(self, start_date: str, num_days: int) -> pd.DatetimeIndex:
        """
        创建交易日日期索引
        
        Args:
            start_date: 开始日期
            num_days: 天数
            
        Returns:
            日期索引
        """
        try:
            return pd.date_range(
                start=start_date, 
                periods=num_days, 
                freq='B'  # 工作日频率
            )
        except Exception as e:
            raise DataError(f"创建日期索引失败: {e}", data_type="date_index")


class RandomDataGenerator(BaseDataGenerator):
    """随机数据生成器"""
    
    def __init__(self, num_days: Optional[int] = None,
                 initial_price: Optional[float] = None,
                 drift: Optional[float] = None,
                 volatility: Optional[float] = None,
                 start_date: Optional[str] = None,
                 random_seed: Optional[int] = None):
        """
        初始化随机数据生成器

        Args:
            num_days: 生成天数
            initial_price: 初始价格
            drift: 漂移率
            volatility: 波动率
            start_date: 开始日期
            random_seed: 随机种子
        """
        # 先调用父类初始化以设置config
        super().__init__()

        # 从配置获取默认值
        config_data = self.config.get_section('data.random_data')

        self.num_days = num_days or config_data.get('num_days', 756)
        self.initial_price = initial_price or config_data.get('initial_price', 100.0)
        self.drift = drift or config_data.get('drift', 0.0001)
        self.volatility = volatility or config_data.get('volatility', 0.015)
        self.start_date = start_date or config_data.get('start_date', '2020-01-01')

        # 设置随机种子
        if random_seed is None:
            random_seed = self.config.get('system.random_seed', 42)
        np.random.seed(random_seed)

        # 验证参数
        self._validate_params(
            num_days=self.num_days,
            initial_price=self.initial_price,
            drift=self.drift,
            volatility=self.volatility,
            start_date=self.start_date
        )
    
    def _validate_params(self, **kwargs) -> None:
        """验证参数"""
        num_days = kwargs.get('num_days', self.num_days)
        initial_price = kwargs.get('initial_price', self.initial_price)
        drift = kwargs.get('drift', self.drift)
        volatility = kwargs.get('volatility', self.volatility)
        
        if not isinstance(num_days, int) or num_days <= 0:
            raise ValidationError("num_days必须是正整数", "num_days", num_days, "positive integer")
        
        if not isinstance(initial_price, (int, float)) or initial_price <= 0:
            raise ValidationError("initial_price必须是正数", "initial_price", initial_price, "positive number")
        
        if not isinstance(drift, (int, float)):
            raise ValidationError("drift必须是数字", "drift", drift, "number")
        
        if not isinstance(volatility, (int, float)) or volatility <= 0:
            raise ValidationError("volatility必须是正数", "volatility", volatility, "positive number")
    
    def generate(self, **kwargs) -> pd.DataFrame:
        """
        生成随机价格数据
        
        Args:
            **kwargs: 覆盖默认参数
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        try:
            # 更新参数
            num_days = kwargs.get('num_days', self.num_days)
            initial_price = kwargs.get('initial_price', self.initial_price)
            drift = kwargs.get('drift', self.drift)
            volatility = kwargs.get('volatility', self.volatility)
            start_date = kwargs.get('start_date', self.start_date)
            
            self.logger.info(f"开始生成随机数据: {num_days}天, 初始价格: {initial_price}")
            
            # 生成日对数收益率
            log_returns = np.random.normal(drift, volatility, num_days)
            
            # 计算收盘价路径
            close_prices = np.exp(np.cumsum(log_returns)) * initial_price
            
            # 生成OHLV数据
            df = self._generate_ohlcv_from_close(close_prices, start_date)
            
            self.logger.info(f"随机数据生成完成: {len(df)}条记录")
            return df
            
        except Exception as e:
            self.logger.error(f"生成随机数据失败: {e}")
            raise DataError(f"生成随机数据失败: {e}", data_source="random")
    
    def _generate_ohlcv_from_close(self, close_prices: np.ndarray, start_date: str) -> pd.DataFrame:
        """
        从收盘价生成OHLCV数据
        
        Args:
            close_prices: 收盘价数组
            start_date: 开始日期
            
        Returns:
            OHLCV数据框
        """
        num_days = len(close_prices)
        dates = self._create_date_index(start_date, num_days)
        
        # 生成开盘价（前一日收盘价加上小幅随机波动）
        open_prices = np.zeros(num_days)
        open_prices[0] = close_prices[0] * (1 + np.random.normal(0, 0.005))
        for i in range(1, num_days):
            open_prices[i] = close_prices[i-1] * (1 + np.random.normal(0, 0.005))
        
        # 生成最高价和最低价
        high_prices = np.zeros(num_days)
        low_prices = np.zeros(num_days)
        
        for i in range(num_days):
            # 最高价是开盘价和收盘价的最大值加上随机波动
            base_high = max(open_prices[i], close_prices[i])
            high_prices[i] = base_high * (1 + abs(np.random.normal(0, 0.01)))
            
            # 最低价是开盘价和收盘价的最小值减去随机波动
            base_low = min(open_prices[i], close_prices[i])
            low_prices[i] = base_low * (1 - abs(np.random.normal(0, 0.01)))
        
        # 生成成交量（基于价格波动）
        price_changes = np.abs(np.diff(close_prices, prepend=close_prices[0]))
        base_volume = 1000000  # 基础成交量
        volumes = base_volume * (1 + price_changes / close_prices * 10) * np.random.lognormal(0, 0.5, num_days)
        volumes = volumes.astype(int)
        
        # 创建DataFrame
        df = pd.DataFrame({
            'open': open_prices,
            'high': high_prices,
            'low': low_prices,
            'close': close_prices,
            'volume': volumes
        }, index=dates)
        
        return df


class GeometricBrownianMotionGenerator(BaseDataGenerator):
    """几何布朗运动数据生成器"""
    
    def __init__(self, num_days: Optional[int] = None,
                 initial_price: Optional[float] = None,
                 mu: Optional[float] = None,
                 sigma: Optional[float] = None,
                 start_date: Optional[str] = None,
                 random_seed: Optional[int] = None):
        """
        初始化几何布朗运动生成器
        
        Args:
            num_days: 生成天数
            initial_price: 初始价格
            mu: 年化漂移率
            sigma: 年化波动率
            start_date: 开始日期
            random_seed: 随机种子
        """
        self.num_days = num_days or 756
        self.initial_price = initial_price or 100.0
        self.mu = mu or 0.05  # 年化5%收益率
        self.sigma = sigma or 0.2  # 年化20%波动率
        self.start_date = start_date or '2020-01-01'
        
        if random_seed is not None:
            np.random.seed(random_seed)
        
        super().__init__(
            num_days=self.num_days,
            initial_price=self.initial_price,
            mu=self.mu,
            sigma=self.sigma,
            start_date=self.start_date
        )
    
    def _validate_params(self, **kwargs) -> None:
        """验证参数"""
        # 基本验证逻辑
        pass
    
    def generate(self, **kwargs) -> pd.DataFrame:
        """
        使用几何布朗运动生成价格数据
        
        Returns:
            价格数据框
        """
        try:
            num_days = kwargs.get('num_days', self.num_days)
            initial_price = kwargs.get('initial_price', self.initial_price)
            mu = kwargs.get('mu', self.mu)
            sigma = kwargs.get('sigma', self.sigma)
            start_date = kwargs.get('start_date', self.start_date)
            
            self.logger.info(f"使用几何布朗运动生成数据: {num_days}天")
            
            # 时间步长（假设252个交易日）
            dt = 1/252
            
            # 生成随机数
            random_shocks = np.random.normal(0, 1, num_days)
            
            # 计算价格路径
            prices = np.zeros(num_days)
            prices[0] = initial_price
            
            for i in range(1, num_days):
                prices[i] = prices[i-1] * np.exp(
                    (mu - 0.5 * sigma**2) * dt + sigma * np.sqrt(dt) * random_shocks[i]
                )
            
            # 创建日期索引
            dates = self._create_date_index(start_date, num_days)
            
            # 创建DataFrame（简化版，只有收盘价）
            df = pd.DataFrame({
                'close': prices
            }, index=dates)
            
            self.logger.info(f"几何布朗运动数据生成完成: {len(df)}条记录")
            return df
            
        except Exception as e:
            self.logger.error(f"生成几何布朗运动数据失败: {e}")
            raise DataError(f"生成几何布朗运动数据失败: {e}", data_source="gbm")
