{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Day 2：基于交易量的量化指标 - 指标计算与可视化\n", "\n", "本notebook主要介绍如何计算常见的基于交易量的指标，并进行可视化分析。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib.gridspec as gridspec\n", "import matplotlib.dates as mdates\n", "import seaborn as sns\n", "import warnings\n", "\n", "# 忽略警告信息\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置绘图风格\n", "plt.style.use('ggplot')\n", "%matplotlib inline\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 加载数据\n", "\n", "从前一个notebook保存的CSV文件或直接从Tushare获取数据。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 从CSV文件加载数据\n", "def load_data_from_csv(file_path):\n", "    df = pd.read_csv(file_path, index_col=0)\n", "    df.index = pd.to_datetime(df.index)\n", "    return df\n", "\n", "# 尝试从CSV加载数据，如果文件不存在，则从Tushare获取\n", "import os\n", "\n", "stock_names = ['平安银行', '贵州茅台', '中国平安']\n", "stock_codes = ['000001.SZ', '600519.SH', '601318.SH']\n", "stock_data = {}\n", "\n", "for i, name in enumerate(stock_names):\n", "    file_path = f'data/{name}_data.csv'\n", "    if os.path.exists(file_path):\n", "        stock_data[name] = load_data_from_csv(file_path)\n", "        print(f\"从CSV文件加载{name}的数据\")\n", "    else:\n", "        import tushare as ts\n", "        ts.set_token('YOUR_TUSHARE_TOKEN')  # 替换为您的Token\n", "        pro = ts.pro_api()\n", "        \n", "        # 获取数据\n", "        df = pro.daily(ts_code=stock_codes[i], start_date='20220101', end_date='20230101')\n", "        df = df.sort_values('trade_date')\n", "        df['trade_date'] = pd.to_datetime(df['trade_date'])\n", "        df.set_index('trade_date', inplace=True)\n", "        \n", "        stock_data[name] = df\n", "        print(f\"从Tushare获取{name}的数据\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 查看数据\n", "stock_data['平安银行'].head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 定义交易量指标计算函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_OBV(data):\n", "    \"\"\"\n", "    计算On Balance Volume (OBV) / 累积能量线\n", "    \n", "    参数:\n", "    data: DataFrame, 包含'close'和'vol'列的DataFrame\n", "    \n", "    返回:\n", "    带有OBV列的DataFrame\n", "    \"\"\"\n", "    data = data.copy()  # 创建副本以避免修改原始数据\n", "    obv = [0]  # 初始OBV值\n", "    \n", "    for i in range(1, len(data)):\n", "        if data['close'].iloc[i] > data['close'].iloc[i-1]:\n", "            obv.append(obv[-1] + data['vol'].iloc[i])\n", "        elif data['close'].iloc[i] < data['close'].iloc[i-1]:\n", "            obv.append(obv[-1] - data['vol'].iloc[i])\n", "        else:\n", "            obv.append(obv[-1])\n", "    \n", "    data['OBV'] = obv\n", "    return data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_volume_ma(data, windows=[5, 10, 20]):\n", "    \"\"\"\n", "    计算成交量移动平均线\n", "    \n", "    参数:\n", "    data: DataFrame, 包含'vol'列的DataFrame\n", "    windows: list, 移动平均的窗口大小列表，默认为[5, 10, 20]\n", "    \n", "    返回:\n", "    带有成交量MA列的DataFrame\n", "    \"\"\"\n", "    data = data.copy()  # 创建副本以避免修改原始数据\n", "    \n", "    for window in windows:\n", "        data[f'vol_ma{window}'] = data['vol'].rolling(window=window).mean()\n", "    \n", "    return data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_volume_ratio(data, window=5):\n", "    \"\"\"\n", "    计算量比\n", "    \n", "    参数:\n", "    data: DataFrame, 包含'vol'列的DataFrame\n", "    window: int, 计算平均成交量的窗口大小，默认为5\n", "    \n", "    返回:\n", "    带有量比列的DataFrame\n", "    \"\"\"\n", "    data = data.copy()  # 创建副本以避免修改原始数据\n", "    \n", "    # 计算过去window天的平均成交量\n", "    data['vol_ma'] = data['vol'].rolling(window=window).mean().shift(1)\n", "    # 计算量比\n", "    data['vol_ratio'] = data['vol'] / data['vol_ma']\n", "    \n", "    return data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_mfi(data, period=14):\n", "    \"\"\"\n", "    计算Money Flow Index (MFI) / 资金流量指标\n", "    \n", "    参数:\n", "    data: DataFrame, 包含'high', 'low', 'close'和'vol'列的DataFrame\n", "    period: int, 计算周期，默认为14\n", "    \n", "    返回:\n", "    带有MFI列的DataFrame\n", "    \"\"\"\n", "    data = data.copy()  # 创建副本以避免修改原始数据\n", "    \n", "    # 1. 计算典型价格 (TP)\n", "    data['tp'] = (data['high'] + data['low'] + data['close']) / 3\n", "    \n", "    # 2. 计算资金流 (Money Flow)\n", "    data['money_flow'] = data['tp'] * data['vol']\n", "    \n", "    # 3. 判断正向和负向资金流\n", "    data['tp_diff'] = data['tp'].diff()\n", "    data['positive_flow'] = np.where(data['tp_diff'] > 0, data['money_flow'], 0)\n", "    data['negative_flow'] = np.where(data['tp_diff'] < 0, data['money_flow'], 0)\n", "    \n", "    # 4. 计算period周期内的资金流\n", "    data['positive_flow_sum'] = data['positive_flow'].rolling(window=period).sum()\n", "    data['negative_flow_sum'] = data['negative_flow'].rolling(window=period).sum()\n", "    \n", "    # 5. 计算资金流比率和MFI\n", "    data['money_ratio'] = data['positive_flow_sum'] / data['negative_flow_sum']\n", "    data['MFI'] = 100 - (100 / (1 + data['money_ratio']))\n", "    \n", "    # 删除中间计算列\n", "    data = data.drop(['tp', 'money_flow', 'tp_diff', 'positive_flow', 'negative_flow', \n", "                       'positive_flow_sum', 'negative_flow_sum', 'money_ratio'], axis=1)\n", "    \n", "    return data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_price_ma(data, windows=[5, 10, 20, 60]):\n", "    \"\"\"\n", "    计算价格移动平均线\n", "    \n", "    参数:\n", "    data: DataFrame, 包含'close'列的DataFrame\n", "    windows: list, 移动平均的窗口大小列表，默认为[5, 10, 20, 60]\n", "    \n", "    返回:\n", "    带有价格MA列的DataFrame\n", "    \"\"\"\n", "    data = data.copy()  # 创建副本以避免修改原始数据\n", "    \n", "    for window in windows:\n", "        data[f'ma{window}'] = data['close'].rolling(window=window).mean()\n", "    \n", "    return data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 计算指标\n", "\n", "以平安银行为例，计算并可视化各种交易量指标。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 选择平安银行的数据进行分析\n", "pingan_data = stock_data['平安银行']\n", "\n", "# 计算所有指标\n", "pingan_data = calculate_OBV(pingan_data)\n", "pingan_data = calculate_volume_ma(pingan_data)\n", "pingan_data = calculate_volume_ratio(pingan_data)\n", "pingan_data = calculate_mfi(pingan_data)\n", "pingan_data = calculate_price_ma(pingan_data)\n", "\n", "# 查看结果\n", "pingan_data.tail()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 可视化分析\n", "\n", "### 5.1 OBV与价格"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建OBV与价格的对比图\n", "fig, axes = plt.subplots(2, 1, figsize=(14, 10), sharex=True, gridspec_kw={'height_ratios': [2, 1]})\n", "\n", "# 绘制价格图\n", "axes[0].plot(pingan_data.index, pingan_data['close'], label='收盘价', color='blue')\n", "axes[0].set_title('平安银行 - 收盘价走势')\n", "axes[0].set_ylabel('价格')\n", "axes[0].legend(loc='upper left')\n", "axes[0].grid(True)\n", "\n", "# 绘制OBV图\n", "axes[1].plot(pingan_data.index, pingan_data['OBV'], label='OBV', color='orange')\n", "axes[1].set_title('平安银行 - OBV指标')\n", "axes[1].set_xlabel('日期')\n", "axes[1].set_ylabel('OBV值')\n", "axes[1].legend(loc='upper left')\n", "axes[1].grid(True)\n", "\n", "# 添加日期格式化\n", "date_format = mdates.DateFormatter('%Y-%m')\n", "axes[1].xaxis.set_major_formatter(date_format)\n", "fig.autofmt_xdate()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 成交量与成交量MA"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建成交量与成交量MA的对比图\n", "fig, axes = plt.subplots(2, 1, figsize=(14, 10), sharex=True, gridspec_kw={'height_ratios': [2, 1]})\n", "\n", "# 绘制价格图\n", "axes[0].plot(pingan_data.index, pingan_data['close'], label='收盘价', color='blue')\n", "axes[0].set_title('平安银行 - 收盘价走势')\n", "axes[0].set_ylabel('价格')\n", "axes[0].legend(loc='upper left')\n", "axes[0].grid(True)\n", "\n", "# 绘制成交量和成交量MA\n", "axes[1].bar(pingan_data.index, pingan_data['vol'], label='成交量', alpha=0.3, color='gray')\n", "axes[1].plot(pingan_data.index, pingan_data['vol_ma5'], label='5日成交量MA', color='red')\n", "axes[1].plot(pingan_data.index, pingan_data['vol_ma10'], label='10日成交量MA', color='blue')\n", "axes[1].plot(pingan_data.index, pingan_data['vol_ma20'], label='20日成交量MA', color='green')\n", "axes[1].set_title('平安银行 - 成交量与成交量MA')\n", "axes[1].set_xlabel('日期')\n", "axes[1].set_ylabel('成交量')\n", "axes[1].legend(loc='upper left')\n", "axes[1].grid(True)\n", "\n", "# 添加日期格式化\n", "date_format = mdates.DateFormatter('%Y-%m')\n", "axes[1].xaxis.set_major_formatter(date_format)\n", "fig.autofmt_xdate()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.3 量比"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建量比图\n", "fig, axes = plt.subplots(2, 1, figsize=(14, 10), sharex=True, gridspec_kw={'height_ratios': [2, 1]})\n", "\n", "# 绘制价格图\n", "axes[0].plot(pingan_data.index, pingan_data['close'], label='收盘价', color='blue')\n", "axes[0].set_title('平安银行 - 收盘价走势')\n", "axes[0].set_ylabel('价格')\n", "axes[0].legend(loc='upper left')\n", "axes[0].grid(True)\n", "\n", "# 绘制量比图\n", "axes[1].plot(pingan_data.index, pingan_data['vol_ratio'], label='量比', color='purple')\n", "axes[1].axhline(y=1, color='r', linestyle='--', alpha=0.5)  # 添加参考线，量比=1\n", "axes[1].axhline(y=2, color='g', linestyle='--', alpha=0.5)  # 添加参考线，量比=2\n", "axes[1].axhline(y=3, color='b', linestyle='--', alpha=0.5)  # 添加参考线，量比=3\n", "axes[1].set_title('平安银行 - 量比 (相对于5日平均成交量)')\n", "axes[1].set_xlabel('日期')\n", "axes[1].set_ylabel('量比')\n", "axes[1].legend(loc='upper left')\n", "axes[1].grid(True)\n", "\n", "# 添加日期格式化\n", "date_format = mdates.DateFormatter('%Y-%m')\n", "axes[1].xaxis.set_major_formatter(date_format)\n", "fig.autofmt_xdate()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.4 MFI与价格"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建MFI与价格的对比图\n", "fig, axes = plt.subplots(2, 1, figsize=(14, 10), sharex=True, gridspec_kw={'height_ratios': [2, 1]})\n", "\n", "# 绘制价格图\n", "axes[0].plot(pingan_data.index, pingan_data['close'], label='收盘价', color='blue')\n", "axes[0].set_title('平安银行 - 收盘价走势')\n", "axes[0].set_ylabel('价格')\n", "axes[0].legend(loc='upper left')\n", "axes[0].grid(True)\n", "\n", "# 绘制MFI图\n", "axes[1].plot(pingan_data.index, pingan_data['MFI'], label='MFI', color='green')\n", "axes[1].axhline(y=20, color='r', linestyle='--', alpha=0.5)  # 添加超卖线\n", "axes[1].axhline(y=80, color='r', linestyle='--', alpha=0.5)  # 添加超买线\n", "axes[1].set_title('平安银行 - MFI指标')\n", "axes[1].set_xlabel('日期')\n", "axes[1].set_ylabel('MFI值')\n", "axes[1].set_ylim(0, 100)  # 设置y轴范围\n", "axes[1].legend(loc='upper left')\n", "axes[1].grid(True)\n", "\n", "# 添加日期格式化\n", "date_format = mdates.DateFormatter('%Y-%m')\n", "axes[1].xaxis.set_major_formatter(date_format)\n", "fig.autofmt_xdate()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.5 价格MA与成交量"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建价格MA与成交量的对比图\n", "fig, axes = plt.subplots(2, 1, figsize=(14, 10), sharex=True, gridspec_kw={'height_ratios': [2, 1]})\n", "\n", "# 绘制价格图与价格MA\n", "axes[0].plot(pingan_data.index, pingan_data['close'], label='收盘价', color='black', alpha=0.5)\n", "axes[0].plot(pingan_data.index, pingan_data['ma5'], label='5日MA', color='red')\n", "axes[0].plot(pingan_data.index, pingan_data['ma10'], label='10日MA', color='blue')\n", "axes[0].plot(pingan_data.index, pingan_data['ma20'], label='20日MA', color='green')\n", "axes[0].plot(pingan_data.index, pingan_data['ma60'], label='60日MA', color='purple')\n", "axes[0].set_title('平安银行 - 价格与价格MA')\n", "axes[0].set_ylabel('价格')\n", "axes[0].legend(loc='upper left')\n", "axes[0].grid(True)\n", "\n", "# 绘制成交量图\n", "axes[1].bar(pingan_data.index, pingan_data['vol'], label='成交量', alpha=0.3, color='gray')\n", "axes[1].set_title('平安银行 - 成交量')\n", "axes[1].set_xlabel('日期')\n", "axes[1].set_ylabel('成交量')\n", "axes[1].legend(loc='upper left')\n", "axes[1].grid(True)\n", "\n", "# 添加日期格式化\n", "date_format = mdates.DateFormatter('%Y-%m')\n", "axes[1].xaxis.set_major_formatter(date_format)\n", "fig.autofmt_xdate()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 量价配合分析\n", "\n", "我们来分析一下平安银行的量价配合情况，寻找一些典型的量价关系模式。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 计算价格变化率和成交量变化率\n", "pingan_data['price_change'] = pingan_data['close'].pct_change() * 100  # 百分比变化\n", "pingan_data['vol_change'] = pingan_data['vol'].pct_change() * 100  # 百分比变化\n", "\n", "# 创建量价配合的分类\n", "pingan_data['price_up'] = pingan_data['price_change'] > 0\n", "pingan_data['vol_up'] = pingan_data['vol_change'] > 0\n", "\n", "# 定义量价配合类型\n", "conditions = [\n", "    (pingan_data['price_up'] & pingan_data['vol_up']),  # 价涨量增\n", "    (pingan_data['price_up'] & ~pingan_data['vol_up']),  # 价涨量减\n", "    (~pingan_data['price_up'] & pingan_data['vol_up']),  # 价跌量增\n", "    (~pingan_data['price_up'] & ~pingan_data['vol_up']),  # 价跌量减\n", "]\n", "choices = ['价涨量增', '价涨量减', '价跌量增', '价跌量减']\n", "pingan_data['vol_price_pattern'] = np.select(conditions, choices, default=np.nan)\n", "\n", "# 查看近期的量价配合情况\n", "pingan_data[['close', 'vol', 'price_change', 'vol_change', 'vol_price_pattern']].tail(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 统计量价配合类型的频率\n", "pattern_counts = pingan_data['vol_price_pattern'].value_counts()\n", "print(\"量价配合类型统计：\")\n", "print(pattern_counts)\n", "\n", "# 可视化量价配合类型的频率\n", "plt.figure(figsize=(10, 6))\n", "pattern_counts.plot(kind='bar', color=['green', 'orange', 'red', 'gray'])\n", "plt.title('平安银行 - 量价配合类型统计')\n", "plt.xlabel('量价配合类型')\n", "plt.ylabel('频率')\n", "plt.grid(True, axis='y')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析不同量价配合类型下的平均收益率\n", "next_day_returns = pingan_data['close'].pct_change().shift(-1) * 100  # 下一日收益率\n", "pingan_data['next_day_return'] = next_day_returns\n", "\n", "# 计算每种量价配合类型的平均下一日收益率\n", "pattern_returns = pingan_data.groupby('vol_price_pattern')['next_day_return'].mean()\n", "print(\"各量价配合类型的平均下一日收益率（%）：\")\n", "print(pattern_returns)\n", "\n", "# 可视化\n", "plt.figure(figsize=(10, 6))\n", "pattern_returns.plot(kind='bar', color=['green', 'orange', 'red', 'gray'])\n", "plt.title('平安银行 - 不同量价配合类型的平均下一日收益率')\n", "plt.xlabel('量价配合类型')\n", "plt.ylabel('平均下一日收益率（%）')\n", "plt.grid(True, axis='y')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. OBV背离分析\n", "\n", "OBV背离是一种常见的交易信号，我们可以尝试识别OBV与价格的背离情况。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义一个简单的背离检测函数\n", "def detect_divergence(data, price_col='close', obv_col='OBV', window=20):\n", "    \"\"\"\n", "    检测价格与OBV的背离\n", "    \n", "    参数:\n", "    data: DataFrame\n", "    price_col: str, 价格列名\n", "    obv_col: str, OBV列名\n", "    window: int, 检测窗口大小\n", "    \n", "    返回:\n", "    带有背离标记的DataFrame\n", "    \"\"\"\n", "    data = data.copy()\n", "    data['price_high'] = data[price_col].rolling(window=window).apply(lambda x: x.iloc[-1] > x.max())\n", "    data['price_low'] = data[price_col].rolling(window=window).apply(lambda x: x.iloc[-1] < x.min())\n", "    data['obv_high'] = data[obv_col].rolling(window=window).apply(lambda x: x.iloc[-1] > x.max())\n", "    data['obv_low'] = data[obv_col].rolling(window=window).apply(lambda x: x.iloc[-1] < x.min())\n", "    \n", "    # 看跌背离：价格创新高，但OBV未创新高\n", "    data['bearish_divergence'] = data['price_high'] & ~data['obv_high']\n", "    \n", "    # 看涨背离：价格创新低，但OBV未创新低\n", "    data['bullish_divergence'] = data['price_low'] & ~data['obv_low']\n", "    \n", "    return data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 尝试检测背离\n", "pingan_data = detect_divergence(pingan_data)\n", "\n", "# 查看背离情况\n", "divergence_dates = pingan_data[(pingan_data['bearish_divergence'] | pingan_data['bullish_divergence'])].index\n", "print(f\"检测到的背离日期数量: {len(divergence_dates)}\")\n", "\n", "# 查看背离日期\n", "if len(divergence_dates) > 0:\n", "    for date in divergence_dates:\n", "        if pingan_data.loc[date, 'bearish_divergence']:\n", "            print(f\"{date.date()}: 看跌背离\")\n", "        elif pingan_data.loc[date, 'bullish_divergence']:\n", "            print(f\"{date.date()}: 看涨背离\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 可视化背离\n", "# 注意：这个简单的背离检测算法可能不够精确，仅作为示例\n", "\n", "# 如果检测到背离，则绘制图表\n", "if len(divergence_dates) > 0:\n", "    fig, axes = plt.subplots(2, 1, figsize=(14, 10), sharex=True, gridspec_kw={'height_ratios': [2, 1]})\n", "    \n", "    # 绘制价格图\n", "    axes[0].plot(pingan_data.index, pingan_data['close'], label='收盘价', color='blue')\n", "    \n", "    # 标记看跌背离\n", "    bearish_dates = pingan_data[pingan_data['bearish_divergence']].index\n", "    if len(bearish_dates) > 0:\n", "        axes[0].scatter(bearish_dates, pingan_data.loc[bearish_dates, 'close'], \n", "                        color='red', marker='v', s=100, label='看跌背离')\n", "    \n", "    # 标记看涨背离\n", "    bullish_dates = pingan_data[pingan_data['bullish_divergence']].index\n", "    if len(bullish_dates) > 0:\n", "        axes[0].scatter(bullish_dates, pingan_data.loc[bullish_dates, 'close'], \n", "                        color='green', marker='^', s=100, label='看涨背离')\n", "    \n", "    axes[0].set_title('平安银行 - 收盘价走势与OBV背离')\n", "    axes[0].set_ylabel('价格')\n", "    axes[0].legend(loc='upper left')\n", "    axes[0].grid(True)\n", "    \n", "    # 绘制OBV图\n", "    axes[1].plot(pingan_data.index, pingan_data['OBV'], label='OBV', color='orange')\n", "    axes[1].set_title('平安银行 - OBV指标')\n", "    axes[1].set_xlabel('日期')\n", "    axes[1].set_ylabel('OBV值')\n", "    axes[1].legend(loc='upper left')\n", "    axes[1].grid(True)\n", "    \n", "    # 添加日期格式化\n", "    date_format = mdates.DateFormatter('%Y-%m')\n", "    axes[1].xaxis.set_major_formatter(date_format)\n", "    fig.autofmt_xdate()\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 总结\n", "\n", "在本notebook中，我们完成了以下任务：\n", "\n", "1. 编写了各种交易量指标的计算函数，包括OBV、成交量MA、量比和MFI\n", "2. 对平安银行的历史数据计算了这些指标\n", "3. 通过可视化分析了价格与各种交易量指标的关系\n", "4. 分析了量价配合情况，并统计了不同量价配合类型下的平均收益率\n", "5. 尝试进行了OBV背离的检测和分析\n", "\n", "这些分析为下一步的量价结合策略开发奠定了基础。通过这些指标，我们可以更好地理解市场的资金流向和交易动力，从而做出更明智的交易决策。"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}