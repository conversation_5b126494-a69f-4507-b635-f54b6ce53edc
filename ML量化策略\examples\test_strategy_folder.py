#!/usr/bin/env python3
"""
测试策略文件夹功能

演示如何使用存放在 generated_strategies 文件夹中的策略。
"""

import sys
import os
import pandas as pd

# 添加策略文件夹到Python路径
sys.path.append('generated_strategies')

from data_generator import RandomDataGenerator


def test_strategy_folder():
    """测试策略文件夹功能"""
    print("🎯 测试策略文件夹功能")
    print("="*50)
    
    # 1. 检查策略文件夹
    print("1. 检查策略文件夹结构...")
    strategies_dir = "generated_strategies"
    
    if not os.path.exists(strategies_dir):
        print("❌ 策略文件夹不存在")
        return
    
    print(f"✅ 策略文件夹存在: {strategies_dir}")
    
    # 列出文件夹内容
    files = os.listdir(strategies_dir)
    print(f"   文件夹内容:")
    for file in files:
        file_path = os.path.join(strategies_dir, file)
        if os.path.isfile(file_path):
            size = os.path.getsize(file_path)
            print(f"   - {file} ({size/1024:.1f}KB)")
    
    # 2. 导入并测试策略
    print("\n2. 导入并测试策略...")
    
    try:
        # 导入简化的双均线策略
        from simple_dual_ma_strategy import DualMAStrategy
        
        # 创建策略实例
        strategy = DualMAStrategy()
        print(f"✅ 成功导入策略: {strategy.name}")
        
        # 显示策略信息
        info = strategy.get_strategy_info()
        print(f"   策略类型: {info['type']}")
        print(f"   优化方法: {info['optimization_method']}")
        print(f"   参数: {info['parameters']}")
        
    except ImportError as e:
        print(f"❌ 导入策略失败: {e}")
        return
    
    # 3. 生成测试数据
    print("\n3. 生成测试数据...")
    generator = RandomDataGenerator()
    test_data = generator.generate(days=100, random_seed=456)
    print(f"   测试数据: {len(test_data)} 天")
    
    # 4. 使用策略
    print("\n4. 使用策略生成信号...")
    
    # 计算技术指标
    data_with_indicators = strategy.calculate_indicators(test_data)
    print(f"   添加指标: MA{strategy.short_window}, MA{strategy.long_window}")
    
    # 生成交易信号
    signals = strategy.generate_signals(test_data)
    
    buy_count = (signals == 1).sum()
    sell_count = (signals == -1).sum()
    hold_count = (signals == 0).sum()
    
    print(f"   买入信号: {buy_count} 个")
    print(f"   卖出信号: {sell_count} 个")
    print(f"   持有信号: {hold_count} 个")
    
    # 5. 显示策略摘要
    print("\n5. 策略回测摘要:")
    print(strategy.backtest_summary())
    
    # 6. 保存结果
    print("6. 保存测试结果...")
    result_df = pd.DataFrame({
        'close': test_data['close'],
        'MA_short': data_with_indicators['MA_short'],
        'MA_long': data_with_indicators['MA_long'],
        'signal': signals
    })
    
    result_df.to_csv('strategy_folder_test_result.csv')
    print("   ✅ 结果已保存到 strategy_folder_test_result.csv")
    
    # 7. 展示文件夹优势
    print(f"\n{'='*50}")
    print("🎉 策略文件夹功能测试完成!")
    print("\n✅ 策略文件夹的优势:")
    print("   📁 统一管理 - 所有策略文件集中存放")
    print("   🔍 易于查找 - 清晰的文件组织结构")
    print("   📦 独立包 - 可作为Python包导入")
    print("   🔄 可重用 - 策略文件可在任何项目中使用")
    print("   📊 完整信息 - 包含参数、性能和使用说明")
    print("   🛠️ 易于维护 - 支持版本管理和更新")
    
    print(f"\n💡 使用方法:")
    print(f"   1. 将 generated_strategies 文件夹复制到目标项目")
    print(f"   2. 添加到Python路径: sys.path.append('generated_strategies')")
    print(f"   3. 导入策略: from simple_dual_ma_strategy import DualMAStrategy")
    print(f"   4. 创建实例: strategy = DualMAStrategy()")
    print(f"   5. 使用策略: signals = strategy.generate_signals(data)")


def show_folder_benefits():
    """展示文件夹管理的好处"""
    print(f"\n📋 策略文件夹管理的好处:")
    print("="*40)
    
    benefits = [
        "🗂️ 组织性 - 所有ML生成的策略统一存放",
        "🔍 可发现性 - 容易找到和比较不同策略",
        "📦 模块化 - 每个策略都是独立的Python模块",
        "🔄 可移植性 - 整个文件夹可以复制到其他项目",
        "📊 可追溯性 - 保留完整的优化历史和性能数据",
        "🛠️ 可维护性 - 支持策略的更新和版本管理",
        "👥 可分享性 - 便于团队协作和策略分享",
        "🔒 安全性 - 避免策略文件散落各处丢失"
    ]
    
    for benefit in benefits:
        print(f"   {benefit}")
    
    print(f"\n📁 推荐的文件夹结构:")
    print("   generated_strategies/")
    print("   ├── __init__.py              # Python包初始化")
    print("   ├── simple_dual_ma_strategy.py  # 具体策略文件")
    print("   ├── rsi_strategy.py          # 其他策略文件")
    print("   ├── strategies_metadata.json # 策略元数据")
    print("   ├── strategy_index.py        # 策略索引")
    print("   └── README.md               # 使用说明")


if __name__ == "__main__":
    test_strategy_folder()
    show_folder_benefits()
