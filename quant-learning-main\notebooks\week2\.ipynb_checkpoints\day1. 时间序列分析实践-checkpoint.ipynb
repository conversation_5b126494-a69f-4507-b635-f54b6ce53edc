{"cells": [{"cell_type": "code", "execution_count": 1, "id": "943c6a65-4309-41af-8692-5ec5a847e61e", "metadata": {"scrolled": true}, "outputs": [], "source": ["# # 如果你本地没有这些包，需要先安装\n", "# !pip install yfinance\n", "# !pip install statsmodels\n", "# !pip install matplotlib"]}, {"cell_type": "markdown", "id": "982b207c-b281-4ec5-9631-28e1941af113", "metadata": {}, "source": ["# 一、安装并导入所需库"]}, {"cell_type": "code", "execution_count": 2, "id": "53066655-2a04-4160-8970-942d0b2d6a73", "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "attempted relative import with no known parent package", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[0;32mIn[2], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdata_processing\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m load_data_av\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mstatsmodels\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtsa\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mstattools\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m adfuller  \u001b[38;5;66;03m# ADF检验\u001b[39;00m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mstatsmodels\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mgraphics\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtsaplots\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m plot_acf, plot_pacf  \u001b[38;5;66;03m# ACF和PACF图\u001b[39;00m\n", "\u001b[0;31mImportError\u001b[0m: attempted relative import with no known parent package"]}], "source": ["from ..data_processing import load_data_av\n", "from statsmodels.tsa.stattools import adfuller  # ADF检验\n", "from statsmodels.graphics.tsaplots import plot_acf, plot_pacf  # ACF和PACF图\n", "from statsmodels.tsa.arima.model import ARIMA  # ARIMA模型\n", "from statsmodels.stats.diagnostic import acorr_ljungbox  # Ljung-Box检验, 检验白噪声\n", "\n", "# 绘图风格（可选）\n", "plt.style.use('seaborn-v0_8-bright')\n"]}, {"cell_type": "markdown", "id": "02eaf80b-6a2a-4b75-a18f-32f7dba5d2e4", "metadata": {}, "source": ["# 二、下载 TSLA 日级数据（示例：2024年）"]}, {"cell_type": "code", "execution_count": 8, "id": "3c169d08-eaf4-40e8-8666-4dc047105c40", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["YF.download() has changed argument auto_adjust default to True\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[*********************100%***********************]  1 of 1 completed"]}, {"name": "stdout", "output_type": "stream", "text": ["Price            Close        High         Low        Open     Volume\n", "Ticker            TSLA        TSLA        TSLA        TSLA       TSLA\n", "Date                                                                 \n", "2024-01-02  248.419998  251.250000  244.410004  250.080002  104654200\n", "2024-01-03  238.449997  245.679993  236.320007  244.979996  121082600\n", "2024-01-04  237.929993  242.699997  237.729996  239.250000  102629300\n", "2024-01-05  237.490005  240.119995  234.899994  236.860001   92379400\n", "2024-01-08  240.449997  241.250000  235.300003  236.139999   85166600\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# 定义下载区间\n", "start_date = \"2024-01-01\"\n", "end_date = \"2024-12-31\"\n", "\n", "# 下载数据\n", "df = yf.download(\"TSLA\", start=start_date, end=end_date, interval=\"1d\")\n", "\n", "# 查看前几行\n", "print(df.head())"]}, {"cell_type": "markdown", "id": "a8fb0dc1-f44c-4fdf-b37b-992d5bf1956d", "metadata": {}, "source": ["# 三、初步数据处理与可视化"]}, {"cell_type": "code", "execution_count": 9, "id": "06c62dc7-5911-4c45-83b2-24715725934c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 由于我们更常用收盘价来分析，获取收盘价\n", "df['Close'].plot(figsize=(10, 6), title='TSLA Stock Price')\n", "plt.show()"]}, {"cell_type": "markdown", "id": "919073bc-dff7-4018-8fb4-413ec58231f8", "metadata": {}, "source": ["## 3.1 计算收益率"]}, {"cell_type": "code", "execution_count": 10, "id": "74afe212-1d26-471d-af59-6e890733b048", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Price            Close log_return\n", "Ticker            TSLA           \n", "Date                             \n", "2024-01-03  238.449997  -0.040961\n", "2024-01-04  237.929993  -0.002183\n", "2024-01-05  237.490005  -0.001851\n", "2024-01-08  240.449997   0.012387\n", "2024-01-09  234.960007  -0.023097\n"]}], "source": ["df['log_return'] = np.log(df['Close']).diff()\n", "# 去除首行NaN\n", "df.dropna(inplace=True)\n", "\n", "print(df[['Close', 'log_return']].head())"]}, {"cell_type": "markdown", "id": "cf23049c-6718-494a-98e8-2efc875cfe9f", "metadata": {}, "source": ["# 四、平稳性检验（ADF）示例"]}, {"cell_type": "code", "execution_count": 12, "id": "20986896-2385-4197-9b67-3ef9e0b899fc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ADF 检验结果:\n", "ADF Statistic: -15.442693154362809\n", "p-value: 2.8513003392814387e-28\n", "Critical Values:\n", "   1%, -3.4568881317725864\n", "   5%, -2.8732185133016057\n", "   10%, -2.5729936189738876\n"]}], "source": ["result = adfuller(df['log_return'])\n", "adf_stat = result[0]\n", "p_value = result[1]\n", "critical_values = result[4]\n", "\n", "print(\"ADF 检验结果:\")\n", "print(f\"ADF Statistic: {adf_stat}\")\n", "print(f\"p-value: {p_value}\")\n", "print(\"Critical Values:\")\n", "for key, value in critical_values.items():\n", "    print(f\"   {key}, {value}\")\n"]}, {"cell_type": "markdown", "id": "a116b052-2413-4d2c-a51a-3dc4af63aa98", "metadata": {}, "source": ["# 五、观察自相关函数（ACF）与部分自相关函数（PACF）"]}, {"cell_type": "code", "execution_count": 13, "id": "2e4b1447-073d-4995-a1b7-397bbd562aca", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1400x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, axes = plt.subplots(1, 2, figsize=(14, 4))\n", "\n", "# ACF\n", "plot_acf(df['log_return'], ax=axes[0], title=\"ACF - TSLA log_return\")\n", "# PACF\n", "plot_pacf(df['log_return'], ax=axes[1], title=\"PACF - TSLA log_return\")\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "313ae453-cb33-4daf-9622-ed596b0c58e5", "metadata": {}, "source": ["从这张 ACF/PACF 图来看，除了滞后 0 之外几乎没有显著的自相关或偏自相关——这通常意味着在“均值”层面上，序列可以近似看作白噪声（即没有明显的 AR 或 MA 结构）。在 ARIMA 的框架下，这往往对应p=0和q=0。"]}, {"cell_type": "markdown", "id": "687b5e29-67d0-4b53-bcd3-afd052745b6a", "metadata": {}, "source": ["# 六、ARIMA 模型拟合与残差检验"]}, {"cell_type": "markdown", "id": "c53a5ebc-3904-4490-a20b-805323c45f22", "metadata": {}, "source": ["1. 判断 “d”：因为你使用的对数收益率，本质上就是对价格序列的一次差分（再取对数），它通常已经是平稳序列，可视作 d=0。\n", "如果你是直接对价格序列做 ARIMA，就需要根据 ADF 来判断是否要令 d=1 ", "或更高。\n", "但现在你是对 log_return 建模，所以多数情况下 d=0即可。"]}, {"cell_type": "markdown", "id": "e13cc324-63be-4918-8dc6-22ae835fc80b", "metadata": {}, "source": ["2. 判断 “p” 和 “q”\n", "ACF（自相关函数）：若在某个低阶（如 lag=1, 2）处显著超出置信区间，往往暗示存在 MA(q) 结构；但你图中几乎没有。\n", "PACF（部分自相关函数）：若在某个低阶明显超出置信区间，通常暗示存在 AR(p) 结构；你图中也没有。\n", "所以，初步结论就是p=0和q=0，也就是在均值上没有 AR 或 MA 项。"]}, {"cell_type": "markdown", "id": "4520fb14-aea5-4a71-ba21-a40a25950008", "metadata": {}, "source": ["3. 这相当于说Mean(Xt)是一个常数或者非常微弱的均值过程（模型实际上就退化成“纯白噪声”假设）。实务里，对数收益率往往确实缺少可预测的线性结构。\n"]}, {"cell_type": "markdown", "id": "1a794676-5b51-4e2b-9cd1-38fa2100371f", "metadata": {}, "source": ["3.1 检查残差\n", "如果这个模型的残差基本符合白噪声特征（用 Ljung-Box 等检验），就说明你的均值部分确实没有可用的线性预测信息。不过，这并不代表序列整体完全没有结构——\n", "\n", "下一步往往要检查 波动率聚集（也就是看 log_return 的 平方或绝对值是否存在自相关）——很多金融资产虽然“均值部分”不可预测，但“方差/波动率”是可以建模的（如 GARCH、EGARCH、GJR-GARCH 等）。"]}, {"cell_type": "code", "execution_count": 16, "id": "e25686db-c325-4e85-ab77-a94e4be5c271", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                               SARIMAX Results                                \n", "==============================================================================\n", "Dep. Variable:             log_return   No. Observations:                  250\n", "Model:                          ARIMA   Log Likelihood                 453.853\n", "Date:                Fri, 07 Mar 2025   AIC                           -903.706\n", "Time:                        19:19:27   BIC                           -896.663\n", "Sample:                             0   HQIC                          -900.872\n", "                                - 250                                         \n", "Covariance Type:                  opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "const          0.0021      0.003      0.807      0.420      -0.003       0.007\n", "sigma2         0.0016   8.65e-05     17.935      0.000       0.001       0.002\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.08   Jarque-Bera (JB):               138.55\n", "Prob(Q):                              0.77   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               1.48   Skew:                             0.57\n", "Prob(H) (two-sided):                  0.08   Kurtosis:                         6.47\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import yfinance as yf\n", "import matplotlib.pyplot as plt\n", "from statsmodels.tsa.arima.model import ARIMA\n", "from statsmodels.tsa.stattools import adfuller\n", "\n", "# 假设你之前已经有 df，包含 'log_return' 列\n", "# 这里直接展示模型部分\n", "model = ARIMA(df['log_return'], order=(0, 0, 0))  # p=0, d=0, q=0\n", "model_fit = model.fit()\n", "\n", "print(model_fit.summary())\n"]}, {"cell_type": "markdown", "id": "7c6a8bc0-4a49-4aca-8393-e6399354fac9", "metadata": {}, "source": ["从这个 **ARIMA(0,0,0)** 拟合结果来看，你得到的模型 essentially 就是：\n", "\n", "\\[\n", "\\text{log_return}_t = \\mu + \\varepsilon_t, \\quad \\varepsilon_t \\sim N(0, \\sigma^2),\n", "\\]\n", "\n", "其中：\n", "\n", "- **\\(\\mu \\approx 0.0021\\)**，但从 p-value=0.420 可以看出，这个常数项并没有显著性（无法拒绝 \\(\\mu = 0\\)）。  \n", "- **\\(\\sigma^2 \\approx 0.0016\\)**，表示残差项的方差。  \n", "- Ljung-Box 检验 p-value=0.77，说明残差没有显著自相关，模型在“均值”层面捕捉到了数据的线性特征，剩余部分可视为白噪声。  \n", "- **Jarque-Bera 检验显示 p=0.00，kurtosis=6.47**，意味着残差分布有厚尾特征（比正态更尖更厚尾），这在金融时间序列的收益率中很常见。\n", "\n", "### 如何解读这些结果\n", "\n", "1. **均值部分无显著预测力**  \n", "   - 由于 (p=0, d=0, q=0)，模型只估计了一个常数项 \\(\\mu\\)。结果显示 \\(\\mu\\) 并不显著（p 值约 0.42），故我们很难说收益率有明显的“平均偏移”——这也符合“对数收益率往往难以被单纯的线性模型预测”的一般经验。\n", "\n", "2. **残差分布非正态**  \n", "   - 虽然“均值可预测性”很弱，但 Jarque-Bera 检验表明分布并不是正态分布（p=0.00，kurtosis=6.47，skew=0.57），说明收益依然具有明显的胖尾/偏斜特性。  \n", "   - 这时，你可以考虑在残差分布中使用 t 分布、或其他重尾分布假设，或者在 GARCH 中也设定 thicker tail。\n", "\n", "3. **后续可分析波动率**  \n", "   - ARIMA(0,0,0) 白噪声模型并不代表序列没有**波动率聚集**等非线性特征——它只是捕捉“均值”部分(线性相关)并告诉你“均值预测力有限”。  \n", "   - 实际金融数据常常在“波动率”上有规律，故可以继续尝试 **GARCH 或者 EGARCH** 等模型看是否存在条件异方差（波动率随时间动态变化）。\n", "\n", "4. **年化波动率近似推算**  \n", "   - 如果你这里是**日频**数据，则 \\(\\sqrt{0.0016} \\approx 0.04 = 4\\%\\) 表示日度波动约 4%（标准差）。  \n", "   - 一年大约 250 个交易日，那么年化波动率大约 \\(\\sqrt{250}\\times 4\\%\\approx 63\\%\\)，这对波动比较大的 TSLA 并不罕见。\n", "\n", "---\n", "\n", "### 小结与建议\n", "\n", "- **ARIMA(0,0,0)（白噪声）** 确实是对很多股票日度“对数收益率”最常见的结果：均值部分基本随机。  \n", "- 残差显示强烈的非正态厚尾，这在“均值模型”里是无法捕捉的——后续如 **GARCH** / **EGARCH** 等波动率建模，或在残差分布中使用 Student-t 假设，会更贴近现实。  \n", "- 如果你想要捕捉短期预测价值，可以换 **高频数据** 或在非线性模型（如机器学习）上寻找特征，但对于日级对数收益率，(0,0,0) 并不令人意外。"]}, {"cell_type": "markdown", "id": "44487226-5fc6-4cae-adcb-f52b89e93c5c", "metadata": {}, "source": ["# 七、继续优化"]}, {"cell_type": "markdown", "id": "838696c8-7c66-4dd7-a804-0a6d1ce9150c", "metadata": {}, "source": ["1. 均值部分：确认是否需要更复杂的 ARIMA\n", "\n", "你已经用 ARIMA(0,0,0) 拟合了 log_return，结果显示常数项不显著，残差近似白噪声，这表明在均值层面几乎没有简单线性可预测性。为了彻底确认，也可以用自动选择 (p,d,q) 的方式比较 AIC/BIC，看是否确实没有更优解。"]}, {"cell_type": "code", "execution_count": 17, "id": "774e2cd6-371f-45cb-a5da-b4dd120938ac", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/statespace/sarimax.py:966: UserWarning: Non-stationary starting autoregressive parameters found. Using zeros as starting parameters.\n", "  warn('Non-stationary starting autoregressive parameters'\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/statespace/sarimax.py:978: UserWarning: Non-invertible starting MA parameters found. Using zeros as starting parameters.\n", "  warn('Non-invertible starting MA parameters found.'\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/statespace/sarimax.py:966: UserWarning: Non-stationary starting autoregressive parameters found. Using zeros as starting parameters.\n", "  warn('Non-stationary starting autoregressive parameters'\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/statespace/sarimax.py:978: UserWarning: Non-invertible starting MA parameters found. Using zeros as starting parameters.\n", "  warn('Non-invertible starting MA parameters found.'\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/base/model.py:607: ConvergenceWarning: Maximum Likelihood optimization failed to converge. Check mle_retvals\n", "  warnings.warn(\"Maximum Likelihood optimization failed to \"\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/statespace/sarimax.py:966: UserWarning: Non-stationary starting autoregressive parameters found. Using zeros as starting parameters.\n", "  warn('Non-stationary starting autoregressive parameters'\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/statespace/sarimax.py:978: UserWarning: Non-invertible starting MA parameters found. Using zeros as starting parameters.\n", "  warn('Non-invertible starting MA parameters found.'\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/base/model.py:607: ConvergenceWarning: Maximum Likelihood optimization failed to converge. Check mle_retvals\n", "  warnings.warn(\"Maximum Likelihood optimization failed to \"\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/tsa/base/tsa_model.py:473: ValueWarning: A date index has been provided, but it has no associated frequency information and so will be ignored when e.g. forecasting.\n", "  self._init_dates(dates, freq)\n", "/Users/<USER>/miniforge3/envs/ta_arm/lib/python3.9/site-packages/statsmodels/base/model.py:607: ConvergenceWarning: Maximum Likelihood optimization failed to converge. Check mle_retvals\n", "  warnings.warn(\"Maximum Likelihood optimization failed to \"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Best ARIMA order: (0, 0, 0)\n", "Best AIC: -903.7064174230513\n", "                               SARIMAX Results                                \n", "==============================================================================\n", "Dep. Variable:             log_return   No. Observations:                  250\n", "Model:                          ARIMA   Log Likelihood                 453.853\n", "Date:                Fri, 07 Mar 2025   AIC                           -903.706\n", "Time:                        19:34:09   BIC                           -896.663\n", "Sample:                             0   HQIC                          -900.872\n", "                                - 250                                         \n", "Covariance Type:                  opg                                         \n", "==============================================================================\n", "                 coef    std err          z      P>|z|      [0.025      0.975]\n", "------------------------------------------------------------------------------\n", "const          0.0021      0.003      0.807      0.420      -0.003       0.007\n", "sigma2         0.0016   8.65e-05     17.935      0.000       0.001       0.002\n", "===================================================================================\n", "Ljung-Box (L1) (Q):                   0.08   Jarque-Bera (JB):               138.55\n", "Prob(Q):                              0.77   Prob(JB):                         0.00\n", "Heteroskedasticity (H):               1.48   Skew:                             0.57\n", "Prob(H) (two-sided):                  0.08   Kurtosis:                         6.47\n", "===================================================================================\n", "\n", "Warnings:\n", "[1] Covariance matrix calculated using the outer product of gradients (complex-step).\n"]}], "source": ["import itertools\n", "import numpy as np\n", "import pandas as pd\n", "from statsmodels.tsa.arima.model import ARIMA\n", "\n", "p_values = [0, 1, 2, 3]\n", "d_values = [0]  # log_return本身已平稳\n", "q_values = [0, 1, 2, 3]\n", "\n", "best_aic = np.inf\n", "best_order = None\n", "best_model = None\n", "\n", "for p, d, q in itertools.product(p_values, d_values, q_values):\n", "    try:\n", "        model = ARIMA(df['log_return'], order=(p,d,q))\n", "        results = model.fit()\n", "        if results.aic < best_aic:\n", "            best_aic = results.aic\n", "            best_order = (p,d,q)\n", "            best_model = results\n", "    except:\n", "        continue\n", "\n", "print(\"Best ARIMA order:\", best_order)\n", "print(\"Best AIC:\", best_aic)\n", "if best_model:\n", "    print(best_model.summary())"]}, {"cell_type": "markdown", "id": "36604987-c4e2-4848-892d-f3929051af22", "metadata": {}, "source": ["如果最终结果依然是 (0,0,0) 或与之相近，就可以确认：均值上基本捕捉不到线性结构。"]}, {"cell_type": "markdown", "id": "8814fc42-076d-418e-91ee-a61fa4186598", "metadata": {}, "source": ["# 2. 检测并拟合波动率模型（GARCH）"]}, {"cell_type": "markdown", "id": "9059e866-df9d-47b4-94c9-482bbd8253f9", "metadata": {}, "source": ["## 2.1 检测波动率聚集\n", "先做一个简单的观察：看 log_return 的绝对值或平方是否存在自相关。如果有明显的自相关或在 Ljung-Box 检验中显著，就意味着波动率聚集很可能存在。"]}, {"cell_type": "code", "execution_count": 18, "id": "be69f25b-7060-4083-885b-b5523397b84c", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAmYAAAF2CAYAAADEElSMAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8ekN5oAAAACXBIWXMAAA9hAAAPYQGoP6dpAAA6SElEQVR4nO3dDZyM9f7/8c9arJuyyM1ayX2k3P1WtNItf7cVpQ5S2CNOoRIR/UKi9kQ5Us5x6kQ5JY5CpXI4dPOr3EXq1MGJSG52key27tnr//h8t2ua2Z29NbPznZ3X8/G4jLnmmmuum5md93zvrijHcRwBAABAyJUK9QYAAAAgC8EMAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQxAkZ09e1bGjh0rderUkVKlSkmvXr0Cst7HH39coqKi5PDhw1Kc9DX1tW1Sr149GTRoUKg3A0AxIZgBlvjzn/9sgkG7du3yXC41NVUefvhhadq0qVSoUEEqVqwoCQkJMnXqVDl69Khnueuvv96sz9+0bdu2gGzz3LlzZfr06XL77bfLq6++Kg899FCBnte2bVuzHX/5y1+kpHjqqadk2bJlod4Ma73//vvWhV7ARqVDvQEAsrz++uumdGTDhg2yY8cOadSoUY5lNm7cKN27d5eMjAy56667TCBTX3zxhfzxj3+UTz75RFauXOlZ/uKLL5bk5OQc64mPjw/INq9Zs0Zq164tf/rTnwr8nO+++87sh+6r7vN9990nJSWYaUANVKlhSQxms2fPJpwB+SCYARbYtWuXfP7557JkyRL5wx/+YALLpEmTfJbR0rBbb71VoqOj5csvvzQlZt6efPJJeemll3zmxcbGmgAXLAcPHpTKlSsX6jmvvfaa1KhRQ5599lkTZHbv3m1CGsLLsWPHTGltqNmyHUCgUJUJWECDWJUqVaRHjx4mrOj97P7617/Kvn37ZMaMGTlCmapZs6Y89thjAfuyGz16tGk7FhMTI02aNJFnnnlGHMcxj2uY0qrIDz/8UL799ltPFelHH32U77oXLFhg9vGmm24ywVHv50bbmP3ud7+TSpUqyUUXXSQPPvignDx50meZVatWSYcOHUxAvOCCC8y2PvroozkC5ODBg80xKleunLRs2dJUveZH23b5C41uGziX/l+Pma7TPRbe7cL0vP3+9783r6/H8/LLLzfVwEX1/fffyx133CFVq1Y11dlXXXWVvPfeezmW++GHH+SWW24xwUXDsFY1//Of/yzwuXK98sor5jkff/yxDBs2zKxLS2NdH3zwgVxzzTXmdS688ELzPtb3hUuPhZaWucfKnZRuh7/tcd9j+tre69FzvHPnTlNyrK/Vv39/z3pHjBhhqpOvuOIKz3FesWJFoY4tEGqUmAEW0CB22223SdmyZaVfv36m7ZVW91155ZWeZd555x0pX768CTUFde7cuRwN6DWY6JdbbjR86Ze5hi4NM61atTJf5mPGjDEBQ6stq1evLn//+99NKZ1Wq7rVpZdddlme27N+/XpTTTtv3jyzr7rPuu/Zg5RLQ5kGI13/unXrZNasWfLzzz/L/PnzzeP65a8Br0WLFvLEE0+YL2Nd/2effeZZx4kTJ0x7O52vX9z169eXxYsXmy95LYXUsHe+9Fjcc889pu3c0KFDzbyGDRt62gRqcHKDgx47DTJ6bNPT02XkyJGFei1dX/v27eX48ePywAMPmMCqgVDP2ZtvvmlKVZUGxRtvvFEOHDhg9jEuLs6EYD2vRaWhTLd/4sSJZv3uvg8cOFC6dOkiTz/9tNkuff9qWNaSXT1/Wgq8f/9+E6J1+fPtcKKvpevXHwsaTF2ffvqpKXXW7dTQpu+X3r17y549e8xxAsKCAyCkvvjiCy2GclatWmXuZ2ZmOhdffLHz4IMP+ixXpUoVp2XLlgVe73XXXWfWm30aOHBgns9btmyZWW7q1Kk+82+//XYnKirK2bFjh89rXH755QXephEjRjh16tQx+6hWrlxpXuvLL7/0WW7SpElm/i233OIzf9iwYWb+V199Ze7/6U9/MvcPHTqU62vOnDnTLPPaa6955p0+fdpJTEx0LrjgAic9Pd0zX5fT13bpsapbt26Odbrb561ixYp+j+3gwYOdWrVqOYcPH/aZ37dvXyc2NtY5fvy4kxd9fe/1jhw50rz2//3f/3nm/fLLL079+vWdevXqOefOnTPznn32WbOcnk/XiRMnnKZNm5r5H374oVNQ8+bNM8/p0KGDc/bsWZ/XrVy5sjNkyBCf5VNSUsy+ec8fPnx4jmOmdDv8bc+uXbvMfH1tlx4HnTdu3Lgc69H5ZcuW9Xl/6vtE5z///PMF3lcg1KjKBEJMS4y0iuuGG24w97VkpU+fPrJw4UJT4uXS0hUtBSgMLa3QUgrvSYe3yK+RtrZj09IYb1q1qd9/WtpT1JKORYsWmX1zq7G0REerxfxV3arhw4f73L///vs926jc9m1vv/22ZGZm5ro/WlqkJZGuMmXKmP3T0j6tngsWPV5vvfWW3Hzzzeb/WnrpTlrqk5aWJps3by7UOnV/tGROS4xcWgKqJXVa/fef//zHzNMqPO2YoSVp3qWlQ4YMKfL+6HP1veHS95OWOuqx9d43XUZ7F59P6Vxecusw0qlTJ09JpdKSVK0G16pfIFxQlQmEkAYvDWAayrQDgEu/1LRx/OrVq6Vz585mnn7B/PLLL4Vav7b50S+rwtB2SdprM3sIdKsp9fGi0N6ihw4dMqFCqxVduu9vvPGGqQbTsdC8NW7c2Oe+funqMhpAlIa8v/3tb6Yacdy4cdKxY0dTParVve66dHt1PdnXfb77UxC6vxpcXnzxRTP5o+3fCkO319+QKt77o22s9FaPl3dbOOWvt29BaTVw9h62bsD2R9+zgVa6dGmf9m3eLrnkkhzztO2mVn8D4YJgBoSQDjehbYA0nOmUnZYkucFMG/xv2bJFTp8+bdpnhRu3VEzbjfmjJVduqWFusocMbXOnQ4RoyYw2ftdSIi2V06CgQdC7dKcosr+ey7skMy9uKZ72jNV2WP5oqU640OPtb/+03ZiWSvoLUYE+xtqOMHvIduV2vt1OK0A4IJgBIQ4rWpXn9ljzpo2Yly5dKnPmzDFfiFodtnbtWlM15l0tF2h169aVf/3rX6Z0zrvUzB2UVh8vLG0ortWNWsLlr/OCVivqscgezLRExruURkvaNAx495TUL2ktKdNJe6zqeGL/+7//a8Kalhbq9n799dfmed5f6AXZHy1t8R601+WvlM1fwNCG8noMNWQUtuQyN7q927dvzzE/+/7orVZraijx3jbv0srz5VYb6ns4v/3LLYDpMVbZj3MwSzIBm9HGDAgR7S2o4Ut7FWpYyT5pDz4NR9obU917771Sq1Yt09brv//9r98qMR39/3zpMAQaJF544QWf+dobU79cu3XrVuh1asDUcKZtxvztqx4DDZynTp3yeV72wPr888+bW3cbjhw5kuO1tBepctel+5OSkmJK0rzbu+m6tG3Wddddl2fw0HZgGuxcWsKp++Ov2jh7uNASHO0VqPv2zTff+K3qLCzdHx2EWEO6S4+tVpVqYG3WrJmZp23YtBet+/5ROtRI9rHuzoe+hlZXahg+c+ZMnvvnjjWW/RhpgNTjpCWf2a+EAUQiSsyAENEvTA1e3o2zvekQC1rioiVJWtKkJQsaCPSLWcOH98j/2oBc22klJiae93ZpyZyWXGmpk7bl0jG/tFpQS7x0aAfvxtUFpfugwxXoMA/+6DHQwKDVkdpGzKXt7vSxrl27miCig9PeeeedZpuUDpGhX+g6bpZ+wWs41S90bYPkNo7XRvE6BpwOj7Fp0yYTXnRYCR1SY+bMmXl2qOjbt6888sgjZggKLdVzh4K49NJLczTa13OhJY1aaqdt9LSkT9uC6RUZtPRO/6+N5zU4aaDU5+vy/sJlXrQtnZ5rDae6TTqWmQ6XocdKA6BbKqhDVGi41tJVHS5DQ72eB+0AkFcJVmFoKNPjcffdd8v//M//mOOl71kdnkLP5dVXX+0J+O57VbdZA52GMV1ex7LTMdk0KOs26ftr+fLlhW57B5QYoe4WCkSqm2++2SlXrpxz7NixXJcZNGiQU6ZMGZ+hFvbv3+889NBDzqWXXmqeX6FCBSchIcF58sknnbS0tCIPZeFNh0HQ14iPjzev37hxY2f69OmeYS4K8xqpqalO6dKlnbvvvjvXZXTICN2PW2+91Wc4iv/85z9mmI4LL7zQDBeiw23okA+u1atXOz179jTbqUMl6G2/fv2c//73vzm2ISkpyalWrZpZrnnz5j7DMOQ2XIY7pMcVV1xhntekSRMz7Ia/4TK2bdvmXHvttU758uVzDEuir6/DRehQIXo84+LinI4dOzovvviik5/sw2WonTt3muOiQ1Xoe6Bt27bO8uXLczz3+++/d3r06GG2qXr16s7o0aOdt956y2zfunXrnMIOl7Fx40a/j+tQF126dDFDZOj2NGzY0Lx3dSgYlw6zcf/995vt0GFXvI+fDnfSu3dv8x7Q8/yHP/zB+eabb/wOl6HDkvijy+oxLsjxA2wWpf+EOhwCAIqHlhLqFQD27t1rhtMAYBeCGQCU4HaM3j0ptY1Z69atTRtCf+0UAYQebcwAoITS9no6tpe2SdRODNpGT3tvukOXaHDT+XnRNmzhODwLEK4IZgBQQmkjex2AV4OYlpJpxwMdL087kyjtqZqUlJTnOrTjgl5rFEAJqMrU3lLTp083PaHcLua9evXK8zkfffSRjBo1ylycuE6dOvLYY4+Z3lTZu9DrerULvPbO0t48Opo4AKDg9O+y/q3Ni/amdMcaAxDmJWY6to4Gp9///vc+XeBzo929tdu7jtekv/D0cjR6qRXt5q2//NxfeBrcdNBN7X6uDVn1MR1wUQc5BAAUjP5t1QlABDb+1/Fp8isx0/GCdOwb74EYdZwbHZBQL7WiNIxdeeWVnrFxdDRvLVnTixvr+D4AAADhyqo2ZjqAZPbLemhpmA5qqfQagVotOn78eM/jOpiiPsd7FOzsdARw7xHFNczpoI464GUgBlkEAADIi5aD6aDiOgB1btd7tS6YaZuxmjVr+szT++np6ab30M8//2wasPpbxr1OnD/JyckyefLkoG03AABAQfz444/m6iRhEcyCRUvYtF2aS7uHaxdyPTh6SZFAeXyeyHNvaYlczsc0HD/YW+TxvDtAAQCAEkgLmbTpVV6XgbMumMXFxUlqaqrPPL2v4UkHSdRrq+nkbxl9bm5iYmLMlJ2uN5DB7L7eIs+9rXWlfh4sJTKst75mwF4OAACEmfyaUOVeyRkCegFm7YnpbdWqVZ4LM+sgh9p123sZbS+m9wNx8ebz1fhikZfHiJTyOubRpbJKy3R+o9xLLgEAAIIbzDIyMmTLli1mcofD0P/v2bPHU8U4YMAAz/I6TMb3338vY8eONW3G/vznP8s//vEPc103l1ZJvvTSS/Lqq6/K1q1b5b777jPDcuQ3SGJxGdRN5Mu//XZ/5O0i2+dnzQcAAAhZVeYXX3whN9xwg+e+285r4MCB8sorr5jBDd2QpurXr2+Gy9Ag9txzz5nGcTpqtTuGmdIRqw8dOiQTJ040nQX0UiM6lEb2DgGh1DD+t/9PThKp+Nul6gAAAHIVkRcx1wZ4sbGxphNAINuYuY6dELng1xKyjA8IZgAARLr0AmYPq9qYAQAARDKCGQAAgCUIZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAlCGYAAACWIJgBAABYgmAGAABgCYIZAACAJQhmAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAlCGYAAACWIJgBAABYgmAGAABgCYIZAACAJQhmAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJEUzGbPni316tWTcuXKSbt27WTDhg25Lnv99ddLVFRUjqlHjx6eZQYNGpTj8a5duxbHrgAAAARNaQmyRYsWyahRo2TOnDkmlM2cOVO6dOki27dvlxo1auRYfsmSJXL69GnP/Z9++klatmwpd9xxh89yGsTmzZvnuR8TExPkPQEAAAjzErMZM2bIkCFDJCkpSZo1a2YCWoUKFWTu3Ll+l69atarExcV5plWrVpnlswczDWLey1WpUiXYuwIAABC+wUxLvjZt2iSdOnX67QVLlTL3165dW6B1vPzyy9K3b1+pWLGiz/yPPvrIlLg1adJE7rvvPlOyBgAAEM6CWpV5+PBhOXfunNSsWdNnvt7ftm1bvs/XtmjffPONCWfZqzFvu+02qV+/vuzcuVMeffRR6datmwl70dHROdZz6tQpM7nS09PPa78AAADCso3Z+dBA1rx5c2nbtq3PfC1Bc+njLVq0kIYNG5pStI4dO+ZYT3JyskyePLlYthkAAMDKqsxq1aqZEqzU1FSf+Xpf24Xl5dixY7Jw4UIZPHhwvq/ToEED81o7duzw+/j48eMlLS3NM/3444+F3BMAAIAwD2Zly5aVhIQEWb16tWdeZmamuZ+YmJjncxcvXmyqH++66658X2fv3r2mjVmtWrX8Pq4dBSpVquQzAQAARFyvTB0q46WXXpJXX31Vtm7dahrqa2mY9tJUAwYMMCVa/qoxe/XqJRdddJHP/IyMDBkzZoysW7dOdu/ebUJez549pVGjRmYYDgAAgHAV9DZmffr0kUOHDsnEiRMlJSVFWrVqJStWrPB0CNizZ4/pqelNxzj79NNPZeXKlTnWp1WjX3/9tQl6R48elfj4eOncubNMmTKFscwAAEBYi3Icx5EIo70yY2NjTXuzYFRrHjshckG3rP9nfCBSsXzAXwIAAJTA7MG1MgEAACxBMAMAALAEwQwAAMASBDMAAABLEMwAAAAsQTADAACwBMEMAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAABLEMwAAAAsQTADAACwBMEMAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAIBICmazZ8+WevXqSbly5aRdu3ayYcOGXJd95ZVXJCoqymfS53lzHEcmTpwotWrVkvLly0unTp3ku+++K4Y9AQAACONgtmjRIhk1apRMmjRJNm/eLC1btpQuXbrIwYMHc31OpUqV5MCBA57phx9+8Hl82rRpMmvWLJkzZ46sX79eKlasaNZ58uTJYO8OAABA+AazGTNmyJAhQyQpKUmaNWtmwlSFChVk7ty5uT5HS8ni4uI8U82aNX1Ky2bOnCmPPfaY9OzZU1q0aCHz58+X/fv3y7Jly4K9OwAAAOEZzE6fPi2bNm0yVY2eFyxVytxfu3Ztrs/LyMiQunXrSp06dUz4+vbbbz2P7dq1S1JSUnzWGRsba6pIc1vnqVOnJD093WcCAACIqGB2+PBhOXfunE+Jl9L7Gq78adKkiSlNe/vtt+W1116TzMxMad++vezdu9c87j6vMOtMTk424c2dNPABAADYxrpemYmJiTJgwABp1aqVXHfddbJkyRKpXr26/PWvfy3yOsePHy9paWme6ccffwzoNgMAAFgfzKpVqybR0dGSmprqM1/va9uxgihTpoy0bt1aduzYYe67zyvMOmNiYkyHAu8JAAAgooJZ2bJlJSEhQVavXu2Zp1WTel9LxgpCq0L//e9/m6ExVP369U0A816nthnT3pkFXScAAICNSgf7BXSojIEDB0qbNm2kbdu2pkflsWPHTC9NpdWWtWvXNu3A1BNPPCFXXXWVNGrUSI4ePSrTp083w2Xcc889nh6bI0eOlKlTp0rjxo1NUJswYYLEx8dLr169gr07AAAA4RvM+vTpI4cOHTIDwmrjfG07tmLFCk/j/T179piemq6ff/7ZDK+hy1apUsWUuH3++edmqA3X2LFjTbgbOnSoCW8dOnQw68w+EC0AAEA4iXJ0YLAIo1Wf2jtTOwIEo73ZsRMiF3TL+n/GByIVywf8JQAAQAnMHtb1ygQAAIhUBDMAAABLEMwAAAAsQTADAACwBMEMAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAABLEMwAAAAsQTADAACwBMEMAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAALAEwQwAAMASBDMAAABLEMwAAAAiKZjNnj1b6tWrJ+XKlZN27drJhg0bcl32pZdekmuuuUaqVKlipk6dOuVYftCgQRIVFeUzde3atRj2BAAAIIyD2aJFi2TUqFEyadIk2bx5s7Rs2VK6dOkiBw8e9Lv8Rx99JP369ZMPP/xQ1q5dK3Xq1JHOnTvLvn37fJbTIHbgwAHP9MYbbwR7VwAAAIIqynEcJ5gvoCVkV155pbzwwgvmfmZmpglb999/v4wbNy7f5587d86UnOnzBwwY4CkxO3r0qCxbtqxI25Seni6xsbGSlpYmlSpVkkA7dkLkgm5Z/8/4QKRi+YC/BAAACCMFzR5BLTE7ffq0bNq0yVRHel6wVClzX0vDCuL48eNy5swZqVq1ao6StRo1akiTJk3kvvvuk59++ing2w8AAFCcSgdz5YcPHzYlXjVr1vSZr/e3bdtWoHU88sgjEh8f7xPutBrztttuk/r168vOnTvl0UcflW7dupmwFx0dnWMdp06dMpN3agUAAIioYHa+/vjHP8rChQtN6Zh2HHD17dvX8//mzZtLixYtpGHDhma5jh075lhPcnKyTJ48udi2GwAAoCiCWpVZrVo1U4KVmprqM1/vx8XF5fncZ555xgSzlStXmuCVlwYNGpjX2rFjh9/Hx48fb+p03enHH38swt4AAACEcTArW7asJCQkyOrVqz3ztPG/3k9MTMz1edOmTZMpU6bIihUrpE2bNvm+zt69e00bs1q1avl9PCYmxjS0854AAAAibrgMHSpDxyZ79dVXZevWraah/rFjxyQpKck8rj0ttUTL9fTTT8uECRNk7ty5ZuyzlJQUM2VkZJjH9XbMmDGybt062b17twl5PXv2lEaNGplhOAAAAMJV0NuY9enTRw4dOiQTJ040AatVq1amJMztELBnzx7TU9P1l7/8xfTmvP32233Wo+OgPf7446Zq9OuvvzZBT4fM0I4BOs6ZlrBpyRgAAEC4Cvo4ZjZiHDMAAGBj9rC6VyYAO3y3V2Tu+yK7U0TqxYn8vrtI44tDvVUAUPIQzADkad4HIvdMF4kSES1e19tpC0VeHiMy6NeSYQBAGF3EHED4lpRpKMvMFDmX6Xs7eLrIjr2h3kIAKFkIZgBypdWXWkLmj85/+f1i3iAAKOEIZgBypW3Kcusd5Pz6OAAgcAhmAHKlDf3zKjHTxwEAgUMwA5Ar7X2ZV4nZ4O7FvEEAUMIRzADkSofE0N6XpbyKzaJLieiY0Dq/EUNmAEBAEcwA5EmHxPjyb7/dH3m7yPb5DJUBAMHAOGYA8tUw/rf/T07iahYAECyUmAEAAFiCEjMAAArJ31Wmg33lae/1Z38tpwDLFXT7irobua2/sPNDISrKnpoAghlQRDb8USmubdCR/l1nzoqcOi2S6WTNL+ytbrPn/xF2HIs1JBR0uXzmOYUJB07Rg8L5BovCnsLChIVwf38gf6WjRTq0ECsQzELsxCmRb3dl/b8gn/2C/IHIb5l8Hy/C84rrD9f5vk5Bvqz4w+z/fepa+61I+ZhQbg0AlFwEsxA7d04k40SotwIAANiAxv8AAACWoMQMABBwew+JfLBeJOWISFxVkW7tRC6uHuqtAuxHMAMABJQGsmcW/XpBVW2fGSWycI3ImL4iXduGeusAuxHMAAABK+HS9Wgo0563nt42v95OXyjSvL5IbUrOgFwRzAAgwgWyhEvX5VlPdlEi768XGXJToLYcKHkIZmGAthoAgiXQJVz6dyr3MXd+fRxArghmEdhWg6AHIFglXPo3Ja/1mccB5IpgZrFgtNWgUS6AYJZw6Q89/ZuS2/q6tyvsFgKRhXHMwuGXrOT+S7aoQS/7pXI06O07FIitBhBOPCVcEpgSLi191x96eu1BV6lSIqWisubT8D809O//S8tFpszPutX7sBMlZhH0S5ZGuSipqJ4vumCUcGnpe+PaIvc8k3W/97UiPdsTykKFmpLwQjCzWKDbagSrUS5figilcPjSsfkz4pZwTVv42zVhtYRLj+X5lHDFV/vt/0ldub5qqDB8SfghmEXQL9lgNMoNhy/FSGRzEIi0MbPC4TNCCVfJRU1J+CGYWSzQv2QDHfTC4UsxEtkeBCJpzKxw+oxQwlUyfwwxfEn4KZbG/7Nnz5Z69epJuXLlpF27drJhw4Y8l1+8eLE0bdrULN+8eXN5//33fR53HEcmTpwotWrVkvLly0unTp3ku+++k5JIv6heGv3bff0lO3980b5gA90oN9CdE8KJrQ1pbe/gEejtC2b1fCDObyR/RiKJnueBySILPxT5aEvWrd5fkfdXXVh27kAJCGaLFi2SUaNGyaRJk2Tz5s3SsmVL6dKlixw8eNDv8p9//rn069dPBg8eLF9++aX06tXLTN98841nmWnTpsmsWbNkzpw5sn79eqlYsaJZ58mTJ6Ukyv5L9nx+YQcy6EXqLzGb/wjbHgQCvX3B+NIJ5PmN1M9IJLH9x5CW3OX1HizJw5fstfQHdMirMmfMmCFDhgyRpKQkc1/D1HvvvSdz586VcePG5Vj+ueeek65du8qYMWPM/SlTpsiqVavkhRdeMM/V0rKZM2fKY489Jj179jTLzJ8/X2rWrCnLli2Tvn37Fnjbjp0QiS4TsF39bb0n/f8/t2VPnMp7mZOn/f+/qKpW+u3//W4UKVc2/23w56LYvKuR9PGirNdm+w6LTF/0a9VytqoprXLWdjreQToU2+dWe+fgZD1elHMSqPdgoLfvhtYib+RSPa+vc2Prwq0v0Oc3nD4jgf47E+j12eqdz/M+x29/nvWDOlQuqiTyYG+RmW95NYmJytpcna/fB7a8B/Xzt/ILkYM/i9SoItK5jUjtIv491fU891ZWDZHut97q34qRvUX+X5ucy5eOzsoEwVTQ9Uc5mnSC5PTp01KhQgV58803TamXa+DAgXL06FF5++23czznkksuMSVsI0eO9MzT0jYNXV999ZV8//330rBhQ1Oa1qpVK88y1113nbmvwS67U6dOmcmVnp4uderUEbk6TaS0V0oBAAAIhrPpIp/FSlpamlSqVCk0VZmHDx+Wc+fOmdIsb3o/JSXF73N0fl7Lu7eFWWdycrLExsZ6JhPKAAAALBMRvTLHjx9vSuGyl5jtf0skj9BaLDJOiGz+r4S1/YdF/ulV/NylzflV531/QGTErwWft12T1UaiqMXZbjXKbROz/r/kiayq26Kat0LkrU+y2pBkpx0ptM1eUaotArnPq77IqraI8ur1qOXiuRXhFzebty9Y5zfQn5FAv6/DQaD3N1Dr0+q3oc/6r6LX97i26S3KuQ6H8xuobQz05+7pN0Q++dr/OdFq3GtaiDzSL2dVZvsrJKjS00Xi40IczKpVqybR0dGSmprqM1/vx8X53zqdn9fy7q3O016Z3st4V216i4mJMVN2FctnTaGkb5xw75besLbIsNqBWZc2vNY2Pq5ln4ks+zRwQz3oH47zOd63tBd56+NcHnSyxn4q7PoDvc+3XC2ScGlWQ3q367428LVlWAabty8Y5zfQn5FgvK/DgQYgl7YV0nMVqOEozuf4NaotMrZvVkN/7yFg3GGN9NyHcvuKy/ls409peXdQ+CmtcOvWH7Vu27IcorIez74+DWbBzgPnzhRsuaBWZZYtW1YSEhJk9erVnnmZmZnmfmJiot/n6Hzv5ZU2/neXr1+/vgln3stoCZj2zsxtnQiv3k3eHyabejd5Dzmiv7rcoUbOZ8iRYO2zboeO3zVhQNatDaEnHLYv0OcXgaE/XrRUyqWlK7b0hFb6A0p7t/e9QeT6Vlm3Re3tHokC3bu6W5j3RA16VaZWIWpj/zZt2kjbtm1Nj8pjx455emkOGDBAateubdqBqQcffNA05H/22WelR48esnDhQvniiy/kxRdfNI9HRUWZjgFTp06Vxo0bm6A2YcIEiY+P9+lggPBj+2ChLv1jqwODBqLEJ1z2OZIE8vzi/OX248W2QXrdHxsovEAPfn7xrz+wcivFtOH9EtJg1qdPHzl06JAZEFYb52t144oVKzyN9/fs2SOlzHD2Wdq3by8LFiwww2E8+uijJnxpj8wrrvit8nfs2LEm3A0dOtT07uzQoYNZpw5Ii/AVrDGfvKtAtC1DIKpAAvVHmHGu7BQOX7LBeF/bKFg/XiLl+AVToI5hMIJU1zD+gRXU4TJspVWf2jszvy6rxSHjuMgX20O6CdbQAQB1MM/cGoBq9UBh/wC77bf8XdLKhmqGYOwzSj7b39eBpIOD6kC/5rJW2WgVs1YdapV4STt+OrZY91+H+nz/j/a1MQvGMdSmG++HKEhpG7MOLezIHsVySSYgFO0CwqHNWri3hUDxC4f3tc3tjyLt+AVDpLaNLS4EM1gj0A2vbb88kaKxOQorHN7XNv94CZfjl72a0KbLCYXLMQxXETGOGcJHINsFhEv7rXBuC4HiFy7v60AJdPujcDh+2YfQ0V6oOoyLLVWt4XAMwxnBDCW24bWnCsQJ3AWuI7mxOewQTu9rG3+82H78wqEXqu3HMNxRlYkSi/ZbKIki9X0dqPZHth+/cKgmtP0YhjuCGUos2m+hJOJ9XbKPXzhUE9p+DMMdVZko0Wi/hZKI93XJPX7hUk1o8zEMd4xjxjhmAABLaBszvdxUbuO26aWeCD+BxzhmAAAgB6oJQVUmAAAWoZowshHMAACwDEPoRC6qMgEAACxBiVmIlSsr0vSS/JcrSA+N/LpxnM/jufbeDnLXkfNdf2H3yd/yBZ1XHELdU8e9Hp7uv/v/gtwCAAqGYBZipUuLxF0U6q0AgsuEND+BrTiDZkntf17gHxNFeF6O+4VZ1sn/ee7/C7NsXq+Zn/wWP58fZsF+e+V1LIp6/AryWIG2rQjrze0xJ0Sf0+hosQbBDEDQmV5lNJwAgHzxpxIAAMASBDMAAABLEMwAAAAsQTADAACwBMEMAADAEgQzAAAASxDMAAAALEEwAwAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAMAAIiEYHbkyBHp37+/VKpUSSpXriyDBw+WjIyMPJe///77pUmTJlK+fHm55JJL5IEHHpC0tDSf5aKionJMCxcuDOauAAAABF3pYK5cQ9mBAwdk1apVcubMGUlKSpKhQ4fKggUL/C6/f/9+Mz3zzDPSrFkz+eGHH+Tee+818958802fZefNmyddu3b13NfgBwAAEM6iHMdxgrHirVu3mnC1ceNGadOmjZm3YsUK6d69u+zdu1fi4+MLtJ7FixfLXXfdJceOHZPSpbNypJaQLV26VHr16lWkbUtPT5fY2FhTEqeleQAAAMFU0OwRtKrMtWvXmlIsN5SpTp06SalSpWT9+vUFXo+7A24ocw0fPlyqVasmbdu2lblz50qQ8iUAAED4V2WmpKRIjRo1fF+sdGmpWrWqeawgDh8+LFOmTDHVn96eeOIJufHGG6VChQqycuVKGTZsmGm7pu3R/Dl16pSZvFMrAABA2AezcePGydNPP51vNeb50vDUo0cPUx36+OOP+zw2YcIEz/9bt25tqjmnT5+eazBLTk6WyZMnn/c2AQAAWNXG7NChQ/LTTz/luUyDBg3ktddek9GjR8vPP//smX/27FkpV66caTd266235vr8X375Rbp06WJKxJYvX26ek5f33ntPbrrpJjl58qTExMQUqMSsTp06tDEDAABWtTErdIlZ9erVzZSfxMREOXr0qGzatEkSEhLMvDVr1khmZqa0a9cuzw3XUKYB65133sk3lKktW7ZIlSpV/IYypfNzewwAAKDEtzG77LLLzHAWQ4YMkTlz5pjhMkaMGCF9+/b19Mjct2+fdOzYUebPn28a8Wso69y5sxw/ftyUuOl9tz2YhsHo6Gh59913JTU1Va666ioT2nQojqeeekoefvjhYO0KAABA+I9j9vrrr5swpuFLe2P27t1bZs2a5Xlcw9r27dtNEFObN2/29Nhs1KiRz7p27dol9erVkzJlysjs2bPloYceMj0xdbkZM2aYAAgAABDOgjaOmc0YxwwAAETUOGYAAAAoHIIZAACAJQhmAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAlCGYAAACWIJgBAABYgmAGAABgCYIZAACAJQhmAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAlCGYAAACWIJgBAABYgmAGAABgCYIZAACAJQhmAAAAliCYAQAAREIwO3LkiPTv318qVaoklStXlsGDB0tGRkaez7n++uslKirKZ7r33nt9ltmzZ4/06NFDKlSoIDVq1JAxY8bI2bNng7krAAAAQVc6mCvXUHbgwAFZtWqVnDlzRpKSkmTo0KGyYMGCPJ83ZMgQeeKJJzz3NYC5zp07Z0JZXFycfP7552b9AwYMkDJlyshTTz0VzN0BAAAIqijHcZxgrHjr1q3SrFkz2bhxo7Rp08bMW7FihXTv3l327t0r8fHxuZaYtWrVSmbOnOn38Q8++EBuuukm2b9/v9SsWdPMmzNnjjzyyCNy6NAhKVu2bL7blp6eLrGxsZKWlmZK8wAAAIKpoNkjaFWZa9euNdWXbihTnTp1klKlSsn69evzfO7rr78u1apVkyuuuELGjx8vx48f91lv8+bNPaFMdenSxezwt99+63d9p06dMo97TwAAABFTlZmSkmLaf/m8WOnSUrVqVfNYbu68806pW7euKVH7+uuvTUnY9u3bZcmSJZ71eocy5d7Pbb3JyckyefLkAOwVAACARcFs3Lhx8vTTT+dbjVlU2gbNpSVjtWrVko4dO8rOnTulYcOGRVqnlrqNGjXKc19LzOrUqVPkbQQAALAimI0ePVoGDRqU5zINGjQwjfMPHjzoM197TmpPTX2soNq1a2dud+zYYYKZPnfDhg0+y6Smpprb3NYbExNjJgAAgBIVzKpXr26m/CQmJsrRo0dl06ZNkpCQYOatWbNGMjMzPWGrILZs2WJuteTMXe+TTz5pQp9bVaq9PrUhnXY2AAAACFdBa/x/2WWXSdeuXc3QF1rC9dlnn8mIESOkb9++nh6Z+/btk6ZNm3pKwLS6csqUKSbM7d69W9555x0zFMa1114rLVq0MMt07tzZBLC7775bvvrqK/nnP/8pjz32mAwfPpxSMQAAENaCOsCs9q7U4KVtxHSYjA4dOsiLL77oeVzHNtOG/W6vSx3q4l//+pcJX/o8rTbt3bu3vPvuu57nREdHy/Lly82tlp7dddddJrx5j3sGAAAQjoI2jpnNGMcMAABE1DhmAAAAKByCGQAAgCUIZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAlCGYAAACWIJgBAABYgmAGAABgCYIZAACAJQhmAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAlCGYAAACWIJgBAABYgmAGAABgCYIZAACAJQhmAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJEQzI4cOSL9+/eXSpUqSeXKlWXw4MGSkZGR6/K7d++WqKgov9PixYs9y/l7fOHChcHcFQAAgKArHcyVayg7cOCArFq1Ss6cOSNJSUkydOhQWbBggd/l69SpY5b39uKLL8r06dOlW7duPvPnzZsnXbt29dzX4AcAABDOghbMtm7dKitWrJCNGzdKmzZtzLznn39eunfvLs8884zEx8fneE50dLTExcX5zFu6dKn87ne/kwsuuMBnvgax7MsCAACEs6BVZa5du9aEJzeUqU6dOkmpUqVk/fr1BVrHpk2bZMuWLaYKNLvhw4dLtWrVpG3btjJ37lxxHCeg2w8AAFBiSsxSUlKkRo0avi9WurRUrVrVPFYQL7/8slx22WXSvn17n/lPPPGE3HjjjVKhQgVZuXKlDBs2zLRde+CBB/yu59SpU2ZypaenF2mfAAAArCoxGzduXK4N9N1p27Zt571hJ06cMG3R/JWWTZgwQa6++mpp3bq1PPLIIzJ27FjTDi03ycnJEhsb65m0LRsAAEDYl5iNHj1aBg0alOcyDRo0MO2/Dh486DP/7NmzpqdmQdqGvfnmm3L8+HEZMGBAvsu2a9dOpkyZYkrFYmJicjw+fvx4GTVqlE+JGeEMAACEfTCrXr26mfKTmJgoR48eNe3EEhISzLw1a9ZIZmamCVIFqca85ZZbCvRa2g6tSpUqfkOZ0vm5PQYAAFDi25hp2zAdzmLIkCEyZ84cM1zGiBEjpG/fvp4emfv27ZOOHTvK/PnzTSN+144dO+STTz6R999/P8d63333XUlNTZWrrrpKypUrZ4bieOqpp+Thhx8O1q4AAACE/zhmr7/+ugljGr60N2bv3r1l1qxZnsc1rG3fvt1UWXrTXpYXX3yxdO7cOcc6y5QpI7Nnz5aHHnrI9MRs1KiRzJgxwwRAAACAcBblROA4E9rGTDsBpKWlmasSAAAA2JA9uFYmAACAJQhmAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAlCGYAAACWIJgBAABYgmAGAABgCYIZAACAJQhmAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJYgmAEAAFiCYAYAAGAJghkAAIAlCGYAAACWIJgBAABYgmAGAABgCYIZAACAJQhmAAAAliCYAQAAWIJgBgAAUNKD2ZNPPint27eXChUqSOXKlQv0HMdxZOLEiVKrVi0pX768dOrUSb777jufZY4cOSL9+/eXSpUqmfUOHjxYMjIygrQXAAAAJSCYnT59Wu644w657777CvycadOmyaxZs2TOnDmyfv16qVixonTp0kVOnjzpWUZD2bfffiurVq2S5cuXyyeffCJDhw4N0l4AAAAUnyhHi6mC6JVXXpGRI0fK0aNH81xONyM+Pl5Gjx4tDz/8sJmXlpYmNWvWNOvo27evbN26VZo1ayYbN26UNm3amGVWrFgh3bt3l71795rnF0R6errExsaa9WvJGwAAQDAVNHuUFkvs2rVLUlJSTPWlS3egXbt2snbtWhPM9FarL91QpnT5UqVKmRK2W2+91e+6T506ZSaXHhT3IAEAAASbmznyKw+zJphpKFNaQuZN77uP6W2NGjV8Hi9durRUrVrVs4w/ycnJMnny5Bzz69SpE6CtBwAAyN8vv/xiCp4CEszGjRsnTz/9dJ7LaHVj06ZNxSbjx4+XUaNGee5nZmaaTgQXXXSRREVFBS0Za/D78ccfqS61AOfDPpwT+3BO7ML5KFnnREvKNJTl1+yqUMFM238NGjQoz2UaNGggRREXF2duU1NTTa9Ml95v1aqVZ5mDBw/6PO/s2bMmZLnP9ycmJsZM3graU/R86YnjA2UPzod9OCf24ZzYhfNRcs5JXiVlRQpm1atXN1Mw1K9f34Sr1atXe4KYJlNtO+b27ExMTDSdCDZt2iQJCQlm3po1a0wJmLZFAwAACGdBGy5jz549smXLFnN77tw583+dvMcc0yrPpUuXmv9rlaL23pw6daq888478u9//1sGDBhgivx69epllrnsssuka9euMmTIENmwYYN89tlnMmLECNMxoKA9MgEAAGwVtMb/OlDsq6++6rnfunVrc/vhhx/K9ddfb/6/fft2Tw9JNXbsWDl27JgZl0xLxjp06GCGwyhXrpxnmddff92EsY4dO5remL179zZjn9lGq04nTZqUowoVocH5sA/nxD6cE7twPiLznAR9HDMAAAAUDNfKBAAAsATBDAAAwBIEMwAAAEsQzAAAACxBMAuC2bNnS7169UxvUh1fTYf2QGg8/vjjZigW78m2K1OUdJ988oncfPPNZkgbPf7Lli3zeVz7H2kvbh1Yunz58ub6t999913ItjfSz4cOIp79M6PDFCF49LKBV155pVx44YXmsoM6RJSOWuDt5MmTMnz4cHPFmgsuuMCMSKADsCM050NHl8j+Obn33nsD8voEswBbtGiRufyTdqfdvHmztGzZUrp06ZLjigUoPpdffrkcOHDAM3366aeh3qSIokPg6OdAf7D4M23aNDPkzZw5c8yA0hUrVjSfGf0iQvGfD6VBzPsz88YbbxTrNkaajz/+2ISudevWyapVq+TMmTPSuXNnc65cDz30kLz77ruyePFis/z+/fvltttuC+l2R/L5UDqmqvfnRP+WBYQOl4HAadu2rTN8+HDP/XPnzjnx8fFOcnJySLcrUk2aNMlp2bJlqDcDv9I/OUuXLvXcz8zMdOLi4pzp06d75h09etSJiYlx3njjjRBtZeSeDzVw4ECnZ8+eIdsmOM7BgwfNufn44489n4kyZco4ixcv9iyzdetWs8zatWtDuKWReT7Udddd5zz44INOMFBiFkCnT582l4vSqhiXDoKr99euXRvSbYtkWi2m1TZ6Hdf+/fubq1HADrt27ZKUlBSfz4xeS06bAPCZCZ2PPvrIVOE0adLEXBLvp59+CvUmRRR34PWqVauaW/1e0VIb78+JNsm45JJL+JyE4Hx4D3hfrVo1ueKKK2T8+PFy/Phxu0f+j0SHDx82l5+qWbOmz3y9v23btpBtVyTTL/hXXnnFfMFoUfPkyZPlmmuukW+++ca0H0BoaShT/j4z7mMoXlqNqVVkev3inTt3yqOPPirdunUzASA6OjrUm1fi6bWf9fKEV199tfnCV/pZKFu2rFSuXNlnWT4noTkf6s4775S6deuaH/1ff/21PPLII6Yd2pIlS877NQlmKNH0C8XVokULE9T0w/SPf/xDBg8eHNJtA2yk1x52NW/e3HxuGjZsaErR9FJ4CC5t26Q/HGkLa/f50EtHen9OtPOSfj70x4x+Xs4HVZkBpEWa+osye08ZvR8XFxey7cJv9BfnpZdeKjt27Aj1pkDE87ngM2MvbQKgf9v4zASfXgd6+fLl5prSF198sWe+fha0qYxeQ9obn5PQnA9/9Ee/CsTnhGAWQFrUnJCQIKtXr/YpBtX7iYmJId02ZMnIyDC/aPTXDUJPq8v0i8X7M5Oenm56Z/KZscPevXtNGzM+M8Gj/TA0BCxdulTWrFljPhfe9HulTJkyPp8TrTbT9rJ8Tor/fPizZcsWcxuIzwlVmQGmQ2UMHDhQ2rRpI23btpWZM2eaLrZJSUmh3rSI9PDDD5sxm7T6UruX6zAmWqrZr1+/UG9aRIVh71+R2uBf/4hpQ1ptvKztN6ZOnSqNGzc2fwAnTJhg2m3o2EEo3vOhk7bD1DGyNDDrj5ixY8dKo0aNzBAmCF512YIFC+Ttt982bV/ddmPaEUbH9tNbbXqh3y96jipVqiT333+/CWVXXXVVqDc/4s7Hzp07zePdu3c348ppGzMdzuTaa681Vf/nLSh9PSPc888/71xyySVO2bJlzfAZ69atC/UmRaw+ffo4tWrVMueidu3a5v6OHTtCvVkR5cMPPzRdzbNPOiyDO2TGhAkTnJo1a5phMjp27Ohs37491Jsdkefj+PHjTufOnZ3q1aub4Rnq1q3rDBkyxElJSQn1Zpdo/s6HTvPmzfMsc+LECWfYsGFOlSpVnAoVKji33nqrc+DAgZBud6Sejz179jjXXnutU7VqVfM3q1GjRs6YMWOctLS0gLx+1K8bAQAAgBCjjRkAAIAlCGYAAACWIJgBAABYgmAGAABgCYIZAACAJQhmAAAAliCYAQAAWIJgBgAAYAmCGQAAgCUIZgAAAJYgmAEAAFiCYAYAACB2+P+TJyHWzrKFbgAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 700x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["      lb_stat  lb_pvalue\n", "10  11.669951   0.307753\n", "20  18.673701   0.543118\n"]}], "source": ["import matplotlib.pyplot as plt\n", "from statsmodels.graphics.tsaplots import plot_acf\n", "from statsmodels.stats.diagnostic import acorr_ljungbox\n", "\n", "returns_abs = df['log_return'].abs()\n", "fig, ax = plt.subplots(figsize=(7,4))\n", "plot_acf(returns_abs, ax=ax, title=\"ACF of Absolute log_return\")\n", "plt.show()\n", "\n", "# Ljung-Box 检验\n", "lb_test = acorr_ljungbox(returns_abs, lags=[10,20], return_df=True)\n", "print(lb_test)\n"]}, {"cell_type": "code", "execution_count": null, "id": "17c23bda-298e-4b9b-8bdc-e889c3b3d885", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python (ta_env)", "language": "python", "name": "ta_arm"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}