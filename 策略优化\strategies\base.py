"""
交易策略基类

定义交易策略的通用接口和基础功能。
"""

import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum

from ..config import get_config
from ..utils.logger import get_logger
from ..utils.exceptions import StrategyError, ValidationError


class SignalType(Enum):
    """信号类型枚举"""
    BUY = 1
    SELL = -1
    HOLD = 0


class Position(Enum):
    """持仓状态枚举"""
    LONG = 1
    SHORT = -1
    FLAT = 0


class BaseStrategy(ABC):
    """交易策略基类"""
    
    def __init__(self, name: str, **params):
        """
        初始化交易策略
        
        Args:
            name: 策略名称
            **params: 策略参数
        """
        self.name = name
        self.params = params
        self.logger = get_logger(f"strategies.{self.__class__.__name__}")
        self.config = get_config()
        
        # 验证参数
        self._validate_params(**params)
        
        # 策略状态
        self._signals: Optional[pd.Series] = None
        self._positions: Optional[pd.Series] = None
        self._trades: List[Dict[str, Any]] = []
        
        # 性能统计
        self._stats: Dict[str, Any] = {}
    
    @abstractmethod
    def _validate_params(self, **params) -> None:
        """
        验证策略参数
        
        Args:
            **params: 策略参数
        """
        pass
    
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        生成交易信号
        
        Args:
            data: 价格数据
            **kwargs: 额外参数
            
        Returns:
            交易信号序列 (1: 买入, -1: 卖出, 0: 持有)
        """
        pass
    
    def calculate_positions(self, signals: pd.Series, **kwargs) -> pd.Series:
        """
        根据信号计算持仓
        
        Args:
            signals: 交易信号
            **kwargs: 额外参数
            
        Returns:
            持仓序列 (1: 多头, 0: 空仓, -1: 空头)
        """
        try:
            # 默认实现：信号延迟一天执行
            signal_delay = kwargs.get('signal_delay', self.params.get('signal_delay', 1))
            
            # 延迟信号
            delayed_signals = signals.shift(signal_delay).fillna(0)
            
            # 计算持仓：1表示多头，0表示空仓，-1表示空头
            positions = pd.Series(index=signals.index, dtype=float)
            current_position = 0
            
            for i, signal in enumerate(delayed_signals):
                if signal == SignalType.BUY.value and current_position <= 0:
                    current_position = 1  # 开多仓
                elif signal == SignalType.SELL.value and current_position >= 0:
                    current_position = 0  # 平仓或空仓
                
                positions.iloc[i] = current_position
            
            return positions
            
        except Exception as e:
            raise StrategyError(f"计算持仓失败: {e}", self.name, self.params)
    
    def run(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """
        运行策略
        
        Args:
            data: 价格数据
            **kwargs: 额外参数
            
        Returns:
            策略结果字典
        """
        try:
            self.logger.info(f"开始运行策略: {self.name}")
            
            # 验证数据
            self._validate_data(data)
            
            # 生成信号
            self._signals = self.generate_signals(data, **kwargs)
            
            # 计算持仓
            self._positions = self.calculate_positions(self._signals, **kwargs)
            
            # 生成交易记录
            self._trades = self._generate_trades(data, self._positions)
            
            # 计算统计信息
            self._stats = self._calculate_stats(data, self._positions, self._trades)
            
            result = {
                'signals': self._signals,
                'positions': self._positions,
                'trades': self._trades,
                'stats': self._stats
            }
            
            self.logger.info(f"策略运行完成: {self.name}, 交易次数: {len(self._trades)}")
            return result
            
        except Exception as e:
            self.logger.error(f"运行策略失败: {e}")
            raise StrategyError(f"运行策略失败: {e}", self.name, self.params)
    
    def _validate_data(self, data: pd.DataFrame) -> None:
        """
        验证输入数据
        
        Args:
            data: 价格数据
        """
        if data.empty:
            raise StrategyError("输入数据为空", self.name, self.params)
        
        required_columns = self.get_required_columns()
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            raise StrategyError(
                f"缺少必需的列: {missing_columns}",
                self.name,
                self.params
            )
    
    def get_required_columns(self) -> List[str]:
        """
        获取策略所需的数据列
        
        Returns:
            必需列名列表
        """
        return ['close']  # 默认只需要收盘价
    
    def _generate_trades(self, data: pd.DataFrame, positions: pd.Series) -> List[Dict[str, Any]]:
        """
        生成交易记录
        
        Args:
            data: 价格数据
            positions: 持仓序列
            
        Returns:
            交易记录列表
        """
        trades = []
        position_changes = positions.diff().fillna(0)
        
        for date, change in position_changes.items():
            if change != 0:
                trade = {
                    'date': date,
                    'action': 'BUY' if change > 0 else 'SELL',
                    'position_change': change,
                    'price': data.loc[date, 'close'],
                    'position_before': positions.loc[date] - change,
                    'position_after': positions.loc[date]
                }
                trades.append(trade)
        
        return trades
    
    def _calculate_stats(self, data: pd.DataFrame, positions: pd.Series, 
                        trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        计算策略统计信息
        
        Args:
            data: 价格数据
            positions: 持仓序列
            trades: 交易记录
            
        Returns:
            统计信息字典
        """
        stats = {
            'total_trades': len(trades),
            'buy_trades': len([t for t in trades if t['action'] == 'BUY']),
            'sell_trades': len([t for t in trades if t['action'] == 'SELL']),
            'max_position': positions.max(),
            'min_position': positions.min(),
            'avg_position': positions.mean(),
            'position_changes': len(positions[positions.diff() != 0]),
            'holding_periods': self._calculate_holding_periods(positions),
            'signal_frequency': len(self._signals[self._signals != 0]) / len(self._signals) if self._signals is not None else 0
        }
        
        return stats
    
    def _calculate_holding_periods(self, positions: pd.Series) -> Dict[str, float]:
        """
        计算持仓周期统计
        
        Args:
            positions: 持仓序列
            
        Returns:
            持仓周期统计
        """
        holding_periods = []
        current_period = 0
        
        for i in range(1, len(positions)):
            if positions.iloc[i] == positions.iloc[i-1] and positions.iloc[i] != 0:
                current_period += 1
            else:
                if current_period > 0:
                    holding_periods.append(current_period)
                current_period = 0
        
        if current_period > 0:
            holding_periods.append(current_period)
        
        if holding_periods:
            return {
                'avg_holding_period': np.mean(holding_periods),
                'max_holding_period': np.max(holding_periods),
                'min_holding_period': np.min(holding_periods),
                'total_holding_periods': len(holding_periods)
            }
        else:
            return {
                'avg_holding_period': 0,
                'max_holding_period': 0,
                'min_holding_period': 0,
                'total_holding_periods': 0
            }
    
    def get_signals(self) -> Optional[pd.Series]:
        """获取交易信号"""
        return self._signals
    
    def get_positions(self) -> Optional[pd.Series]:
        """获取持仓序列"""
        return self._positions
    
    def get_trades(self) -> List[Dict[str, Any]]:
        """获取交易记录"""
        return self._trades
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self._stats
    
    def get_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns:
            策略信息字典
        """
        return {
            'name': self.name,
            'class': self.__class__.__name__,
            'params': self.params,
            'required_columns': self.get_required_columns(),
            'has_signals': self._signals is not None,
            'has_positions': self._positions is not None,
            'total_trades': len(self._trades)
        }
    
    def __repr__(self) -> str:
        """返回策略的字符串表示"""
        params_str = ', '.join([f"{k}={v}" for k, v in self.params.items()])
        return f"{self.__class__.__name__}({params_str})"
