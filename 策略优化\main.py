#!/usr/bin/env python3
"""
量化交易策略优化系统主程序

企业级量化交易框架的主入口程序。
"""

import sys
import argparse
import pandas as pd
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config import get_config, setup_logging
from data.generators import RandomDataGenerator
from data.loaders import CSVDataLoader, TushareDataLoader
from strategies.dual_ma import DualMAStrategy
from backtest.engine import BacktestEngine
from performance.metrics import PerformanceAnalyzer
from utils.logger import get_logger, setup_logging
from utils.exceptions import QuantTradingError


class QuantTradingSystem:
    """量化交易系统主类"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化量化交易系统
        
        Args:
            config_path: 配置文件路径
        """
        # 初始化配置
        self.config = get_config(config_path)
        
        # 设置日志
        setup_logging()
        self.logger = get_logger("main")
        
        # 初始化组件
        self.backtest_engine = BacktestEngine()
        self.performance_analyzer = PerformanceAnalyzer()
        
        self.logger.info("量化交易系统初始化完成")
    
    def run_demo(self) -> Dict[str, Any]:
        """
        运行演示程序
        
        Returns:
            演示结果
        """
        try:
            self.logger.info("开始运行演示程序")
            
            # 1. 生成模拟数据
            self.logger.info("生成模拟数据...")
            data_generator = RandomDataGenerator()
            data = data_generator.generate()
            
            self.logger.info(f"数据生成完成: {len(data)}条记录, "
                           f"时间范围: {data.index[0]} - {data.index[-1]}")
            
            # 2. 创建策略
            self.logger.info("创建双均线策略...")
            strategy = DualMAStrategy(
                short_window=20,
                long_window=50,
                ma_type='sma'
            )
            
            # 3. 运行回测
            self.logger.info("开始回测...")
            backtest_result = self.backtest_engine.run(data, strategy)
            
            # 4. 计算性能指标
            self.logger.info("计算性能指标...")
            portfolio_returns = backtest_result.portfolio_history['total_value'].pct_change().dropna()
            metrics = self.performance_analyzer.calculate_metrics(
                returns=portfolio_returns,
                trades=backtest_result.trades
            )
            
            # 5. 输出结果
            self._print_results(backtest_result, metrics)
            
            return {
                'backtest_result': backtest_result,
                'performance_metrics': metrics,
                'data': data,
                'strategy': strategy
            }
            
        except Exception as e:
            self.logger.error(f"演示程序运行失败: {e}")
            raise QuantTradingError(f"演示程序失败: {e}")
    
    def run_backtest(self, data_source: str = "random", 
                    strategy_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        运行回测
        
        Args:
            data_source: 数据源类型 ("random", "csv", "tushare")
            strategy_config: 策略配置
            
        Returns:
            回测结果
        """
        try:
            # 加载数据
            data = self._load_data(data_source)
            
            # 创建策略
            strategy = self._create_strategy(strategy_config or {})
            
            # 运行回测
            result = self.backtest_engine.run(data, strategy)
            
            return {
                'result': result,
                'data': data,
                'strategy': strategy
            }
            
        except Exception as e:
            self.logger.error(f"回测运行失败: {e}")
            raise QuantTradingError(f"回测失败: {e}")
    
    def _load_data(self, data_source: str) -> pd.DataFrame:
        """加载数据"""
        if data_source == "random":
            generator = RandomDataGenerator()
            return generator.generate()
        elif data_source == "csv":
            # 从配置获取CSV文件路径
            csv_config = self.config.get('data.csv', {})
            loader = CSVDataLoader(
                file_path=csv_config.get('file_path'),
                symbol_filter=csv_config.get('symbol_filter')
            )
            return loader.load()
        elif data_source == "tushare":
            # 从配置获取Tushare参数
            ts_config = self.config.get('data.tushare', {})
            loader = TushareDataLoader(token=ts_config.get('token'))
            return loader.load(
                symbol=ts_config.get('symbol', '600315.SH'),
                start_date=ts_config.get('start_date', '2020-01-01'),
                end_date=ts_config.get('end_date', '2023-12-31')
            )
        else:
            raise ValueError(f"不支持的数据源: {data_source}")
    
    def _create_strategy(self, config: Dict[str, Any]) -> DualMAStrategy:
        """创建策略"""
        strategy_config = self.config.get('strategies.dual_ma', {})
        strategy_config.update(config)
        
        return DualMAStrategy(
            short_window=strategy_config.get('short_window', 20),
            long_window=strategy_config.get('long_window', 50),
            ma_type=strategy_config.get('ma_type', 'sma'),
            signal_delay=strategy_config.get('signal_delay', 1)
        )
    
    def _print_results(self, backtest_result, metrics) -> None:
        """打印结果"""
        print("\n" + "="*70)
        print("量化交易策略回测结果")
        print("="*70)
        
        print(f"策略名称: {backtest_result.strategy_name}")
        print(f"回测期间: {backtest_result.start_date.strftime('%Y-%m-%d')} - "
              f"{backtest_result.end_date.strftime('%Y-%m-%d')}")
        print(f"初始资金: {backtest_result.initial_capital:,.2f}")
        print(f"最终价值: {backtest_result.final_value:,.2f}")
        
        print("\n" + "-"*40)
        print("收益指标")
        print("-"*40)
        print(f"总收益率: {backtest_result.total_return:.2%}")
        print(f"年化收益率: {backtest_result.annualized_return:.2%}")
        print(f"累计收益率: {metrics.cumulative_return:.2%}")
        
        print("\n" + "-"*40)
        print("风险指标")
        print("-"*40)
        print(f"年化波动率: {backtest_result.volatility:.2%}")
        print(f"最大回撤: {backtest_result.max_drawdown:.2%}")
        print(f"下行偏差: {metrics.downside_deviation:.2%}")
        print(f"VaR (95%): {metrics.var_95:.2%}")
        
        print("\n" + "-"*40)
        print("风险调整收益指标")
        print("-"*40)
        print(f"夏普比率: {backtest_result.sharpe_ratio:.3f}")
        print(f"索提诺比率: {metrics.sortino_ratio:.3f}")
        print(f"卡玛比率: {metrics.calmar_ratio:.3f}")
        
        print("\n" + "-"*40)
        print("交易指标")
        print("-"*40)
        print(f"总交易次数: {backtest_result.total_trades}")
        print(f"胜率: {backtest_result.win_rate:.2%}")
        print(f"盈利因子: {backtest_result.profit_factor:.2f}")
        
        print("\n" + "-"*40)
        print("成本统计")
        print("-"*40)
        print(f"总佣金: {backtest_result.total_commission:,.2f}")
        print(f"总印花税: {backtest_result.total_stamp_duty:,.2f}")
        print(f"总滑点成本: {backtest_result.total_slippage:,.2f}")
        print(f"总交易成本: {backtest_result.total_commission + backtest_result.total_stamp_duty + backtest_result.total_slippage:,.2f}")
        
        print("\n" + "="*70)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="量化交易策略优化系统")
    parser.add_argument("--config", type=str, help="配置文件路径")
    parser.add_argument("--data-source", type=str, default="random",
                       choices=["random", "csv", "tushare"], help="数据源类型")
    parser.add_argument("--demo", action="store_true", help="运行演示程序")
    
    args = parser.parse_args()
    
    try:
        # 初始化系统
        system = QuantTradingSystem(args.config)
        
        if args.demo:
            # 运行演示
            system.run_demo()
        else:
            # 运行回测
            result = system.run_backtest(args.data_source)
            print("回测完成！")
            
    except Exception as e:
        print(f"程序运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
