import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates

# 解决中文显示问题
plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像是负号'-'显示为方块的问题

# --- 模拟参数 ---
num_days = 252 * 3  # 模拟大约3年的交易日
initial_price = 100  # 初始股价
drift = 0.0001  # 日对数收益率均值 (趋势)
volatility = 0.015  # 日对数收益率标准差 (波动率)
initial_capital = 100000  # 初始资金
transaction_cost_rate = 0.001  # 单边交易成本比例 (例如 0.1%)

# --- 1. 生成模拟数据 ---
# 生成日对数收益率
log_returns = np.random.normal(drift, volatility, num_days)
# 计算价格路径
price_path = np.exp(np.cumsum(log_returns)) * initial_price
# 创建日期索引
dates = pd.date_range(start='2020-01-01', periods=num_days, freq='B')  # 'B' for business day frequency
# 创建DataFrame
df = pd.DataFrame(data={'close': price_path}, index=dates)

# --- 2. 计算技术指标 ---
df['MA20'] = df['close'].rolling(window=20).mean()
df['MA50'] = df['close'].rolling(window=50).mean()

# --- 3. 生成交易信号 ---
# 原始信号：MA20 > MA50 时为 1 (买入状态)，否则为 -1 (卖出/空仓状态)
df['Signal_State'] = np.where(df['MA20'] > df['MA50'], 1, -1)

# 延迟信号：将信号延迟一天，模拟“下一日开盘”交易
# 使用 .fillna() 处理初始NaN值，假设期初处于 "卖出/空仓" 状态
df['Trading_Signal'] = df['Signal_State'].shift(1).fillna(-1)

# --- 4. 计算收益与成本 ---
# 市场日收益率 (基于收盘价的百分比变化)
df['Market_Return'] = df['close'].pct_change().fillna(0)

# 确定实际持仓 (1 表示持有多头，0 表示空仓)
# 如果 Trading_Signal 为 1，则持有多头；如果为 -1，则空仓
df['Position'] = np.where(df['Trading_Signal'] == 1, 1, 0)

# 策略的原始市场收益 (未扣除成本)
df['Strategy_Market_Return'] = df['Position'] * df['Market_Return']

# 计算仓位变化 (用于计算交易成本)
# diff() 后，1表示开仓，-1表示平仓。fillna(0)处理第一个值。
df['Position_Change'] = df['Position'].diff().fillna(0)

# 计算交易成本：仅在仓位发生变化时产生 (开仓或平仓)
# abs(Position_Change) 在发生交易时为1
df['Transaction_Costs'] = abs(df['Position_Change']) * transaction_cost_rate

# 策略的净日收益率 (扣除交易成本)
df['Strategy_Net_Return'] = df['Strategy_Market_Return'] - df['Transaction_Costs']

# --- 5. 计算累计收益 ---
df['Cumulative_Market_Return_Value'] = initial_capital * (1 + df['Market_Return']).cumprod()
df['Cumulative_Strategy_Return_Value'] = initial_capital * (1 + df['Strategy_Net_Return']).cumprod()

# --- 6. 绘制结果 ---
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10), sharex=True, gridspec_kw={'height_ratios': [2, 1]})

# 子图1：收盘价、均线及买卖信号标记
ax1.plot(df.index, df['close'], label='模拟收盘价 (Close)', color='blue', linewidth=1.5)
ax1.plot(df.index, df['MA20'], label='20日均线 (MA20)', color='orange', linestyle='--', linewidth=1)
ax1.plot(df.index, df['MA50'], label='50日均线 (MA50)', color='green', linestyle='--', linewidth=1)

# 标记买入和卖出点
# 买入信号：Trading_Signal 从 -1 变为 1 (diff = 2)
buy_signals = df[df['Trading_Signal'].diff() == 2]
ax1.plot(buy_signals.index, df['close'][buy_signals.index], '^', markersize=10, color='red', lw=0, label='买入信号')

# 卖出信号：Trading_Signal 从 1 变为 -1 (diff = -2)
sell_signals = df[df['Trading_Signal'].diff() == -2]
ax1.plot(sell_signals.index, df['close'][sell_signals.index], 'v', markersize=10, color='green', lw=0, label='卖出信号')

ax1.set_title('模拟股票价格、均线及交易信号', fontsize=16)
ax1.set_ylabel('价格', fontsize=12)
ax1.legend(loc='upper left')
ax1.grid(True)

# 子图2：累计收益对比
ax2.plot(df.index, df['Cumulative_Market_Return_Value'], label='市场累计收益', color='gray', linewidth=1.5)
ax2.plot(df.index, df['Cumulative_Strategy_Return_Value'], label='策略累计收益 (含成本)', color='purple', linewidth=1.5)

ax2.set_title('市场累计收益 vs. 策略累计收益', fontsize=16)
ax2.set_ylabel('累计资金价值', fontsize=12)
ax2.set_xlabel('日期', fontsize=12)
ax2.legend(loc='upper left')
ax2.grid(True)

# 设置X轴日期格式
plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
plt.gca().xaxis.set_major_locator(mdates.AutoDateLocator(minticks=5, maxticks=10))
plt.xticks(rotation=45)

# 优化布局
plt.tight_layout()

# 显示图表
plt.show()