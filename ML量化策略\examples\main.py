#!/usr/bin/env python3
"""
ML量化策略系统主程序

专注于机器学习驱动的量化交易策略开发和优化。
"""

import argparse
import sys
from typing import Dict, Any, List

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mlquant.data import RandomDataGenerator
from mlquant.optimization import MLStrategyOptimizer
from mlquant.strategies import DualMAStrategy, RSIStrategy, BollingerStrategy
from mlquant.backtest import BacktestEngine
from mlquant.performance import PerformanceAnalyzer
from mlquant.utils.config import ConfigManager
from mlquant.utils.logger import setup_logger


class MLQuantSystem:
    """ML量化交易系统"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化系统
        
        Args:
            config_path: 配置文件路径
        """
        self.data_generator = RandomDataGenerator(config_path)
        self.ml_optimizer = MLStrategyOptimizer(config_path)
        self.backtest_engine = BacktestEngine(config_path)
        self.performance_analyzer = PerformanceAnalyzer(config_path)
        
        print("🚀 ML量化策略系统初始化完成")
    
    def run_single_optimization(self, strategy_name: str = 'dual_ma', 
                              data_days: int = 1000) -> Dict[str, Any]:
        """
        运行单个策略优化
        
        Args:
            strategy_name: 策略名称
            data_days: 数据天数
            
        Returns:
            优化结果
        """
        print(f"\n🎯 开始单策略优化: {strategy_name}")
        
        # 1. 生成训练数据
        print("📊 生成训练数据...")
        train_data = self.data_generator.generate(days=data_days)
        
        # 2. 优化策略参数
        print("🧠 开始ML参数优化...")
        best_strategy = self.ml_optimizer.optimize(train_data, strategy_name)
        print(f"✅ 优化完成: {best_strategy}")
        
        # 3. 生成测试数据进行验证
        print("📈 生成测试数据进行验证...")
        test_data = self.data_generator.generate(days=data_days//2, random_seed=999)
        
        # 4. 回测验证
        strategy_obj = self.ml_optimizer.strategies[strategy_name]
        test_signals = strategy_obj.generate_signals(test_data, **best_strategy.params)
        
        result = self.backtest_engine.run(test_data, test_signals, best_strategy.name)
        
        # 5. 性能分析
        report = self.performance_analyzer.analyze(result)
        report.print_report()
        
        return {
            'best_strategy': best_strategy,
            'backtest_result': result,
            'performance_report': report,
            'train_data': train_data,
            'test_data': test_data
        }
    
    def run_multi_strategy_comparison(self, strategies: List[str] = None,
                                    data_days: int = 1000) -> Dict[str, Any]:
        """
        运行多策略比较
        
        Args:
            strategies: 策略列表
            data_days: 数据天数
            
        Returns:
            比较结果
        """
        if strategies is None:
            strategies = ['dual_ma', 'rsi', 'bollinger']
        
        print(f"\n🏆 开始多策略比较: {strategies}")
        
        # 生成统一的训练和测试数据
        print("📊 生成训练数据...")
        train_data = self.data_generator.generate(days=data_days)
        
        print("📈 生成测试数据...")
        test_data = self.data_generator.generate(days=data_days//2, random_seed=888)
        
        results = []
        optimized_strategies = {}
        
        # 优化每个策略
        for strategy_name in strategies:
            try:
                print(f"\n🔧 优化策略: {strategy_name}")
                
                # ML优化
                best_strategy = self.ml_optimizer.optimize(train_data, strategy_name)
                optimized_strategies[strategy_name] = best_strategy
                
                # 回测验证
                strategy_obj = self.ml_optimizer.strategies[strategy_name]
                test_signals = strategy_obj.generate_signals(test_data, **best_strategy.params)
                
                result = self.backtest_engine.run(test_data, test_signals, 
                                                f"{strategy_name}_optimized")
                results.append(result)
                
                print(f"✅ {strategy_name} 优化完成: 收益率 {result.total_return:.2%}")
                
            except Exception as e:
                print(f"❌ {strategy_name} 优化失败: {e}")
                continue
        
        # 性能比较
        if results:
            print(f"\n📊 策略性能比较:")
            comparison_df = self.performance_analyzer.compare_strategies(results)
            print(comparison_df.round(4))
            
            # 汇总统计
            self.performance_analyzer.print_summary_stats(results)
            
            # 创建收益曲线图表
            equity_curves = {r.strategy_name: r.equity_curve for r in results}
            chart = create_simple_chart(equity_curves, "策略收益对比")
            print(chart)
        
        return {
            'optimized_strategies': optimized_strategies,
            'backtest_results': results,
            'comparison_table': comparison_df if results else None,
            'train_data': train_data,
            'test_data': test_data
        }
    
    def run_market_scenario_analysis(self, strategy_name: str = 'dual_ma',
                                   n_scenarios: int = 5) -> Dict[str, Any]:
        """
        运行市场情景分析
        
        Args:
            strategy_name: 策略名称
            n_scenarios: 情景数量
            
        Returns:
            情景分析结果
        """
        print(f"\n🌍 开始市场情景分析: {strategy_name}")
        
        # 生成多个市场情景
        print(f"📊 生成 {n_scenarios} 个市场情景...")
        scenarios = self.data_generator.generate_multiple_scenarios(
            n_scenarios=n_scenarios, base_days=800
        )
        
        # 在第一个情景上优化策略
        first_scenario_name = list(scenarios.keys())[0]
        train_data = scenarios[first_scenario_name]
        
        print(f"🧠 在 {first_scenario_name} 上优化策略...")
        best_strategy = self.ml_optimizer.optimize(train_data, strategy_name)
        
        # 在所有情景上测试
        results = []
        strategy_obj = self.ml_optimizer.strategies[strategy_name]
        
        for scenario_name, scenario_data in scenarios.items():
            print(f"📈 测试情景: {scenario_name}")
            
            signals = strategy_obj.generate_signals(scenario_data, **best_strategy.params)
            result = self.backtest_engine.run(scenario_data, signals, 
                                            f"{strategy_name}_{scenario_name}")
            results.append(result)
        
        # 分析结果
        print(f"\n📊 情景分析结果:")
        comparison_df = self.performance_analyzer.compare_strategies(results)
        print(comparison_df.round(4))
        
        # 汇总统计
        self.performance_analyzer.print_summary_stats(results)
        
        return {
            'best_strategy': best_strategy,
            'scenario_results': results,
            'comparison_table': comparison_df,
            'scenarios': scenarios
        }
    
    def run_parameter_sensitivity_analysis(self, strategy_name: str = 'dual_ma',
                                         data_days: int = 1000) -> Dict[str, Any]:
        """
        运行参数敏感性分析
        
        Args:
            strategy_name: 策略名称
            data_days: 数据天数
            
        Returns:
            敏感性分析结果
        """
        print(f"\n🔍 开始参数敏感性分析: {strategy_name}")
        
        # 生成数据
        data = self.data_generator.generate(days=data_days)
        
        # 获取策略和参数范围
        strategy_obj = self.ml_optimizer.strategies[strategy_name]
        param_ranges = strategy_obj.get_param_ranges()
        
        # 测试不同参数组合
        results = []
        test_count = 0
        max_tests = 50  # 限制测试数量
        
        import random
        random.seed(42)
        
        print(f"🧪 开始参数测试 (最多 {max_tests} 组合)...")
        
        while test_count < max_tests:
            # 随机生成参数
            params = {}
            for param_name, (min_val, max_val) in param_ranges.items():
                if param_name.endswith('_window') or param_name == 'period':
                    params[param_name] = random.randint(int(min_val), int(max_val))
                else:
                    params[param_name] = random.uniform(min_val, max_val)
            
            try:
                # 生成信号并回测
                signals = strategy_obj.generate_signals(data, **params)
                result = self.backtest_engine.run(data, signals, 
                                                f"{strategy_name}_{test_count}")
                
                # 记录参数和结果
                result.params = params
                results.append(result)
                test_count += 1
                
                if test_count % 10 == 0:
                    print(f"  已完成 {test_count}/{max_tests} 组合测试")
                    
            except Exception as e:
                continue
        
        # 分析结果
        if results:
            # 按夏普比率排序
            results.sort(key=lambda x: x.sharpe_ratio, reverse=True)
            
            print(f"\n📊 参数敏感性分析结果 (前10名):")
            print(f"{'排名':<4} {'夏普比率':<10} {'总收益率':<10} {'参数'}")
            print("-" * 60)
            
            for i, result in enumerate(results[:10], 1):
                params_str = ", ".join([f"{k}={v}" for k, v in result.params.items()])
                print(f"{i:<4} {result.sharpe_ratio:<10.3f} {result.total_return:<10.2%} {params_str}")
        
        return {
            'all_results': results,
            'best_result': results[0] if results else None,
            'worst_result': results[-1] if results else None
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="ML量化策略系统")
    parser.add_argument("--mode", type=str, default="single",
                       choices=["single", "compare", "scenario", "sensitivity"],
                       help="运行模式")
    parser.add_argument("--strategy", type=str, default="dual_ma",
                       choices=["dual_ma", "rsi", "bollinger"],
                       help="策略名称")
    parser.add_argument("--days", type=int, default=1000,
                       help="数据天数")
    
    args = parser.parse_args()
    
    try:
        # 初始化系统
        system = MLQuantSystem()
        
        if args.mode == "single":
            # 单策略优化
            result = system.run_single_optimization(args.strategy, args.days)
            
        elif args.mode == "compare":
            # 多策略比较
            result = system.run_multi_strategy_comparison(data_days=args.days)
            
        elif args.mode == "scenario":
            # 市场情景分析
            result = system.run_market_scenario_analysis(args.strategy)
            
        elif args.mode == "sensitivity":
            # 参数敏感性分析
            result = system.run_parameter_sensitivity_analysis(args.strategy, args.days)
        
        print(f"\n🎉 {args.mode} 模式运行完成!")
        
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
