"""
交互式图表组件

提供基于Plotly的交互式图表组件，包括：
- K线图表
- 技术指标图表
- 交易信号图表
- 实时数据更新
- 参数调整界面
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union, Callable
import warnings

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    warnings.warn("plotly not available. InteractiveCharts will not work.")

from ..data.indicators import TechnicalIndicators


class InteractiveCharts:
    """
    交互式图表组件
    
    提供丰富的交互式图表功能，支持实时数据更新、
    参数调整和多种技术分析图表。
    """
    
    def __init__(self, theme: str = "plotly_white"):
        """
        初始化交互式图表
        
        Args:
            theme: 图表主题
        """
        if not PLOTLY_AVAILABLE:
            raise ImportError("plotly is required for InteractiveCharts")
        
        self.theme = theme
        self.colors = {
            'up': '#00CC96',      # 上涨颜色
            'down': '#FF6692',    # 下跌颜色
            'volume': '#636EFA',  # 成交量颜色
            'ma': '#FFA15A',      # 移动平均线颜色
            'signal': '#AB63FA',  # 信号颜色
            'buy': '#00CC96',     # 买入信号
            'sell': '#FF6692'     # 卖出信号
        }
    
    def create_candlestick_chart(self, data: pd.DataFrame, 
                                indicators: Optional[Dict[str, pd.Series]] = None,
                                signals: Optional[pd.Series] = None,
                                title: str = "K线图") -> go.Figure:
        """
        创建K线图表
        
        Args:
            data: OHLCV数据
            indicators: 技术指标字典
            signals: 交易信号
            title: 图表标题
            
        Returns:
            Plotly图表对象
        """
        # 创建子图
        rows = 3 if 'volume' in data.columns else 2
        fig = make_subplots(
            rows=rows, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.05,
            subplot_titles=[title, '技术指标', '成交量'] if rows == 3 else [title, '技术指标'],
            row_heights=[0.6, 0.2, 0.2] if rows == 3 else [0.7, 0.3]
        )
        
        # 1. K线图
        fig.add_trace(go.Candlestick(
            x=data.index,
            open=data['open'],
            high=data['high'],
            low=data['low'],
            close=data['close'],
            name='K线',
            increasing_line_color=self.colors['up'],
            decreasing_line_color=self.colors['down']
        ), row=1, col=1)
        
        # 2. 添加技术指标
        if indicators:
            for name, series in indicators.items():
                if 'sma' in name.lower() or 'ema' in name.lower():
                    fig.add_trace(go.Scatter(
                        x=series.index,
                        y=series.values,
                        mode='lines',
                        name=name,
                        line=dict(width=1),
                        opacity=0.8
                    ), row=1, col=1)
        
        # 3. 添加交易信号
        if signals is not None:
            buy_signals = signals[signals == 1]
            sell_signals = signals[signals == -1]
            
            if len(buy_signals) > 0:
                fig.add_trace(go.Scatter(
                    x=buy_signals.index,
                    y=data.loc[buy_signals.index, 'close'],
                    mode='markers',
                    name='买入信号',
                    marker=dict(
                        symbol='triangle-up',
                        size=10,
                        color=self.colors['buy']
                    )
                ), row=1, col=1)
            
            if len(sell_signals) > 0:
                fig.add_trace(go.Scatter(
                    x=sell_signals.index,
                    y=data.loc[sell_signals.index, 'close'],
                    mode='markers',
                    name='卖出信号',
                    marker=dict(
                        symbol='triangle-down',
                        size=10,
                        color=self.colors['sell']
                    )
                ), row=1, col=1)
        
        # 4. 技术指标子图
        if indicators:
            indicator_row = 2
            for name, series in indicators.items():
                if 'rsi' in name.lower():
                    fig.add_trace(go.Scatter(
                        x=series.index,
                        y=series.values,
                        mode='lines',
                        name=name,
                        line=dict(color='purple')
                    ), row=indicator_row, col=1)
                    
                    # RSI超买超卖线
                    fig.add_hline(y=70, line_dash="dash", line_color="red", 
                                 annotation_text="超买", row=indicator_row, col=1)
                    fig.add_hline(y=30, line_dash="dash", line_color="green", 
                                 annotation_text="超卖", row=indicator_row, col=1)
                
                elif 'macd' in name.lower() and 'signal' not in name.lower():
                    fig.add_trace(go.Scatter(
                        x=series.index,
                        y=series.values,
                        mode='lines',
                        name=name,
                        line=dict(color='blue')
                    ), row=indicator_row, col=1)
        
        # 5. 成交量图
        if 'volume' in data.columns and rows == 3:
            colors = [self.colors['up'] if close >= open else self.colors['down'] 
                     for close, open in zip(data['close'], data['open'])]
            
            fig.add_trace(go.Bar(
                x=data.index,
                y=data['volume'],
                name='成交量',
                marker_color=colors,
                opacity=0.7
            ), row=3, col=1)
        
        # 更新布局
        fig.update_layout(
            template=self.theme,
            xaxis_rangeslider_visible=False,
            height=600,
            hovermode='x unified'
        )
        
        # 隐藏x轴标签（除了最后一个子图）
        for i in range(1, rows):
            fig.update_xaxes(showticklabels=False, row=i, col=1)
        
        return fig
    
    def create_strategy_comparison_chart(self, results: Dict[str, pd.Series],
                                       title: str = "策略对比") -> go.Figure:
        """
        创建策略对比图表
        
        Args:
            results: 策略名称到资金曲线的映射
            title: 图表标题
            
        Returns:
            Plotly图表对象
        """
        fig = go.Figure()
        
        colors = px.colors.qualitative.Set1
        
        for i, (strategy_name, equity_curve) in enumerate(results.items()):
            # 计算累计收益率
            returns = equity_curve / equity_curve.iloc[0] - 1
            
            fig.add_trace(go.Scatter(
                x=returns.index,
                y=returns.values * 100,
                mode='lines',
                name=strategy_name,
                line=dict(color=colors[i % len(colors)], width=2),
                hovertemplate=f'{strategy_name}<br>日期: %{{x}}<br>累计收益: %{{y:.2f}}%<extra></extra>'
            ))
        
        fig.update_layout(
            title=title,
            xaxis_title='日期',
            yaxis_title='累计收益率 (%)',
            template=self.theme,
            hovermode='x unified',
            legend=dict(x=0, y=1, bgcolor='rgba(255,255,255,0.8)')
        )
        
        return fig
    
    def create_parameter_sensitivity_chart(self, param_results: Dict[str, float],
                                         param_name: str,
                                         metric_name: str = "夏普比率") -> go.Figure:
        """
        创建参数敏感性分析图表
        
        Args:
            param_results: 参数值到指标值的映射
            param_name: 参数名称
            metric_name: 指标名称
            
        Returns:
            Plotly图表对象
        """
        param_values = list(param_results.keys())
        metric_values = list(param_results.values())
        
        fig = go.Figure()
        
        # 添加散点图
        fig.add_trace(go.Scatter(
            x=param_values,
            y=metric_values,
            mode='markers+lines',
            name=f'{param_name} vs {metric_name}',
            marker=dict(size=8, color=self.colors['signal']),
            line=dict(color=self.colors['signal'], width=2),
            hovertemplate=f'{param_name}: %{{x}}<br>{metric_name}: %{{y:.4f}}<extra></extra>'
        ))
        
        # 标记最优点
        best_param = max(param_results.keys(), key=lambda k: param_results[k])
        best_value = param_results[best_param]
        
        fig.add_trace(go.Scatter(
            x=[best_param],
            y=[best_value],
            mode='markers',
            name=f'最优参数 ({best_param})',
            marker=dict(size=12, color='red', symbol='star'),
            hovertemplate=f'最优{param_name}: {best_param}<br>最优{metric_name}: {best_value:.4f}<extra></extra>'
        ))
        
        fig.update_layout(
            title=f'{param_name}参数敏感性分析',
            xaxis_title=param_name,
            yaxis_title=metric_name,
            template=self.theme
        )
        
        return fig
    
    def create_rolling_metrics_chart(self, equity_curve: pd.Series,
                                   window: int = 252,
                                   metrics: List[str] = ['sharpe', 'volatility']) -> go.Figure:
        """
        创建滚动指标图表
        
        Args:
            equity_curve: 资金曲线
            window: 滚动窗口
            metrics: 要计算的指标列表
            
        Returns:
            Plotly图表对象
        """
        returns = equity_curve.pct_change().dropna()
        
        fig = make_subplots(
            rows=len(metrics), cols=1,
            shared_xaxes=True,
            subplot_titles=[f'滚动{metric}' for metric in metrics],
            vertical_spacing=0.1
        )
        
        for i, metric in enumerate(metrics):
            if metric == 'sharpe':
                rolling_metric = returns.rolling(window).mean() / returns.rolling(window).std() * np.sqrt(252)
                y_title = '夏普比率'
            elif metric == 'volatility':
                rolling_metric = returns.rolling(window).std() * np.sqrt(252) * 100
                y_title = '波动率 (%)'
            elif metric == 'return':
                rolling_metric = returns.rolling(window).mean() * 252 * 100
                y_title = '年化收益率 (%)'
            else:
                continue
            
            fig.add_trace(go.Scatter(
                x=rolling_metric.index,
                y=rolling_metric.values,
                mode='lines',
                name=f'滚动{metric}',
                line=dict(width=2)
            ), row=i+1, col=1)
            
            fig.update_yaxes(title_text=y_title, row=i+1, col=1)
        
        fig.update_layout(
            title=f'滚动指标分析 (窗口: {window}天)',
            template=self.theme,
            height=150 * len(metrics) + 100
        )
        
        fig.update_xaxes(title_text="日期", row=len(metrics), col=1)
        
        return fig
    
    def create_correlation_heatmap(self, data: pd.DataFrame,
                                 title: str = "相关性热力图") -> go.Figure:
        """
        创建相关性热力图
        
        Args:
            data: 数据DataFrame
            title: 图表标题
            
        Returns:
            Plotly热力图对象
        """
        correlation_matrix = data.corr()
        
        fig = go.Figure(data=go.Heatmap(
            z=correlation_matrix.values,
            x=correlation_matrix.columns,
            y=correlation_matrix.index,
            colorscale='RdBu',
            zmid=0,
            text=correlation_matrix.round(2).values,
            texttemplate='%{text}',
            textfont={"size": 10},
            hovertemplate='X: %{x}<br>Y: %{y}<br>相关性: %{z:.3f}<extra></extra>'
        ))
        
        fig.update_layout(
            title=title,
            template=self.theme,
            width=600,
            height=600
        )
        
        return fig
    
    def create_live_chart(self, data: pd.DataFrame, 
                         update_callback: Optional[Callable] = None) -> go.Figure:
        """
        创建实时更新图表
        
        Args:
            data: 初始数据
            update_callback: 数据更新回调函数
            
        Returns:
            Plotly图表对象
        """
        fig = self.create_candlestick_chart(data, title="实时K线图")
        
        # 添加实时更新配置
        fig.update_layout(
            updatemenus=[
                dict(
                    type="buttons",
                    direction="left",
                    buttons=list([
                        dict(
                            args=[{"visible": [True, True]}],
                            label="播放",
                            method="restyle"
                        ),
                        dict(
                            args=[{"visible": [False, False]}],
                            label="暂停",
                            method="restyle"
                        )
                    ]),
                    pad={"r": 10, "t": 10},
                    showactive=True,
                    x=0.01,
                    xanchor="left",
                    y=1.02,
                    yanchor="top"
                ),
            ]
        )
        
        return fig
    
    def add_custom_indicator(self, fig: go.Figure, 
                           indicator_data: pd.Series,
                           indicator_name: str,
                           row: int = 1,
                           color: str = None) -> go.Figure:
        """
        添加自定义技术指标
        
        Args:
            fig: 现有图表
            indicator_data: 指标数据
            indicator_name: 指标名称
            row: 子图行号
            color: 线条颜色
            
        Returns:
            更新后的图表
        """
        if color is None:
            color = self.colors['ma']
        
        fig.add_trace(go.Scatter(
            x=indicator_data.index,
            y=indicator_data.values,
            mode='lines',
            name=indicator_name,
            line=dict(color=color, width=1),
            opacity=0.8
        ), row=row, col=1)
        
        return fig
