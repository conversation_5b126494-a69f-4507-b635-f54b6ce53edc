"""
技术指标基类

定义技术指标的通用接口和基础功能。
"""

import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union, List

from ..config import get_config
from ..utils.logger import get_logger
from ..utils.exceptions import IndicatorError, ValidationError


class BaseIndicator(ABC):
    """技术指标基类"""
    
    def __init__(self, name: str, **params):
        """
        初始化技术指标
        
        Args:
            name: 指标名称
            **params: 指标参数
        """
        self.name = name
        self.params = params
        self.logger = get_logger(f"indicators.{self.__class__.__name__}")
        self.config = get_config()
        
        # 验证参数
        self._validate_params(**params)
        
        # 缓存计算结果
        self._cache: Dict[str, pd.Series] = {}
        self._cache_enabled = self.config.get('system.cache_enabled', True)
    
    @abstractmethod
    def _validate_params(self, **params) -> None:
        """
        验证指标参数
        
        Args:
            **params: 指标参数
        """
        pass
    
    @abstractmethod
    def calculate(self, data: pd.DataFrame, **kwargs) -> Union[pd.Series, pd.DataFrame]:
        """
        计算技术指标
        
        Args:
            data: 价格数据
            **kwargs: 额外参数
            
        Returns:
            指标值
        """
        pass
    
    def _get_cache_key(self, data: pd.DataFrame, **kwargs) -> str:
        """
        生成缓存键
        
        Args:
            data: 价格数据
            **kwargs: 额外参数
            
        Returns:
            缓存键
        """
        # 使用数据的哈希值和参数生成缓存键
        data_hash = hash(tuple(data.index.tolist() + data.values.flatten().tolist()))
        params_str = str(sorted(self.params.items())) + str(sorted(kwargs.items()))
        return f"{self.name}_{data_hash}_{hash(params_str)}"
    
    def _validate_data(self, data: pd.DataFrame, required_columns: List[str] = None) -> None:
        """
        验证输入数据
        
        Args:
            data: 价格数据
            required_columns: 必需的列
        """
        if data.empty:
            raise IndicatorError("输入数据为空", self.name, self.params)
        
        if required_columns:
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                raise IndicatorError(
                    f"缺少必需的列: {missing_columns}", 
                    self.name, 
                    self.params
                )
    
    def _check_minimum_periods(self, data: pd.DataFrame, min_periods: int) -> None:
        """
        检查最小周期数
        
        Args:
            data: 价格数据
            min_periods: 最小周期数
        """
        if len(data) < min_periods:
            raise IndicatorError(
                f"数据长度 {len(data)} 小于最小周期数 {min_periods}",
                self.name,
                self.params
            )
    
    def __call__(self, data: pd.DataFrame, **kwargs) -> Union[pd.Series, pd.DataFrame]:
        """
        调用指标计算
        
        Args:
            data: 价格数据
            **kwargs: 额外参数
            
        Returns:
            指标值
        """
        try:
            # 检查缓存
            if self._cache_enabled:
                cache_key = self._get_cache_key(data, **kwargs)
                if cache_key in self._cache:
                    self.logger.debug(f"使用缓存的指标值: {self.name}")
                    return self._cache[cache_key]
            
            # 计算指标
            result = self.calculate(data, **kwargs)
            
            # 缓存结果
            if self._cache_enabled:
                self._cache[cache_key] = result
            
            return result
            
        except Exception as e:
            self.logger.error(f"计算指标 {self.name} 失败: {e}")
            raise IndicatorError(f"计算指标失败: {e}", self.name, self.params)
    
    def clear_cache(self) -> None:
        """清除缓存"""
        self._cache.clear()
        self.logger.debug(f"清除指标 {self.name} 的缓存")
    
    def get_info(self) -> Dict[str, Any]:
        """
        获取指标信息
        
        Returns:
            指标信息字典
        """
        return {
            'name': self.name,
            'class': self.__class__.__name__,
            'params': self.params,
            'cache_size': len(self._cache)
        }
    
    def __repr__(self) -> str:
        """返回指标的字符串表示"""
        params_str = ', '.join([f"{k}={v}" for k, v in self.params.items()])
        return f"{self.__class__.__name__}({params_str})"


class MovingAverageBase(BaseIndicator):
    """移动平均线基类"""
    
    def __init__(self, window: int, **kwargs):
        """
        初始化移动平均线
        
        Args:
            window: 窗口大小
            **kwargs: 其他参数
        """
        super().__init__(name=f"MA{window}", window=window, **kwargs)
        self.window = window
    
    def _validate_params(self, **params) -> None:
        """验证参数"""
        window = params.get('window', self.window)
        
        if not isinstance(window, int) or window <= 0:
            raise ValidationError(
                "window必须是正整数", 
                "window", 
                window, 
                "positive integer"
            )


class OscillatorBase(BaseIndicator):
    """震荡指标基类"""
    
    def __init__(self, name: str, period: int, **kwargs):
        """
        初始化震荡指标
        
        Args:
            name: 指标名称
            period: 计算周期
            **kwargs: 其他参数
        """
        super().__init__(name=name, period=period, **kwargs)
        self.period = period
    
    def _validate_params(self, **params) -> None:
        """验证参数"""
        period = params.get('period', self.period)
        
        if not isinstance(period, int) or period <= 0:
            raise ValidationError(
                "period必须是正整数",
                "period", 
                period, 
                "positive integer"
            )


class VolumeIndicatorBase(BaseIndicator):
    """成交量指标基类"""
    
    def __init__(self, name: str, **kwargs):
        """
        初始化成交量指标
        
        Args:
            name: 指标名称
            **kwargs: 其他参数
        """
        super().__init__(name=name, **kwargs)
    
    def _validate_params(self, **params) -> None:
        """验证参数"""
        pass
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> Union[pd.Series, pd.DataFrame]:
        """计算成交量指标"""
        # 验证数据包含成交量列
        self._validate_data(data, required_columns=['volume'])
        return self._calculate_volume_indicator(data, **kwargs)
    
    @abstractmethod
    def _calculate_volume_indicator(self, data: pd.DataFrame, **kwargs) -> Union[pd.Series, pd.DataFrame]:
        """计算具体的成交量指标"""
        pass
