{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 机器学习策略演示\n", "\n", "本notebook演示如何使用机器学习方法构建一个简单的交易策略。主要步骤包括：\n", "\n", "1. 数据获取与预处理\n", "2. 简单因子/特征构造\n", "3. 目标变量（下期收益）的定义\n", "4. 训练简单的线性回归模型\n", "5. 策略回测\n", "6. 使用Backtrader进行回测"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 0. 导入依赖包"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import yfinance as yf\n", "import numpy as np\n", "import pandas as pd\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_squared_error\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "\n", "# 设置显示选项\n", "pd.set_option('display.float_format', lambda x: '%.4f' % x)\n", "plt.style.use('seaborn')\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']\n", "plt.rcParams['axes.unicode_minus'] = False"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 数据获取与预处理\n", "\n", "我们获取TSLA过去5年的日线数据。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 设定时间范围（从现在往前推5年）\n", "end_date = datetime.now()\n", "start_date = end_date - <PERSON><PERSON><PERSON>(days=5*365)\n", "\n", "print(f\"获取数据时间范围：{start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}\")\n", "\n", "# 下载特斯拉数据\n", "data = yf.download('TSLA', start=start_date, end=end_date)\n", "\n", "print(\"\\n数据概览：\")\n", "print(data.head())\n", "\n", "print(\"\\n数据基本信息：\")\n", "print(data.info())\n", "\n", "# 绘制收盘价走势图\n", "plt.figure(figsize=(15, 6))\n", "plt.plot(data.index, data['Close'], label='TSLA收盘价')\n", "plt.title('特斯拉股价走势')\n", "plt.xlabel('日期')\n", "plt.ylabel('价格（美元）')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 简单因子/特征构造\n", "\n", "构建两个简单的因子：\n", "1. 动量因子：过去5日涨跌幅\n", "2. 成交量比值：最近5日均量vs最近10日均量"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 复制数据\n", "df = data.copy()\n", "\n", "# 动量因子: 过去5日涨跌幅\n", "df['momentum_5'] = df['Close'] / df['Close'].shift(5) - 1\n", "\n", "# 成交量因子: (最近5日平均成交量) / (最近10日平均成交量) - 1\n", "df['vol_ratio'] = (df['Volume'].rolling(5).mean()) / (df['Volume'].rolling(10).mean()) - 1\n", "\n", "# 查看新添加的列\n", "print(\"因子数据预览：\")\n", "print(df[['Close', 'momentum_5', 'vol_ratio']].tail(10))\n", "\n", "# 绘制因子分布图\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "\n", "sns.histplot(df['momentum_5'].dropna(), bins=50, ax=ax1)\n", "ax1.set_title('动量因子分布')\n", "ax1.set_xlabel('5日动量')\n", "\n", "sns.histplot(df['vol_ratio'].dropna(), bins=50, ax=ax2)\n", "ax2.set_title('成交量比值分布')\n", "ax2.set_xlabel('成交量比值')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 目标变量的定义\n", "\n", "定义下期1日收益率作为目标变量。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 计算下期收益率\n", "df['future_ret_1d'] = df['Close'].pct_change().shift(-1)\n", "\n", "# 去掉NaN值\n", "df.dropna(inplace=True)\n", "\n", "print(\"添加目标变量后的数据预览：\")\n", "print(df[['Close', 'momentum_5', 'vol_ratio', 'future_ret_1d']].head(10))\n", "\n", "# 绘制目标变量分布\n", "plt.figure(figsize=(10, 5))\n", "sns.histplot(df['future_ret_1d'], bins=50)\n", "plt.title('下期收益率分布')\n", "plt.xlabel('收益率')\n", "plt.show()\n", "\n", "# 计算因子与目标变量的相关性\n", "corr = df[['momentum_5', 'vol_ratio', 'future_ret_1d']].corr()\n", "\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(corr, annot=True, cmap='coolwarm', center=0)\n", "plt.title('因子与目标变量相关性')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 划分训练集与测试集\n", "\n", "按照时间顺序，使用前80%的数据作为训练集，后20%作为测试集。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 计算分割点\n", "split_idx = int(len(df) * 0.8)\n", "split_date = df.index[split_idx]\n", "\n", "train_data = df.iloc[:split_idx].copy()\n", "test_data = df.iloc[split_idx:].copy()\n", "\n", "print(\"训练集范围:\", train_data.index.min(), \"→\", train_data.index.max())\n", "print(\"测试集范围:\", test_data.index.min(), \"→\", test_data.index.max())\n", "print(\"\\n训练集样本数:\", len(train_data))\n", "print(\"测试集样本数:\", len(test_data))\n", "\n", "# 可视化训练集和测试集的划分\n", "plt.figure(figsize=(15, 6))\n", "plt.plot(train_data.index, train_data['Close'], label='训练集', color='blue')\n", "plt.plot(test_data.index, test_data['Close'], label='测试集', color='red')\n", "plt.axvline(split_date, color='black', linestyle='--', label='划分点')\n", "plt.title('训练集和测试集划分')\n", "plt.xlabel('日期')\n", "plt.ylabel('价格（美元）')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 训练线性回归模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 准备特征和目标变量\n", "features = ['momentum_5', 'vol_ratio']\n", "X_train = train_data[features].values\n", "y_train = train_data['future_ret_1d'].values\n", "\n", "X_test = test_data[features].values\n", "y_test = test_data['future_ret_1d'].values\n", "\n", "# 训练模型\n", "model = LinearRegression()\n", "model.fit(X_train, y_train)\n", "\n", "# 预测\n", "y_pred_train = model.predict(X_train)\n", "y_pred_test = model.predict(X_test)\n", "\n", "# 评估\n", "train_mse = mean_squared_error(y_train, y_pred_train)\n", "test_mse = mean_squared_error(y_test, y_pred_test)\n", "\n", "print(\"模型评估：\")\n", "print(f\"训练集MSE: {train_mse:.8f}\")\n", "print(f\"测试集MSE:  {test_mse:.8f}\")\n", "print(\"\\n模型参数：\")\n", "for feature, coef in zip(features, model.coef_):\n", "    print(f\"{feature}: {coef:.6f}\")\n", "print(f\"截距: {model.intercept_:.6f}\")\n", "\n", "# 绘制预测值vs实际值的散点图\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "\n", "ax1.scatter(y_train, y_pred_train, alpha=0.5)\n", "ax1.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--')\n", "ax1.set_title('训练集：预测值 vs 实际值')\n", "ax1.set_xlabel('实际收益率')\n", "ax1.set_ylabel('预测收益率')\n", "\n", "ax2.scatter(y_test, y_pred_test, alpha=0.5)\n", "ax2.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--')\n", "ax2.set_title('测试集：预测值 vs 实际值')\n", "ax2.set_xlabel('实际收益率')\n", "ax2.set_ylabel('预测收益率')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 策略回测\n", "\n", "在测试集上进行简单的策略回测。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 在测试集上生成预测\n", "test_data['pred_ret_1d'] = model.predict(test_data[features])\n", "\n", "# 生成交易信号\n", "test_data['signal'] = (test_data['pred_ret_1d'] > 0).astype(int)\n", "\n", "# 计算策略收益\n", "test_data['strategy_ret'] = test_data['signal'] * test_data['future_ret_1d']\n", "\n", "# 计算累积收益\n", "test_data['strategy_cum'] = (1 + test_data['strategy_ret']).cumprod()\n", "test_data['buy_and_hold'] = test_data['Close'] / test_data['Close'].iloc[0]\n", "\n", "# 计算策略评估指标\n", "strategy_return = test_data['strategy_cum'].iloc[-1] - 1\n", "bh_return = test_data['buy_and_hold'].iloc[-1] - 1\n", "\n", "strategy_sharpe = np.sqrt(252) * test_data['strategy_ret'].mean() / test_data['strategy_ret'].std()\n", "bh_sharpe = np.sqrt(252) * test_data['Close'].pct_change().mean() / test_data['Close'].pct_change().std()\n", "\n", "strategy_drawdown = (test_data['strategy_cum'] / test_data['strategy_cum'].cummax() - 1).min()\n", "bh_drawdown = (test_data['buy_and_hold'] / test_data['buy_and_hold'].cummax() - 1).min()\n", "\n", "print(\"策略评估指标：\")\n", "print(f\"策略总收益率: {strategy_return:.2%}\")\n", "print(f\"买入持有收益率: {bh_return:.2%}\")\n", "print(f\"\\n策略夏普比率: {strategy_sharpe:.2f}\")\n", "print(f\"买入持有夏普比率: {bh_sharpe:.2f}\")\n", "print(f\"\\n策略最大回撤: {strategy_drawdown:.2%}\")\n", "print(f\"买入持有最大回撤: {bh_drawdown:.2%}\")\n", "\n", "# 绘制策略收益曲线\n", "plt.figure(figsize=(15, 6))\n", "plt.plot(test_data.index, test_data['strategy_cum'], label='策略收益')\n", "plt.plot(test_data.index, test_data['buy_and_hold'], label='买入持有')\n", "plt.title('策略收益 vs 买入持有')\n", "plt.xlabel('日期')\n", "plt.ylabel('累积收益')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()\n", "\n", "# 绘制月度收益热力图\n", "monthly_returns = test_data['strategy_ret'].groupby([test_data.index.year, test_data.index.month]).mean()\n", "monthly_returns = monthly_returns.unstack()\n", "\n", "plt.figure(figsize=(12, 6))\n", "sns.heatmap(monthly_returns, annot=True, fmt='.2%', cmap='RdYlGn', center=0)\n", "plt.title('策略月度收益热力图')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 使用Backtrader进行回测"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import backtrader as bt\n", "\n", "class MLFactorStrategy(bt.Strategy):\n", "    params = (\n", "        ('model', None),  # 预训练的模型\n", "    )\n", "\n", "    def __init__(self):\n", "        self.model = self.p.model\n", "        self.momentum_5 = bt.indicators.PercentChange(self.data.close, period=5)\n", "        self.vol_5 = bt.indicators.SMA(self.data.volume, period=5)\n", "        self.vol_10 = bt.indicators.SMA(self.data.volume, period=10)\n", "        \n", "    def next(self):\n", "        # 计算因子值\n", "        momentum = self.momentum_5[0]\n", "        vol_ratio = (self.vol_5[0] / self.vol_10[0]) - 1 if self.vol_10[0] != 0 else 0\n", "        \n", "        # 构建特征向量\n", "        X = [[momentum, vol_ratio]]\n", "        pred_ret = self.model.predict(X)[0]\n", "        \n", "        # 交易逻辑\n", "        if pred_ret > 0:\n", "            if not self.position:\n", "                self.buy()\n", "        else:\n", "            if self.position:\n", "                self.close()\n", "\n", "# 准备数据\n", "data = bt.feeds.PandasData(\n", "    dataname=test_data,\n", "    datetime=None,  # 使用索引作为日期\n", "    open=0,\n", "    high=1,\n", "    low=2,\n", "    close=3,\n", "    volume=5,\n", "    openinterest=-1\n", ")\n", "\n", "# 初始化cerebro\n", "cerebro = bt.<PERSON><PERSON><PERSON>()\n", "cerebro.adddata(data)\n", "cerebro.addstrategy(MLFactorStrategy, model=model)\n", "\n", "# 设置初始资金和手续费\n", "initial_cash = 100000.0\n", "cerebro.broker.setcash(initial_cash)\n", "cerebro.broker.setcommission(commission=0.001)\n", "\n", "# 添加分析器\n", "cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name='sharpe')\n", "cerebro.addanalyzer(bt.analyzers.DrawDown, _name='drawdown')\n", "cerebro.addanalyzer(bt.analyzers.Returns, _name='returns')\n", "\n", "# 运行回测\n", "print(f\"初始资金: ${initial_cash:.2f}\")\n", "results = cerebro.run()\n", "final_value = cerebro.broker.getvalue()\n", "print(f\"最终资金: ${final_value:.2f}\")\n", "print(f\"总收益率: {(final_value/initial_cash - 1):.2%}\")\n", "\n", "# 获取分析结果\n", "strat = results[0]\n", "print(f\"\\n夏普比率: {strat.analyzers.sharpe.get_analysis()['sharperatio']:.2f}\")\n", "print(f\"最大回撤: {strat.analyzers.drawdown.get_analysis()['max']['drawdown']:.2%}\")\n", "\n", "# 绘制回测结果\n", "cerebro.plot(style='candlestick')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}