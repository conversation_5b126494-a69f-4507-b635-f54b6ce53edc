{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 机器学习策略演示\n", "\n", "本notebook演示如何使用机器学习方法构建一个简单的交易策略。主要步骤包括：\n", "\n", "1. 数据获取与预处理\n", "2. 简单因子/特征构造\n", "3. 目标变量（下期收益）的定义\n", "4. 训练简单的线性回归模型\n", "5. 策略回测\n", "6. 使用Backtrader进行回测"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 0. 导入依赖包"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["import yfinance as yf\n", "import numpy as np\n", "import pandas as pd\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_squared_error\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "import os\n", "import talib  # 如果报错找不到ta-lib，需先安装并确认本地编译环境\n", "import sys\n", "\n", "from dotenv import load_dotenv, find_dotenv\n", "# Find the .env file in the parent directory\n", "dotenv_path = find_dotenv(\"../../.env\")\n", "# Load it explicitly\n", "load_dotenv(dotenv_path)\n", "\n", "# Add the parent directory to the sys.path list\n", "sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))\n", "\n", "from data_processing import load_data_year, flatten_yf_columns, standardize_columns\n", "from plotting import plot_results\n", "from strategy.buy_and_hold import BuyAndHoldStrategy\n", "from back_test import run_backtest\n", "\n", "\n", "# 设置显示选项\n", "pd.set_option('display.float_format', lambda x: '%.4f' % x)\n", "# 绘图风格（可选）\n", "plt.style.use('seaborn-v0_8-bright')\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['PingFang HK']\n", "plt.rcParams['axes.unicode_minus'] = False"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 数据获取与预处理\n", "\n", "我们获取TSLA过去5年的日线数据。"]}, {"cell_type": "code", "execution_count": 53, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["获取数据时间范围：2020-03-11 到 2025-03-10\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[*********************100%***********************]  1 of 1 completed\n"]}], "source": ["# 设定时间范围（从现在往前推5年）\n", "end_date = datetime.now()\n", "start_date = end_date - <PERSON><PERSON><PERSON>(days=5*365)\n", "\n", "print(f\"获取数据时间范围：{start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}\")\n", "\n", "# 下载特斯拉数据\n", "ticker = 'TSLA'\n", "data = yf.download(ticker, start=start_date, end=end_date)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.2 数据预处理"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["data = flatten_yf_columns(data)\n", "data = standardize_columns(data)"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "DatetimeIndex: 1255 entries, 2020-03-11 to 2025-03-07\n", "Data columns (total 5 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   close   1255 non-null   float64\n", " 1   high    1255 non-null   float64\n", " 2   low     1255 non-null   float64\n", " 3   open    1255 non-null   float64\n", " 4   volume  1255 non-null   int64  \n", "dtypes: float64(4), int64(1)\n", "memory usage: 58.8 KB\n", "None\n", "             close    high     low    open     volume\n", "Date                                                 \n", "2020-03-11 42.2820 43.5720 40.8667 42.6800  199837500\n", "2020-03-12 37.3700 39.6333 36.4167 38.7260  283636500\n", "2020-03-13 36.4413 40.5047 33.4667 39.6667  339604500\n", "2020-03-16 29.6713 32.9913 29.4780 31.3000  307342500\n", "2020-03-17 28.6800 31.4567 26.4000 29.3340  359919000\n", "2020-03-18 24.0813 26.9907 23.3673 25.9333  356793000\n", "2020-03-19 28.5093 30.1333 23.8973 24.9800  452932500\n", "2020-03-20 28.5020 31.8000 28.3860 29.2133  424282500\n", "2020-03-23 28.9527 29.4667 27.3667 28.9067  246817500\n", "2020-03-24 33.6667 34.2460 31.6000 31.8200  343428000\n", "              close     high      low     open     volume\n", "Date                                                     \n", "2025-02-24 330.5300 342.4000 324.7000 338.1400   76052300\n", "2025-02-25 302.8000 328.8900 297.2500 327.0200  134228800\n", "2025-02-26 290.8000 309.0000 288.0400 303.7100  100118300\n", "2025-02-27 281.9500 297.2300 280.8800 291.1600  101748200\n", "2025-02-28 292.9800 293.8800 273.6000 279.5000  115697000\n", "2025-03-03 284.6500 303.9400 277.3000 300.3400  115551400\n", "2025-03-04 272.0400 284.3500 261.8400 270.9300  126706600\n", "2025-03-05 279.1000 279.5500 267.7100 272.9200   94042900\n", "2025-03-06 263.4500 272.6500 260.0200 272.0600   98451600\n", "2025-03-07 262.6700 266.2500 250.7300 259.3200  102193000\n", "2020-03-11 00:00:00\n", "2025-03-07 00:00:00\n"]}], "source": ["print(data.info())              # 看看总共有多少行、列，各字段数据类型\n", "print(data.head(10))           # 查看前10行，确认最早日期\n", "print(data.tail(10))           # 查看后10行，确认最晚日期\n", "print(data.index.min())  # DataFrame中最早的日期\n", "print(data.index.max())  # DataFrame中最晚的日期\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 加入更多技术指标\n", "\n", "构建两个简单的因子：\n", "1. 动量因子：过去5日涨跌幅\n", "2. 成交量比值：最近5日均量vs最近10日均量\n", "3. 先举几个常用指标的例子：RSI, MACD, 布林带。"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              close  momentum_5  vol_ratio  RSI_14  BB_upper  BB_lower\n", "Date                                                                  \n", "2025-03-03 284.6500     -0.1388     0.2863 28.8091  406.5409  274.2691\n", "2025-03-04 272.0400     -0.1016     0.1697 26.4700  404.1738  265.4722\n", "2025-03-05 279.1000     -0.0402     0.1253 29.9017  397.3141  261.0209\n", "2025-03-06 263.4500     -0.0656     0.0620 26.9042  393.4019  253.4611\n", "2025-03-07 262.6700     -0.1035     0.0085 26.7602  388.5007  247.1973\n"]}], "source": ["# 复制数据\n", "df = data.copy()\n", "\n", "# 动量因子: 过去5日涨跌幅\n", "df['momentum_5'] = df['close'] / df['close'].shift(5) - 1\n", "\n", "# 成交量因子: (最近5日平均成交量) / (最近10日平均成交量) - 1\n", "df['vol_ratio'] = (df['volume'].rolling(5).mean()) / (df['volume'].rolling(10).mean()) - 1\n", "\n", "# 计算RSI (默认周期14)\n", "df['RSI_14'] = talib.RSI(df['close'], timeperiod=14)\n", "\n", "# 布林带\n", "upper, middle, lower = talib.BBANDS(\n", "    df['close'],\n", "    timeperiod=20,\n", "    nbdevup=2,\n", "    nbdevdn=2,\n", "    matype=0\n", ")\n", "df['BB_upper'] = upper\n", "df['BB_middle'] = middle\n", "df['BB_lower'] = lower\n", "\n", "# 也可以增加其他指标，比如ATR, CCI等，根据需要添加\n", "df.dropna(inplace=True)  # 丢掉因子无法计算的前几行\n", "\n", "# factors = ['momentum_5', 'vol_ratio' ,'RSI_14','MACD','MACD_signal','BB_upper','BB_lower']\n", "factors = ['momentum_5', 'vol_ratio' ,'RSI_14','BB_upper','BB_lower']\n", "# 看看加上技术指标后的DataFrame\n", "print(df[['close'] + factors].tail(5))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 目标变量的定义\n", "\n", "定义下期1日收益率作为目标变量。"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["添加目标变量后的数据预览：\n", "             close  momentum_5  vol_ratio  RSI_14  BB_upper  BB_lower\n", "Date                                                                 \n", "2020-04-07 36.3633      0.0409     0.0341 45.4660   40.9203   24.7995\n", "2020-04-08 36.5893      0.1397     0.0800 45.8690   39.6232   25.5274\n", "2020-04-09 38.2000      0.2608     0.0272 48.7744   39.7858   25.4478\n", "2020-04-13 43.3967      0.3561    -0.0236 56.8260   41.4042   24.5249\n", "2020-04-14 47.3260      0.3751     0.0475 61.7249   44.2006   23.4940\n", "2020-04-15 48.6553      0.3380     0.0749 63.2444   46.7505   22.9416\n", "2020-04-16 49.6807      0.3578     0.1159 64.4177   48.6161   23.6360\n", "2020-04-17 50.2593      0.3157     0.1495 65.0949   50.6157   23.8114\n", "2020-04-20 49.7573      0.1466     0.1144 63.9577   52.1107   24.4419\n", "2020-04-21 45.7813     -0.0326    -0.0262 55.6629   52.6247   25.6108\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAngAAAIPCAYAAADgsKNDAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8ekN5oAAAACXBIWXMAAA9hAAAPYQGoP6dpAACtaklEQVR4nOzdBXRURxsG4DfuTgQPCe7uLi0OhWLFtcXd3b24uxQp7rQUd4JDcJcQIe5G/jOTP2E3u4EQEpJs3uece8jeO7u5e9nsfvvNfDNaMTExMSAiIiIijaGd1idARERERCmLAR4RERGRhmGAR0RERKRhGOARERERaRgGeEREREQahgEeERERkYZhgEdERESkYRjgERH9YNHR0QgPD0/r0yAiDcYAjygD2LFjBxwdHdP8HAoWLJiij7llyxb06dPnm87ByMgIFStW/OJma2uLYcOGqX2M3bt3I2/evN997i4uLrh9+7baY/Pnz8f58+e/+LxLlCiR6PGyZcti4cKF332ORJR5McAj+oKNGzdCS0vrm7ePHz/GP8agQYOSfL/U+lD/ludRtGjRJD2mu7v7Fx9n3Lhx8W3d3Nzw7Nkzlc3Q0BCrV6/GzZs31R738PBQ+b0i0P3vv/++uP30009JvjZnzpz54vNILFDcsGED2rZti9DQUKX9L168wMiRI2FiYpLkcxDXslGjRuDCQkSUUnRT7JGINJTI9hw7dixJbd+9e4datWop7Rs7diz69euX6H0iIiIwYMAAPHr0CA0aNEi0XWRkpGyTFPny5YOOjo5KYPS157Fv3z789ddfSfodWbJkwdOnTxM9bmVlFf9zr169cOTIkUTblilTRu3+zp07y+A0YQAlsnRf+3/o0aMHksrGxgZXrlxR2T9ixIhE7zNnzhwUL14c48ePx7x58+L3z507V74GSpcuneTf//LlS1y9elUGlEREKYEBHtFXGBgYqHTpBQUFYdKkSZg8ebJSpkZXV/VPSnQXik0dHx8f/Prrr3j79i0uXLjwxW5YkQUrVKhQks7Z19cXlpaWKs/ja12sWbNmVdnn7++PDx8+4P3793LcmAgyReZNnGtSuzoPHDgQn50S/4rMXM6cOWVgkyNHDty/fx+lSpVCYGAg9PX1oa0d27mgLuARwato/yVdunRR2Tdt2jR5jUW2TFzL+vXry/3Dhw+X/2/qnou5ubnKvtevX8tgWxBZyp07d+LJkyfynMW1Epk9kYkVGUhB/D+IYDgumA8ODla6LnXr1pWPaWZmFh/Ai6yguEaKAb26oJ2IKDEM8IiSQXTBinFWU6ZMSfZjiK5EEYiIoO3SpUuJBoFxcufOjVevXuFHE1m9rl27xt8W51uhQgW1Ga/EiMDk06dPmDBhgrx2IjgWRGAVt8Xd7tmzJ6ytrWWGLGHALLKCIrj8WgZPFDG0b99eaV/JkiVhamqKkydPygxfXID3rVmzn3/+GY8fP1baV6BAAaXbvXv3jv9ZdNfOmjUrvjt46dKlaNGihbz9999/IywsDJcvX5b/t4oB/IMHD+Lvl1jQTkSUGAZ4REkgPoRF5ieOyKyIwEBkWZLTrXbu3DkZKIwZM0YGOz8iMyOCkqSca5EiRZRuiyBUbKIrcteuXfFZpefPn391zJidnZ3MgomAShRTiEDm8OHDX7yPCAKbNm2KGzduyADIwcEBDx8+xNmzZ+Xxb+l6XblypSxmqFSpEho3biz3ia5ikUn75ZdfZNAsgq4v/T8OHTpU6ba67KEI1ps3by4zuwkpPq64ZuJ3xmnZsqUsuBDXdO3atejevXt8kUWHDh3k+E0iouRggEeUBCJblXBsneDk5BT/swjS4rrlviYgIEBmo6ZOnZrkcxBZKcUgMzH29vZqgxVxrnFBUmJEQLV+/XqV/SKQE8dEl7Kfn5/MJIlA8GtTfYjuShGoiG7IXLlyycpT0bX7pefh7OwsA8GOHTuiVatWMhgWz10E2ckR150qiEBOBHSiG7hZs2byZ0F0oYpzS0hdgYW6bnhxXcRjqDumKGGAJ7KI27dvl9dDFGwQEaUUBnhESVCzZk2lbJUonBCZHDGGSpFiF6oYTybGrakjxtxFRUUlWjQhgiFjY2OlfSILpm6MXEIiGFIX4Onp6cnxbl8iukbVuXfvniyoEAGMGDMoijUSBlwi0yayUHGZMkWnTp2S5x53XmLcoriGYtyZILqnxe24TKbI+omuYdEtGVfZKzaR0RLj6JJCZA+PHj2qtG/z5s0yUBeFGiJ7JjbRhSp+r7rxj6JLNynE/0327Nm/2k78XtG9HTcOT/wfi4pfcd9vqbolIvoaBnhE30gEUHv27JGZLJHZS2w8mBjrJboBvySxoonTp0/LoDKOyO58LcMjxvGJbt+4AoWUtGjRIpltEgGKKI4QY8zWrFkju1stLCxQvXr1+LYiEya6V//444/4fdmyZZOVxMuWLVN63OnTp6u9LbpVxRxzorpVkciQJuy6FFk2EWCJOfLiiMycaKfIy8sLS5YskYUx4veMHj1aFkV8S/e46DoVzy0xiXXzTpw4UXbFiy5YkfmMyxwKYsyhyOwpBvsJiyxEgUyePHmSfJ5ERAzwiL6RCGxE9k0Mlhdzly1evFhlQL8gxmQlNkZNBEZiXJvifHnfS4z/+lLGKblj8G7duiUzlWL6j9mzZ8tgT2ShRMAkupj79u2rFOB5enrKYEZMjaIYbIqCATHtiLhm1apVk4FWwvMRY/xEwKZYXJCQ+P1isuI4ohJXdBVXrVo1fp8I3BISFa8iSI7LGopspJje5FvG4InfmzBzKTK14vmLDGX+/PnVzmUYV0Urnrs6M2fOxLp165T2KRZZxAW8RERJxQCP6BuIzN2QIUNw4sQJVKlSRXatiS5JEdQMHjw4xX+f6FJNrJs34VxxIsCLC14SG9v2te5NMeWHCGAViS5MEcSJFSTiuk/FnG2im1FMUJxw7j7RBSqym+KYyHjFEcGn2P755x957USXsaiUFdkpETCLYEZMZSLG+sVVuKojgiTRTRxnxYoVMtgUxRlxRDCkODedILqYRbev+L9TF4CJQDahhP+n6rpxxXMQXeoiaC9WrJgMAEXF7rcQXdtii8MiCyL6XgzwiJJABE/iw14MiBcrL4gARRDVmaKCskaNGrKrT1R/piSRXRLBz5eILlJBBFxfCvBExkqMk0vKYykSk/mKrJriRMUiyNu0aZMMRMRYN0ViXJkI8kR7xQBPsbtWdOOKIgoxwbGoJBbBmOiWFMUVYj68xPTv31+OX1QsZhHj9EQWTXGfCCRF4KVo7969ap+fIP7v1I1P/Nq4ODE2UWTfRDZXzFMngmER8ItiFhFQExGlFQZ4REkgxp6JDJDI8ogPckUiIBHBg+hebNKkSYr+3m9ZM1UEG4pVvSlFdMsmHKcmphkRQZli1ixhlk0UNIixZ+qIuexEgCcyg6J7W1S1iilNRBdnYsTkxCKASkhkOEWlrAiyEhLdpnHVzyk9h9zBgwfl/IBinGW7du3kPtE1LQJQ0QUtvgioKzghIvoRGOARfYUY3yWyUnFdaOqmQhFdd2JFBLEiQVoQY/1E12PcPGopOQZPXRGCmLtNFJuIIE0d0W0rsm1i+g+RNRQZUBEcX79+HRcvXpTVrSL4EtdMVLKKKltRACEKMUSQLMbTiaW+xNizuCyayPypG4emrsjiW4ksqbr/VzGdTdz4ubh2YjyiGGcnso0ioBPFGoorb4jXyZ9//iknMxZTsYigVF0mk4goNTHAI/oK8cGfMGuXHCKzIzJNImASY+GSOgWHOmKKFjGGTTzWmzdv5Lg5EcDVq1cv0fuk5Fq0IggURRcisBFBnBh3JsbdxU3tIronReAmuk9FgCcyWdeuXUP58uVl97bI3Ilu7bjASKwEISpzRRB4/PhxeZ5iYmXxeKKIQmT4UpO3t3ei/8dxRRYioC1XrpyseBWBtMhQqqtsFc9JBJ1i7j8x9Yu4j5icOeHYxjhinJ0oHElIVOsqjgGMq8QlIkoKBnhESQhmvrb2qeI8eIlNZyGWNYvLAooxbIl1XyaFyJzduXNHZu5EACWyXSIzlnDJrG9di1aMQ0tKMCWmhhGbCFjF8xUBngjc4sYmCoqVrqILW4wPFEHpl4ju7rgxeGJpMxF4ifPZtm2bXAlDHTF2T7RNrAtWBFCKgZEIwBI+RzE5tLrJl7t16xYfhIpgWgRpIhBMbCyfIlFoIcYhimKTL01dIzKAilPKJEYxk0hE9DVaMV9ba4iI0i0R2KTGvHdERJSx8ZOBKANjcEdElD7s27dP9pQormiUcNiFGLohlqkU/4rsfmripwMRERFRMn348EGOyxXjdcUMA+qIQrCGDRvKscmurq5yInxxW+xPLQzwiIiIiJJJLFkpZgoQ46ATI8ZfiwncRTFZ3NKNtWvXVlnBJiUxwCMiIiJKJpGNE6sAiamfEnPo0CHZTpGYSknMp5laWEVLREREpGYOVLEpEmPsxKYoKfOLiq7YhDMsiInpv7YUpUYEeEf0Ep/egZIu3yPVdTYpefSgfiwFfTuzUK+0PgWNcLt+r7Q+BY1haPPlKXso6areSd1igbSKHVzGtsPkyZOV9iV3PkofHx+VuU/F1FFiv8YHeERERETpxejRozFkyBClfQmzd0llbW0tx+kpEmtoi/2phQEeERERUQLqumOTS0wiL1blEROgK65xnj17dqQWFlkQERERpSKxXKNYx1rR/v375drbqYUBHhEREWVIWnpaqbZ9D7E2uJgGxc3NTd4W61GLNbZXrFgh9y1duhQnTpyQ+1MLAzwiIiKiFBQQEICnT58iODhY3s6ZMyeOHj0q1+guXLiwzN6JgE903aYWjsEjIiIiSgExMTHyX0dHR7x9+1bpWJkyZXDy5En8KAzwiIiIKEPS1v2+rlRNxi5aIiIiIg3DDB4RERFlSFp6zFMlhleGiIiISMMwwCMiIiLSMAzwiIiIiDQMx+ARERFRhsQq2sQxg0dERESkYZjBIyIiogzpe5cU02TM4BERERFpGAZ4RERERBqGXbRERESUIbHIInHM4BERERFpGAZ4RERERBqGAR4RERGRhuEYPCIiIsqQOE1K4pjBIyIiItIwyQ7woqKisGfPHkyfPh1BQUG4ePEiwsPDU/bsiIiIiL5QRZtaW6YM8N6+fYv8+fNj6dKlmDFjBkJCQmSgV6JECXmMiIiIiDJYgNe3b18MHDgQp0+fhrm5udx38OBBNGjQAIMGDUrpcyQiIiKi1C6yuHTpEjZt2qT8QLq6GDNmDAoUKJCchyQiIiL6Jlo6Gb8rNV1l8CwtLREQEKCy38fHRx4jIiIiogwW4HXu3Fl20YogT0tLS24vXrxAz5490bFjx5Q/SyIiIqIEtHW0Um3LlAHeuHHjZEFF7ty54eHhIQsuihQpgooVK2LChAkpf5ZERERElLpj8ETGbvLkyRg1ahSePn0qbzs7O8PY2Dg5D0dERERE6WElizNnziBLliwoXrw4/v33X0ycOBFOTk4ygxdXWatp7JvVRam/FuBskfoIff0+rU8n3QgMDMDypYtx985t2NrZofkvLVGzVp1E20dERGD1ymVwuXYFJqZm+OnnBvI+im7evI5NG9bh/bt3yJ49Ozp364HSpcvGH3/18iXWrFqOp08fw8zMHHXr/YzWbX+Djo4OMqKTp05h3/798PL0QsmSJdGvbx+YmZmpbXv9xg1s27Ydb96+ReFChdCnT2842NsrzVE5/88F+OjtjbmzZ8XvF1/GVqxaLYdT2NpmQbu2bVG7Vi1oun/OXsLOw//A46MPyhQrhGG9OsPc1ERt23uPnmLY9D8xc+QAlC5aKP56rtm+FycuXEVIWBjKFS+CQd1+g41V5hlv7NCiMXL16AjD7A7wuXAVj8ZMQ5S/6jhsaGkhV8+OyNGxNfSsrOB/6y4ej52O0Dfv4pvUeuYCHUNDpbsFP3uJyzWbIrOwbdQQ2Tu0h4GDA/yuXcOzaTMQHRio2lBLC9k7tofDr79Cz8oSAXfv4fmMWQh///nzR0tXF/mmTIKBnR3u9ej1Y58IaV4X7cKFC9G8eXP4+vrKwoq2bduifPnyuHPnDvr37w9NY+Bgi+Krp6PwnFHQMdBP69NJVz59+oSpkyfAxNQUCxYtQ6cu3bBm1QrcuO6S6H0WLZgHPz8/zJg1H/0HDMaRQwfwz/Gj8cefP3+GWdOnot1vHbFxy3b81qET5syajjdvXsvj/v7+GD92BCpVqYJ1G7di7PhJOH/+LA7s24OMyMXlOtasXYduXbpi0aKFMDA0wKQpU+W1Tej58+eYNXsOmjdvhpXLl8kvVWPHjZdzUQo3bt7EoCFDcN/VVel+3t7eGDlqNH7+qR62bNqI/v36yd9589YtaLLLN+9iyaYd+KNDK6ydPQFGBgYYOWuRyrUNDgnF4g3bMW7+MkQnOLZsy9+48/AJFk0cjm2LZsDCzBTj5y9HTEwMMgObWlWRb/wwPJu5ENcatUN0aChKrF8sg4+EcvfuimxtW+B+v5G4VL0xfC9eRZm9G6FrHvtlRcfYCFraOjjpWAonc5eM367U+SUNnlnasKpSGXmGDsarRYtxu30HeT0LL16o9npm79IJ9s2b4/HoMbjetDn8r7mg+MZ10DEzlcctK1VEiS2bYF66FDIrLW2tVNsyZYC3ePFi7Nu3D9WqVcNff/2F3377TXbXzp49G8ePH4emsaxQAjomxrhUo11an0q643r/Hvx8ffH7H31hZ28vs2wdO3fFnt071bZ3//ABN264YNDgYcieIwcKFS6C3v0G4O+d2+M/MPft2YVGjZuiYqXKMDU1RYWKlVG5clWcP3dGHj97+iTy5suPxk2ay+ydk3NetG7zGy5duoCM6O/du9GtSxeULl1KZuL69e2Ljx8/qgRpwr4DB9CsaRNUr1ZNZtA7d+oIC3NzXLx0SR4/c+YsatWsiY7t2yvd79DhwyhXvhx+/uknmRksXqwYmjRujFOnTkOT/bX/CHp3aIXyJYoiq50thvbqBC9vHxmwKXrj5o5X795j8aSRMoCLExIahr3HT2J03+7Imc0B1pYWMnv38PlLfPD8iMzAsU83Gdz5nL+MsHduMntnmNUelhXKqGnbHQ9HTkbAHVdE+vji9cqNCLh9H1l/bSKP69lYy/0xUVGIiY5W2jKLHF274NWiJfC7chXhbh/wfPpMGNjbqQ3ScnbrimdTpyHI9QGifP3wftNmBN2/D7smjeVx2wb14XXsGN6sWJUGz4Q0MsD78OGD7JoVtm/fjl9//VX+nDNnTrlsmabxOPAfbrUfjHB3r7Q+lXTn2tXLKFuuPPT09OL3VapcBffv3UVwcLBKexeXqyhatBhMFbofS5QohaDAALx+9Urefv/uLQoWiu0eiyMeP+T/j+eYxwlt2ioHMAYG+tDVSfaIgzQjrpGrqysqVaoYv09fTw/ly5XDlStXVdpfvXoNlSpVir8txr9Wrlwpvu2QwYPQskULIMGXz3fv3qNQQdVrqu7/SFMEBYfIQK5auVJK17ZS6RK44KKcuSzo7Ig/xw9D7uxZlfa7eXjBxMhIab+2tjZ0tHUQ9P+sqSYTmSLL8qXh9e+p+H0xEZH4eOo8bOvVVGorgjc9S3ME3nuotF8Ee9bVK8uf9W2sEOHji8xKx9RUBnI+p2O/rAoxkZHwOX8BNjVrKLXVs7aCrrk5gh4+Utof6PoQVv9/D3g6YRLeb94KZJJssjpaOtqptmV0yXoGBQsWxH///Yfr16/Dzc0NNWrEvjBF9q5Qgg9m0mwi02TvoPyhaGFhCUNDI/j4eKu09/7oBXt7B6V9YtycnZ09vL1jMyILFi+XWbs4oaGhuHrlMgoWKiJvFy9REgULFVYaA3jwwD6Ur/g58MkoRNepkZGRyng7BwcHeUxRWFgYAgMDlcbbJWwrAj51xo0dg+bNPo9xio6OxtlzZ+UYPk310dcPxoaGMFfIyAnZ7W3hlSDISOy65XXMiaMblyrtE8Ghro42HHNkg6YzsLdFVFAwovyUx9uFvn4HAwc7pX1Rfv74FBEJo1zZlfabODvCwM42PmiJ9PVDjk5tUO7wdlQ8uU92/4qu28xA39YW0cEhiEowj2zYu3fQt1O+npH+AfgUGQnD7MqvM2PH3NDPkuWHnC9lwgBv5syZci48MS2KKK4Qb45i6pQBAwZg7ty5KX+WlG4FBQbCMMGAacHI2EgeS0gEKCL4U2lvZCSPqXv8aVMmwM7ODlWqVlM6JrJPHdq1Qvu2v8Lc3ALNmrdARhMYGAQjNdfP2Fj1esRlx8W1UmprZKz22n2pyGXe/D8RHBSMJv/v6tFEAUHB6q+tkaE8lhy3XB9hxvL1ckyfyAZqOj1LC0SHhKrsjwoOlscUiW7WD3sPocC0MTKQE2PK7Br9BGNnR+gYxf4/6NtYw7JcaRhktcfD4RPxcNQUWFYojUJzJyMz0LUwR3SoauZXBH3imPLOaHgePgKn0SOhKwp6tLRgU68ujBxzQ1vN65oooWT1adWvX1/OfycyCvb/zya0bt1arkMrxgV9TXh4uNwURcZ8gp5Wxk+JapoHrvcxbepEtccKFy4iu1rF6yCh0JBQpW7YOCJTFRam+oEhsnQJs1geHu6YNGEMsmbNjrHjJ8uuMUUi0Fm8bKXMIv61ZSPWrlkpxwJmJGZmpghVc/1CQlSvhxiPGHet4n6WbUNDEq24TUhMTj5p8hRZZDB/3ly1wbmmEJWyaq9taFiiVbRfcvzMRcxdvQkDu/6Gpgm6JzVVpJ+/2uyaromJPJbQk4mzUXDmeFS99i+i/APx8dQ5vFm9Gdnbxw7j8b1yHTfadIe/Qhf53V5DUO3aCTweP1OOz9NkovJYx0h1OjExxltdVfLLOfPgPG4Myh0/KqtsRVfu+y1/waFlxvsym1o0YULi1JLsQUsmJiY4fPgwbt++LW+LqR0KF/7cbfa1DKCYR09ROy1rtNdh2jm9yZe/AJYtX6P2mK6eLv7esQ0e7h+U9osqVxHEWVvbqNzHJost7ty+qbRPdBd6enrAxiaLUiXt5AljUa1GTXTr3ktp+pNHDx/IbmErKyv5O8Q2cPAwdO7QFu07dFYKftI7GxsbGbCJDJxikObu7i6PKRLBmGjj7uGBvArPUV1bdcT9xo2fAKc8eTB0yGAYGBhAk2WxspTTmgQEBil107738IKtyDAlkSj+2brvCP7afxRzRg+WU61kFuEeXtA1NYGupblSN61R7hwId/dUm4lyHTAaD4aMl7dFMYVj3+4IefVW3g57+15uSr/DzR0R3j4wyplN4wO8CC8vGcyJsXWK3bSGOXIgwlPN9QwJwZMx4+RUKHHXM0e3rgh9G3s9ib4kWSkzkb0rWrQo5s2bh8jISLnNmTNHFl6IbMrXjB49WgYBiltrbevknAqlMjEQ38raWu0mKljLla+I69evyddAnCuXL6JI0WLyS0BC5cpVwP3795S6b+/dvQNTM3PkdnSUt93ev8PoEUPwS8tf0bNXb5W57fbt3Y1///k8rYoQER4h24lii4xEXKMihQvj8pUr8fsiIiPhct0FFSqUV2lfoXx5XL58RSn4uHzlqtq2ikQAOXTYcFmpO2rkCI0P7gRTE2MUL5gf5xWyReLaXrl5F1XKlkzy44jAbteRE1g2dXSmCu6E6MAg+Lncgm29z/MlaunrwaZWNXz873OhQJySW1fAqnL52CrZqChRkQKHXxrB40Ds36vTkN7IO3qQ0n30bW3kHG8hrzU/aIkOCkLArduwViio0NLTg3XVKvA5e06lfZHlS2FRvpzS9bRtWB8fj2nebBWUTgK8fv36yW7aW7duyTnxxCbmwKtbty769v16F5n4cBGTIStu7J7NmIoWKw5LSyusWrEUnh4ecoLizRvX49dWbePb/DlvNk6dPCF/dsiaFWXKlMPCBfNkIPfwgSuWLVmI1m3axQ9037RxvZw+pWmzFjK7p7gJDRo2llOpXL50QY7DE5MeL1wwF7Xr1IOeXsYK8ITWrVph/YaNuHnzFjw8PLF02TKZzSxWtCju37+PMWPHxQ9pEPPfHTh4EOfOn5dfpjZt3iznFKxapcoXf8eOnTtlMNmje3cZFCa8ppqqwy+NsGLrLly7cx/unh8xf/VmZLGxQsnCBXD7wWMMmjIX4eERid7f1z8A6/8+gH5d2iJ3jmyIio6O3zLLPHivlq1D3jGDYV2tEgyzZ0XBGeMQ7u4B3ys3ZIVtqb9WQdsw9gtDwM27yD9hGEzyO8siDNFdK7Jzoq3g9c9pZG/fSnbZ6tvbwqRgPhRbOR/vt+1RKeTQVO/Wb4DjoAGwrFgBBlmzwnnsaIR7esH/xk2YlyqFIiuXQfv/X8AC796Tc+YZOztB384WeceOkUUqoi1RqnTRnj17VgZ0CYm58ERXLWUeYlzc+IlT5EoWgwb2kdWwPX/vgzJly8nj4kPQze09cueOzc4JojtVrGQxeuRQmJiYonHT5vi5fkOlufX8/f3QvEl9pd9Vu249DB4yAiVLlUb/gUOwY9tWzJs7SwaYNWvWlitZZETlypVFzx7dsW7DBnz0il3JYtKE8fLaisyb2wc3uZqC+GKU19lZZuDEShaLlyxB4UKFMX3a1K8uE3jvvivevHmDps2aK+0vVqyY0moXmqZS6eLo37ktVmzZBU/v2JUsZo8aGHttg4Lx/oMnIuW1Vf/F4OGzlwiPiMDkhavkpmjJ5JHxq11oMu/TF/B06jzkHTMIhtliV7K4022AnJpDFFoYOeaSWSiEhePl4jXQMTZG6W2roaWnK+8b11YIdH2Euz0HwnnkQOSfOBzhnt5w33tIBpGZhe/FS3g5f4EM8gzsY1eyeDBgkLxGotDCKGfO2OsZHo63a9dB29gIRVaugLauLnwvXsTDgYMz9bQoCWnChMSpRSsmGV9D8+TJg3/++Qf58+dX2v/48WOZ2Xv58uU3n8gRvQLffB9Sle9RbKaMvp8eEs/s0LcxC+Uckinhdn0uRZVSDG00vwr6R6l6J+0yilcrVUi1x65wWXUu0owkWf2iLVq0QO/evZXG24lxeWKfWLaMiIiI6EdU0abWltElK8CbNWuWXLUid+7cKF26tNzEmpj58uXDtGnTUv4siYiIiCh1x+CJysqNGzdi0qRJuHfvnhwcLypoc+XKlZyHIyIiIqIU9F2Ldzo6OsqNiIiI6EfT0oCu1DQP8ERhRWLrNcYR9RqizYsXL1Li3IiIiIgoNQM80SWbkJiBP25dTDFZsYWF8tqERERERKlFK8ESlpSMAK9GjRpKFbOdOnWSc0kdO3ZM7hNLJTVu3BjLli1L6kMSERERUSpIVug7YMAAZM2aFTt27Ijf9/TpUznb/tChQ1Py/IiIiIjoRxRZnDx5Eq6urkpdstbW1pg/fz5KlSqFVauUZ3wnIiIionQe4IklkyIiVGf5//Tpk8rC8ERERESpgUuVpXAXbZMmTdC/f39ZWBFHrJk5fPhwNGz4eU1RIiIiotTClSxSOMBbuHChnOw4R44cKFOmDMqWLYts2bLJqtrFixcn5yGJiIiIKC27aA0NDbFr1y45350Yiye6ZgsVKoT8+fOn1HkRERERUVqsZCHWnxUbEREREWlIgEdERESUVlhkkThOAU1ERESkYRjgEREREWkYBnhEREREGoZj8IiIiChD0tJmnioxDPCIiIgoQ2KRReIY+hIRERFpGAZ4RERERBqGAR4RERGRhuEYPCIiIsqQtHU4Bi8xzOARERERfaetW7eiTJkysLW1Rdu2beHr66u2XXR0NObNm4d8+fLBxMRE3ufYsWNIaQzwiIiIKMNW0abW9i1EgDZ06FDMmjULLi4uMnBr2rQpPn36pNJ2yZIlWL16NbZs2YL3799j1KhRaNOmDW7evImUxACPiIiI6DvMnj1bBnf16tWDo6Mjli9fjnfv3uH8+fMqbbdt24bJkyejYsWKsLS0RKtWrWTGb+fOnUhJDPCIiIiIksnf318Gcs2aNYvfZ2BggEaNGuHgwYMq7Y2NjaGnp6e0T19fX+5PSQzwiIiIKMOuZJFaW3h4OAICApQ2sS8hNzc3mJmZwdraWmm/k5OT7IJNqGXLlpgzZ468X0xMDK5duyYDwebNm6fotWGAR0RERJTAzJkzYWFhobSJfQn5+PjA1NRUZb8I+sSxhH7//XfY2Ngge/bssk2FChUwcuRIlChRAimJ06QQERFRhpSaS5WNHj0aQ4YMUdonul4TEpm7oKAglf2BgYEqWT2hQ4cO8t9Lly7Bzs5OFmWI3yOqb1u3bv3jA7y///5b5Re7urpi5cqVePXqFZydndG/f3/5LxEREVFGZmBgoDagSyhbtmwymBPZOsWA7sWLFzJLp+jJkyc4dOgQvL2948fcibgpJCQEf/75Z4oGeEnuom3Xrp1Sue/Zs2fl3C3ipBo0aIDIyEiUK1cOt27dSrGTIyIiIkrPLCwsULVqVaWCCjFW7+jRo2jSpIlSWxEr6ejoyKIKRUZGRnJ+vJSU5AyeGAioaNq0aZgxY4ZS+rJ48eKyH/nff//95hPJ9+jEN9+HVD0tWC+tT0FjVLuyOK1PQWMEbl6f1qegEbSOuaT1KWiMwCidtD4F0iCjRo1Cly5dZMYuf/78mDJlivy5evXqssJ26tSpOHDgAAoUKIC8efOiW7dumDRpErJkyYIbN25g3Lhx6NOnT4qeU5IzeFpayv3cols2YWT666+/ymoQIiIioswy0XGDBg0wf/58meQqW7YsgoODZUZPW1tbdt0+e/YMERER0NXVxZEjR2RMVa1aNdjb26Nfv35yiNvgwYPTR5FFqVKl5AmLpTbiPH78WG0lCREREZEm69ChQ3wBhSIxP57iHHkis7dp06ZUP59v6qIV5cEi2hSVHmK+FlFhIsbdiRSjGDA4ffp0udwGEREREaWdJAd4YgkOLy8vPH36VP4rNjF7syiqEEtziPlbRLpRjM0jIiIiysjTpGSaAG/EiBFfPP7gwQOYm5vH3xZrsIk0ZMKxe0RERESUulJsJQvF4E4oXbq0zPIRERERZbSlyjK6VHsGCadVISIiIqIfI+OHqERERESkhAEeERERkYZJ9jx4RERERGlJW4eFnIlhBo+IiIhIw6RaBi9nzpxyQV0iIiKi1MB58FIhwAsLC8OjR48QEBCgckwsrnvz5s3kPjQRERER/egA77///kO7du1kcJc1a1alyYzFzy9evPiecyIiIiKiHx3gDRgwAGPGjEGvXr1gYmLyPb+fiIiIKFk0YULi1JKsK+Pt7Y3u3bszuCMiIiLSlACvS5cu2LFjR8qfDREREdE3FFmk1pYpu2hnzJiBMmXKYMGCBXBwcFAagyecOnUqpc6PiIiIiH5EgNenTx9ERUWhZ8+esLa2VgnwiIiIiCiDBXj79u3DtWvX4OjomPJnREREREQ/PsBzdnZGeHj49/1mIiIiou+gCWPl0lWA16NHD7Rs2RLDhg1Dnjx5VLpoxUTHRERERJSBArxp06bJfydPnqxyjBMdExEREWXAAO/ly5cpfyZERERE34ATHadwgLd58+YvHu/UqVNyHpaIiIiI0irA27Bhg9Lt6Oho3Lt3Dzo6OihevDgDPCIiIkp1LLJI4QDv9OnTKvtCQ0Pl/Hj16tVLzkMSERERUQpJsc5rIyMjzJw5E1OmTEmphyQiIiKiZEjR0YnPnj2Dt7d3Sj4kEREREf2ILtqEWbqYmBi4u7tj165d6Ny5c3IekoiIiOibsIr2B0yTYmNjgzVr1qB58+bJeUgiIiIiSssAT0xwnCtXLmRkgYEBWL50Me7euQ1bOzs0/6Ulataqk2j7iIgIrF65DC7XrsDE1Aw//dxA3kfRzZvXsWnDOrx/9w7Zs2dH5249ULp02fjjr16+xJpVy/H06WOYmZmjbr2f0brtb7L6OLOxb1YXpf5agLNF6iP09fu0Pp105+gFF2w/ehru3r4oV6QARnVrDXNTY7Vt7zx5gUFzVmHekB4oUzif3PfRLwDzN++By/3HMDMxRq1yJdC3bRPoZKJvu0Zlq8K0ZiPoWGVB+JP78Pt7LWJCg9W21cuRBxbNOkAvlzOiPnog4NA2hD+68/l4TidY/NIJetkdEeXljuCzRxHicg6ZUXCgP7avmYYn911glcUBdRp3RPnqjRJtHxYagj2b5uHe9TOyt6dE+dr4peNgGBmbxre5evYQ/t2/AT5ebsjhWAC/dhmB3HmLQJMFB/lj19opeOp6DVZZsqJmo04oW7Vxou3Dw0Kwf/Mc3L9xGjGIQfFyddD0t6EwVLiOj+5cxPHdy+H25gmy5cqPXzqPQu68xaDREqykRZ8l691eLE/26dMnlf1iuhRra2ukd+Lcp06eABNTUyxYtAydunTDmlUrcOO6S6L3WbRgHvz8/DBj1nz0HzAYRw4dwD/Hj8Yff/78GWZNn4p2v3XExi3b8VuHTpgzazrevHktj/v7+2P82BGoVKUK1m3cirHjJ+H8+bM4sG8PMhMDB1sUXz0dheeMgo6BflqfTrp08fYDLNq6H/3aNcWmacNgZKiPofNXq/zNBYWEYsGWvRi9aIP824sTFRWNfjOXwdLMBNtnj8aC4b/j4Ys3mLtxNzILg0IlYdG8owzUvOaPQUxEGGx6Dlf7YaBjZQOb30ch5OYleEzuh6DTh2HdfSh0bOxij1vbIku/8TKgE8f992+GWcPWMCpVCZmNeA2unD0AxiZmGDl7G5r9NgC7N86F662Lid5n05IxCArwxcjZ2zFy1jYE+vtg59oZ8cfvXDuNfVsWoG3PsZi+8l9UqtUcy2b0RaC/t0Zfx7Vz+8HIxBxDZ+xE47YDsW/TbDy8fSHR+2xdNgrBgb6y/dDpOxHk74Pd62NXlRLevnyAzUtGoHaTrpi49ASKlKmJNXP7IjQk8Ac9K8qwAd7bt29x7tw5uYlvYXE/K27r169HVFQU0jvX+/fg5+uL3//oCzt7e5ll69i5K/bs3qm2vfuHD7hxwwWDBg9D9hw5UKhwEfTuNwB/79wur4Wwb88uNGrcFBUrVYapqSkqVKyMypWr4vy5M/L42dMnkTdffjRu0lxm75yc86J1m99w6VLif9CayLJCCeiYGONSjXZpfSrp1uZD/8lsW4ViBZHN1gYju7aCp48fbj1+rtTujbsXXrx3x/Kx/WBp9vlb/IVbroiMjMKwzr/C1soCjtnsMb1/Fxy74AIf/8zxZm9WpykCDm1H+ON7iPbxgt/f66BjaQN9p4IqbU2q/ISwe9cRcvkkPoUEIfT6eUS8fAzDomXkcdMaDRF66wpCLp+SxyOePYD/no0wrdMMmc2zBzcQ4OeN1t1Gw8Y2GwqXrIym7frh3/3r1bb3/PAaD+9cRofek2BlYw9r26xo3X0Url/8B5GREbLNiQMb0ah1b+QrXAbGpuaoXOcXZM+dH/dvnIemev7wOgL9PqJl1zGwts2GgiWqoFGbATh5cJ3a9l4fXuPx3cto8/sUWNo4yIxfiy6jcfPycUT9/zqeOrQBtRp3QfHydWFiZok6TbvB2MRCZggpc0pyF62vr298AYVYb7ZLly7yX0XZsmXD4sWLkd5du3oZZcuVh56eXvy+SpWrYPnSRQgODoaJiYlSexeXqyhatBhMzczi95UoUQpBgQF4/eoVHPPkwft3b1Gteg2l+4nHDwmO7RJyzOOE/AWUP1wMDPShq5OsXvIMy+PAf3Ij9URW7vaj55gzuHv8Pn09PVQpWQTnbtxHmUKxXbBCoTw5sWRUH5XHeOPuify5syt1x1pbmMHGwhy3Hj1DnQqloMm0DI2g71wI3uvnf94ZHYUw15swLFYWEc8fKrU3Kl0ZvluXKe3zXj49/mddu6zyvooi3j6Hfs480DYxw6fgzBE0C/dunEWR0tWgq/DeKbpct6+ehtDgQBiZfH6PFNzfvYRDdieYmFnE79PV1cOn6ChERoRBT09fBoF58hdXup+unj5CQ4KgqVxvnkXhUtXltYhTvHwd7Fo3RWbcjIyVr6PH+xewz54HJqYK11FPX17HiIgwefuey0k0/W1I/HFtbR2M+fMQNB0nOk5ckqMLsUJFXHGFtrY2Xrx4If/NiD5+/IiChQor7bOwsIShoRF8fLxVAjzvj16wt3dQ2ifGzdnZ2cPb+6MM8BYsXq4y8fPVK5fRvecf8nbxEiVVxgAePLAP5Stmvm4eSpyXrz+MjQxhYar8GsxuZwPX52+U9iX8ghVHBHLvvXxkdjmuTUhYuMwCirF5mk7HwhoxYaGICVEebxft7Qm93HmVG2tryzF6nwL9YfFLZxgUKIZPQQEI/HevHLcn7xfgJ7tpFenaZYu9u7llpgrwfL094JS/hNI+MwtrGBgaw9/vo0qAV7xcTbkpcjl/FA45nGBsYi5vz1l/RuV3PHtwHY1a/Q5N5eftDsf8yp8JpubW0Dc0RoCvl0qAV7RsLbkpun7hCOyzx15Hb893MqATdq6ehGcPr8PGLofs+s2Rp9APeEaUHmkndyWLjBrcCUGBgTA0NFTZb2RsJI8lFCjbG6m2NzKSx9Q9/rQpE2BnZ4cqVaspHRMZwg7tWqF9219hbm6BZs1bfPfzIc0REBQCY0PVsYki6Av4fzb4a6qWLgI3T29sP3YGUdHRCA4Nw7xNu5HNzgZh4bHdOZpM29hUjrlL6FN4KLSNlQNnbVNzOc2CebP2iHjzHN7r5iH0zlXY9BopCyqEkGtnYVKlHvT/P+hfBHum1RvI7lotfQNkJiFBATKYS8jAyFgWX3zNjUv/4OD2JWjZaaja4z4f3bFiZn8UK1sTjvk0tzggJNgfBmo+UwwNTRAS9PXreOvycRzduRjNOgyXtwP8PkLfwAg7Vk2QY+96DF8Cx3zFsXhSJ/j7eKbKc6D0L1n9gzVq1MDRo0fluDtPT9UXjxiL9yXh4eFyUxQRHg59g5R5s3zgeh/Tpk5Ue6xw4SKyqzUsTPUDIDQkVKkbNo6ZbB+q2j40VB5T5OHhjkkTxiBr1uwYO36ySiAsgsLFy1bKLOJfWzZi7ZqVciwgkSAqZUPCVIOwkNAwmCfILCdGZP8WjfwDk1f+heU7D8MhixV+b9UQvgFBckyeposNvFS/wGkbGOFTgqxeTETs+1D4o7sIvRE7HjbYyx26DjlkUOf39xrZpeu/ZwOsuwyElp4eotzfw3f7StgOnoZP/********************************************************************************************************************************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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 计算下期收益率\n", "df['future_ret_1d'] = df['close'].pct_change().shift(-1)\n", "\n", "# 去掉NaN值\n", "df.dropna(inplace=True)\n", "\n", "print(\"添加目标变量后的数据预览：\")\n", "print(df[['close']+factors].head(10))\n", "\n", "# 绘制目标变量分布\n", "plt.figure(figsize=(10, 5))\n", "sns.histplot(df['future_ret_1d'], bins=50)\n", "plt.title('下期收益率分布')\n", "plt.xlabel('收益率')\n", "plt.show()\n", "\n", "# 计算因子与目标变量的相关性\n", "corr = df[['close']+factors].corr()\n", "\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(corr, annot=True, cmap='coolwarm', center=0)\n", "plt.title('因子与目标变量相关性')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 划分训练集与测试集\n", "\n", "按照时间顺序，使用前80%的数据作为训练集，后20%作为测试集。"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集范围: 2020-04-07 00:00:00 → 2024-03-11 00:00:00\n", "测试集范围: 2024-03-12 00:00:00 → 2025-03-06 00:00:00\n", "\n", "训练集样本数: 988\n", "测试集样本数: 247\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABNUAAAIiCAYAAADvkf6JAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8ekN5oAAAACXBIWXMAAA9hAAAPYQGoP6dpAADiBklEQVR4nOzdB5gTZdcG4LMsvUgRlCpNkCogiIJgR6mKFSt2rCh2xc8Gdj/1tyMfir2hCIooVhAVEBAQEESliUgv0uv+1zPD2XkzmdRNmSTPfV1rstlsdnYlycwz5z0nr6CgoECIiIiIiIiIiIgoasWivysREREREREREREBQzUiIiIiIiIiIqIYMVQjIiIiIiIiIiKKEUM1IiIiIiIiIiKiGDFUIyIiIiIiIiIiihFDNSIiIiIiIiIiohgxVCMiIiIiIiIiIooRQzUiIiIiSpqCgoKkPfaePXtk7969SXt8IiIionAYqhEREVHOKGrAs27dOjnrrLOsS7eHHnpIHn744YiPgRDov//9ryxbtsz6fPny5XLLLbfI9u3b4/6ddu/eHdV98bMXLFgQ0+O/+uqr0qlTp4Cfh9/z559/Dvt9HTt2lKFDh0q3bt3km2++8bzPDz/8IO+//37h52+++aaMHDky6m3r2rWrDBo0SFasWCEdOnSQNWvWhLzvxRdfLNddd13Ir+/cuTPg/ysCuy+++CLqbSEiIqLcw1CNiIiIcgJCkuOPP94KbkxXXnmlFczofXbs2FH4tXr16smoUaMKPx8xYoTMmDFDKlWqFPT4CIO8bnd799135d5775Vy5cpZnx9wwAEyZswY+eCDD6L6PbZt2ybz5s2zft61114rDRs2lAEDBlihHII6rw+t5ho/fry0adNGPvnkE+vz3r17S15eXsAHfmdYvHixbNmyxXMbNm7cKMcdd5xMnDgx5HYWL17c+rl33HGHnHfeedbjmRAEXnHFFTJr1qzC2/Dzb7/99qirz8qWLWv93tWrV5eaNWtafwcT/k6hfge3wYMHW/8+9Gdje7t37y6//PJLVN9PREREuYehGhEREeWE/Px8ufrqq+Xyyy+XJ598svB2hGioUoI33nhDjjnmmJCP8fbbb1shXLFixazKJzOMmj59ulxzzTVBIZUZJiEAGjhwoGzdulWqVKlifb1EiRLy22+/yYUXXhj0veXLlw/4+SeffLIVJLVs2VLOOOMMWb9+vfW7PPLII1ZgVqdOHc8P/DxAaPTyyy/LOeecIx999JF1G6rJENThY/LkyYU/68wzz5RJkyYF/Q2wXfh5d955p7U9U6ZMCRmqIaQ89thj5dlnn7V+T9OwYcOs0AsVfvr7XnDBBfLHH39Y/6/cfwtU94UK1QA/A2GhGfR17txZpk6dKpFMmzbN+p0ef/xx6/8tIKw8++yzrd8zmUtYiYiIKHMxVCMiIqKcgZAEVWGoFIu2MkzNnDnTCs4uueQSKwzC5V9//WV9PProo1bQpZ+bH7Vr17a+HwFT3759ZcmSJVaIhqBGP1Cd1adPn4Db8LF58+aAbUAg9s8//xRWX91zzz1WtZmGb3Xr1g34/kWLFgX9HgjUPv/888IlnQi7vv76a6tar1SpUlH/PVCB9t5770mrVq2CAjB8fPvtt9ZyS1zH3/2iiy4q/F4EcTfffLOce+65AduLYA/w93X/LbBE1g3Vfvo9qFSbPXt2wFLVaCBURUB61VVXSZcuXQK+hqDtxx9/lJdeeimmxyQiIqLcUDzdG0BERESUSieeeKIVjqESKRboI3b99ddL1apVraqy5s2bF1ZPoTrq9NNPLwzQ3BAK3XDDDfL9999bn7dt29aqxlIIhrDs0L189LXXXpNTTz218HP8bNi1a1dhIBRLL7YNGzZY4R4quEyoeFu6dKnEqlevXoUhmFv//v3l0EMPtZZ4alWZ3hfLKitXrmwtpx07dmzh92hF2OGHHx7w92natKln1Rwec+HChfLxxx9bQeX8+fOtSwSH4fqnKVQoYmkq/iYIRt0OOuggq6Lu/PPPt0K7U045Jcq/DBEREeUChmpERESU9RBCoQ+YGZaYoU0kaLQ/evRoef75563lgFgmiaoxVGEpVMDdf//9Vp+z5557LuD7ERYhuJkwYYIVEKEv28EHHxxQ9YVloui3Fg56h5m/R+vWrQuvf/bZZxF/Dyy1xO+CyrSKFStKrObMmWMFVgp/DyyXbdKkSdB9a9WqZS0BdX8NwR6qwkqXLm0FYGaPOwSEZcqUsZZsej2m+s9//mP1lPv999+t3mwYmnDIIYdY34Nlq+7Q0MvatWutykEMbkBVnYZ+blhmiwEIGFCB/69YPmz+fyciIqLcxVCNiIiIsh4q0zAdUg0fPtxa8hctLLlEgIVKM/Qiu+uuu6RGjRqyevVqaykpqqXQjw3LOL2gT9eLL75Y+DmCKLPHGCrFEA7pkAD1wgsvWFVd6s8//7QCOoRbGBSA6ZmNGzcu7AsWTTN+fC8mcsYz2RJDFRCI6dLTTZs2hbwvQjsEV274/4CPBx54wPpbmr+zVqqdcMIJAX8f/I7m9rZr1866DZVpCBMRUmrFH5b1ooowHEwLxcCGRo0ayXfffWf9vwwHffT2228/q+pu7ty58n//939h709ERES5gaEaERERZT2EMAiudPlnrLD8Dx/vv/++NRBAe4WhAu7VV1+1Kp2wNBNVVtFAxVo8lWr777+/dTlkyBDrEuEOhiegIiwa6JmGCq+777476gmb7lBNl1WagwNQvYXKPdWjRw9riWukJaWnnXaaZ6UaKunCVaqhj5z2Znv66aet3wXBJYYVoP8Z+tOFc+CBB8oTTzxh9XrDvw1zAmko+F0QzsbzdyMiIqLsxEEFRERElPUQOqFfGT7cARQqxKJZCopllzfeeKMVHmHp4t9//21VLWGy5hFHHGGFbFgOiY9Q0z8VKqTM+6CfF5r+ezX8/+qrrwK+Fz3JMDQBUCmGQCuWoAfbjimXqLxSRZ1u+dhjj1lVY4DKLwSNoSrVTG+99VbA76qhJJbIuv8OXgMIUJGGvz/+xqg+Q987LHEtWbJk2J+Lx0MvNfxbQLUc/qb4QK88TDTVz/GBEBXw+2CJKbaNiIiICFipRkRERDkNPb7cAwK8YMknpmticiaCLCw/RJN9rYCDW2+91bpEaKXM8Aq8BgtosOfVqwsVWArLGzFFE5M6UY2F6ZSXXXaZNRSgZ8+eUf/OWKZqTrrE36BKlSoSL3yvBllYSomqPfxNw4VqAwcOtCr0TAgHEfBF2+8OU08RrKFP3DvvvCPNmjWzep/Fon79+oXX0ZsNPfHMKjlsD/6/6IRVIiIiIsVQjYiIiHIamtBjEmWk5ZqoQNOJmzfddJNV6YTAa9y4cVKtWjU5/vjjreWVZsCGxvYaiqGqDN8bajnip59+ai1FdENvMVRJIdzp16+fVV2FZZOAn4clqQMGDIi62gzBHHq1nXvuuYW3ocoL25BImJa5bt26oNsxsMEMIk1YkoklnfidvODvYPZaA0zkvOWWW6y/BZZnmiFkLDCwAJWHqFQz/fvvv1YwGu/jEhERUfZiqEZEREQ5CcMH0CMMjfsjLelr1aqVjBgxQo4++mjrexSCLPQnw4cJIQz6giEoO/30063bdOloKBge4AVDDbAE8ccff5SPP/7YaspvVrQ1aNBARo0aFfXgAUwpRYCG3wnTShEIIsRCSIgKLWx3LFCt57XtLVu2tAJLhHhY1on+b9dcc42sWrVK6tSpE/YxsU1e0Lvu2GOPDahsQzUcluZiO2rXrm3djp9h/n+KBpaN4vfA39OEADCeSalERESU/XjKjYiIiHIO+n+hKgzTPFE9hoApHCxlPPPMMwuDGgRxCKdQwYZwToMzhaqmvn37ymuvvVZ427Bhw6wQTj8wMADh0pdffmktd0SwhVAL4Q4GIOj9tKcXthdLHN2hj/48wPf98ccfhR9e/dzwGFgiiWAOvzcCQPRBq169unzyySfSsGFD635o+I9hA+Eg5DrjjDMClnlieSt+J/Q6w99t0qRJsnPnTrnzzjutyjUEX+bfAcHbOeecYwVaCCdRKYbJoPh9Z8+eHXBfM1BDBRl+D/yNDzroIFmyZElh0HbooYdaE1nx/whTPiPB9qK/G5b4ev1biTWgIyIiotzAUI2IiIhyCpZhoufZbbfdZjWpx6RKNO8PB4EOwhVMl0TlGKqwJk6cKIMHD7aa25crVy7oexCqffbZZ7J69erC2xD4aBB18803W9VvOo0UARu+hsfF4AOEX1u2bCn8Xixv7N69e9jtXL58uTUEQT+OO+64gK9rRZfZd+yGG26Qzp07S4UKFeTiiy+2qusAgVqoZbH4eyAoQ/XZ0KFDrV5k6NMGCLHQ7w1hY69evazACoMVEHQhuFOoJnvqqaekRYsW1u34f6F92RBuYbuw/VjaiRDT/Nn4mfj9EGqinxomgD744INWbziEifg7o//dkUceGbHKDI9x6qmnWkHeUUcdFfR1LEdt3bp12McgIiKi3MRQjYiIiHIGQiqEPR988IHV2B/VY2jyHwnCJiwRRXCDarKffvrJqqz64YcfrIb7WCKIjz///LMwYENFGUKdt99+2/ocEzFRBXbllVda1Vi//vqrFZ6ZEC4h1MI0zTfffNP6HP3booUgyazsWrRoUcDXsUwUvc4QfCFcRI+20aNHy//+9z9rWzCF9Pzzz7eqvhBQ4QPVbmXLlg14HNy2cuVKef31161Q8ffff7f+rgi3pk2bZlWfYaksgsUXX3zR+hsjYAN8rXfv3lKvXj1rKStCN4RrZj86uOCCC6zpm6h2Q/CIqjqEc6iww1JXPP6sWbOkY8eOViiGryMsxARQ/CxU/yG4W7ZsmbVtCEXNgRQYDoEgDY+NZakIWREU4n6ocsP/S/Sew1TWWIZAEBERUQ4pICIiIsohK1assC6HDRtWcOmll4a9b926dQs++uijgm+//bZgxowZBXv27Cn82qZNmwo+++wz6/p5552HKQEF9erVK5g6dWrhfSZPnlzw559/Wtd/+umnghEjRhTs3r076Ofk5+cXTJo0Kej2X3/9teDzzz8Pun3Xrl3Wz5s3b17hbbj+wAMPBNwP2zh8+PCCnTt3Wp8PGTKk8D6vvfZaQZs2bayfofbu3VtwzjnnFAwdOrTgscces35GhQoVCl5//fWAx8X9vvrqq4Jo3HjjjQWNGzcuWLVqVeH3Pv300wVLliwJuu/gwYMLTj755KDbt2/fbm3v5s2bCz9327hxY8Epp5xSULVq1YIFCxZYtz366KPW74CPpk2bFsyePbvw/j/88EPB/vvvX/DBBx8U3rZjx46CcuXKFX7PfvvtV3Ddddd5/j8jIiIiysN/0h3sEREREaUDdoPMpv+59rujB5suuVS4zT1hM5Ng6We0kzo3b94s5cuXD7gNFWwK1W65+u+DiIiIImOoRkREREREREREFCP2VCMiIiIiIiIiIsqkUO3qq6+2SurdH5MnT7a+jua6aAxbpUoV6dSpk9WU1u3JJ5+UZs2aWU130fh3x44dafhNiIiIiIiIiIgol6Q1VMOIeUyNQu8O8wPTpzCRqmvXrtY499mzZ1tTozCeHVOfFMapDxkyxJrcNX78eGuM/KWXXprOX4mIiIiIiIiIiHJAWnuqHXPMMXLXXXfJSSedFPQ1jJ0fPny4FZZpg1iMrMdIdNyOJrQYS//KK6/IcccdVziiHbcheKtfv35U24DHQRhXoUIFNqIlIiIiIiIiIsphBQUFVqEXVkRGGn5UXNJcqVatWjXPr33yySfSu3fvgKDr9NNPl5NPPtm6PnfuXNmwYYMVzKmqVatK586dZcyYMVZlWzQQqNWpU6fIvwsREREREREREWWHv/76S2rXru3vUA2VZ6hWW7BggTRp0kQefPBBadWqlSxbtiyo2qxBgwaybt062bZtm/X1unXrBqWGuM/ff/8d8mei55rZd00L9dC/DdVqRBQfLN3+9ttvrcrREiVKpHtziLIGn1tEycHnFlHuPrewjWhDBH379vXtdhJl4vMrG6BKDXlUNBlR2kK1PXv2yNq1a+Xxxx+X//znP1ZZ3WuvvSZHH320/Pbbb1Z4Vr58+YDv0V9o/fr1nl/X+yCsC+Xhhx+W+++/P+j2SZMmSdmyZRPyuxHlKjyHpkyZku7NIMo6fG4RJQefW0S5+dzavn273H777dZ1FGqULl063ZtElDXPr2ywdetW6zKaFmFpC9VQIfbxxx9byzc1LHvkkUfkp59+kjfeeMOa+Ll58+agtBAqV67s+XW9D74Wyp133ik33XRT4ef//vuvtfwTfd3222+/BP6GRLl31uTLL7+ULl268KwJUQLxuUWUHHxuEeXuc2vLli2F19FeqFy5cmndHqJsen5lA+RE0UpbqFa8eHHp2bNn0O2Y9rl48WJr3SqWZJoWLlxoBWZlypSxvr5kyRJr0IC5BBT30b5rXkqVKmV9uOEfJP9REhUdn0tEycHnFlFy8LlFlHvPLXO7/LydRKHw321yxfK3DT/GIInmzZsnjRo1kt27dwfcjgEEmOCJwG306NGFPc9g1KhR0qtXL+t68+bNpWLFivLdd98Vfh3LSSdOnOgZ1hERERERERERESVK2irVDjnkENl///3lsssukwEDBkitWrXkpZdekpkzZ1pNI1FNhv5n9913n/Tr10++//57ef75562mfIDqtIEDB1pfQy82PNbNN99sBWruAQeJ6P+GMktKjvz8fKtyMZr1ykREREREREREOR2qIRT75JNP5LbbbrOWayK4wpCCCRMmSNWqVa37jBs3Tvr37y8tWrSQZs2ayXvvvSetW7cufAwEauirhmBuw4YNVhXbM888k9DtxONj0qhZMUfJabZYo0YNKVmyZLo3hYiIiIiIiIjIv6EaVKtWTYYPHx7y66g4GzNmTNjHwNABc/BAIiHoQ6CGwAfbykqqxENYuXPnTmtiK3roYUmw2SOPiIiIiIiIiMiP0hqq+R2WfCL0QaCG4QiUHPjbohEgBk8gYONIayIiIiIiSha0GtLiDa8hdkRE0WKoFgVWqCUfq9OIiIiIiCgV0M+5R48e6d4MIsoCTDKIiIiIiIiIiIhixFCNAmAJ5s8//xx0Oyaybty4MeT3YZnstGnTZO/evdb1r776SrZt25bkrSUiIiIiIoq9zc+rr75qfeA6EVG8GKplqXnz5sk111wT8CZx6KGHysqVKwPu9/3338uxxx5b+Dmmp7711lsB99m0aZNcf/31YXudYVLr5Zdfbi2VxcfQoUPlgw8+SOjvREREREREVFTo43zJJZdYH7hORBQvhmpZqmHDhrJmzRo57bTTZOvWrdZtuEQV2eLFi6VFixZB37Njxw5588035eqrr7bCtkqVKlkfNWvWlH///VcOPPDAwtsQ0Ck85j333CNbtmyRpk2bSpMmTazvv/baa63r5gceh4iIiIiIiIgo03FQQZYqWbKkvPPOO1a12siRI+WCCy6I+D0ff/yxHHbYYVYgN3XqVCuUQxPPAQMGSL169axLL4MGDbLuj4q28uXLW7e9++671kQdhHRERERERERERNmGoVoMCgpQ7ZWen122LKaQxvY9+fn5MmTIkKinlw4bNkyuu+46qzfa888/L3369LFuHz9+vNVvwAsCu1deeUUqVKggHTp0sH4mrF+/3qpKa926deF9+/btKzfddFNsvwQRERERERERkQ8xVIsBArV9hVgpt3mzSLly0d0XQwL+7//+z7p+wgknyI033hjxe3744QdryebRRx8tDz74oLz88sty5plnyjfffGMNHDjppJOsIQSoXjO1bNlSJkyYIJ06dZIrr7yysO/aTz/9JL/88ovVZ828LxERERERERFRNmBPtSyEfmm33HKLHH744TJr1qyovueQQw6xKtWKFStmBXHNmze3qtCOP/54ee+992ThwoWe39eoUSNraSig51r16tWtD/RdQ8Cmn+MD1WxERERERERERNmAlWoxLsFExVi6fna0NMRCVdmiRYsKb9+8ebOUK1dOtm/fHvQ9VatWlS5dusgZZ5whn3/+uTXMAP3UPvroI6vqDFVq69atk9q1a1v3x+XkyZMDHuOuu+6yQjld/onBCAj3TM8995yceOKJsf76RERERERERNlrxgwRFKxUrpzuLaEYMFSLAVqTRbsE028QcCHowiCBtWvXen4dwwx27dolRx55pBW+Yennhg0bZL/99rMme6IibdmyZdb377///gHfj9tNXbt2tQYb4JKIiIiIiMgvSpUqJe+//37hdaK0+/57kc6dRQ44QGTlynRvDcWAoVqOQMVakyZNQg4tKCgosJaAYunnMcccIxUrVrRuQ/Xa3XffLR07drTuh4o1hG7jxo2TBg0ayI8//iinn3560OOhqm3KlClBb1LHHXecNZWUiIiIiIgoHbAi56yzzkr3ZhA5PvrIvly1Kt1bQjFiqJblEIJhUEH79u2lM5LvEFCZ9vTTT1tB2uzZs2XUqFHSrl07a6kogjVUqgGWd2IgwbPPPitPPfWUFbatWLHC+tqSJUvkjjvusEI5LBtF5dunn35qDTG46qqrrDcvIiIiIiIiIjLs3JnuLaA4cVBBFkMghrJmBGVvvfWWXHHFFWGnf5533nlWNdutt95q9VhDj7RBgwZZ1W1mH7bLLrtM3n33Xfn333+tPm1vv/229O7dW6655horwEOAhu+pVq2a9XN37NhhBW1YDorKNiIiIiIionTZvXu3jBgxwvrAdaK027Ur3VtAcWLpUBbbuHGjvPHGG1K5cmUZPXq0tGrVyrodFWN16tQJuC9u69OnjwwdOtTqu4beaiVLlrR6ovXo0UOmTp1auMwTj/d///d/Vli2Z88emT59ujzyyCNWIOeWn58vN998sxWojR07VubPn1+4lJSIiIiIiCjVcBxz9tlnW9dRJMAVNZR2rFTLWHz1yGLXXnutdYlKteOPP77wdkzu/Oyzz6zrWJo5fvz4oO8tUaKEXH755dZ1LOF0QwCnnnjiiaCvY4KoO1zr1atXkX4fIiIiIiIioqzDUC1jMVTLAViKGWpAARERERERERGlEUO1jMWeakRERERERERE6cKeahmLoRoRERERERERUbqwUi1jMVQjIiIiIiIiIkoXVqplLIZqRERERERERETpwkq1jMVBBURERERERJQzSpYsKcOHDy+8TpR2rFTLWAzViIiIiIiIKGeUKFFCLr744nRvBpF3pVpBgUheXjq3hmLA5Z9Zatu2bUl77N27dwfdVlBQINOmTZO9e/da17/66qukbgMRERERERFR1lWq7dmTzi2hGDFUy0IIsw477DB5//33rc/z8/Pl4IMPtj5q1aolF1xwQeF9Z86cKccee6x1/YsvvpA+ffrIli1bPB+3fv36Mn78eOv+mzZtCvjauHHj5PLLL5e8vDzrY+jQofLBBx8k9fckIiIiIiKKp0jg008/tT68CgaI0lqpxn+TGYWhWhYqU6aM9QYxePBgmTFjhtSoUUN+++03+eyzz+Stt96y7oNwzF3yfPzxx1uhW6dOnWTFihVBj1u8eHFp2LCh3HTTTfLll18W3o7KtHvuuccK45o2bSpNmjSR77//Xq699lrruvnx77//puAvQERERERE5G3Hjh3Ss2dP6wPXiXxVqcZQLaOwp1qWatCggfz8889WvwBAZdkll1wiDzzwQMjvQWj25JNPWoHZ/PnzpV69egFfxxsOqt1QiVa3bl05/fTTrdsHDRokU6dOtX5G+fLlrdveffddGTNmjLz55ptJ/T2JiIiIiIiIsq5SDcfuDRuKnHtu2jaLImOoFgs0DNy6NT0/u2zZqJsVzpkzR+6///7CqrRoLFiwQJYuXWpdb968udSsWVO2b98ecJ927drJK6+8IoceemjhbSNHjrRuq1ChgnTo0MFaagrr16+3qtJat25deN++fftaVW5EREREREREtM/mzYGh2pw5InffbX/ep49IMS4y9CuGarFAoLavEistT7Jy5aK6a7NmzaRixYpy/vnnyzvvvBPV9/z0009WZRkqzu6++27PaThVqlSRNWvWBNzWsmVLmTBhgrVk9Morr5TSpUsXPt4vv/xi9Vkz70tERERERERE++zdK7JhQ2CoZg4r+Osvkbp107JpFBlDtSxUrFgxeemll2TIkCFWv7NoYHgBPq666qrC2xo3bizr1q2zrnfr1k0qV64sq1evDvi+Ro0aFV4/8MADrX5uUKlSJStgq169euHXUc1GRERERERERPtgCCCCNTNUMz//7TeGaj7GUC3WJZhmWWaqf3YMsAzzzDPPtPqkAZZylipVKqbHwJJQVJodeeSR1uXVV18dVKlmuuuuu6xAT5d/bt26VW655ZaA+zz33HNy4oknxrQdRERERERERFlpXyFLQKhmDi5AqHbSSSnfLIoOQ7VYoKdZlEsw/QDVZa+//rp1ffny5dYU0KKoVq1aUKWaWrZsWcDnXbt2lQEDBliXRERERERERBRHqDZ/fso3iaLHUC1LzZo1S3bv3i0tWrSQyy67TKZNmyYNGzaU+vXrWxVskaAnWvv27QNuQ0+0cePGWdfHjh0rRx99tNU3TaeAmrBsdMqUKUHVcccdd1zUfd6IiIiIiIgSrWTJktYKGr1OlFbr10euVCPfYqiWpYYPHy4XXnihdf2ee+6Rzp07y+DBg62Kst69e8vcuXOtaZ5eUN2G5aNffvll4TTRBx98UI455hgZP3681aft4YcftgYidOzYUVasWGHdb8mSJXLHHXdY9/voo4+sHm2ffvqpNcQAvdp0KSoREREREVG6lChRQq699tp0bwZR9Ms/ybc4lzUL7dy506oGO/fcc61eatddd53Url3bqlRD1dro0aOlefPm1u07duwI+N4//vjDuvzwww+tIO6rr76ylo6WK1fOqlTDEtKnn37auk+9evVk8+bN8vbbb1tB3TXXXCM33nijFaDl5eVZy0Xfeust62cgaMNy0B9//DEtfxMiIiIiIiKijAvV0GopXb3dKSKWDmWhP//8U04++WQrSLvttttky5Yt8sorr0j58uXl888/l1NOOUWOOOIIq4Js06ZNcvPNNxd+76WXXmot58TkzjFjxkjFihUDHvupp56yArQnn3zS+hyh2vTp0+WRRx6RJk2aBG0LKt7w+AjUsGR0/vz5VnUbERERERFROuzZs0cmTpxoXUchAY5ZiHwbqgF6m5cvn9LNougwVMtCTZs2LRxQgGWbKG9WqC77+eefraWYWpVmOu+88wqvuwM1wJJRcyhB9erV5Yknngi6H8I7E96oevXqVYTfioiIiIiIqOiwmge9nrVIAKtyiHwdqrlWmJF/cPlnljMDNcXeZkREREREREQ+wFAtozFUIyIiIiIiIiLy4/RPYKjmWwzViIiIiIiIiIjSgZVqGY2hGhERERERERFROjBUy2gM1YiIiIiIiIiI0hmq6RRahmoZhaEaEREREREREVE6Q7UDDggdqm3fnvrtoqhwDCQRERERERHljBIlSshjjz1WeJ0obbZtcwIzhGr//COyZw8r1TIIQ7UstmfPHsnLy5NixYrJzp07pWTJklF/77Zt26w3mOLF7X8i8+bNk6ZNmyZxa4mIiIiIiJIPx0W33nprujeDyJn8iaWflSvb17n8M6Nw+WcWu/vuu+X++++XKVOmyNFHHy07PJ6I48ePl2OPPTbo9gEDBsigQYOs63v37pVevXrJTz/9FPXP1jCOiIiIiIiIiMIs/axSBWWT9nWGahmFoVoWK1++vFVxdsQRR8iRRx4p99xzT+HXSpcuHfL7vvzyS/n888/lpptusj5Hpdudd95pnc1B9Vu0KlWqFPTx7LPPFvG3IiIiIiIiih+OaaZOnWp9xHJ8Q5TUUE0LUxiqZRSWE2V5qPYP1mSLWD0DUKm2detWKVu2bMjvWbFihVxxxRUyfPhwKwRTl1xyibz33nty3333yeDBg6P6+Rs2bEjAb0FERERERJQ427dvl/bt21vXN2/eLOXKlUv3JlGuh2pY+slQLSMxVIvDli1bQn4tPz8/oAos3H1RAVamTJmI9431RX737t2yYMEC+eOPP2TixIly4YUXym+//WZ99O7dW1577TXP71u6dKl0795d7rjjDjn++OODtvWtt96ylorid0TVG24jIiIiIiIioiL0VEOlmha/bNzIUC2DMBWJswIs1McZZ5wRcN8DDjgg5H27desWcN969ep53i9WixYtkrPOOkumTZsmmzZtkpNOOkmee+45K1QLFah999130qlTJ3n88cflqquu8rxPtWrV5JtvvpEvvvhCevbsGVAqjbANAaFuM4JF83dAM1BUuRERERERERGRa/lno0b29d9+Y6iWQViploUaNWokc+fOlYULF0rXrl2tSjUEYG3atJFXX31VDjvssKDvadKkiXzyySfy1VdfWYFcKHiMCRMmyOTJk62KNdNnn33mOfQAGKgRERERERERhQjVmjSxr8+fL3LIIYH3Y6jmWwzV4oB196G4g6ZVq1aFvK97+eTixYslkVD5tmbNGqtnwOuvvy5Vq1a1QjEvqKjDR6tWreTmm2+2bmvRooX1fRrCTZo0yZooWqJECencuXNCt5WIiIiIiIhIcj1UmzdPpEED+zomgqJqjaGabzFUi0MsPc6Sdd9oILRr27atDBs2TO699175+uuvJS8vL6rvxVLRdevWBYRw+LwyGiiGcOqpp1qBmxcMSLjtttvi+C2IiIiIiIiIstCmTfZlhQoideva11evxjQN+zraQaHvGkM132KoluXQt61///5WsNa6deuovw8TPtFbzQzhUPVmTgRVBQUF8sQTT1gVbeb9d+3aZX0N/dSWL18eMMCBiIiIiIiIKKdt22ZfYoChWWSDYQXAUM33OKggS+3du1feeOMNefjhh63qstNOO826/dlnn7UCLv3cy0svvSQ//vijDBgwIOD22bNnS506dYLuP2rUKCuAQ4A2cOBAeeedd6zbL7nkEmv5KCDUQ7hHRERERESUTlhdg5U8+Ai10oYo5aEailC0SMUM1YChmm8xVMtC8+fPl+bNm8uXX34pEydOlDvuuENOP/10mTNnjlVRhmmcGnxhOqj2dtuyZYvccsstMmjQIGvC53777RcwUfTdd98N6qWG77nxxhutsA6Pc9BBB1kTQgGTUN9++23rOpZ+/vzzz/LRRx+l8C9BREREREQUCCtpMEgNH7hO5ItQDYFa2bL25xs2OMtCgaGab3H5ZxY65JBDrFBMq8rwOYYrdOjQQW6//XYrLOvXr5+MHj3amgr64IMPWve77rrrZMOGDTJ9+nSpXr26fPrpp9KnTx8rLNu5c6ecd955QdM9x4wZI0cffbQceeSR1uf4Gf/9738Ll56iWu3vv/+WWrVqyf/+9z+54oorrN5r7iENRERERERERDkbqgGWgG7Z4lSqVaxoX+rn5DsM1bIQ+pqZyzTxOarPcCZGw6yhQ4daH6YXXnjB6numfdG6d+9uhWyA7/MKwhC69e7du/BzTAzVSjU81rx58+TAAw+0Pu/UqZNMnjyZgRoREREREaW1VQ6OU6Bp06Y8PqH00YEE2n9cK9VWrbIvmzUT+fJLkcWL07SBFAlfPXJIpDeLMmXKBAwawPXixYtbH+G+t1SpUoXX8/PzrSWgqkaNGgHfW1GTdiIiIiIiojTYtm2bVQyAD1wn8lWlGuzaZV+ecop9uXSpyO7dkR/v229Ffv89KZtK3lipRkRERERERESU7lBNK9V0SMHRR6MJoMjOnSLLlonUqhX6sWbPFjn+ePt6QUEyt5oMrFQjIiIiIiIiIvJLpRq0aSNSvLhIvXr254sWhX8shGrq2mudx6akYqgWhQKmvEnHvzERERERERHljL17Rdas8e6pBocdZl82aGBfLlwY/vHMSbYvvCDSs6c99ICSiqFaGOgPBph8Scm1detW67JEiRLp3hQiIiIiIiKi5LroIue6V6Wahmr160dXqWaGaoABgt2742A7MdtLnthTLQw06C9btqysXr3aCns4FSY5FWoI1FatWiWVKlUqDDKJiIiIiIiIstabbzrXvXqqtW0bf6Ua7LefyHffibzzjshllyVmmykIQ7UwMP0S0ysXLVokS5YsSffmZDUEatWrV0/3ZhARERERERGllrtSDZ8fckhgpVqkUM3tvPNEhgwR+euvRG4puTBUi6BkyZLSqFEjLgFNIlQBskKNiIiIiIhSdfxxyy23FF4nSrtSpQIr1Vq1socUmJVqkZZ/7toV+HmVKvblunWJ3VYKwFAtClj2WVobBxIREREREVFGF048/vjj6d4MIkdenn1Zs6Z92bGj8zWtVFu1SmTz5tCP4S4EqlzZvly/PrHbSgEYqhERERERERERpVu/fiIHHCDSo4dzW6VKdkCGcGzx4tDfy0q1tGCoRkRERERERDlj7969snTpUuv6QQcdxIF05B/oqXb++cG3o1pt/XrJwxLQUK2TGKqlBV89iIiIiIiIKGds27ZN6tevb33gOpEvhhSEs6+vmhWqhcJQLS0YqhERERERERERpcrevc71X3+NfH8sCY3UH80M1dCjjaFaSjBUIyIiIiIiIiJKFXOogIZfkZaFwpYt0Ydq5qACM8SjhGKoRkRERERERESUjlCtZMnI9y9f3r4MN/3TDNXQJxDVbaVLi+zZI/L770XZWgqDoRoRERERERERUars2BFXqJYXbaXaPfeIlCghcvjh9uc//hj/tlJYDNWIiIiIiIiIiFIdqiH4imb6rLtSraBAZNMm71CtVSuRgQPt6x072pdTpiRow8mNoRoREZEHVMrjg4iIiIgoKaFaNFVqZk+1f/+VQ955R4o3aSKy334iL70UHKp16CCSn29fb9TIvly6NHHbTgGKB35KREREK1aIYF+le3eRt99O99YQERFRIhUvXlyuueaawutEaeupVqpUdPffV6lWbMIEaWLePnmyyJVXBoZqqH5TtWvbl3//XfRtJk98BSEiInIZNUpk40aRd94R+d//nJODRERElPlKlSolzz//fLo3g3KZVqrFGKoF2brVue4VqtWqZV8uWxbfdlJEXP5JREQUZiATW1AQERERUVpDtVBneLdtc66Hq1Rbty7wvpQwDNWIiIhc/vrLuf7zz+ncEiIiIkq0goICWb16tfWB60RpO4MbbU+1eCvVKlYUKVvWvs5qtaRgqEZERORi9nJFfzUiIiLKHlu3bpUDDjjA+sB1ooxd/hmpUi0vT6R+ffv6b785t+/dG+MGUygM1YiIiFwYqhERERFRxvdUg8MOsy+nT7cv//MfkapVRf74I8aNJl+Han/88YeUL19eXn311cLbFi1aJD179pQqVapIp06d5Isvvgj6vieffFKaNWsmNWvWlCuvvFJ26D9OIiKiOMyebQ9SUv/8k86tISIiIqKso7lFtMs/XT3VCvRzrVTDWeDhw71DtbZtA3uaPPigyPr1Ik88Effmk89CtV27dsn5558fMM5406ZN0rVrV2nbtq3Mnj1b+vfvL3369JGZM2cW3mfo0KEyZMgQGTZsmIwfP16WL18ul156aZp+CyIiynSohD/00MDbWKlGREREREnpqRZtpVrp0iL5+YG90sxKtWuucb4WrlJt927ndvPxKG5OipVG9957r7Rv317KlClTeNuHH34o1atXl/vuu0/y8vKsQG3WrFny9NNPy/Dhw2Xv3r3y8MMPyyuvvCIdO3a0vge3N2zY0Kpwq6/rhomIiKK0aVPwbQzViIiIiCityz/RG61CBZENG+zP99tPZPlyp1JtwoTQoVqbNvb3//23yNSpoe9HmRmqTZgwQcaOHSuTJk2Sbt26Fd7+ySefSO/eva1ATZ1++uly8sknW9fnzp0rGzZskGOOOabw61WrVpXOnTvLmDFjrMo2L1geai4R/ffffwur5fBBRPHR5w+fR5TJ1qzBfwN3MDCBfPPmXVHv8yQanlM//3yAzJlTILfeusvaJyKiouP7FlHuPrfMbeNxIKVDsS1bBHVie0uUkD1R/vsrXqGC5O0L1fZWqGB9f8HWrbJ71y4pgR3WfXYXKyYF5mOWKiXFGzeWvN9+kz3vvGN9n/UYK1dG/bNzza4Y/i5pDdXWr18vV1xxhYwcOTKgSg2WLVsWVG3WoEEDWbdunWzbts36et26daVYsWJB9/kbCWwIqG67//77g25Hv7ayOmqWiOL25ZdfpnsTiOK2eHEFETk+6PZ33/1GqlXbLukyaNCp1uWePT9Iq1ZW8kdECcL3LaLce25t3+68p48bN05KY2kdUQod/NNP0lxE/t60SX4eOzaq7zm+oECwpwprd+2SA3Bl2zb55vXXxS49ss355RdZ4nrMw6pXlzq//SbbRowQHXmw5tdfZVKUPzvXbI1hKnDaQrWCggJrsMCAAQOkRYsWQV9HeIbBBaYKKHfcF8Z5fV3vs3r16pA/984775SbbropoFKtTp06ctJJJ8l+KKEkorjTfOw8denSRUqwlJgy1A8/BJaB1ahRIP/8kyfNmp0ghx9ekPYzZTVrHiHdu6dnO4iyDd+3iHL3uYWVSxdeeKF1vUePHlIqXeXolLOKTZliXdZs2lSqd+8e1ffk16iB6iPrepUGDURmzpS8ggI5wehNDy3r1JHmrscstmCBtUS0vNHXpNqePdI9yp+da/7dt6LR16Haa6+9Zr2YXX311Z5fx8TPzZs3B9yG4QVQuXJlz6/rffC1UPCC6fWiiRd8v77oE2USPpcok7nfVmrXzrOmf65ZU9wXbSfy8/2xHUTZhO9bRLn33MJ2vf766+neDMplW7ZYF/mVK0t+tM8TowioWKVKhdeLT5wYcLf8jRuDH7N9+6CHy1u92rfP0XSL5e+Stumfo0aNsvqp1ahRwxpIgI8ff/xRbrjhBut67dq1rYEDpoULF1qBGZaK4utLliyxBha471OrVq0U/zZERJQNNm4M/Lx69fQPKzBbOrCfGhEREVEW0EqoWFbL7Vu5BwVon6UVat9+G3g/r8rLww8Pvg0r/Pbsif7nk79CNUzqnD9/vsycObPwo127djJo0CDres+ePWX06NHWMlEziOvVq5d1vXnz5lKxYkX57rvvCr++du1amThxovW9RERE2RCq6VAnIiIiSgwcY27ZssX6MI83iTIlVLOWLuzebV9fuNC+HDhQBIMdBwwI/l70jx8+PPA2FCitXRv7tpM/QjUs4dQKNf0oWbKkFZTh+plnninLly+X++67zxo88N5778nzzz9v9WCzNrxYMRk4cKD069fPmhy6YMECufjii61AzT3ggIiIKJ5QDa0r0h2qmX1SXcXZREREFGcTcvTnxkcsDcmJEr7TGW+ohiq1/fd3PsdqvcGDRT7/HGGL9/eb91crV0b/88l/0z/DwcABTGLp37+/NcigWbNmVrDWunXrwvsgUENftcsuu0w2bNhgVbE988wzad1uIiLKvko19FVLF3Nfn1VrRERERFlUqVaxYvyVak8+KfLNNyInnijSoQMqj8J/v1fv+TFjRA4+WATLSSnzQ7Xx48cHfI6KszH4nxwGJnma0zyJiIiyafmnGarxZDoRERFRFkjE8s++fe2PaHmFalgyipZan30W/eOQP5Z/EhER+c2GDf4L1bZtc6YTsFKNiIiIKAskIlSLlRmqmdexZJTixlCNiIjIo1LtoosCQ7V09TFmpRoRERFRlvjjD5GrrhJZtSr1oZrZa+2AA2L/fvLEUI2IiMgVqj3xhMgrrzih2vbtzgnFVGNPNSIiIqIsgcGLL70ksmePSMOGIrVrxxeqlSwZ+882v+eQQ2L/fvLEUI2IiMgVqrVqZfd6Rc9W7R+brmEFrFQjIiIiyhJr19qXjzwiMndubOFYUSvVAGeN77hD5JRT4vt+8vegAiIiIj+EauYgpkqV7NvDVart3m1PNk8GVqoRERElVn5+vpx55pmF14lSBssfoE0bkVKlYvteI1QriDdUu+QS+/LTT+P7fgrCSjUiIqIwoZrua6NK38szz9j7OK4B1kkZVMBKNSIioqIrXbq0jBgxwvrAdaKU0TOk8fy7MyvVino2lz3VEoahGhER0b7QbNOm2EO1G26wTzo+9lhytouVakRERERZVqlW1FAt3ko1deCBRft+KsRQjYiIyJhs7g7V9EQglni6/fWXc71+/eRsF3uqEREREWWJRIVqec5KhoSEajt3Fu3xchhDNSIiImPpJ/ZxzBYX4SrVxo1zrmOogdqwITBwK4psrVQrKBB59117sjwREVEqbdmyRfLy8qwPXCfKuFCtqCEYdnYxiVRt3ly0x8thDNWIiIhC9FOLVKn2+efe4Req1g46SGTNmqJvlxmk7dghWeOHH0TOPVekX790bwkRERFRBoRqxqTQPH2conjqKecxGS7HjaEaERFRmFAtVKXarl0iX34ZHKotWWJXqsFvvxV9u7Zudcr7E7H/5BdaobZiRbq3hIiIiChFZfp6hrSoAzIStVNYrpx9yVAtbgzViIiI4qhUmzw5sA+bhmrffZe4dhfm42ZbqLZypX3JPnFERESUE8wlB0UN1byWUBQlVOPyz7gxVCMiItrXBy2WSrXZswM/13Dol1+CbyuKbA3VtEItm/rEEREREYVk7sjFGartueEG2XLAAbL3kksSs03ly9uXDNXixlCNiIgojuWfuu+h+0Qafi1fnthQzQydsjFUY6UaERER5QTdkStWTKREibgeYu/jj8tXL70kUrlyYrbpgAPsy2XLEvN4OYihGhERkRGqVaoU3fJPDdWqVQsdqiWiCssMnbJpUIEu/8TfCC1GiIiIiHJmSEFReoQkor+IatbMvpw3L3GPmWP2HSoQERHltngr1XCC76+/klep5g7VEEAlcl8q3ZVq+Lti6IMx0IqIiCip8vPzpXv37oXXiXw/+TNZmja1L3/9Nd1bkrEYqhEREcUxqECHJGnVfPIq1QITNARrftoXK2qlmv6dGKoREVGqlC5dWj799NN0bwblGt0x9GOoloiR9TmKyz+JiIiKWKmmYRoew+zzmuieatnSV23nTpF165zP2VeNiIiIsp4fK9W0j8n69enekozFUI2IiCjM9M9oe6oh/LrttsD7JHr5Z7aEaqtWBX7OUI2IiIiynh9DtQoV7EtO/4wbQzUiIiIR+fff2CrVdPmnhmowdGjgfRI9qCBbhhVoP7VE/p2IiIiitWXLFilXrpz1getEORuqlS/vhGp796Z7azISQzUiIiIR2bQp8ISdu1It1PJPM1RzK2oFFoYShKtUQ4P/bAjVWKlGRESptnXrVuuDKKdDNXPHl8+HuDBUIyIiChOqaaVaqOWfdeqEfsyiVmDZ0z7tQQVlyxYE7I8NH47bRMaOlYweUgCsVCMiIqKsp6FVmTLiG9iWYsUCd4YpJgzViIiIjJBMq+CjXf65337BIZFWtxX1hJ/5/ZUrS0CodumldtDXu7dkHFaqERERUc72GsHOo1/k5QUuAaWYMVQjIiKKYvlnqEq1cuXsJaC4VAcfnNhQrXjxvYX7O+5BBSVKSMa4+26Rli1FZs4MvJ2VakRERJQzO5t+CtVAdzJZqRaXfYcKREREuWvnTvsj2ko19DozK9twkq92bZHffrNva9hQZP78+MMinMhEr1gN1UqW3COlShX3HFSQKaEaQskHHrCvz5kT+DVWqhEREVHW82OlmnlGmaFaXFipRkREOc+sdneHal6DChYutCvGEGgdcIB9G0I1dcgh9uX69bFvC4YPHHaYSPPmzrLSUqX2SOnSgT3VMi1UmzUr9NdYqUZEREQ5E6q5l0Wkm24Pl3/GhaEaERHlPN2HKFnS/og0qODbb+3LI45wes2aodpxx9mXv/4a+3TySZNE/vxTZPlykXfesW8rVWp34aCoTA3Vpk0Lvq1GDfuSlWpERJRKxYoVk2OOOcb6wHWilODyz6zE5Z9ERJTzQvVTC1Wppj3BOnZ0bjNDtWOPtcM5PO6SJSL160e/LePGOddffNGsVPOu6sqUUG316uDb8Hf55x+GakRElFplypSR8ePHp3szKNf4ffknK9XiwlieiIhynu5DeIVqXpVqGzfalxhQ4A7VqlSxT/g1bWp/Pnt2bNtihmoKoVqlSvb1desyM1TzWgqrYSOXfxIREVHW83uotmFD8NfQzHfCBLs/CXliqEZERDlPK9Xc/dRCDSrwCuHq1rUv69SxL7XXmgZw0VZz/fyzff3wwwNDtapV7Z5qa9Z4V9L5nTsMBC7/JCIiopzh155qepZzwYLgrw0YYC/BwAh38sRQjYiIcl64SjUNrcxKNa/loieeKHLLLSKPP25/XqqUeE7rDOfLL+3JooceKtKhgztUE89Qzd0DLlNCNZykLVvWvs5KNSIiSqUtW7ZItWrVrA9cJ8rpnmotWniPZ4chQ+zLRx9N7TZlkAw5v01EROSfSjWv+2MZpgZq8YZquvTz5JNFatZ0bi9Z0gnVUM1mVuBn2vLPnj1FfvpJ5JNPnIEPrFQjIqJUW+M+S0WUTJhcpcsX/BqqzZ1rn93Ny0v3FmUUVqoREVHOi6ZSLdLyTzd3qDZrlnezfoV9mC++cEK1evVCL/80T6pn2vJPrCJYuVKkfXtncior1YiIiChrYWcQwRVGu4M2yvWLxo3tHUosT122LN1bk3EYqhERUc6LplLthRdEfvkl8v3dodrOnfb3tW4tcvDBoe+PgQYrVthLIjt1CpwY6l7+mYnDmbRSrXJl5zZd/slKNSIiIspaU6aIzJvnfF69uvgKeokgWAPstOrZ3eeeS/eWZQSGakRElPOiqVSDNm1C91QLV6n28ceB/Wm96ICCI46wv9esVEMV2/7725VqqHYzK9UQ2mVSpRqmoyqtVGOoRkRERFnrr78CP9ezin5cAvr55/bSiSVLRPr3D7yP2WCYCjFUIyKinBdNpZq2w0DAFc3yTx0ggFAt3LJPpUsgdUVAxYrO1zZuLFUYRmHauVbMZUqoht9t+/bgUI2DCoiIiCjrzZ8vvqeh2rRpzm2lSwfeZ9Wq1G5ThmCoRkREOS9c5ZkZqmmopSfqoln+GW2opr3X9PsCf2apwm3Dzx45MrNCNV36ib+l+TfWSrUffxRZvDg920ZERESUMI88InL77ZkfqmGJ6owZIj16OPfhpFxPDNWIiCjnRbv8E8z+rdGGatEMGNNKLvOkoP7shg03SrlywVNC9fEzZekn+qmZA6XM1Q/nnJP67SIiotxUrFgxadeunfWB60QJgalWd90l8thjgWcLMylU0zPH2CFt0kRkzBiRAw+0b+PSAk8ZMjOMiIgo/cs/zbYYCITcX4umUg37W17f51WpNmeOyJtv7pGmTedJfn4d62ei/5hWfun2YFmqn48JzFDNZIZq6OFLRESUCmXKlJGpU6emezMoUyF4Qpn94Yc7ZfdayYWdMli40G6Qix2/BQuc+1x4ofhSgwZ2kOZ1lpdNcMPy8S44ERGR/yrVNFQLV6XmDtU0tNPPvXiFaoccInLPPXulXLndYXu43Xyz+JqGgGY/NTD3Q92BGxEREZEvDRokcswxIpdcEni7ucOHUA1Qsaa9Oj76SOTFF8WXcMa3Vi3vnTQ2wQ2LoRoREeW8WCrVdPlnuCEFZjiG/ShzWJKeAHTzOjHoZm7fwQc714cOxTADyajJn+6/bc2aqd0mIiIiorg89JB9+d573mdp4c8/A5d+HnqoSO/eEtDPw2/MM7usVIsaQzUiIsp54SrVMO0znko1c/qnWZ0WS6Wam7l97do517GP8+67knHLP7HSQJUokdptIiKi3LV161apV6+e9YHrRDHBkk4vZqimlWoaqqE/md+FCtW0Uo3PFU8M1YiIKOeFq1Rzh2AaqkVbqYbvN4clFaVSzfyZaONhGjZMMm75J4LH776zr/u50o6IiLJLQUGBLFmyxPrAdaIimz1bZPDg0KFa06aS8aEal3964qACIiLKef/+Gzooc4dqsS7/dIdqRalUM0M/s1JNJ6DPnCnSurVkzPJP2H9/+5KhGhEREWUsLO80ZWKlmhmkcfln1FipRkREOQ2V+rqPoBPDTe7KMg3Voh1UgLBIB0FFE6pFU6mGXmStWjm316hhX778svi6Us1rGEHFis7ficUCRERElBVwRnHDBqe3WqNG4nvmmV0OKogaQzUiIspp//zjhGTRLP/UAC7aSjWt0op2+Wc0PdUwFdT8+Q88YF+++WboNh9+rVTTUA3b7T4Bir8JesWtXZuCjSQiIiJKpAULRFautK/Xri2+x55qcWGoRkREOe3DD+3L6tW9vx6qsizaSjV3qFaUSjX9mVhhUKyYyMcfi7zzjsgFF9i344SoOc09E0I1DMHSKaDuJaD/+Y/IueeK9OqVgo0kIiIiioVOpQp1xnTyZHu5AnZ0qlUT3+P0z7gwVCMiopy1erXInXfa16tW9b6PGYI1buxcj7ZSzR0UhQrVoqlUO+kkuwdZnz725wibzjnH3qfT79P+cJmy/DMvT6RSJSd8GzFC5KWX7M//9z/7ctKkVG0pERERURhmrwoNm7BD6eX7750ztzgb6nccVBAXDiogIiLJ9aWfoC0v3G64QeS550T69hXZbz+7kj+aUE1PXrqFOpkZzaCC7t3t/TYEUW7YHjyGH0O1cJVqUKuWvcQTk1XPPtu+rUsXnhAlIqLkyMvLk2bNmhVeJ4qpGa87VFu1yvu+EyfalzVrSkbgoIK4ZEBcSkRElBzmVM6WLb3v06CBfb9XXhE5+eTYl3+6nXKKd0N+DdvCLf+EUPv+CPzAb8s/seoBy1LDhWp16tiXGlhqELd7dwo2kIiIck7ZsmVl7ty51geuE8Vcfg9afeYO1TSEWrEis0I1DiqIC0M1IiLKWebJxnCTMxF0Icw65hhnfyNSpZq5L+KmE0RjrVQLR0M1v1WqDR7shIheyz/NUG3uXOc2c2IqERERkS/omUJz502HEbRoIfLIIyIjRwZ+TyaGahxUEDWGakREJLkeqnXsKFKvXuT7o6l+t2729YYNw98X/dfat/f+2vLl8Q0qCEdDPr+FavfdF3lJrIZqv/7qHXi692GJiIiI0l6ppjtvumN3+OEit98u0rWrfT1bQjU09IXFi1O/TRmAoRoREeUsXSoZaSmn6bXXRGbOFDnqqPD3w4qATz+NvlItmkEFmVapZi5zPeig0PfzqlRzh2oIKVm9RkREibB161Zp3ry59YHrRFEzz/LpztvffweHZ2edldmhmrnkolMn+/Lnn3mW0wNDNSIiylka3ERayukOr1q1iu6+mCiK/az+/QNvR0P+RFeq+bGnmg4ocFehhQrVzP0099RUDGgYNizRW0hERLmooKBAfv31V+sD14niqlRD89c9e5xKNUxe8jqbmCmhmld1GtSu7Zzd/O67tGyanzFUIyKinBVPpVqssB/1zDMil1yS3Eo1Py7/XLTIvqxRw146GylUM40ZE3wbwsmffkrgBhIRERHFG6rpWVGvSrVMDNXMnVAzVIPjj7cvv/kmtduUARiqERFRzoqnUi1eL74ocsYZ3qEaTpInqlJt/Hjxjd9+sy8j9aszT+yqDz8Mvm3nTvtvqH8rIiIiopRyL3/ETolXpZp5HWcXMy1Uw3IL03HH2ZfffpvabcoADNWIiChnpaJSzdxP6dPHe/knVg9ov7Ci9lTDvs6SJeILI0bYl0cfHf5+CBKrVQu8DaspTOecY4efCCQXLEjwhhIRERHFU6m2ZYvIihXBFWkow+/VS+S004IDKr/Kz3euu7f52GPty19+sXtyUCGGakREJLleqZaKUM1c5uiuVDMrr+IN1cx+uEuXii9o1Zy5bbEsAa1f37mO0E0rChFCEhEREaU9VENpPc4EYkLVgQc6t+fliXz8scjIkfb1TICAUFWuHPi1Aw4QadnSf8sifIChGhERSa5XqqVi+af2eQWsEjArsRIRqjVp4gxQ2LZNfEG3A/th8YRq110X+HcpXty+zlCNiIiIfLH8c8AA+7J69cBKr0xkjl7XnS7TYYfZl3/+mbptygAM1YiIKGelulIN+1s4kYlQaNWq4CEF2Bfz2oeJlk4/90Oohj5x6IEWbVDoDtVKlhS56CLn8xIlGKoREVFi5OXlSd26da0PXCeKu1ItXIPYTA7VvGjjX93BIwtDNSIiylmpHFQACIW03YbZV62oQwr8GKrt2hUYkMUaqvXtGzh4Co/BUI2IiBKhbNmysnjxYusD14nirlTLtAmf4Vx7rT2u/dJLvb+uO3TmTh4xVCMiotyVykEF7iWgZl81rVSLd+mnH0O1WJe0mqHaW2/Z01JNDNWIiIjIN5VqQ4dmX6XaQQeJrF0rMmxY+FCNlWoBGKoREVHOSnWlmhmqnXGGyJlnZm+lmhmqRVOpZv4/OOWU4GWwCN0YqhEREZEvQrUaNbKvUk3PhIZaEs1QzRNDNSIiylnpqFQzK7IwMAoV9ImuVNu6VdJO97fQJy6avr2dO9vT2487LvD/x3vv2asRzjuPoRoRESXGtm3b5PDDD7c+cJ0o6jOG+u/FHaplQ6VaJGhwCwzVAhShHTIREVFmS2elmsK+WTZXqkUbFFasKLJ0aXBV29ln2x/AUI2IiBJh7969Mm3atMLrRDH1U0Ml14EHZmelWjjsqeaJoRoREeUk7EOnevqnV6iGqrJYA6hMCNX0JGY0Sz/d2x8KQzUiIiJKe6i2336YdpF7lWpc/umJyz+JiCgnmUskUxmqNWoU+DkCsEQt/9T9Oz+EaokKCr1CtT17EveYRERERDH1U6tcOfhMYC5UqnH5pyeGakRElNP91FDB7z7ZmEwtW4auVMum5Z/xVKpFwko1IiIiSpstW+zLcuWCzxpWqiRZj5VqnhiqERFRTjKXfoYacpQM7qmWCNUSPagg2yvVGKoRERFRymmYhJ2bYq4oJZU7k+nCnmqeGKoREVFOSseQAvXLL9lfqZaMUE2niJqh2qJFIvfdJ7JmTeJ+DhEREVHUOze6LDLbsVLNEwcVEBFRTi//TGU/NXMJaJs2IjNmBE7/zKZKtVQt/+zYUWTFCjuoHDkycT+LiIiyW9WqVdO9CZRpQu3cFPWsaKZgTzVPDNWIiCgnpbNSDbSPm7n8k5VqsYdqCNRgwoTE/RwiIspu5cqVk9WrV6d7MyjThNq5yZVQjZVqnrj8k4iIclI6K9XcoVqiK9XMyaa5MqggV1ZeEBERkc92btyTQLMVe6p5YqhGREQ5KRsr1WrVsi9nz3ZCw1wZVMBQjYiIiJKKlWr2JSvVAjBUIyKinJTuSjVzqWaiAqjDDxc55BA7MHzuOZHevUVGj7Yf/7HHRBYulJRhpRoREfnVtm3b5Nhjj7U+cJ0oKuypZl8yVAvAUI2IiHK6Ui3dyz8XLBDZsiXwtnhhmnu/fvb1gQPtQA3B2pAhIrffLtKwYeD9EU7t2SNJwUo1IiLyq71798qECROsD1wnigor1exLhmoBGKoREVFOV6qla/mnVqq98IIdrCVqWy66KHhf748/nOvLltmXWHLapIlI586SFAzViIiIKKsr1SpWtC979pScwJ5qnhiqERFRTkp3pdq8ec71r75K3Lbsv7/ImWcG3mY+7owZzrTMP/8UmTTJ6emWSFz+WXT//itSUBB8+5NPilx1VeKqDPE4Tz8t8vnniXk8IiKirOQ+YzhzpshLL9nLAXIBK9U8MVQjIqKclO5BBS1bBt+WqG059tjAzzdscK5PnWqHaWZ/tfXr7ctZs0SmT0/MNrBSrWg+/FCkShWRBx8MvB2tf7Dvjn34H39MzM96/XWRAQNEunUT+eabxDwmERFR1tEwSXdu6tWz+24k8gyin7GnmieGakRElJPSPajg3nudVQMqUdvStGnoUG3wYJF27exKNTNUe/NNkdatRTp0CLy/nyvVzEqtbArVUJ2GakP8fvffH/i1X35xfv/vv0/Mzxsxwrk+f35iHpOIiCjr6BnDXAnR3Fip5omhGhER5aR0V6pVrizy7rupCdU2bgz8HKGZGaSsW2cvA9U2GatXF30bUA0H7uAwkaHa1q3ZGapp4AuHHRb4NbOSMBGhGv4t6PJjWLOm6I9JRESUE5VquYY91TwxVCMiopyU7ko1aNEi8PNEBXxYNqgQanlVnpnDzlCpphNI3WFVvIElJo9Cr16SMPn5gRVq5jbr17LBypXOdfdE2GnTnOtY/hnv0Dr8m0BF3CefBO4bM1QjolxRtmxZ64Moarleqcbln/4L1Xbv3i2DBw+WevXqSYUKFeT444+XGdpBWUQWLVokPXv2lCpVqkinTp3kiy++CHqMJ598Upo1ayY1a9aUK6+8UnboP3QiIiIfV6pBrVqBlVyJDPgQlmjAFmk5JyrVzCDNDKvigUANj9ewoUj79pK0SjVzO736rGVDqOYeImFWquH/69y58fdru/56kb59A58HDNWIKBeUK1dOtmzZYn3gOlFUWKnmnN1M1LSkLJDWUO3hhx+W0aNHy5gxY2TJkiVy8sknS/fu3WXjxo2yadMm6dq1q7Rt21Zmz54t/fv3lz59+shMTNjYZ+jQoTJkyBAZNmyYjB8/XpYvXy6XXnppOn8lIiLKEH6oVMvLC6xWS+S21KjhhE2RQjVUqpmhWlEr1d5+27487zz7d0yUcKFaNp00XbHCO1TDkAIN0Q491L589tnYHx/92lCl9txzzm2YJgoM1YiIiELI9Uo18/fmEtD0h2p79+6Vp556Sl588UVp0aKFVY122223WRVrkydPlg8//FCqV68u9913n9SqVcsK1K6++mp5GjPf930/QrmXXnpJOnbsKI0bN5bhw4dbAR0q3IiIiEJBoLBqlX19//3Tuy1mqJbIqjkNoLDPk8pQDf3Yxo2zr59/viSUO1SbPTs7Q7VQlWqYzooTwwceKPJ//2ffZvbGi4ZXZdsZZzgTYxmqERERhZDrlWrm7+0upc9haQvVtm3bJoMGDZI2bdoU3paXlydlypSREiVKyCeffCK9e/e2blOnn366fPzxx9b1uXPnyoYNG+SYY44p/HrVqlWlc+fOVrBGREQUCoIDVP1AnTrp3ZZkVapp2wtUc+nvqo4+WgSF3Y0bO8s/E9VTDQMPEPw0by5yyCGS1FDNfLvP1ko1s6uF9lNr29YZYIDANJb/X8OGBX5+1FEiH3wgUq2a/TlDNSLKBdu3b5cePXpYH7hOFBV9U87VUA2Varozpn1USPb9RVIPa9evu+66ws8LCgpk5MiR1hJOVJ7deeedUr9+/YDvadCggaxbt84K5JYtWyZ169aVYsWKBd3n77//Dvlz0XPN7Lv277//Wpe7du2yPogoPvr84fOIMsHChfhvCalRo0CKFdud1gr2Jk1w8qi4lCxZIHl5wdsS73ML1Xj4Hc1Jkqpixb0yZMgeefTRYnL33fmyadNe2bIF22GfyNq0CdthPUDM1q61f58aNfbKrl2J7beRl4f3/HzZudN+7MmTixdu844dBbJrV3Y0Vlu6FFMX7P2b7dud32vqVPv2Nm32SJkye6Vs2eKydWueLF26y+pfFwmOG19/3fmbwX772X9Lu0qyhKxblz1/x0j4vkWUu88tBGljx44tvJ6fTdNuKGnyd+yw3p13FysmBWn6953u51fxChUkb/162YUzsiidz1Kx/H3TFqqZ7r77bmsp6J49e+Tbb7+V0qVLW+FZedcpeywNhfXr13t+Xe+zGmtPQsCS0fvvvz/odgxB4PQXoqL78ssv070JRBFNmoSGY+2lQoX1MnbsxLRuy7ZtxaVy5ROkdu1NMnbsjwl7bq1Ygfe0LoWflymzS7Zts8vXFi5cJ2PH/iBLljQQkZbyxx//yJo1lXDKy/r61Km/ygEHxNdKYfJknBA7VLZsWSFjx06VRFqwwN7ev/5aLh9+OEsWL+5R+LWNG7fK2LFfSTb4/ntU4VfaF3DukrFjP7Ouz5rVQUQOkC1bZspnny2T/fY7QbZuLS8jR06W5s3XRXzcBQsqy7p1Rwfctnnzchk7drps2IA+Kd2scHXMmLHiOmeZ1fi+RZR7zy2zOm3cuHHW8SdRJJ1WrBB0DZk+Z46sSGdT3jQ+v7oULy7Yw5w0bpysz+K2W1tjWAbgi1Dt1ltvlUsuucQaNnDuuedayzfRY22zq6QQwwugcuXKnl/X++BroaAC7qabbgqoVKtTp46cdNJJst9++yX09yLKJUjz8eLepUsXawk3kZ/98YedGBx6aCVrQE669eolkp9fSfLzuyfsufXXX4GfV61avPC2vLz9rd97xYo8azlgxYo1Atot1K/fXLp3bxrX7zJ7tv23bdy4esL/tkuW2I9drVpNqV27esDXihcv64v/l0WBPn/ff58ny5Y5FRN79pQo/L0eecS+/aijWkn37ofKwQfnW0tF69btIN26FVj/fw86KPTj5+fb/49Lly6Q7dvt602b1rAeH0uEL77Yvt8xx3RP61TcVOH7FlHuPrcw9VNhWB4ngFI08gcPti7bduggBd265eTzq/gBB1gNdDu2bCkFJ5wg2UpXNPo6VFu5cqUsXLhQOnToYIVZ+MDSzSlTpsj//vc/qV27dtDAAdwfgRn6ruHrmBiKgQXmElDcBy+MoZQqVcr6cMM/SL++6BNlEj6XKBOsXWtf1qxZTEqUSH9JTjRPmVifW2XKBH5eqVJeYahWsWKe9Vh6LmnbtmIBfbl27MiXEiXiWwqjj1OpUuL/tvr2vXdvMZk3z37s2rVFli1DTzX7d8pk2Df97bfA2xB+FS9ewpqiqseAlSoVt/7N1Kxpf75yZXE59VSRzz+3J3pee6334+u5yNq18+SPP+zrlSvb/6/RIgW7U3v34meWkDDnJ7MO37eIcu+5ZW6Xn7eTfGZfA9fiCGHT/G8mbf9u9+08FsfZuCx+3pSI4XdL25EElmgiXXU3hkS/tIoVK0rPnj1l9OjRVq81NWrUKOmF0/mCBsjNrft99913hV9fu3atTJw40fpeIiKicNMuoXJlyVrufYFKlUQ++USkXTuRl16yb9MT8whrzEEF5vVY6Ym9ZFQ6mYMK5sxxmvZny6CCP/+0L7t0ERkyxL6O3SAdzKD/X3TFSQOshhWRoUPtQA1uvz30lPuNG4OHc+iKJ4R2+v+MvYeJiIjC7OSkeelnWunOQgyVXNkubaFas2bNpF27dnLhhRda1WXokTZs2DArSDvvvPPkzDPPtIYW3Hfffdbggffee0+ef/55GTBggL3hxYrJwIEDpV+/fjJp0iRZsGCBXHzxxVag5h5wQET+DjfuuAM9nNK9JZRLciFU0wDKDNVwzgnPNUzmNEM1/D0wsTMR0z91HysZHRX0d0Kl4ezZ9nWdgpnpoRqCMA3P3ntPpG9f52s4//jppzpgw9mX11UXZnUbgrdQEzw1VKtVy7nN7J2mj+s13IKIiCjn6VKH/dFZLUfpDh53FtIfqiEUGzFihDVYANM+69WrJ2+99ZY1MOCQQw6xbkfTyOnTp0uLFi3kmWeesYK11q1bFz4GArWrrrpKLrvsMjn22GOlZs2a8sorr6TrVyIiw2uviTz0UOT7degg8uijdnUFUarkaqjmpvN53CFMqFANgRxC8HCVTKkI1SZNEvn22+yqVDP/5vj/YnaqQKhmFuFr+NWpU/AyX4gUqpn/7s2Bd6xUo0yG154bbsBAj3RvCRFlJexo6BtkLodqurPw9dd2M1hK76CCatWqhQ3BUHGGoQXhYOiAOXiAiPzhuuvs953zzhOpVy/0QaRWWOgBMlEq5OryTzezUi2aUK19e/sSQc6993rfR09cJiNUc58UxZLFVq3s6+gFhmo7MyTKJPo3R+VYyZL274ZL7MO7OmUUhmpYunnMMc7ST/eJ9FChmvlvgZVqlC3uusvuKfjMM/ayaaJwMJjAbDNEFJG+ueKNs2JFkVwP1UaNElm3TmTCBMl16e/OTERZB/soeiIHk+lCMZcsmQfg33yDKX9J3EDKeblYqea1/xdq2FmkUGXBgvT0VNPG/ObvZAZEmVythn6/GljqIFbtd+YOOc1WLl6zmUJVqm3YEPxvoWVL5zor1SiToYJV4TiPiCgpoRom+ZhnpHKNedD2ww9Fa8SbJXL4XwMRJYt5YLt6dej7/fpr4IE4DhwnT7b7BIWqbiMqauD78stiTD6UrIWKLQ1nIlWqua1cGXybWTgebtVDMpd/YgmkDlnQkAjBk/6eoSq0MoEGZ7ok1wzVOnYMvK+5NDSWUE0r1RCqTZliD0Po0cP5OivVKJOZq5BmzkznlhBR1odqucw8a4olAtOmSa5jqEZECWcuVYo2VIN//rFDNaJk+ewzkcsvdz7P5lDNXa0Wrqea2jcLyHouuu0bvh0xMEtmqIYTw/36OT0Yn3rK/h11CShOmGZTqKZLeN1VN2ZY2qSJyEEH2dd1TlOkUA3/FrCU98orAx+LlWqUqVAV/9df2VG1Sqmxfft2Oeuss6wPXCeKiEMKvHeef/xRch1DNSJKW6g2b17g5ziQN5c14cAO718jRtj9koiKyv2+n+2hmtlXLZpKtauucio+zGmgmExpCnf8oVVOyVj+qR580F7q1b+//Tn6isHEiZJVodrff0f+PoRib75p/03OOit8qKa3hzrJ7q5U++mnzP6bUu5wTxBnqEaR7NmzRz744APrA9eJImKo5r0TMclYe5+jGKoRUcLt2BFfpRqWnJnhGQ4oTzlF5OyzRUaOTMKGUs6ZPdu5fuaZ3pMTc6lSzWzqjyWGDRvaIQ2OL8xgxj3cyaxkMvs84/u0tUYyKtXM7T7ySGf7mze3L5culYzvqWaGat26Rfe9nTuLDBzoVKphqa75Oqz/n7SSp3Zt78cxK9Xw/UccIXL00VwOSv7HUI2IUvZGbVYA5CKvUK0gt4d+MFQjooQzq1hCVUzggE37Wh12mNM8XpcnwWuvOZVFs2YlbXMpBw+8EEC8/bZkvUihmunii+37V6sWvATUPXBEQ7W33rL3rTBcBMzwJZmhmpueNM7k5uRaqWYGve++G3zyIZwLLhCpXl3kzz/tKYgmvLbq/7dQoZpZqWYGlFwOSpkWqrmra4mIikxfWNyToHI9VFuzxjmoy1EM1Ygo6cs/UWWGg0PT77/bVS1omN2smROqaT8mePhh5/rChcneasp2qHxEUIS+XAjVzKWRubr8ExAu3n23yKWX2p/XqGFfmv2J3IMLNGRBiINhARggAPr8LVkysJl+qvbvsiFUMyvVEEw2bRp4v3BVuwjF9HVz0KDASuFly5y/lbuXnlel2uLFzu3uqjciP0GBhIZqVaval6xUI6KE273bvmSoFnzbpNxeAspQjYiSGqpNny5yxhki557rVLsg3NCQDYGa9rXCwblZqWZC5QVRUehBF5YKhpp6mc1ChWp4biKA0aWUhx5qX/78c+RKNfeKiFT0Uwu3f5dt0z+VOUzgtNPCP07fvvb/QwSco0c7t0da+umuVFu0yLmdPbzJz/BvFc99hPlt29q3sVKNiJIWquXCWdloQzUdUz59uuQyhmpElHBmVYNZ4YKJyzijfMIJdlNtd6jmrlSDLl3sS4ZqlKhQ7fDDJWdoUBPLckz9+6BJvTtU0/0o7ZuWysmf0VaqZWpbj3ChWqNG0T8OKjHRCw2WLw8eehAuVAtVqaahKVG6/fJL8JAjfW3HFGD9N8xKNSJKOC7/DN5R0f4b69dLLmOoRkQJF6qqAScxUP3y22/ObQjVtILGXamGCpphw+zrOAudqQfL5A+5GKqZFWXRnlht394J1fQ5p+E4Bhm4H9cPoZru0+Ekcqb2//IaVKA++MAOyr74IrrHOvDA4AETujQ23NAys1LNXP7LSjXyA7SNQHCGoSpaMOJ+bdfXOYZqRJRwXP4ZXD6vjWA35fZEI4ZqRJRwoQ7A5swJ7K2G12FM9zQr1TRUQwN09HrSr+Hgngd2FC/8+0GlZK6FavEE0ThoxYEp+s4uWRJYqXbwwf4M1fBaoisQMrWvmtegAtWypcjkyU7lbiQHHBBcKawnkfU1NVKlmvm9fO0lP3jiCecEnLkk3XxtxxJQ4PJPiqRs2bKyefNm6wPXiSJiqObo0MGZcgWZekYzQRiqEVHChWpqjb4nw4fb1885R2TmTPsg3Wv5JwYYgLmfE2rJGVEk+HeloYK78TsFQjiFYM1cAqoHsFqphqWB7pOSe/emr6daNvRV03+f+tpXFBqqmZVq0YRqZqUaQzXyE4T8mAiudGkzThzodPA2bVipRtHLy8uTcuXKWR+4ThQRe6o5JkywX5hbtLA/x/RPTKDLUQzViCilyz9xwFunjsibb4o0bmzfbg4q0IN3neCF5ulagcJQjeKF930NaXPxhHSoIQXRLAEFDVgOO8yp+n/sscD9SlSZpqtSLRsmgGpIULNm0R9Ll396Vap5De1SrFQjv5oyJfDfofYIxFRb7DugcKRJE1aqEVESsaeaAzuA6CehOw6LF9t9e3IUQzUiSrhIB2BXXOFMGjQP+Fevdg4s69Z1vq4hiNl0nSgWWr0Urp9UNqtevWihmobdqPK76y7na+aBK4IYhmr+CNW0Ug39K9HYPdZKNQSkeD1WDNUo3TREc38+e7Z9iUCtVCknVGOlGkWyY8cOufjii60PXCeKiMs/Q+84wIgRkqsYqhFRwoXbN0GYdtll3q/HOrkPO8Z6UAjlytmXrFSjooZqWgGZa7RyKdZQDdWlCMu01yEe5+ST7evo8WVKd6imgWmmhmoaEtSqVfTHQjWwwlLef/5x/i7R9FTDUl58KIZqlG7mJFvz+bJwoX2ple9+Wf65YIFdQUf+tXv3bnnttdesD1wniojLP4Olo9+HDzFUI6KEC3cAduqpwZUYGpqpgw4SKWa8OjFUo0Qt/8y1SjUNt848M7bvO+QQez8J1aHffmvfhgoQVJViYi9ogKbw/PRDT7VMDNXQhkSrARNRqYa/P05OqPnzo6tUc78WK4ZqlG4aoum/X/Rk9Qqj/bD8E4EaqnqPOopTy4myCpd/BmOoZmGoRkQJ53UAVq+efXnttZEP5Myln+byT4ZqFK9cXf6JBt6vvy5yzTWxfR9C7Xbt7Osff+wsIUU/NYRXXstJ8fz0w/LPTBxUgKWWCNbwdzerdIvipZcCQ+VoeqrhOKFaNfu62XuQoRqlwjPP2IPkvHpda6WaDppD2I/XnFChWjor1b75xq70/PVXkRkz0rcdRJRgXP4ZfvlnDmOoRkQpWf45erTIxIkixx8f/DV343h3qKahG3uqUbxydfknwuwLLwys/Ix1CeiYMcFLSLVazY+hWiZWqunyWkz+TNS+et++IkceaV9/4w07VMNjR6qE++QTkXfftSvndKn+tm2J2SaiUBCk3XGHPeFz7tzQodqJJ4o0aGDvZ4waFRyq6aqsdFSqoSrt9NNFrr7aue3TT1O/HUSUJFz+GYyhmoWhGhElnFdVA5ZCdOrkfX8c8JvBWqhQDWex//wzkVtKuWLVqtysVEtEXy5dlmhWpzVvHnx/hmqxQSjw4IMiL77onDBI5GRaVBW2bu0EZdC/vx3chXPEESJ9+tgrOnTyMivVKNnw3q7hrS4jN5nhGQJjePllf1WqYbs/+ijwtmnTUr8dRJQkXP4ZzP23KMjNNe8M1Ygo4fQATA/IojmpYy4BDbX8E5VuPXokbDMph0yZ4oS7FB3z+RttqJbOnmqZNqjgoYdE/vMfe2nuhAmJD9Xc1YW4fu+9sX0/QzVKFZ1S61WVjoBMp9EiPLvkEjs0xhJQTLiF2rXTP6jAa+n51Kmp3w4iShIu/4xsZ26OXmaoRkQJpwdgaGoeT/VwqEo1wA60ufNNFAkOxtBbDI47Lt1bkznKlAn8PFOWf2ZKT7XZs53rd96Z/FDt0UcjV6mF+jfAUI2SzXxfd/dPxfRarUJDeI5hRl26OF9HH8KGDZ37pGv5p9drD7bdPbmUiDIUQzVv48Y513O0V0+RQ7VNXjXaRJTTtKcalmjgILF798jfE65STZfuKfRcIYoW+vlBq1aBIQPFVqlWo0ZmhGqZUqlm9p7U0ModZBaVLv/s3NnurRcrVqqRHyrVNJRCP0BUqMHllztfP/lkp2+kufwTy54HD07daiR3qKZL6LkE1J/Kli0rq1atsj5wnciycKFIr14i06cHf4091byddJLzN2GoFtke1zieGTNmSMOGDWWHV1dyIspZegCG5RioEtJ+PtGEatgx1t4oSpd3qDffTM9ZaMpMH3xgX6JPFMUfqiGUVF696fwUqmVCSw+vFRKJPq7r0EFk5kz7JHI8wyoYqlE6KjfdlWraN80csnHqqSJnnilywglOpad7+ecpp4jcc48zLACHMToUJBWhGoYqAJeA+lNeXp5Uq1bN+sB1Isurr9oTmvDi4caeaqGVLev9Ap4jot7FWrt2rVStWlVeeOEFKdi3tzpw4EC54oorpE6dOlbCj48yZcpI8eLFZWeOrqclosCeaniNjeZgTpd/IlBznwB6+GH78qmn7GUeqFz7/PNEbzVlK50kx6Wf8YdqeA5r1ZPq2dOpEtEDSt3fTGdPNWyD9ijzMz0fqRM2IRnFEghD462A02m5S5cmdJOIAmDRC4pDlPuYTCvVzBNuqEgbMULkq68Ce2Vqpdq8ec5teOz337fvV62a0w4g2aFau3b2JX52jh5nEmWeNWvsSzRtdJ9R4vLP0Mru24FhpVpkHTt2lG+//VbOO+88+eKLL2TOnDly2223WSHb1q1brY9t27ZJhQoVpKS+qxFRztGDRXelSzSVau6ln3DOOfbZ5QEDRC64wL4NZ6A//jgRW0vZDPtDWuWgPXcoOmYQ07hx4BJtGDlSZNkyuxrK7HuUrlDN3F4EqHv3Ska8TprLav22AklDAayCcS1WIEqYOXMCPw+3/DMSPSn311/ObbfdZlcq//67HbonazmmGap9+KHI4Yfb1xcsELn00uT8TIofVlpde+211gdXXVEh7SGBccTffx/4NS7/DK0sQ7Wo7b///jJixAirYq1Xr17y2muvScWKFYNKZllCS5Tb9MROqVKJCdXM5WQXXeTcdt99cW8iZTksO0YFw6JF9lJAhDxadUPRMUNxXVppwj4lKkf0uauhGj7Pz5eUc+96+P0YSQv6zamqfgvVmjSx/39u3iwyf366t4aylXv40JAhzrFrqOWfoXid03e/FqxYIUkN1QYOFDn9dJFDD3W+hmo18pfdu3dbK7DwgetEQem4e1kMl3+GVpahWlQ+2dcUCctAf/nlF6lXr57MRKMOwQFLBjQvIaK0LP+Mlk4KrV8//P2wk4qm29qDhS8/uQ0H+tdc41TrKxzQYDocDm60So3ne2JjPn/DVZ5pKPTrr+nrp+bF76Gabp85PCOWExGpgHD0kEPs64sXp3trKBf6qWmV2f/+F375ZyjhFspo2J/sUE2XouP5bFaoRTtE5bvv7IFM3L+htEKvlVz8R2g+Uc2plsDln6GV23eGlaFaeO+++67MmjVLunfvLnfffbcVst1///3y274O4qtXr7amp6xcuVL2+n3NBRH5bvnntdfavYWuuCLyfbXpNt7bkrVzTJkBPXJefDGwUTVoxf6oUfblEUekftsynbmcMlyopr3WtAF4OpZ+ZmKoppVq6BPpvs1P0INKqz+JklmppgEuoNI4nko196os8zH1eqpCNXj5ZWcKaDTVnsgwzjhD5OKLRe69NznbSRQRloLgjI+ZbudiqIa16ehzobj8M7SyHFQQlc8//1xuvPFG+ffff63BBI0bN7aGFDzwwAPW5+3bt5cjjjhCjjzySKlcubLs4mg+opwVT6Va8+Yiw4aJHHRQdAf7jRrZ1zmqPneZ1WlmU2p3NQD+bT3ySOq2K9cq1bBE0LwvK9Vi2z6zVx1DNco1CJE0VDNPfpjPi1gq1cyl6niMY45Jb6hm/lz0dAv1N8DviEsso9f3NgxiyCSTJgUX9lAG2rBB5P777esffSQZW2V3xx3xNV/WnUh98/vmG+drXP4Z2pFHivToEVh+n0Ni+hdx8cUXS5UqVWTMmDFyyCGHWNM/GzZsKIsWLZJKunaLiHJePD3VYnXCCSIolMXU6169kvdzyL9Gj3au//CDc+DVvbtzOyobPv3UWV5M0TODsnDPZexbYln2Tz+lP1T78UcMVcqsUM382/pxmxmqUTJhqSeqXPE6Urt28OvP+vX2dNBoQ7VmzQJPBui/33SGarrdoX4uOuyceqp9HQOZ1JIl9sCVaCaopxsGmehrL4JB99+AMoiWhkb7pPMbBIGXX26HY9gJxGSzaCE0+/dfZ2cS67Cxc9O3r30bl3+G9uCDkstifpm+4IIL5Oabb5Z//vlHzj77bLn00ksZqBFRkZd/xqpnz+ATSJRb3GfxMSUWfdRmzXL2h9DnK9TwC4p++WeklQ5t2jjX0xmqYRKpHkRruO9XWpVm9oBiqEa5Zu5cJ/DSJeSg17W6C8fG7gnEXszXqnr1AsMdDdX++EPkyy8lZaGaFm6ECtXME0T/93/OdQwfNPMNP9OTWrByZTq3hIoMSbaf35TCQQqNtdNabYZ/mObggUjMFyHsUIKeMQSGahRC1P8iHnroIesSI4cHDRpkTUrBkk9MBNWvmVDFRkS5KZ7ln7E6+GD7kj3VchP2l77+OvC2iROd6717i7z+un/6e2UiM+yJtP942GHO9XT/zbXyK5ZjAexzv/WWyLnnBla2pLpSzY8taRmqUbL07y/y3HP2dbR0wPLP55+3P9clkBqqacuHaGDSJvqRoZ/Z1KnO7Y0bO9dPOsmuqDKn70YLyzQxXAE9PTXEQ0iuFXWhQrVQYZO5ZLV8eXvarkJFvvZk8zNM2vbKZCgDZXKohjcqVJphMhWq7NAPDX3RzHXg4Whohu/X0ssZM+wlsSgiYk81KmqlGnqk4SMvL8+a+rlx40b59ddfZcOGDYVfMz+IKHelYvmnHuhh59PvFSmUeF98Ye/btGwp8vjjgVVSqFRD9X+6w51MZ05LzZRKNTPMj+VYAFWON9wgcvbZkpZKtYcftvfXH31UfIehGiUDCkI0UIMGDUTOO0/k/PMDQ7UFC4IDsUjOOsuuUsZSUDPgMpeXxjvRFq2aUDXXqpXIk086t2thDF433Qt4IoVqOizvnnvs3xvvYSeeaN/m10o1ZBVYGaevY2aoFkthUDqVKVPGamGED1wnj1At03awJ092nnQ6RUnLYaNhVqLVr28n57ht7Fj7dvZUoxCi/hdx774RNP/9739l5MiRsnnzZvnwww/lySeflOOOO86qTDvAHGFFRDkJZ3BTUalWsaJ9oI/3NxzsZcKZXEoc9JrRMOeWW0ROO83uS3PccXZ/L0qsSKEaws38fLuvTrpDNQ3zUYmCwWU4UI9EJw2OHy8pgX10rUrD9qKf8m23+bN3UtWq9iVDNUok90AZhGp4DbnxRrtqFOESniPaJ71Fi/h+jrlkFPsNoZYsRgvPVa2Qx3AlVEXfdRfa49i3Va5s/x6xhGo6LA/bitcDvIfp9/j1eXf44fbfAf+fbr7ZXlKbaaFasWLFpB7WCFPkSrVbb7X/ASNJNc+4+QkSaDwhoUYNZ/JZLEtasBMD+iTu2tWehIUloNiZ4PJPCiHm3bdXX33Vuvz999/loosukqlTp0r9+vXlR3QHJqKch/cbBGvJDtXwnq4VFDhzTLlFD070wKNhQ7vBM6oHKPWhGp7r2iA83RWCGqqh+sOPlV/uKZ+6vX4M1EBfZ81pu0TJCNXM13S8r//8s73MEmHThRcW/bXL/dqEIQmxMoMxnEw46iiRDz+0T+yAV4P+aEO1smW9n3d4vfBbxZrmFJ9/bmcwQ4c6X+NrRYbDUkczVMNyyv/+V+SNNwLTU7/B5DIzHNN11e4Xm3DcoZkOatB/1Fz+SSHEvAt3xhlnyHvvvSf9+vWTvXv3SsmSJeWGG26Q3poME1FOMyvFkxmqgRbHpuNMLpocjxqV+p9LgTv0OTq5O+WieS5jIq/ZDDxdzGXnqGjEgazfjgPMpalm7zo/0oN79IvKtPY65F/mcW6PHiLHHusUmOB4FseuU6bYtyGwRwVYPNq3t6vc8DPcwfXSpbE/HoYHmGGYuyrLK1TT2/A760nHUJVq7ucd+r7hb4NqfO0v5yeo8sX2mSc3M6VSbefOnXLrrbdaH7hOISrV5s93PtfJmH6kU6r0zT+eUM1dqebuf8Dln5SoUA0VarfddpuMGDHCKpslIgoVqiX7YDFdlWrYKcbSMpyZxg4vJR8qCrA8TnvguCvVKDlQ/YfVMf36Rb7vI4/Y+7RaseGHUA19m9CIHE3O3a8TL7wgctVVTmP0VNJwChW3ft83R38o3Ua/LkWjzKPHuegfjgITbWmFY1ntfTZpkn2JHmbxwn7IL7/Y7QFAe4/Dd985hSfx7ON4Vbp5hWq67BTLWTVAixSq6bLrd9+1/w7Y74ilNVQyYPu7dRPp1Cnwdvx94eSTM6tSDT3A0dYIH+wHHiZUw/JH5ef/udOmOdcvu8x5MsaS8rpDNXf/Ay7/pBBi+hexYMECOeuss+TZZ5+V66+/Xv4Iceq3UqVKXA5KlKP0YBE7ssnO3dNVqWZWa6AnC86sU2JhHwhLdfDvCAcuCHawQ499PfTJYqiWGk89ZTfjjqaFivYC8mtVHY4L9DUDYdt11wVXjSRzuEqoIQV+bU+jsH04rkB1KF5r3c3eiYoSqpmTL1XduvYJFA3VdAVWvMzn2IgRIg8+aIfqOAYfOFDkscfiq1Qzs4ZwoRqWdWrPSbz2YMKnwmsQmv6HqlQzC6h0umi64KQJlnt66dPHPtk4blzoZa6UAZBwf/xx4A4v1mArcxmkn4IlPJG0og6ND9HoUJukFmX5p7tSjcs/KYSonw1Y8jlo0CB5+eWX5cgjj5R27dqFTPVZwUaUu3SKVbKXfqazUs0M1TJtMJLf4e95zTV2L1z0h/30U7s/rjZ1x+d46/ntN/tzhmrJ5/fQxy1UMGY2D8fBKfbBsbuCAV9//hn+e+OBx0clTPPmzslu92tIqkK8RLzWaqhGlOxQTfuLL1xY9Eo1NzwWqlOPP17kzDPt6dFdutgfsYZqXrxCNbyGoloNvzNWz2lIiEp39IrT39OrUs2U7lDtm2/sS7xu6nuy+r//syeuuieBUgZBItqrV/A/OkwOMc94fvutvZ76iSdErr5afAFvUNhW/OO87z77zTURlWpmc0O8qevX/RQoki9EnX717dtX3nnnHStQg5o1a0rdunU9P+pwDB9Rzlq9Oi/gfSgbK9XMIM1rKQfFD/tow4fbO+zoW+eu6Me+EQ6CcLIQZ/s5uIvcogmq9MAY/4bOOCM5S9ax3Ay9htDIPFylWibQ13OtqCFKdqWaKZGhmsLzHsu/4cUXo/8+rxNpZpDmFaqBTkVGpZq2fGrdWuTrr537mKEahu/guB295HRZZbpDtRkz7Mt77w3MXsaOtZfZ67AJVBm6QzfKAG+/bV8ibdYVZ1gqYE7PRLiEKZh4E8UZUL/QKjX8I9SdAH1xQcp7/fXRrfUOVamGJ75Zgqnr1YliDdVGjhwp5513nnzxxRfW59OmTZNvvvlGJk6cKPPmzZOtWp5CRDktlcvy0lWpZu5U6w4yFR12wl9+2fkcFWlY7qln8TWM0Pug35ceqBBFCtXM563usmC/2FxalsjKMfRCggULgr+WiZVq2qbmpZfSvTWUDbSayasiyx2qIbBJBs0EUAEd7RAOr0q11193rodaFaZ91XSfAcU/7n0XM1RDkPjDD/YEVPSE9EOoptuO18z+/YNPcGJpOLIIvE9PnJgnq1czeMgoKK2GK64IvXM1eHBgyOa3UM2clGQm9s8+G5hgR1uphrXbOjb4lVecs2Hmk5UollCtR48eVqD2yCOPyBNPPGENKkBvtQceeEC6desmVapUkU6dOsn7778vBV6jbYgoJ6xalZeyUM2sVMOObqpeesyDc508joo19BR5773UbEM2QvsLHGiZ+3LDhtn/X7FvdPTR9m0asnXunJ7tJH8LFVSZB8N6HfvL+jqS6GXr5uoQd9VGpoZqoNU9RPHavNnpzaVTg8OFal7VbImAqaAaAkV7cs4dquE5fOKJzueh9kPcoZoulUS1mnIfp2NyKaqx9Zg+3aGaDn7Ee7T+PqCvofhb6v+7Ll2KyxVXnCRPPsmWQBkBo3BRYogwqUOHwDcn/I9FHw4vfjnm11CtSRPnNvfIYHPZQ7ShGtZua4L80ENOOWqm9cWgpIvplQ5LPkePHi2vvfaaHHXUUfLRRx/JuHHjZPHixbJu3Tq599575ZlnnpEzzLUURJRT0lGp9tNP9sHxRRdJykM1hGhYxvHMMyLvvy9yzjmp2YZspBVoWFmAPlfab1YPvNq0Cbx/48Yp3kDKCKGCMfNg2KxUM0PcRIZc5j63e4k6QoVMXP5JlKil0Xg+HnywyGGHhe6pluxQDc9Rdx/yWJd/YvvxPEYFJ5Z7o8jHi4ZQKJjBYdLkyfbnurQTQhW/pCJU++ij0EMIlP58bI/ZPtt8fbjhBrtirUIFO2yZMoXhQ0ZVqeEfNP4Hm2+G+IeNyT5e9MxyummjXTNUQxhoPqmmT4/8OF7TPbEsAi8W2u8lWS9IlBuh2oQJE2T37t1SoUIF+fDDD2XAgAGywXgilS1bVrp06SLjx4+Xq/3StJCIUk7P9prVH8ni/hlvvCEpYe5Uo5r8tNNE/v7buQ3LvXCiD73BKLqz31jm+eGHzhIz7aGDCp+OHe2KNTNUw0EM23eSl1DBmNmlwqxUa9o0+CR1NNBfDP2QsUTL62S9eZCOtjSmr74KrlLxM4ZqlEi6NBonobwKPtyhmrvgJBn/trVyLBy8PrhntO1rNS39+tn920Mdb2t4//33aKnjDEcxBySECtX0e5MVquE9+PTTRbp1C9/SwqxUwwAW/H/BajvzRAaKevB699xze3zfIqNMmTIyZ84c6wPXcxreyMwlAOYbKdb74kl51lneFW5+rVRzTyiKp1JNXySwUx+pcSLltKhGV2A5J5Z9/vrrr3LNNddIjRo1rFDtY3PkLhGRVamWuuWf6TrQc5+pRvNe831cWzpg/Pwll6R22zINWlQgRFMtW4q0bev0ucJSEpxBx068Gaphh97c5yGKZfmnWamGqsi777ZbxegAgWhg2h0Ga9x/v71MC1Ue5sE/JvuZxx3t2jmfa0VI796SEbz6XhHFAydQPvvMvh6qshvPS3PCZKVKyd+PwBTOTp3CD78xX0NQyIKiliOOiO7nhFothgzjgQfsxwuV6yS7Us0cjoj+9AjXwoVq2B5sK04shBqCqP/P/FLI5KVYsWLSHDsT5JwRR/mo+41Uyyy9nohadp1OeEPHkhF3TzUwSyqR9qKkFE92nFELV6nm3sE85RRneAMr1SjeSrW8vDz57LPPrBBt9uzZcvHFF8u7775r3eb18Xmk+mEiykrYAf7pJ3vPURvrJpO7Qj1VvKZ/TZkSfFumLO1KF1T3PPpo4G0I2HDwgUFNWCKDqWJakWhWFPFEIYWiB6DR9FTTg1hUm0K0zcrB7MGEJei6v63MXs5mpRr+3f/xh30dAXImYKUaJcq4cXa1F7KMcHmG+TwOFdwkAiYAq1dfjf69H1N9sY1YGRcNDd/ME454XmE/4a67RG6/PfT3JjtUM6vJdBWgvk6irQYmbps/XyvnkEuE2s/R/GXjRi7/zAi6tFHDJq9QzWykp2I5E5UMI0Y4Z30QdrnPALnTbDQFRVlppEo194sOQjXFHVDyENPbVKtWreTNN9+U0047TW6//XYZNGiQnGh25ySinLZgQWVZsSLP2uE65pjk/zzth4KzpekO1cwJlSqZZ9ezwezZzmREhBs9eohceaVzwIIPkzlVDZVBRF6OOy76SjX38UMsoZp7WZNWcXh9boZqa9bYPx+vX5myhNl9nIKT+ckMOih7aQVnq1bh74cgKRVLB82KUl2SGYq+hiBI+uIL+3OvnMELOuPgPQ3PJV3eGm1YraGa+zUm1iW3Q4aIvP22015BmX9nvC+r5593Jpvi/Vn3faKZul2xYoHvK9V27twpD+1rPj9w4EApmctnQvVNUdcgm6Ga7sx67dS610On2tlnO9exZMQdopmVagqjd998M/rln/rYqOLDGTFWqpGHuEayYBDBV199JXvd46yIKKdNnlyjcOcrVfsmqejd5qY7lphGiUryUPzcS8QPxoyxL3v1svfncMIx0vTFiRPtRshYrkfk5fDDo++pppVq+nqlJ90vv9xemqkrQaJ5fptVJNgvN3+eGaphwBrgwDZTpn+6QzU/HyiTv+m/nUgnnaIJbhLBLDpBX0+vE2Rerxt4zYg2UNNjdLQwQBN/FW3lmVbTaTFRPM49F/2x7T6Q2kNO+z6ar2Xa712zB4Vl7pGqgUMt//TLgEi3Xbt2yf3332994HpOc1eqmeFUuOWffvq7ufuphVt3HeofpdegAn0c7VUS7ZpvyilxzzmuV6+enHTSSYndGiLKWHh/0lBNl1Jl67IkDdWw72H2LnXDWWW/7kwmE6agHn98cHP2UKEaQthooecNellFCt8od+HENIKrqVMDK2a9ln96VaotX25Poh09OnAplJseiOpBuXmA7D741efC77+LvPaafT1c7ya/qV5d5IUXvHswESUjVEvV8+Opp5zXCbwuYHpnqPdtfe8vyvuPeYyP/nLRiKeSNtzfH8U2WHqr7afMUG3RIvvn4ATD3LmB7+saKEZTpar/f/fsybPabmHZb7iTkOSzSjWTvsmZT1r9R+mnUM3dTy1UpRpoD7ZoK9UAa7TRIBVTPYgSFaoREZnmzEEPofJSqlRByCa3qahUw84bpl+HOxguKnPHulmz4LO2+r6OYl6zWiVX9OljT0HDmfFQsARu8uTYQzWiaGDABQYDoPpRT7KHGlRgHh/gQBL90RQOBEPRA1GtPDFDNXfvZoRqOJDF6wWWVAGuZxIsX2vRwr4+b166t4ayPVR77jmRQw+N3OesqPB+PX68HXjj9eCbb+wlkl7cFa7x6trVvgzX2smkr09erSeiYVbcourtyy+dUA8BohmqIVPAkKVffrHzEvx/Ouyw2KrUnPDNXtGEfpOoSEc7K752+JSeCTJDteuus5dkeO2kaUPEdPdUi1SppssazjzTGcIAeNLHMqgAtGdDqOo3ymkM1YgoIUaPtl9OTjyxIKDxb6or1dA35OmnRe69NzWhGt533f1JcEZXT44VpQdKpkBfNFT1gNnfDhPaQ53xx/Q3fA19dczlMESJfn0YODBypZq5XH3YMO/+QrGEau5lXah+Gz7c3l/HEBcM6HAP6cgEHTs6z22iZIZqmMqLcAeN8lMBx9v9+9vXv/46+Ov4GoKhRIRq77xjB3eYOJyKSjWzbxxe68wcBK9V7qXsjzzi7ENhOb3+XWLpFYvcoVw5u4oJS+m1oClS3zryyfJPePZZe82wNrQ11ztrXzE/Vap5hWrXXCMyY4a9lnn+fGeHAE0RYxlUQBQBQzUiSmio1rt3anstuivVUDGX7OVJumOrS0C0Mg8H8KhOw9l17QeT7X3VsB+GM/3YaUaFoLvxurkzr2bOdAKFnj1Ts52Uu/QA2KwaDVWpBp9+6lzXnkMmhME4zli3LnKlGgJ37JvjdWFfP2yrp9Ftt4lUriwZG6qZ1XxEyQjV0kGnlq9cGXg7+qyhck5fD04+uWg/B787KrnN/CKZoZrZigH7JPrapb+r7qfg98IJwY8+sidvw6BBgRNOsX8TrRo1NgdV2EVqC0E+XP6puncXueMO+yyqBm1+CtWQxLvhH3Tr1naajLPgKGE3m5vGsvyTKAyGakRUZOjBMWtWnhQrViA9eqS2iZi7ykmXFoQLs3CyCtscL3dfFRwk/+c/dg8nrQrXUA0nyS691D6ozjYffyxSq5bzOSoE3bCExD1oAM2atVdLKpcKU27S4AxLQTVsD1eppr37YNWq4MfDqhHzIDNcpRpO7JvPEdz3jDMkY2lVLgcVUDaGaugd6A7V0EKpYUPnPR/BGvqwpZKGaqh0jWdfYsUK5zqWfJrBFr6GSloNzc0lqZhSeuSR9nJ6hc+jde21s+Tgg+19whp2y12Gan6EIMlsFhwKAqqHHxY55RT/hGpaPffEE4Ej4kPRHfdQCXWoQQVEETBUI6Iiw1lNaNZsTdCUuGQzd/Z0olW4UA29SZs2FWnfPv4hAu5QDe/pWMZhbou+z6O3GJZ9YRlLtnnmmciVeObvjaWw55wT+HUEbETJhCXGSnstuivV3CelH3/cvsQBtPt1QnsBug/EvSrVsBTerN5EZWamTPz0osdbRZlCSLnNz6HagQcGh2o6WET7tad6HwfM14x4qtXMHo+Y7mm+hqFllk75xO+GyjTdf9FeajhZOHKkfZIQbbaiVafOJpk+fbe1jB6V7MBQzYfM3gjhKtVM7pHZ6d72s86K7v664x6qQSEr1ShODNWIqMi0/0j79sbp0BTBmVST9vRCgON1RnfoUKdR/qhR8f3MaCaAaaWayrYeRNiPQvNhd7UZKnguvNCZOG5WqqHpu54RV9EufyGKF3oCabCmB5fuSjUTBntpQ37czx0gmUuaEcrpc92rUg1Nvc1QzavlSybR461cHMBCuRWqaZiOfYV0v18lMlTDqjf3sAA8r/G+jSFDaGPx5JN2pmDmFJjqjiErsZ4UwGskXk91Xw2BXrqLm1Tp0qXlp59+sj5wPWeZb3LR/h38UKmGAExDvWgbHUZaSx1uUAFRGAzViKjItI/ZwQenfk2Qe0iAwg6xuSOJZV+XXOL0NdKDZ3NkfKwHBeF2sM0hQ6ABVLbAUlcEDtgBN5ez3XmnyOuvOwOXzFBtypTUbyeR2WpFwyB3pZq2i8GBHw4ccZCpxxaoVsO/3Zdftl9XUO2qVZZYAq3T8MxQDf2XtFLNnPKpk4EzPVRjpRrFewysw3v8GKppj1bkBFgm6R6+Y07RTCVzeXpRQzXti6YDHXEdS0Dxvo1KPEDLCry/n3eeJAz6taESDlOQzb6V6ZSfny+HH3649YHrOUvfELFTq1O2MiFUMyvsok28o61U4/JPihFDNSKKCs5selUnYAcZPUfgoINcI+9SINz7njl5E0saXn01+D7xjHdHoAQtW4a+j7uZb7aFajqNHJVpOlkdgcQJJwT+/uhfpwcBGqrddZf9fWj2TpSOZYtelWpjxtgHfFjOieVOOlkYoRr6CF1+ucg33zj9jR94ANOOnVBNX2/w+qDPdxys4vviafLtR/r3YqUaxcNsF2AOEvQLHG9r2IdqNVS7m+/d6coP8HqkwVo8oZoZgmMJOqrFMIEUJwXwvuw1sT2a9lSxwGCW44+3r+uJCfLx5M9YQjW8IWBdcNeuIu+/LyljvhFFW2EXqacal39SnBiqEVFE2AFDtYXZwNbdw6xmzQIpXz49e5zoWxZpBz5UY+1oK8ax5Avv33hMrW7r0CG6Pk66E/n335J1odoxx9iBw9tvi3z2mbMfgobsODjBmX0El9jvmj7d/tr559v/z9DLhSgdyxa9KtVw4GoeSOpSMHO554IFzgFhvXqBgwrQKwj74+g1qKpUsUM6TMv88svQlbWZ9nfE8zrdrXQo85hV3u7hIH5cAopAzWzyn65KtaJOANVKNUwd/uQT+3UPJwN69Yo+i0gE7T9p/k3TaefOnfL4449bH7ies6KZ/Bmup9r999sl3ujxgTXEqaJnx/CPONoKO30ihapU4/JPihNDNSKKCI1tzQotk+4c1a6d2qmfJlQ9oW9Sss6KYwgBDoY7d7bP6mIJWIMGzs63l8aNg29Ld7UaqnB0SUtRYB9Ke8TpFMRzzw1c5oaAQoPFIUPsKiDs/+D/Q6YvgaPsrFRz015oZmNtDdZBB5OgQhP79Hhe4PXhvffs2/F8+O9/7et4fUJVW6Yz/16sVqNs6qem9H0d+zbTpgV+LZ0r3SIV2EQTqsWSmaRqumo67dq1S2677TbrA9dzltdZplgq1XQCUDLcc4/d3E+bJGNHGstEbropcNlqtLj8k5KEoRoRRaTLmlCh4W7+rzvJKO1PJ6+zrWaoptdvuSVwh95syRCKHhj//LNTFYfR89HsPPolVENvMxzk9+4d+j6LFjn98cLBgQb+buiPYgZpbrrU7aWX7P51gKmr0Z5QJEplpVqoUO3PP53btGoNvYf0MfHvuVEj+zr283GMgSpWVLTq0uhsgeIEPdaItq8aTkLgtTMRgT5ltkwK1RD8uJcp4vmdiZVq+lz1WuaZSn6rVCMJ/EcVS9miGaqhR4IpkQElzmp/8IG9A403Eyx1wI7qU0/Z/RhiDQP1iYSKNA3QTKxUozjx0IaIItIG3HjfdZ9h1AOldPdH8doXMHuqaaiGg1/zfTRStQWCJvNxHnkk8tLPUP1I0jkBFNO89ISi18Et/iYIClFRE2qprJo0yb5E5R4q0kLx6jmnU0GJ/FCpFk2opkvczabluvRTafWl9g28/nrJWrH2VcNxT9u29t8MEwTN5bGUWzIhVDOrqbRfLN73cUINyyczeflnukM1swqQfESrtmIZ7WqGaqtWBX4tUZNszCW52ElFHwVtagro4wbhdkTDHSx4PZlYqUZxYqhGRBGZU+3cZ26dSrX0Lf90v0/qe71XTzWEf2ZflEgHhqH6tUWqVPMye7b3ibFkWrNGZOjQwGlbCNjWrQu83++/2zu62LfCMtFwZs60Lw87LPz9NJQwMVQjP1SqxbL80wzV9LmhSz+VuaS5Ro3AibjZJtYJoN9/b1/i5MSoUSI33BBc2EC5wS+V7dFWqmmo1qSJ3eogncfZiQjV/LL8k6FaFlSqaU817DSaBwmJ7A1g7sRjx157K+CMrimWf1DRhmqsVKMYMVQjoojM90vzJBFo1VO6zzw3bepc1yWJXss/ExWqtWgR/bZheRh2iLHvkeqpV9deK3LllXa4pjC18IADAsMzLM9SZg8pLzNm2JetW4e/n1fPOSz/JEpnpRpWkMSy/FOr08z+kqEq1eC88xI/Oc/PAWUks2bZl6hSU1wKmpsyoVLNrKbSUM0doqdDNiz/NCcq43WYfEL/UcVTqbZwYfDXkhGqoWptxAinjws+lHk9EiTj2oPEq68al39SnBiqEVFE5vJHd6jml51kbZgP2iBf349xxlmrqxCqnXlmdD3VsNPnFaqhT1I0Z6wxGRMuvNA+0629zVI5Qcycbt6zp8jVVzsn49DrzCtU0wOJUH8TTECMJlj0CtUQ5hGlKwjSScYqmko1r4qPcJVqRx0lObWUNhxMPMaQEl2po5NS9e9IucUv+wvRvG9hX0dXtWEYSbpFGlqYCcs/0YsSsA/kLm6iNNJ/VPH0VPNqxJuo5Z/u5SZ6hgs71w89JPLWW/YZmocfju1xww0r4PJPihNDNSJKSKVaupd/YrJe9+4il1ziHLhpGDhggHM/hGovvBB4Qg0VWxq6mXA7Dgq1yl199ll02/ThhyL/+5/dQ0j7i6FaA8FSqqZfmQMTLrjA/t2fftr+/PXXnX6y0Vaq4e+l34NBBdGclVZdusS48UQJDoJwkDx/vnN7uEo1LOMMNVQjXKiW7UucNaBEtYl7cI0bjnXweoHpyfi76EE9D6hzk4ZUfl7+qe+Zuuwb/979sL3ZsPwTr8OaZ6xdm95toQRVqi1fHn+lGvqQTJ0aumzRDNV0iSfelPfbz/75KAuPJ6EP92RipRrFiaEaERUpVDN7laUT3l/RN+yVV5xt0fdjs1oLX8N78B13ODt2WDrarp0doKnp00UaN7avH3mkSP/+9vWLL/auYAl1Vvbyy+33/xtvDAwiR46UlJ4Z1ko1QLUazsbjAAd/M+zP6JLOSJVq+v8b+xuRdtDdS+AwwIkoHbz+reJEdLhlmvg6wiAv7uWfeF1Bu5d33gn9PdkWUGIIG6p2zZP9eC3RA3hcHzvWvo4TC/h/oKEaK9Vykxa1mO0a/MZdYY0qtVj6oPshVMNzD0NTsL+GjEBP4lWpImmn+yR+CNVKly4t3377rfWB6zkrnkEF7rPNsVaqYfoH/jGgJwgGEERaJqOjt7EDX9QnJCvVKAkYqhFRXIMKsNM2b57dfB/8cCZXuUO1bt2cr2l1lR4Y4iBYJ2ubSz1PPdW5ftxxIo8+KvLxxyLPPx/fNrmb+ieq5UQk2ksNIaEGCwgS+va1r7/8sr3jbU78DFepZvami2W/pl8/O1wkSgevZZ7hqtRUqADdq8fS2WeLnHOOZL0+fUQqVLCvo+oPS9sffND+fPBg+2sTJtiFBZiejAD+5JPtr+v3sVIt92CfQfcXDj1UfMvdosAP/dRiDdXefdc+GXjwwSKff263uUCg3bChpJ2fQrX8/Hw59thjrQ9cz1nxDCoId0Yq0g4uzujeeafzOd4wIlWqmaFaUenvid8bb1I4ENC/ASvVKE4M1Ygo5p5qWDaIg030JsJ7HgK1Nm0KfBuq6cEzqtN0x1QPss3fTafUgVm1dvzx9mP06hW+B1MkZrjn1ds10bA0S0M199n3Sy+1L1FJoj2PdD/DrFT78cfAwUqx9sTB3xz/PrQykCgdvCo0onkue4VqWu2aq664wn7d1EElONHyn//YJybuvde+7dlnnQnDeP5rhRor1XIX3lfw7wbH4uZyab/B+6BZee+HfmrRhmro5Y736y++sD/HcxJtp6BtW3/kBGaohkKhSEvIyefLPxXOOkdbqfbll4Gfv/Za4M54uOWfiXjz1d8TaTPOoGMZKfrGIIHm9E+KE0M1IorIrCrATtBNNzm9xtAvFMGMnyvVtAeYuWTL64D699+d67r0M5E9kl591bn+55+SdPj9df/A3f8M1SVoqI4dWvz/hJNOck4I4m+GPmu4jw4kwP/zF1+Mbb8GPZUQ7NWvn5jfiSgeOAGAXoJmuBtvpZpfKlfSzezXCMOHO9cbNXJef80KVa1UY6iWezCkR5d++n06rvlv2y+hWrgVawqVsugF+cYbzm3ffGNfHn64+ILui9xzjx2yn356+rZl165d8vzzz1sfuJ6zijKoQHXo4PQYiVSp5m5MjAaGTzyRulBNf89Ro5zyWeyoImDTcJDLPylGDNWIKOpQTZf7adk+mvuPH28vM/ATPYjTE19azW3uA3iFaub7t/6uOMsby8m7SMtKdAc3FaEaGokDdly9fgdU3pl/HyzRwv2wTAcB2tdfO/+/f/vNnpr65pux99AL1eydKJWuvz7wAC6apt069CRcP7VchYN301NPBVbM6OuvGapxUEHu0lDNz0s//RyqaZZgtmpw5xIffWRf15Np5nAI9I31A61Uwz4QthMnZVM5Ed20c+dOue6666wPXM9Z8VSquXuqIS3VN9VwoRrO5I4b55zdVVpemYrln/p76tQu7Nxi5wD/BnSpBivVKEY81CGiAO4hPPhcD4Dc/Tj8enDprlTTHTbzxJNZpaL9xcz3b72OCpdE0r8hltGaO77JoJNZQzUnbtAgMPjC9FStzMHgAnPn/e67RSZPdj7P5eVvlLnMMD2af8OsVIu+Um3BAuc6jqk0VDMDeFaq5S4dhpMJoZpZqe6X57tWeGlLB7cffrAvO3USGTIk+MShXyrVzjrLrla87jp7PwwFYu4BWJQBgwrM0En7meg/unDLP7EEAv+I8WagyyMAU0DdS0C1h0CyKtW0FwrKrNGIUM80A0M1ihFDNSIKqGzCWdmrrnJuw3ujBm0tWwZWXUWzfCrdoRq2Xav6zVAN1XWY0odA7YYbnPt7NeRPpFq17BN8CPrCDQRIBK+D2lChKAI1fK77OIMGBfZ9GzEi8HsZqlEmMqvTonluM1QLzd2n0aw2wfuG1/JPVqrlJvTd01VVnTuL75kn0/xSqaYVXsgjsLwTk3dRQa70OYXXpyuvDByaghNrfmnBgB61qKpD30UNLzHwhDJsUIHZbFefJPoGGy5U06WfJ54YeMYXZ5ndAwvMUE3fYBKx84knB6rksGOLaV14Y8JSlgcecO7DUI1ixFCNiAo99JDIsmUiL70UvKOGSiZtTO2nHU0verCMMA0n4LyWf2L78buiP6qeATZ7sKF/qflYiYL3aQ2zkj2swOugNlSohnYYGqZhG7G0V5eqekn034UoFczqjWim0Xot/0S/MLL7LYaaAIxjqnDLP9GbkdVquQPHqnhfRaDit3YRkSaA4kSYH+h+Ctox4GQggiksaXfvq2k1qPm8w9LPWKZ1p4qu/mOoloGVama4pQGUe5mIl08/tS+7dnXeEJT2HPEK1UKdzYkHKgfmzbOXfZrMCSp+GE9LGYWhGhEVmj49+DbdUcN732GHeS+P8Btsq+5A4sDOa/kn6H10PwDtFLBvYVagR3PgHe8S0GT3VYtUqWYOL9D/tzgb3rGj04slVEPpLl0SuqlEKQ/Vojkpj75hF1wQ+NqhgztyHQ7UUTXj1Ztu5Eh7CZr7NVSrbcBdlJDN0Av7jDNE5syRnPPHH84Qi8GDJSP06CFSrZrIscf6Z6iC1/JPs9pdQ2rNKTRc89PSTzeGahlcqdanj8iddwYGYTqxzCsMg+XLRaZMsa9jqaX5jxS++sre8bzlFvsfhdfj4MmZLGaouGhR8n4OZSWOtiCiQnPnBn7+ySciF13kHBiZoRqWC/oVqurwXo1QCSfMvJZ/msz3dQz/0QmXOABPxgAg7WWW7FAtUqUaQkW0kcB2YEiBuu8+u2INvVnw/x9nxHv3tisY0acNQYOf//8ThWIuWY/mYBnPESy1QpCmk0P92ksyHbB6B6GarvZBgK+vOwhT9DaFYAktbMxBKrkAU7Lx2olQzVyylwsw3AYru9BaQE/Y+B3+zWLJqrsXezppIG0GaWZfVvMEqHu/xq8nArQwKNeeE1kxqABvoFjeYtJQTRv6ummg1qaNvSPpPiODgxA8JgYI4MyMu+INa57xkQp+LO0kX2OoRkQW9B4zTwphQM8ppzifYwcN74EIX3CyCSGL33eKNVTzWv4ZajolBhAlq5+au1It2cs/I1Wq6clGNyzRwYe53A3BJCeMU6Yz26TEUoFyxBHO93CabeigEn03v/8+8OtmqI/rqPxD0JJLoZoeY5rDHHKFhkBHHy0ZxW89Y91tKtxVa1qppmGaGaq5h0z5rVINrxmYAsqTdRm0/NNLpFBN//F6LeHEjubvv4uMGhW6UuzssyXp8PMff1zk4YeT/7Moq3DXkIgs7iprd59R7KDhxM3nn4v88ov3kh8/MVs7hFr+GYqOoE92qJbuSrVYMFCjbBNLqIZKI6xM4ZS68EtqvYoI3BOlsawu3BTDbBZqEnM2++cf+xIn5Sh+eN6497uw36b7au7ln+Z7tjnp20/MNiKoik/1AJNSpUrJmDFjrA9cz1nxLP8M9wIXavmnO/k1Sy11jTJKRENJRaiG5SpIef2aRJNvMVQjIs+qKfSAyWQaiIXrqRaK9vcw+/8kku7gRqpUw3bjYB7VgagcTEalGlGuimVpF04onHCCPTGYQlf0mMvMUIWCqj6dKByuN1Q2M0PFZL2nZEKoVr16urcksyHvQLCPqZkYsKRWrvRe/mk+v/wa5iIkfP99Z3tRIJRKxYsXlx49elgfuJ6zUlWp5v5HqmdYvM7IeJ2h0dJGIh/K4VcQIjK5K60x2c20dKlkFK3OMnuqhatMwXJWrTpHDzF3I/9khGobNtgn9ELt8KIS/rvv7OuYxImD+ljaPCR7GStRJtP9f0pcpZoZqg0davfhdFfX5FqoZg6Rw+s3QrZcatfDSrXEweRUnZ6K3qfYb1uxwt6ncBcBaQsHDGn087+3s84S+eADexAjBlmg7dZnn3GZfUrpzmJRl6CYgwq8Xujc/0gxAfTWW+03Cvd6ayy/xI7vQQfZ23f66UXbNqIk40sWEUVVNYUdt0wS6/JPNOzXIC7ZoRoOQvUAI9wSUG30rdM2sT133x358V94QeSxx5zHTsYEU6JMhWqIDh1Errsu3VuSHfRYCK9r9es7t6NtjtcxWq4s/8RxJaqFzd8TPdUGDAi837Zt9nCcUCumYrF1q30cqu956YbVXdo7j6FaYmnln1bWu4uA2rcXmTw5M4YAIC/RE7noa4shWamwa9cuefXVV60PXM/ZpZ/a1+Dgg4v2WHqGGC9A7h4yODOvgw30HylCN+ysnnNOcGUaXjCeekrkxhvtBPnQQ4u2bURJxlCNiKKaHo1JkNkcqqHqXc8Aa6iWzKU60SwBRaWawllbHHQ9+mj4paCzZolce63I7beLzJhh31a3bqK2mijz3XKLyI8/BjbypqJXqmEqqllsEOr1U09WZNqJmljgeBK/f61aTnGGeuaZwM9RnXPNNcHLZONx//12RfPrr4sv4P8x3q/w/mWu9KKiO+AA+/Kyy+zg0l0EpANW9H5+hmwFKwVQVQfo3ZsKO3fulEsuucT6wPWchLOvSL8RdBW1vwHeDLQvGyaamS680LmuoZoJZ2TMnm58waAMw1CNiMKGOx9+KHLXXfa0tkxiVqFHs/wTdOdTKwaSVakW7bACrVQbONCpzsfvYi4nUlgygeDzttsCW2P06JF5U9eIKHNokIYTBTgOOvdckYsvDh2qaXNyFEegsiobafUQgg6vKac6ERPeesu+nD7daW1U1PfxqVPFF/QEFQb7mZN3qejMfYeJE4Mr1TIN9rfuvNO+XtTnAcVASxnRr6yo64Tx/VpRhhc007RpznWvM1p4gTAT4GTugBMlAUM1IgobqmHZ4QMPZF61k7nEKNpBBb16BX6ezPd0VHVE6lWnlWo4IMGOsv5O2qNGIQREP5IffrCXTugSN4RyH33k734qRJQdlWooNMBrzdtviwwfHvr+WBaKYydUMGnokm3QL1P9/Xfw1ydNcq6b1X3m7fHQYAXLTP1gzpzgXnuUGObydVSl62CiTG73oMNjdBglpfAMQKKGAKA8Us/0hmq+GSr5xVlgXQ9c1P5uRLkWqr3xxhvSokULKV++vHTq1EmmGUn2okWLpGfPnlKlShXra1/o0aLhySeflGbNmknNmjXlyiuvlB18JSaKGUKnUOFOpi6RMpthRxuqYVo3xrqrZC7/1McONSTJDNW0zYVW5ptV9TgoxYnBZcsCv7dNG7saLlJ1HhFRUWBCMYKhbt2i/x4tZvjlF8lK5mu0exUUYPkxYJfV7J35009F+7karJitA9KJoVryXHqpyBln2NfHjLFDaoRSmTxlVSvsc3UlZlaEamjm5/ViZp49CHVggf4maAr53nuJ2RaiXAnVRo8eLbfeequ88MIL8tdff8mll14q3bp1k1WrVsmmTZuka9eu0rZtW5k9e7b0799f+vTpIzNnziz8/qFDh8qQIUNk2LBhMn78eFm+fLn1GEQUGwQyCJ70LKHK5KmRGqph6U20yz/h+edFmja1K9Fbtkze9mk/11DNqfH/ZMkSp1LNbPRsVqpdcIF3JQR+ByKiZOvb1w5zGKpFH6qhqhhLX7/6ym5npNzFHfFWqmF5qR+W1uoy1Fat0r0l2Qf7M1qtpodGqIDP5KmZug+ayFANz4MnnxQ57TSRlSsT97hZI1mh2s8/Ozvf7lDNrFozIWw77rjIZ8CJfCitL72PPfaY3H///XL00UdL5cqVrUCsVatW8umnn8qHH34o1atXl/vuu09q1aplBWpXX321PP3009b37t27Vx5++GF56aWXpGPHjtK4cWMZPny4jBkzxqpwI6LYl37qksRELUXJtOWfgIpz7Asg0DIn2SWr51uoSjVz0JG2mTAr1d54wz77r8MI0Oja/N2TWWVHRGSK9Rgo20M180SH10kPvMdglVTPnoG3F3X3VUO1SP06UwF9QLVSDdN2KfHcYWUy91kyNVTDisKbb7YHIWBpOhkKChIfquEsMCZOoAx39uzgMkQdeUyUZdIaqi1YsECO1HF7+5QqVUo2btwon3zyifTu3VvyjGZAp59+unz88cfW9blz58qGDRvkGKw72Kdq1arSuXNnK1gjoujpjjx2yLp2ta/fe29mVzvFs/xTYQARprYlU7hKNVR9aNiGKeP6Mqjh2pAhdnXI3LmBVfPuyaJERH5khmo4rss25nJ8s1INvS9RcYyleho4meGIGYrFw/z+dPdVwxJX/L/FfkUmL0n0M5ycM/vdZnqoprlLLJ18sHz6kktEvv8++Guo6h8/3vlcq//JGM+LHU6UN2qfkaLCDqtWq7VtKzJ5sh3cmWOQuZNKWSit9ZWrXSORli1bJhMmTJB7771X3nnnHanvendo0KCBrFu3TrZt22bdt27dulLMVeeM+/ztdVpwH/RcM/uu/buvAcWuXbusD6JctGABnkf5Uq/eHnnoob3y4495cvzxBQGV25Ho88cvzyN76WoJ1wkxPM/FF+w+rSVk/Xr8nfelfvtMmpRX+PL89dfONpcvb/9/cvdPg1KlcCd7fWunTntk1669yf8lKCX89twiKiocv+XnF5e1a/Nk6dJdhVW42fLc+vNPvH7bZ0OWL0dqmCf9+u2R557bK+eemy8ffhi473rEEXtk1qx82bQp+P0gFps2OT933rz0vg+MGmW/X3XpwvejZGrVKl+WLLH/PTVo4J+/dTzPrWLF7H2fHTv2yq5dxrroMIYOLSavvpovr76KgG2XHHSQ87XbbsPIWee5tnBh4OPiGPLtfeVruJ5r77F5c+ZYe5oFDRrIbhxPJ+j3L9a2reRrH/QOHaQgL0/y9p092XPffbIXZ4tz7G+daNwvTI1Y/r6+WbSMnmq9evWSU045Rdq3b2+FZxheYKqwr7Hh+vXrPb+u93GHdSYsGcWSUzcMQSgbao03UZb76KOjcc5T8vLmyMSJi63bvvwyvsf6Mt5vTDC8f5cs2UN27nRe5r755gspXz7+A5ZE2rAB6xy6yYYNefLJJ2OtHm5qxIjG6IomnTsvk8mTnbHkf/2F9bl2SUPjxuvkzDMXyMMPHyF9+vwmY8f+Jj17tpBff91fWrf+UcaO5RtttvHLc4soEWrWPE7++ms/eeWVaXLYYauy5rmF954FCzDFzn7vWbHCDrlWrlwkY8fOlYoVUaUR2LCzTBk0xWorq1blyciR46R06egCBdOuXQgFnBHW48cvk5YtnT7EqYRKvBEjTrZCtRo1psjYsaH3y6loypY9BGv3rOvbtk2WsWPXiJ/E8tyaNQvl+B1k9eqNMnbsd1F9z4wZrUXELtcbOHChnHeevZxx5coy8uabJ1nXjzxyuUyeXFNmz94kY8eOd/397GM/r2F42a7e2LHWHuXKSpVkytixCXvcGnv2yL5aNYsGavDVQQfJ9gT+rFzH/cLk2hpDc9K8goL0F97PmDFDevToYU36fO6556RkyZJyxBFHyJ133mktAVUI0vbff3/rF8RgAnzdHFwA119/vfUC+cgjj0RdqVanTh1Zs2aN7JfJc6iJ4oTy+Lp1S0heXoEsWbI77mUaSPPx4t6lSxcp4ZORk+3aFZdffnGWkK9btyvkJO9Uw8mPcuXsv9OKFbsKl4NC7975MnZsMXniiT3Sv79z1vmdd/LkoovsA7WLLtor//vfHlm71l4CksnNiSnznltERXXBBfny/vvF5KGH9sgtt+zNmucWmqHXqRP8WHfdtUfuvXevTJuWJx07Bp7T/vXXXdKsmf09jRoVyIwZu4MGB5m+/TZPLr44X559do+cckpBYauDmjWdn9ux414ZPz72cC4RfvopTzp1Ki4VKhTI8uW7A9opUWKZ+wXLlu0qbBORbvE8t775Jk+6di0uLVoUyM8/R3cC9MIL8+W99+wdoJo1C2TixN3Sv3++1fMet7duXSCvv75bDj20hPXvcc2a3YUtNXLa5s1SvHVryVu6VPbceKPsNXuIFNX27VIixDH1LvQ2QfNiKhLuF6YGciK0F0Nrskg5Udor1fAP4swzz5R77rlHbrrppsIearVr1w4aOLBw4UKpUqWKlClTxvr6kiVLrIEF5hJQ3Ofkk3F2zBt6tuHDDf8g+Y+ScpGulq5bN8/zQCBWfnouNWsW2Ai7bFlsm/gCtgMBH9pMVK9eQmbNsvsM4Qw/WlDAUUflS4kSTgmbGbxVrVpMSpQoxl41OcRPzy2iokIfsfffxzLFfJk0Kd8aCNeuXeY/t0INCNhvP/v1HL8j+nhv2OB87eCDnZ/9++958v77Jaw+UaGcdZbdCunMM4sX9qTbvj3wPr//br9HpIO2Nu7ePU/Kl+drVjJ16WJfordarVr++1vH8tzSBUO7duVF/T1btjjXly/Pk4YNA78P/wYbNbJv27QpTzZtKlE4yGn37t3y0UcfWddPO+00KZ5LUyefeEJk6VLrrGz+lVdKfiL3LfBYw4aJXH65M6Z+3DhrilYJ9GZhqpkw3C9Mrlj+tmmtbfj++++t5Z6Y4HnzzTcHDCVA1dro0aPFLKQbNWqUtUQUmjdvLhUrVpTvvnPKg9euXSsTJ060vpeIQvv8c7thMqrUMGrcbOyfTdyDFvy2v2SeUX7wQXs4AZaBYngBqhRaY1VDUJ+44ICNiCjTaIN1nFBAMIC5U6GmIbunSn72mQjmVqV/rUUwHDsCXr/NriJanIF99Hnz7AFBCM7eesu+zYSiEZxgCcWrzcu+FsGFPwedUMzgLpVGj7YvjcUmlCQYfLF4scjUqZLx4pn+qf/uzYENpmOPtYdP4e8EZr0GVi6dffbZ1oe5iikn6AvV//2fyCFYQpxg5k4qxv/ibAPG1TNQoyyV1lBtwIAB0rdvX6tSDWcL9APVZ7ht+fLlct9991mDB9577z15/vnnre+xNrxYMRk4cKD069dPJk2aZE0Svfjii61AzT3ggIgC3XSTyIcf2jv9H3yQO6Ga35ZIms258f/jvfecz6tVC5xA7g7V9EwrEVEmql3bvkQ1MQ6i0boEVVy4HRP9QjnpJFSfiJx6qlMR5RcI+UaOtK/fdpvIG284XytTxrmOCuN69UReeUXkvPOCH+e335xgyovXKpRvv7UvcXysAcKvv0rKYdsRGiIo7NYt9T8/FyFQwj5DtoRqkfItVPjrpFu9vOWW4Pvh32DHjvZ1PTR0LYJKvREjAse1p4uOnk/U1M9woRrOIGM9brg17UQZLm2HmJs3b5bp06fL0KFDC0sX9WPQoEHWwIFx48ZZ92nRooU888wzVrDW2ijdQKB21VVXyWWXXSbHHnus1KxZU17BHgoRRdzhhVWrsjukMUM1VKn57QSZWam2Z4/I1VcHLl11Y6UaEWWLWrWCb1u40G5JoBXUXubbfcgt0505Lr7w44/2eywq1Hr0EDn9dJEhQ0Q6d449YHr55dBf2ze3KwAq3gArrlAYAl99JSmnYeBxxwW+ZxFFoicSw1WqIXBr0ULksMPs61qp1qaNyOGHB963cWOnchMhti9CtbPPFrnjjvS/eGmohqa8yWDupB54YHJ+BpGPpG0xFCZ3RpqRgIqzMRFOQ6IPGz6IKDr72kcEycZQrVEj5/pufwz9DGBO/HR78cXw1Qns80pE2RaqqVBtTLDbaPZQQoDlJ2gjBH36OK/XV15pf8Qq3MG/+V7wzDN2YDBtmv05ltHi74eKOQyGu+ceSSnNCrTXF1G8yz8RsCM0M0+Q4jm/ZImzglFDNTwnhg+3W4XhEho2dL7PF5VqZlqIySLpgrO4WEefzDO05uNmQxklUQQ+6zBERMk2apRTuWUGTeH6t2TyWU9Up/mx7044/foF7gx6HUixLykRZTIsh8RxlxZMRNP/EpUpOB70Y6iGY1QMXgDtzx0PHWCjQ4S8mL3abrgh8GsI2PD9oOFDKun/Tw7RoaIs/5wzx14Ojn24v/5yWpSYz3m0zNDln6je1CXVGPaEFRlXXeWzUM3rxS5dL1a6Y5yKSjWGapQDfNZhiIiSTZd+uieLmWf/s7VazW+wNEGZU0pD9bczK9u8locSEWUS81gLSyQVmux7cb9PLVjgn5Mm775r94VDVY0uv4xFkyb2JVaG6XFvqPflbdvCB2662mrlytT/fdauzd7qd0rd8s+nnrLDNUy1nT3be/k3egLr1FvzpCMqNCdODFxy7YtQTZ8coGlgOuhEGCx5SFafM5w1eecdu7EkXwwoBzBUI8oh2EHXUvlrrgnf1D9b+DlUw8HTxRfbk+zMyrRwy0LRwBvLfMItnSIiygSoylKoKrniivChmlZgoXoFg2fwnrZ8ufjCq6/al5ddFl//zgkT7EpyvC/o8v5Q1WoatmHYffv2wT9P+3UinND3/GR77jmRwYOd3IB9PylWmu8gCP7mG+f24493ciBMC/ZaTWn2GcT+UadOgY+toRompaZtZYZZqabLL9O5Hcl+kp5zjsgFFyT3ZxD5BEM1ohzyzz/2JXbYW7Wy3+/gxhtF+veXrOTnUA0HlOj90bVr4HIePXD0gvCtbduUbB4RUVKZvSHxeoiJnu4hOl5hEo4F9SAZ1WrphmWWWHKGoM9rmmc0EITh98dJFT1pEilUu/dekSlT7EpnTPxEsKVFIhoyoFrNSyIr2DZsELn+ert/29Kl9m0M1ShWZtEUwi8TllSjKg191DRoU/j3HqklRp069nMLQZzuC5csWVKGDx9ufeB6SivVUpV2A0r9zBK/VIVqRDmEoRpRDtEz+jVr2me2UZmNHWtMWitdWrISzuRnCq1W0wNLIqJcqVTDdV0OGqlSDWHcIYf4p68aKogB24Rwq6gihWrm3wEwDRHv79deG1ythoASlTm33GJPIoVHH7XbDIwfLwmB6ml3SMcVXxTv8k8vqOJEoIZ/+3iOIVBWOtkzHPRp1OemhmolSpSQiy++2PrAdd9WqmHCWLhxwOGgxO/QQ+3lKLpuPNmTP4lyEEM1ohyiOxII1XLFCSeIfPhh4JIBv/rpJ7vqANPbiIhyLVTTMAkBkbm0y12hhftqT0o/VKppCKhBVlGZfwcv+ncINwVatwV9VD/+2J6KePXV9gk1LDHFcfVbbyVme6dODfwcP6NixcQ8NuUOVJKZS5mxclD7oiEYvv9+Z7Ks2bIk2iBbQ/tQlbAprVSLNlRDWn366Xap3u+/x/4zf/45cCfTnGBSu3bsj0dEnhiqEeUQ3UFPxJn0TIL9EZyo8ztU4qM/DhFRLi7/xAkfNBzHhE+vsMyvlWoaqiVqyJ1XpRqOrefOtZfAaeAYLlTTymdMkw7V1uiHH5ITqlWqFL43KJEXBGrmKkzsu40d6/RanDHDvjzuuMCBTuZE4HA0aNbn6+7du+XTTz+1PnA9LZVqL7wg0qNH6KkkOokh3nG+06cHNm40pzU0aBD74xGRJ4ZqRDlEG72Gmi5JRESUrko1HFTrZONff7UvEa599ZV9IKzLLP1WqaaVL8kK1VDggoodLPO84QbnfuFCtUGDnP6b7uN1HVSEKrZE9Et3h2pcVUbxwsRPpcMGTjop8D6HHx5Y0RZthai7Um3Hjh3Ss2dP6wPXU16p9uef9hMayeHXX3t/j9l7LZ6JoWal2ttviyxc6LyQamNKIioyhmpEOUR3ns3R40REROlgVqVoY/3mze3LOXNQSSJy1FH2ci8cON90k9OYXCvVUHThtVQ0myrVLrnEadA+dKjztwvXgwrHy+h1hrYPaMlkev5552doeBmvFStEli0LDDmOOKJoj0lkPp/MoQRw8MH2JdqMIWh++OH4KtVSzqxUW7NG5L777Be5cBNFzCAt1H284JdEev7ee85tKOtFCauOVmWlGlHCMFQjyiF6wouhGhER+YlWXbVpY18iEMJ7Fo493VBhhaWiCOKw9KuowZBfe6ppqPbJJ8H3wZRRM8gKpXp1kd69g2/X8BJLShNRpYYKw7vvti8ff7xoj0m5q2NH+/Ldd53b3EMqNUy+9FJ7sKUudY7EHN6RFmal2nffBTY1DBWYmZVq2hg5GjgD8eKLzudHHhn8gqsvAkRUZAzViHIIQzUiIvILc2KkVq1haZeGNVqkgYPoXbuc+2I5IwIlPU78/nvJqko1Pfj3ChQBQwAeeaRoPyPRoRr+v2HJKR5PQ0GiWH3xhV352KdP4O1oO+ZeMh6ruAcVoMIs2sZt0YZq+gKoyXiiQ7XJk53rdeuK/Pe/IsWK2evIMbkLpcAc0UuUMAzViHIIl38SEZGftWolUqKEHSihIk0PpIsXF5k40V6xhGVf0LmzfTlmjF30sW1bdoRq2pMMvw/6lLtDKoRXBx4Y22M++qh9+eST9mW9evYlAoxEhWpERYUCKq9Q9rXXRC67zFm5GA8ddhlTv3+86CDlHjhQErr8U115Zfikz1z++b//iYwebV9HyBdqPLB7bf1hh9nr6NFL7YMP7Mld+gJARAnBUI0oh+gJL466JyIiP0JVmi7n0sF1Wp2CxuXo7d2zp/25TnVGvzFMuLztttQO/vn2W7vYJNGhGk58oahEf472UMfv98QTzqCBWNx6q92jfMAA+/OyZe3LogSR+N0ZqlEqoKhq2LCi/TvT4SZYLo4l0Xg+RIR11giwHntMElap9uyz9vQFNDc85pjgSjWzhNesVANsOCaG3nGHnT6iAk3Ld0eNckI4c214jRpOo0V94hNRQhVP7MMRkZ9x+ScREfkFqs+8YAjB/PnBoZqbu6Lluefs49VUQLCFvk8Y3KcFKIkK1RCoVapkPy6Ow/XxEYjp8XGscIxtDvtLRKi2eLG9fags1ICTyK/q1HGuo+Cr/Kwo1o2bpZx79zppt0KijmS5a9fgr5m07BT69hW57jr7OpJ5c2kn1rkjOcQAA4Rr7lANrr02MC0H3G/wYHuKw8yZTkNGr35qRJRwDNWIcgiXfxIRkV+glzaWbV54YeDtOtkzUqimy7nSYelSZ+mptkZKZIsiNGdHmIalajiWN5eFJgImqBY1VNMqNSzZDTeJlMgP8vMDP5+1uKo8h6ysXHkpqcsl8WTGk+6gg+zqr4BvmOVMUlEYTYzbsT4VYVmkKjWcSdBRx+aLHZZm4ufhEo8XCnbg3UGbBmv6OKiq27DB+dr554d+PCJKCC7/JMohrFQjIiK/QF+wv/4SefjhwNv1OFODq1ChGloduavdEtFPPBoaRo0f74RgoSrv4qEBGpa7amVZ6dL+DNW49JMyhbmKc63UFNR89d+2VUpo4vbOO3ZJJ+7obr6G3mT4GtabKw3AMDnEXLbppuWmeKEwl2ZijDFG9CI5x2NFmqJwxhn2mF1dD3///cH3QdNFbVCH3yNcBR0RJQSfZUQ5ApXkW7fa1xmqERGRH5jHl+7eRypUqIZjRff7WVR9khJA30/RnymRSz8Vjr3hrrsCP/dTqPbTT/YlQzXKFCjcGjLEvn7jg1XtKwi0EHqhr5lWdd15p72+WbVubb/g4LahQwNfBABTVdD0LVKlmtcTuV07pzQXZxnCQXKP0GzSJHvowH/+46xrxde0ISUgrCOilGCoRpQjtJEyDmA4qICIiPxKK9UihWpefdXmzpWUcIdRiQ7VtOfZ5s325amn+i9U++UX+7Jt28RsE1EqYOAmVkfedHsxGV++vKDYdM+KFXYVmAlLKXU4wIwZIm+8ERiQ/fZb4P379Ytcqea1RlyfQNOmBfZwQ5CHyjUMNFAtWjh90lDqi6DvzTftn43RqPfd59yXoRpRyrCnGlGO0AMN9DBFU2EiIiI/qlrVXv6IyZeRQjUUa2BAn/Zf08l+yWYWqehS1GQEiwjXRowQ6dYtfaHazp32cT3+PyBXwHF7x45O2yazATxRJsDJ5S1btstx+1LrzcuWSbkffgi804032pc64UOTcw3IwvU+i6VSTUM1vIhpc8LmzUW+/95ewompKHhB+PxzkauuCv7+o4+2P3SJKhGlHEM1ohwxZ4592bJlureEiIgoNFRU4xhy8uTIoRqWiqLAA33ZBg5MXaWaO1SLdypnKLffbk/U7NEjOS0bog3VkDmggOeLL5zbTjjB6XeHFWeYVEqU0RYt8r4d6b5WoGkgNnu2HXp5/cPHsAEEYfFUqmEJqTY/xguA+Th40uEjEvN7li+PfH8iSggu/yTKsVBNK8eJiIgyYQlouFBN4RjX7HGWTOhH7g6jEr3SCsfr556bvB6o0YZqZ50VGKi5W0qgQs+rLx5RRpkwwbmuYRlCtG+/FWnSJDgQwwvNjz/a1/v0Cd7ZNv3zj92jzf0YXsMK0FMNa9rdS1FjUa+efXn88fE/BhHFhKEaUY7AiTVgqEZERJkUqkWztLJZM/ty/vzkTwDdsSN40F+mtS/SUA1/q127QlfjYcUZ1K4d+DW0oEpGLzmitEB4Bt2721VlI0eKzJwp0qqVcx+vQAyee06ka1f7+s8/B38d67dVyZLej6HDCmDwYKepYry/C4YZPPpo/I9BRDFhqEaUA3DyS5fEcPknERH5nTmA4OSTI98fbY9KlxbZvj30Sq5E8aruSvTyz1SFauGq1XTFGpZ43nRT4NdGj05OLzmitMBAAECIhtLL004LbhboVTKL9B9NILWXmVeolp8f+efrCF2c+e7bV4oElWp33x06BCSihGOoRhQlPSubaXCAgYnbaPOA/qcYVEBERORnJ55otwdC/+2GDSPfH8et+v7255+p7acGmXb8igAy2lANq+AGDBB5+WXna0OH2pcM1SirmJVpbl7rnI86yr4MF6rpkAK4+Wbvx77uOvtJ9v770YVwROQrDNWIovDss/ZZaHOqdab473/tBs7QtKl9xpmIiMjP0GZo8WKRsWOj/x6tFlu5UpJKQyizJ3iDBpJRkA9otZpO8QwXquH+l15qT2Q97jjnPpz8STkTqnlN13SHaui1gnG5XqEa+qqFSt/xJHvqKXtHnYgyDkM1oihcf71zIinT4KSXYj81IiLKFFhV5TVIL5QDD0xNZblWqmHbFiywp49mYm8xDQeRB6CqHRBi6tRVM1Qze7ijz9o994jcdlvowhsivytRooQ8dvnl8hiua/lmpOUcWPf8xhvBoRqWXOLJgQaF7hHEa9Y4L2hElJUYqhFlObPXqbvRMBERUbbQUC1VlWp4f23USKRtW8loCAkRjmHwYK9e9pJbBIVeoZr2Wr//frsPOnMCylQlS5aUW6+9Vm7FdT3zHGk5B3akMZa3TRuRzp1FGje2b0cpZ6gloBqqZdoacSKKGkM1oiynY++hefN0bgkREVHmh2paqVaUAX1+88ILIg89ZA82QrHN2WeLLFzoHaoRZQ2zxLR9++i+Bz3Ppk8XmTAhsM9apFCNCTRR1mKoRpTF9uyxe9Lo0tXzzkv3FhERESVH9eqprVQzJ2hmomeeEeneXeSKK+zPhwxxvoYJqtqPlaEaZaM9e/bI1KVLZWqVKrIHN9yKmrUoIUxzDy4IFappTzVWqhFlLYZqRBGY/Ua9Bv/4GXb8cdYZHnlEpBif8URElOWVav/8k9yfky2Vav37i3z6qcjAgYG3m4MIoFatlG4WUUps375d2nfsKO3XrZPt8+bZfdESMeRgzhyRggLn9i1b7Mvy5Yv2+ETkWzzEJorA3fAY1V+ZQt/Hs+GMOhERUTj169uXWLaoJ5SSIVsq1RSyBHN66YUXinToYF9v3dqe+kmU1RIxxrZhQ/vs9ebNIqtWZe8LBhEFYahGFMHy5c51nHjCOPlMYZ5NZ5UaERFlM4RD6DOOY9jff0/ez8mWSjWvpbPai33UKJFx40SmTOHyT6KolColctBB9nV9AcKBA0M1oqzHw2yiGEI1s99oJlWqZdOOPxERkZcSJZwTSE2aJK9aLRuPkXXpLNSoIXLAASInnWRP+iSiKB18sH35xx/2JaZ+6AtRNr1gEFEAhmpEEbh7s2RSpZqGauXKpXtLiIiIks8c4Jes9+tsrFSrXDkwVCOiODRtal9+/31gAg8M1YiyFkM1ohgr1TIpVNMdf4ZqRESUC556yrm+enVyfkY2VqqZuNyTKE5nnGFfvv++yI4dzosFJp1heSgRZSWGakQ+CtVQFffii3aP00Tg8k8iIsol7dqJNGpkXzd7hSdSNlaqmUtlM23SOZFvdO5sn8netElkyZLABJ5PLKKsVTzdG0Dkd6kM1U4/XWTyZJG5c0Wee67oj8dKNSIiyjXoB4Y+4axUi17btiKvvprurSBKnRIlSsi9995beD0h0NQRk0Tnzxf56y9nLXU2vVgQURCGakRR9lSrW9c+6bRuXfJ+FgI1eOWVxIRq7KlGRES5plo1+zJZoVo2VqpdeaXIxo32cAKiXFCyZEm57777Ev/AZqhWqZJ9G0M1oqzG5Z9EUVaqNW+eup5qO3cm5nGyccefiIgoUqVaKpZ/ZtNxMgp17rpL5PDD070lRBmudm37EqFaNr5YEFEQhmpEYSBAW7s2ulBtxAi7lQLeQ7dvF7n+epHhw4Pv98MP3mfPzSBtz56EbD4r1YiIKGcr1R54QOToo4PbOCRq+SdPWBFlrr1798rcuXOtD1xPaKUaLFuWnWvFiSgIQzWiMA47zLnesKF9GWr559ln2xO0L7rIvv7ssyKXXhp4n6++EunUSaRNm+DvX7o08PNELFthqEZERLnmlFPsarVdu0QmThT5/PPEPj6LT4gy37Zt26RFixbWB64nzMEH25dokMxQjSgnMFQjCgEnrRYvdj6vWNG+xECfcL79VuSTT7yrzjBhG/7+O/j7Ro8O/Hz6dCkyLv8kIqJc0769yIoVImecYX/+77+JfXxWqhFR2Bcg3ZHXgwaGakRZjaEaUQhmpRgq1sqXt69v3hzb45jLRbVyzOtnPfpo4G1Tp0b3+AUFofvGsFKNiIhyUV6eyP77JydUY6UaEYXUqJE9oAC9YH780b6NCTxRVmOoRhQCWiGokSMjh2qhdq61J5u5I+4Oxa65xg7WWrQQeeIJ+/aXXw4dwpnuuUfkwANFPvss+Gt6gqxChciPQ0RElE30vS9ShXmsWKlGRCEVKyZywgn2dW2uzASeKKsxVCOKEKphElbduk6o5hV0IRjbvTvwtvr1g0M183vR6wU++kjkgw9EihcXee01kX797J+3ZInIvfdG3k40Ygb0cXPTs/O6dJWIiChXJCNUQ2sI3T9gqEZEnvr2tS9RrQYM1YiyGkM1ohB0p1knY4erVMNZaw3JlC47CRWqaeD11lv25Y03OstMX3jBvu2pp0R++SW67fXaro0b7cv99ovuMYiIiLJFokM1TOk+9FC76hzFKDVqJOZxiSjLdOvmjCEGTE4hoqzFUI0ohH/+sS91pzlcqLZhQ/BtXqEaGicrVKJh4qcONTj1VOdr3buL9OplnxEfOlTkjz+8txEVcu4hCSYN7hiqERFRrkl0qDZnjj3QD55+WqRmzcQ8LhFlmRIlRC64wPn8qKPSuTVElGQM1YhCWLcuMBzTUA0VaThb7VURFi5UQxi3aJHz9bZt7WWeeLySJe3PTSedZF8+/7zda+3330UuvVSkZ09nouibbwZ+zxVXBPZtY6hGRES5St/7EjWoYNYs+/K440Suuy4xj0lE6VGiRAm55ZZbrA9cT7iLLnKuM1QjymrF070BRJkSqpkTNBGQVakSulIN/UkrV3a+hoqyDh2CK8sUTmaVLu09kRt27BDp00dkxgz7899+E2nWzGnZoMtU//xT5MEH7Q9gTzUiIspVia5U03YMrVol5vGIKH1Kliwpjz/+ePJ+AF4onntOpFQpe6IYEWUtVqoRhaAVZhqemSexrr468L7r19uXqDx78kmRt992Qjj0URs/3l42EsrDDwff1qaNyDHHOJ9roGYuTVX4uf/9r339oYdEvvnGvs6eakRElKuSFaqhrxoRUUTXXity+eXp3goiSjKGakRRVqqZ3n/fe6hBy5b2wAH0IzVDtSFDwv8ss5epQoiHMA591d59NzB4W748cDDC11+LHH10YKXcqlXOMlWGakRElKuhGtonFBUqzXX5JyvViDLf3r17ZfHixdYHrhMRxYuhGpFrp/mnn+wwSkM1c5mnyeyrhqEDcNBBzm0aqqGP2kcfOdVm//mPSPHiIl27OvfLywu9Tfgaln7ecYfIhRfatw0cKLJ6tX0dE8jq1xepXj3w+1591bmu/eCIiIhyhbZhAAz9KQpUiKOCHe+5aL9ARJlt27ZtUr9+fesD14mI4sVQjcjw3nsiRxxhT99EpVe4UG3NGuc6pnjqMkx3qPbll3ZVGXqktW4tMmiQHYhh6ieGEEyfHv32aXCGyjgNzVBJh518hG8YeKDwc/RMfX5+9D+DiIgoG+BEl07wnjkzMUs/DzkkuAcqERER5S6GakQSuIxSL3WKptfyT9DQzaxU8wrVVL9+9iXCr0qV7Gq1a66xd9CjtXu3c13DuKpVndt+/FGkf397OSmWnUKDBtE/PhERUbbA++1NNyWmrxr7qREREZEXhmpE4j0MAHA2GgGYGjs2fKjmtfxTtWtX9O276irnOpaVuvuxtW0r8swzInPninz2mT0wYfToov9cIiKiXB5WoP3UGKoRERGRiaEa0T579ojMnm1fv/RSuwLs5ZcDl05262YPAQDtaYbqsb//jlyp5u55Fo/GjUVuucW+Pm9ecKWaQtCGnm3nnhu4TURERLkkUaGa7h8wVCMiIiJT8YDPiHIYBhPo8IGXXhIZNsx7gAAme5qVapjEiUAO0zrN4MwM1RDMeYVf8dDKtO3b7csuXRLzuERERNmmqKEavm/CBKc6vGHDxG0bERERZT6GakSuwQPa7ywUd6imQwrq1LEHBqiyZQO/J1HDAszlnuj31rdvYh6XiIgo2xQ1VEPl+gcfOJ/XrJmY7SIiIqLswFCNaJ+1a8MPJnCHWhqqefVTc1eqJWLpp/vnw9VXB4Z3RERElJhQraAgMFArU0Zkv/0St21ElD7FixeXazAxbN91IqJ48RWEyFWpFmmZplaqaU81r8mf7lCtRo3Ebac+VsmSItdem7jHJSIiyjZFCdXmzAl+//VqC0FEmadUqVLy/PPPp3sziCgLMFQjirFSLdTyz3CVas2aJW47DztM5K67RFq2TGwFHBERUbaGaps325VnsYRiX3wR+PnChYndNiIiIsp8DNWIYqxUC7X8M1yl2uGHJ247cUDwwAOJezwiIqJsD9X27hXZujV4Mnc448YFft6oUWK3jYjSp6CgQNbs2/mvWrWq5LEMlYjiZLRVJ8pdGzaI3HGHM6ggluWfoSrVsDyzdWuRWrVEevRI+CYTERFRBAjR9Fg5liWg27aJfPedU7F27rkib76ZnG0kotTbunWrHHDAAdYHrhMRxYuVakQi8sMPznVM8YwmVMNSki1bRObN865UgylTnICNiIiIUguBWuXKIuvWifz1V/RtEyZOFNmxQ6R2bZETTxTp0iXZW0pERESZiJVqRCIyf75z/ZJLIi8l0ZDsuuvsHi2lS3uHcbgfAzUiIqL06dzZvvzyS/sS79uvvy4yY4b3/f/7X5GTT7avn3QShxMQERFRaAzViMSpNrvnnsiDCrBzrdVqr75qXz74oEiZMkneSCIiIoqZBmRff21fvvyyyEUXiXTrVtzzJNuttzqfI1QjIiIiCoWhGpFRqdakSXT311BNBxtcf31ytouIiIiKRocF/fKLXaX29NP25+vW5cn27fkB9/31V+d6xYr20k8iIiKiUBiqERmhWtOmsYdq9eqJFGd3QiIi+v/27gQ6qirP4/gvYclGWCIIEpBFCJIAarPIhNVGRQHpKOCoxwMogkcdWpSZVlBRGrCV1hZbR7HdGG0bUFpQoSXASILKgI0iDeKGEASkWcKSkBAIyZtz3+tKUiQoFVP1Kq++n3Nev1tv4//s3FTlX/e+P8JSaqoUHe1U+d63z3kmqs+ePQ38jl240FkPGiRt3vzTo9cBAEBkI6mGiGeqeObmOtM6U1LO7pxmzfyfsQYAAMJTfLzUoUP5aDVTtMBn167yN/FVq6Q333TavXv/dOEiAAAAxtcg4vlGqZnqneaDd6Aj1UiqAQAQ3rp1k775Rvr8cykvr+qk2vz5/qPbAHhX3bp1NcY8XPFfbQCoLn6DIOL5ihSc7fPUTk+qNWxY8zEBAICa07WrtGiRtGaN/3ZfUs08a+2DD5xtXbpII0a4ECSAkImJidE8X8UxAPgZmP6JiBdokQKD6Z8AANSupJqRnV11Us2MYsvJkerXl9atM39wuxAkAACodUiqIeIFWqTAYPonAAC1a/qn4StSEBvrrPftS9Dx41JmpvO6Xz8pIcGlIAGEjGVZKigosBfTBoDqIqmGiPdzp3+SVAMAILy1a+efLDPv+UlJlkpLo/TAA9GaM8fZPniwayECCKHCwkI1aNDAXkwbAKqLpBoimnkP3bkz8JFqTP8EAKD2iI6W0tLKXyclmfd9Z3TKs8/W0Y4dznaSagAAIBAk1RDRzDNUzIhv8+G6adPqJdUAAEDtmQJqmPd8X1LNp3Hj8mevAQAAnA2qfyKiVSxSEBV19udVnEJSVFTzcQEAgJpVMWFm3vcbNfLfb75gC+SzAAAAACPVUOudOiVNny599FHg527dGvjUz6qmlAAAgNqTVOvSRUpN9R+pNmhQ6GMCAAC1G+kA1HovvCA98ohTsStQvmpfPXsGfu7DDzvPZ7nttsDPBQAA7iXVzJdpFad/xsdLTz7pTlwAAKD2Yvonar0PPihvm+ejne3UjX37pE8+cY4fPjzwf9ck8swCAADCn3mO2uTJ0uHDZpSaM9Ld59VXKTwEAAACR1INtV5ubnl77VqpT5/Apn5ecIF03nnBiQ0AAISPJ54ob5sv1W69dbNyc9OUkcHkDSCS1KlTRyNHjixrA0B1kVRDrWZGpn35Zfnrvn2l0tKzG622bZuz7tgxePEBAIDwNXz4dg0ZcqHq1SOpBkSS2NhYvfXWW26HAcAD+ASBWu2HH6T9+/23HThwduf6kmodOtR8XAAAAAAAwNvCKqm2ePFixcTEKCcnx35dVFSkCRMmqGXLlkpLS9NTTz1V6ZzMzEz16dNHSUlJGjZsWNm58CYzvXP7dqe9ZIn0+uuVj9m58+yuxUg1AAAAAABQq5Nqe/fu1bhx4zR58mSdPHmybPutt96q/fv3KysrSy+++KKeffZZe+3z+eef68Ybb9SkSZO0efNmXXzxxRo8eLDy8/NduhME0+7dzvPSzDPQXntNuvZaacoUZ9/o0VKnTk5782bnmOuu++lCBUbLlkEOHAAAAEDYKCgoUFRUlL2YNgDU6qTaunXrdOzYMX388cdl27Zv3673339fr776qlJSUpSenq7nn39ejz76qCzzIC1Jc+bM0cSJEzVq1CglJydrxowZatq0qd5++20X7wbBUnEQ4pgx/vt69JAuushpP/mkM5pt8WLpxIkzX8+Xe23YMBjRAgAAAAAALwuLQgUZGRm61gw7qmDZsmXq37+/mjRpUrbtl7/8pQ4dOqQtW7aoa9eueu+997Rq1aqy/eabBnOdd999V2NOz7r8y4kTJ+zFJy8vz14XFxfbC8JXbm7UGX9ku3U7pZwcs79OWVVPY+bMEk2bVlrlOfn55lpRios7peJiJ1GL6vP1H/oRULPoW0Bw0LeAyO1bFWPj70DUJrWhf3lBIP99wyKpZpJhp9u9e7fatWvnt61u3bpq06aN9uzZowsuuMBOsJ1+TPv27bVo0aIz/lu/+93vNH369ErbV6xYofj4+J91Hwiu7OxWkrpX2h4dXap//nO5jhxpI6mr376ZM+vo2LHPdepUtH3+0aMxuuyyXbr22m3Kzb1KUow2blyj3FymDNeUlStXuh0C4En0LSA46FtA5PUt8+zuis/oNtVAgdoknPuXFxQWFtaupFpVTMKsefPmlbYnJiba+w4fPmy/btCgQZX7z2TKlCm69957/UaqtW7dWldeeaUaMg8wrO3c6cxW7t+/VKmplgYPtuzRaZdcYik9fbCKi6P0yiuVz1u48Bfau7c8cbtkSarmzk1RUZHz4z9kSD+df37o7sPL2Xzzy/2KK65QvXr13A4H8Az6FhAc9C0gcvtWxeeomWdyJyQkuBoP4KX+5QW+GY21Oqlmqnma56ydzhQhMPt800LNMY0bN660/0xMdVGznM78QPJDGd58z0BLSYnW889X3t+6ddXn+RJq3bpJ//iHdPRolDZurCdfTYxzzjH/3wct7IhDXwKCg74FBAd9C4i8vlUxrnCOEzgTfm6DK5D/tmFRqKAqrVq10o4dO/y2lZSUaOfOnXZRAjNV0yTPTj/GFDgw++E9R4446wo5VD/nnVfezsgoT8IZpk+8+640fLjz+o47yvedNtgRAAAAAACg9ibVhg4dqjVr1pRN8zRWr15tJ9K6dOlivx42bJjeeeedsv2mKqh5fc0117gSM8InqWYqgZpkWdu2zusJE6Q2baRZs5ztmzY52+PizLP6gh05AAAAgHBRp04dDRkyxF5MGwCqK2zTCabgwNVXX61bbrlFv//973XgwAHdfvvtmjp1allhg0mTJmnQoEFKS0tTenq6nnvuOe3fv18jRoxwO3wEManWqFHV+xMTy9vd/1XP4P77pcWLpWnTnNcmH/vnPzsj2Yzjx4MaMgAAAIAwYwoTLFu2zO0wAHhA2I5UM1555RWde+65GjBggG677TZNnDhR48ePL9t/ySWXaP78+ZozZ449em3Tpk129RZTrADeYgq6+oq6nmmkmjF3rnT33Wako/P69tul5culc88tP+ZXv3KmgwIAAAAAAHhmpJqZwlnxG4Q//elPP3q8qdZiFnjXtm3SqFHlr800zjMxSbSz0bmzU7QAAAAAAADAcyPVAOP0vOq//dvPv+bEic76wgt//rUAAAAA1B4FBQVKSEiwF9MGAM+MVANO99FH5e05c2qmsMC4cVJSktSr18+/FgAAAIDapbCw0O0QAHgASTWEtaIi6dNPnfZ335kCFjVzXVPr4rrrauZaAAAAAAAg8jD9E2Ftwwbp5EmpeXOpXTu3owEAAAAAAHCQVENYW7vWWffp44wuAwAAAAAACAck1RDWPv7YWaenux0JAAAAAABAOZJqCFuW5T9SDQAAAAAAIFxQqABh69tvpYMHpZgY6Re/cDsaAAAAAF4QHR2tAQMGlLUBoLpIqiHsp3727CnVr+92NAAAAAC8IC4uTllZWW6HAcADSMsjbDH1EwAAAAAAhCuSaghb//d/zpqkGgAAAAAACDck1RC2RQq++85pp6W5HQ0AAAAArygoKFCzZs3sxbQBoLp4phrC0uHDUlGR027Z0u1oAAAAAHjJQVMRDQB+JkaqISzt2eOsk5Kk2Fi3owEAAAAAAPBHUg1hnVRLTnY7EgAAAAAAgMpIqiEskVQDAAAAAADhjKQawpKvSEHr1m5HAgAAAAAAUBlJNYSltWuddc+ebkcCAAAAAABQGdU/EXaKi6X16512375uRwMAAADAS6Kjo9WjR4+yNgBUF0k1hJ2cHKmoSIqPlzp1cjsaAAAAAF4SFxenv//9726HAcADSMsj7Gzf7qzbtTPfHLkdDQAAAAAAQGWkLBAS2dnS7NmSZf30sTt2OOv27YMeFgAAAAAAQLUw/RMhMXCgs27TRvr3fy9PnkVFSW3bSgUFUmysVKdO+Ug1kmoAAAAAalphYaFSU1Pt9tatWxVvnjsDANXASDUEXcXRaaaq57Zt0oMPOs9LM1M8zz1XatBAuvxy8wYnLV3qHJuS4lrIAAAAADzKsizt3LnTXkwbAKqLkWoIuoMHy9tmdFrv3lJubvm2AwecdVaWlJDgtM85R7rpphAHCgAAAAAAcJYYqYag27WrvP3ee+UJtX79pH37pMzMyudMny41bhy6GAEAAAAAAAJBUg1BZUZTX3115e1PP+2MTDNTP6+8UvrVr8r3JSZKt98e0jABAAAAAAACwvRPBNXq1dL+/ZW333WXFF0hpfvCC1KvXs4otvHjpbr8ZAIAAAAAgDBG6gJBNWdOedtM9zSFChYvdqp8VtS8uTR1asjDAwAAAAAAqBaSaqjx6Z4vvSQlJzvVO32VPL/6ynmdny81bOh2lAAAAAAiVVRUlFJTU8vaAFBdJNVQo/73f6UJE/y3DRkiderktEmoAQAAAHBTfHy8vvjiC7fDAOABFCpAjfrDHypvu/tuNyIBAAAAAAAIHpJqqDFffim9/37lZ6VdcYVbEQEAAAAAAAQHSTVUW2Fh1UUJMjKkvDyn8IBJsvGYAgAAAADhorCwUGlpafZi2gBQXTxTDVUqKZEee0yqX98ZadatmxQd7RQi2LxZmjvXKUhw+eVSx47Snj3SX//qnHvPPVJiojRrltt3AQAAAAD+LMvS1q1by9oAUF0k1SKcGVG2ZYu0d680fLhUr56zffFi6cEH/Y8dNUr65htp06bybWYk2ulTPvv1C0HgAAAAAAAALiKpFuGuv17KzHTad94p/fd/O6PRHn/c2da6tbR/v3TihPTWW/7ndu8u9eghJSVJb78tff21NGUK0z0BAAAAAID3kVSLYNu2lSfUjOeek2bPdpYNG5xtTzwhxcQ4z0kz7rtP+o//cEa0mSIEPjNnSmvXSj17hvgmAAAAAAAAXEBSLUJ8/720ZImT9DJL3brSvHmVj3voIempp5z2eedJV10lNWwoffGF1L69FBtb9fXN89b69g3uPQAAAAAAAIQLkmoRwBQRuPRS6Z//dF63aeOMRPuf/3Fe9+4trVvntH0JtTvucKaC+qZypqa6ETkAAAAAAEB4inY7AATff/1XeULNjDTbuVNq1kzavdt5HlpWlnTNNeXHn3++9PTTPBsNAAAAgPdERUWpTZs29mLaAFBdJNU8zhQPWLDAaW/cKA0Z4r//ppucZ6aZRJrPf/5neRVQAAAAAPCS+Ph45eTk2ItpA0B1kVTzuHfecap5mmejXXyxf3EB4+67nbUvqda0qTRuXOjjBAAAAAAAqE1Iqnnc1q3OOj3dWVdMqr3xhtShg9MeOVLq1s2Z9smXNQAAAAAAAD+OQgUe99VXzvrCC531vfdKn3/uJNHM1E8fU9lz0yZ3YgQAAACAUDl+/Lj69+9vt9esWaO4uDi3QwJQS5FU87CSEunLL512587OOjFRWrzY1bAAAAAAwDWlpaXasGFDWRsAqovpnx62aJGUlyc1aSKlpLgdDQAAAAAAgHeQVPMoU5zg0UfLixHUr+92RAAAAAAAAN5BUs2jli2T/vEPqUEDaeJEt6MBAAAAAADwFpJqHh2lNmuW077zTikpye2IAAAAAAAAvIWkmgetWSOtWyfFxEj33ON2NAAAAAAAAN5D9U+POXJEuu8+pz1mjNSihdsRAQAAAEB4adq0qdshAPAAkmoec8st0vr1Tnv8eLejAQAAAIDwkpCQoAMHDrgdBgAPYPqnx9x4o5SWJs2bJ/Xo4XY0AAAAAAAA3sRINY8ZOVIaNUqKinI7EgAAAAAAAO9ipJrHREeTUAMAAACAMzl+/LgGDhxoL6YNANXFSDUAAAAAQMQoLS1VdnZ2WRsAqouRagAAAAAAAECASKoBAAAAAAAAASKpBgAAAAAAAASIpBoAAAAAAAAQIJJqAAAAAAAAQICo/gkAAAAAiCjx8fFuhwDAA0iqAQAAAAAiRkJCggoKCtwOA4AHMP0TAAAAAAAACBBJNQAAAAAAACBAJNUAAAAAABGjqKhIQ4cOtRfTBoDq4plqAAAAAICIUVJSor/97W9lbQCoLkaqAQAAAAAAAAEiqQYAAAAAAAAEiKQaAAAAAAAAECCSagAAAAAAAECASKoBAAAAAAAAAYr46p+WZdnrvLw8t0MBarXi4mIVFhbafalevXpuhwN4Bn0LCA76FhC5faugoKCsbeKkAihqi9rQv7zAlx/y5Yt+TMQn1fLz8+1169at3Q4FAAAAABBCLVu2dDsEAGGcL2rUqNGPHhNlnU3qzcNKS0v1ww8/KDExUVFRUW6HA9TqbL5JTu/atUsNGzZ0OxzAM+hbQHDQt4DgoG8BwUP/Cg2TJjMJNZN0j47+8aemRfxINfMfqFWrVm6HAXiG+eXOL3ig5tG3gOCgbwHBQd8Cgof+FXw/NULNh0IFAAAAAAAAQIBIqgEAAAAAAAABIqkGoEbExMTo4YcfttcAag59CwgO+hYQHPQtIHjoX+En4gsVAAAAAAAAAIFipBoAAAAAAAAQIJJqAAAAAAAAQIBIqgEAAAAAAAABIqkGoEqvv/66unTpogYNGqhv377asGFD2b4dO3Zo2LBhSkpKsvetWLHC71zzqMYnn3xSHTt2VMOGDXX11Vfr22+/9Tvm66+/1vnnn6958+aF7J4Ar/erTz/91D7XXL9z58564403QnZvgJf71ttvv62uXbsqPj7eXr/11lshuzfAy33Lp6ioSBdddJHGjh0b9HsCIqF/LVy4UFFRUZWWxx57LKT3GBFMoQIAqGjJkiVW8+bNrezsbOvQoUPWyy+/bDVt2tTat2+flZeXZ6WkpFjTpk2zdu/ebS1YsMBq3LixtXHjxrLz58yZY3Xq1MnedvDgQWvmzJlWhw4drMLCQuvkyZPW9OnTrRYtWljNmjWzXn31VVfvFfBCvzL27NljJSYm2tc118/KyrL72cqVK128a6D29y3Tl8z1MjMzrcOHD1tLly61+9q6detcvGug9vetiiZNmmSfP2bMmBDfJeDN/vXMM89Y48aNs4qLi/2WkpISF+/am0iqAagkPT3dmjt3rt+2QYMGWa+88oqdBOvfv79VWlpatm/KlCnW2LFjy163bNnSWr58edlrc+wFF1xgffDBB9aBAwes3r1723+MDBgwgKQaIkYw+5UxdepU64YbbvC7/owZM6zRo0cH8a4A7/etjIwMa/bs2X7XHz9+vHX//fcH8a4A7/ctH3NM9+7d7T5FUg2RItj9yyTkzDkIvrpuj5QDEH6++eYb9e7d229bTEyMjh49qg8//FAZGRn28GGf6667ToMHD7bb5ph9+/b5nW+O9Z1/zjnnaO3atX7nA5EgmP3KN6W6f//+VV4f8LJg960RI0ZU6ltxcXGqV69ekO8M8HbfMvbv36+77rpLy5Yt0/z580NyX0Ak9K8DBw7YU0MRfDxTDUAl5pewea6Fz+7du5Wdna309HS73a5dO7/j27dvr0OHDun48eNq1KiRTp06Za8rPudp27Zt6tmzZ9l8fiDSBLNfGYsWLdKvf/3rsv0lJSVasGCBfX3Ay4Ldt26++Wb7GaA+X331ld588037WTeAlwW7b5lZU+PGjdPUqVPVqVOnEN4Z4P3+Za4fHR2tW2+9VSkpKfZz2cxnRdQ8kmoAftSuXbvsPxyGDx+uXr162b/MzcM0K0pMTLTXhw8frnT+li1b7G9WJk+erOTk5JDFDURyvzIPfB49erSOHDlijwAAIkUw+9b7779vPzDaFAGZMmWKfX0gUgSjbz3//PP2qM9bbrklRHcBRE7/Mkk1U8jAFDBYunSpnVwbM2aMli9fHqK7ihwk1QCc0caNG3XppZfav9x9VTrNHxTHjh3zOy4/P99eN2nSxG+7qVLTp08f+8PSrFmzQhg5ELn9Kjc3V4MGDdJ3332njz/+WAkJCUG9HyBS+tZll12mTZs22cm1F154galqiBjB6Ftbt27VH//4R7svMYMBkSxY710zZszQmjVrNGrUKHukmkmq3XfffXrmmWdCcl+RhKQagCqtXLlSAwcOtL/xMB946tevb29v1aqVXeK5ou3bt9u//M23jRVLRJtvTObOnatHHnmED0xACPpVTk6O/cHKXG/16tVq0aJFiO4M8GbfOnnypFatWmWvY2Nj1bp1a1111VV68MEH9fjjj4f4LgHv9C2TCPj+++/tkZ/mvcosTzzxhBYuXGi3zfN3Aa8L5ufCfv36qW3btn7X6N69u/1ZETUsBMUQANQyH374oRUbG2vNnz+/0j5TkcZU7axYjeaBBx7wq9ZkzktMTLRWr179o/8O1T8RSYLdr3Jzc+1KUBMnTqRcOiJKMPuW6UtNmza1PvroI7/tL7/8sl2ZDfCyYPatY8eOWXv37vVbJk+ebF1//fV2+8SJE0G8M8D7712dO3e2PvvsM7/tjz/+uHXNNdfU+L1EOpJqACoxZc0nTJhgFRcX+y3mF3ReXp7VsWNHu0zz7t27rQULFliNGze2Nm7caJ978uRJq0WLFtajjz5a5fkVkVRDJAl2vzJ/jKSmplpFRUV++0+dOuXynQO1u2899NBDVlpamrV+/Xrr6NGj1sqVK63k5GTr9ddfd/nOAW98HvR5+OGH/ZIGgJcFu3/95je/sXr16mW/Z5kvXhcuXGg1adLETuahZpFUA+AnPz/fMoNYq1rMhx1j+/bt1tChQ+1f7unp6VZmZmbZ+V988cUZzz89gUZSDZEiFP2qZ8+eVe43/QzwqlD0LZOY/u1vf2u1a9fOiouLs7p27Wq99tprrt0z4LXPgz4k1RApQvXeNWPGDKtNmzZWQkKC1bt3b2vFihWu3bOXRZn/qekppQAAAAAAAICXUagAAAAAAAAACBBJNQAAAAAAACBAJNUAAAAAAACAAJFUAwAAAAAAAAJEUg0AAAAAAAAIEEk1AAAAAAAAIEAk1QAAAAAAAIAAkVQDAABAmZKSEhUXF7sdBgAAQNgjqQYAAOBhWVlZio6OVmxsbJWL2Tdv3ryy4+fPn6877rij7HVGRoaWLl1qt+vWrasGDRrYS0xMjMaOHevKPQEAAISDum4HAAAAgODq37+/nVyrys0331zW3rRpk+6//34VFBRo3bp19radO3fqs88+0yWXXGK/PnbsmL02ibgzXRMAACASMFINAAAAtosuukiPPfaYRowYoS1bttjLoEGD9Nxzzyk5Odnt8AAAAMIKSTUAAAD4WbBggdq2bWsvK1as8NvXqlUre7nnnntciw8AACAckFQDAACAnxtuuEE5OTn2cuWVV/rt27Ztm73Mnj3btfgAAADCAc9UAwAA8Lg1a9bYRQmqcvLkSV1++eVnfS3fderVq1dj8QEAANRGjFQDAACIgEIFRUVFVS433XTTWU3/PHXqlEpLS12IHgAAIDwxUg0AAACVpn++9NJLdjsjI6OsCqjRo0cPe33w4EENHDjQxSgBAADcRVINAAAAtr/85S+aMGGCPSpt0aJF9raCggKtXLlSjzzyiJ1Q++STT+zt8+bNU1ZWlssRAwAAuIfpnwAAALCZqaB5eXmaNGmS9u3bZyfOVq1aZSfW9uzZowEDBrgdIgAAQNhgpBoAAIDHBVKoYNq0aYqOjlZMTIySk5M1cuRILV26VC+++KKys7PLjsvPz1dUVFRI4gcAAAhHJNUAAAAioFDBmaZqjh49uiw5tmnTJq1fv16ZmZn26549e+rOO+/U008/bT9nzUz//PTTT+3r1a9fv+y5awAAAJEoyrIsy+0gAAAAEB5MhU8zUu10J06csEevAQAAwEFSDQAAAAAAAAgQhQoAAAAAAACAAJFUAwAAAAAAAAJEUg0AAAAAAAAIEEk1AAAAAAAAIEAk1QAAAAAAAIAAkVQDAAAAAAAAAkRSDQAAAAAAAAgQSTUAAAAAAAAgQCTVAAAAAAAAAAXm/wEesdN4F4TAWQAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1500x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 计算分割点\n", "split_idx = int(len(df) * 0.8)\n", "split_date = df.index[split_idx]\n", "\n", "train_data = df.iloc[:split_idx].copy()\n", "test_data = df.iloc[split_idx:].copy()\n", "\n", "print(\"训练集范围:\", train_data.index.min(), \"→\", train_data.index.max())\n", "print(\"测试集范围:\", test_data.index.min(), \"→\", test_data.index.max())\n", "print(\"\\n训练集样本数:\", len(train_data))\n", "print(\"测试集样本数:\", len(test_data))\n", "\n", "# 可视化训练集和测试集的划分\n", "plt.figure(figsize=(15, 6))\n", "plt.plot(train_data.index, train_data['close'], label='训练集', color='blue')\n", "plt.plot(test_data.index, test_data['close'], label='测试集', color='red')\n", "plt.axvline(split_date, color='black', linestyle='--', label='划分点')\n", "plt.title('训练集和测试集划分')\n", "plt.xlabel('日期')\n", "plt.ylabel('价格')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 训练XGBoost"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["# 从训练集中取 X 和 y\n", "features = factors\n", "X_train = train_data[features].values\n", "y_train = train_data['future_ret_1d'].values\n", "\n", "# 同理，测试集\n", "X_test = test_data[features].values\n", "y_test = test_data['future_ret_1d'].values\n"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Gradient Boosting - Best Params: {'learning_rate': 0.01, 'max_depth': 3, 'n_estimators': 100, 'subsample': 0.8}\n", "==== Gradient Boosting - 训练集 ====\n", "MSE: 0.001371923589009181\n", "R2:  0.11294516836465363\n", "==== Gradient Boosting - 测试集 ====\n", "MSE: 0.001798591652712661\n", "R2:  -0.05969947112957885\n", "Feature: momentum_5, Importance: 0.2331\n", "Feature: vol_ratio, Importance: 0.2372\n", "Feature: RSI_14, Importance: 0.0833\n", "Feature: BB_upper, Importance: 0.2327\n", "Feature: BB_lower, Importance: 0.2137\n", "\n", "Sorted Feature Importances (Gradient Boosting):\n", "vol_ratio -> 0.2372\n", "momentum_5 -> 0.2331\n", "BB_upper -> 0.2327\n", "BB_lower -> 0.2137\n", "RSI_14 -> 0.0833\n"]}], "source": ["from sklearn.ensemble import GradientBoostingRegressor\n", "from sklearn.model_selection import GridSearchCV, TimeSeriesSplit\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "import numpy as np\n", "\n", "######################################\n", "# 1. 定义时间序列折叠方式\n", "######################################\n", "# 这里假设做 3 折时间序列拆分，你可以自行调整 n_splits 的大小\n", "tscv = TimeSeriesSplit(n_splits=3)\n", "\n", "######################################\n", "# 2. 使用 GridSearchCV 寻找最优随机森林 (带 TimeSeriesSplit)\n", "######################################\n", "\n", "# 1. 定义基础模型\n", "base_model_gb = GradientBoostingRegressor(random_state=42)\n", "\n", "# 2. 定义超参数网格\n", "param_grid_gb = {\n", "    'n_estimators': [100, 200, 300],   # 梯度提升中弱学习器（决策树）的数量\n", "    'learning_rate': [0.01, 0.1],      # 学习率\n", "    'max_depth': [3, 5],              # 每棵回归树的最大深度\n", "    'subsample': [1.0, 0.8],          # 每次训练子样本的比例 (1.0 表示不采样)\n", "}\n", "\n", "# 3. 使用网格搜索\n", "grid_search_gb = GridSearchCV(\n", "    estimator=base_model_gb,\n", "    param_grid=param_grid_gb,\n", "    scoring='neg_mean_squared_error',  # 用 MSE 评价模型好坏\n", "    cv=3,\n", "    n_jobs=-1\n", ")\n", "\n", "# 4. 训练\n", "grid_search_gb.fit(X_train, y_train)\n", "\n", "# 5. 输出最优超参数\n", "print(\"Gradient Boosting - Best Params:\", grid_search_gb.best_params_)\n", "\n", "# 6. 获取最优模型\n", "best_model_gb = grid_search_gb.best_estimator_\n", "\n", "# 7. 评估最优模型\n", "y_pred_train_gb = best_model_gb.predict(X_train)\n", "y_pred_test_gb = best_model_gb.predict(X_test)\n", "\n", "train_mse_gb = mean_squared_error(y_train, y_pred_train_gb)\n", "test_mse_gb  = mean_squared_error(y_test, y_pred_test_gb)\n", "train_r2_gb  = r2_score(y_train, y_pred_train_gb)\n", "test_r2_gb   = r2_score(y_test, y_pred_test_gb)\n", "\n", "print(\"==== Gradient Boosting - 训练集 ====\")\n", "print(\"MSE:\", train_mse_gb)\n", "print(\"R2: \", train_r2_gb)\n", "\n", "print(\"==== Gradient Boosting - 测试集 ====\")\n", "print(\"MSE:\", test_mse_gb)\n", "print(\"R2: \", test_r2_gb)\n", "\n", "# 8. 查看特征重要性\n", "feature_importances_gb = best_model_gb.feature_importances_\n", "for f, imp in zip(features, feature_importances_gb):\n", "    print(f\"Feature: {f}, Importance: {imp:.4f}\")\n", "\n", "# 如果想要按重要性从大到小排序，可执行：\n", "sorted_idx_gb = np.argsort(feature_importances_gb)[::-1]\n", "print(\"\\nSorted Feature Importances (Gradient Boosting):\")\n", "for idx in sorted_idx_gb:\n", "    print(f\"{features[idx]} -> {feature_importances_gb[idx]:.4f}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 使用Backtrader进行回测"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["import backtrader as bt\n", "\n", "# 自定义成交量指标，把成交量数据单独显示在子图中\n", "class MyVolumeIndicator(bt.Indicator):\n", "    \"\"\"\n", "    简单示例，把data的volume包装成一个单独的子图指标。\n", "    \"\"\"\n", "    lines = ('vol',)\n", "    plotinfo = dict(subplot=True, plotname='Volume')  # 让它单独开子图\n", "\n", "    def __init__(self):\n", "        self.lines.vol = self.data.volume\n", "\n", "class MLFactorStrategy(bt.Strategy):\n", "    params = (\n", "        ('model', None),            # 预先训练好的机器学习模型\n", "        ('target_percent', 0.98),   # 目标仓位百分比\n", "    )\n", "\n", "    def __init__(self):\n", "        self.model = self.p.model\n", "        \n", "        # 关闭主图中Data自带的Volume绘制\n", "        self.data.plotinfo.plotvolume = False\n", "\n", "        # 自定义成交量指标以及其SMA指标\n", "        self.myvol = MyVolumeIndicator(self.data)\n", "        self.vol_5 = bt.indicators.SMA(self.myvol.vol, period=5)\n", "        self.vol_5.plotinfo.subplot = True\n", "        self.vol_10 = bt.indicators.SMA(self.myvol.vol, period=10)\n", "        self.vol_10.plotinfo.subplot = True\n", "\n", "        # 添加其它因子指标\n", "\n", "        # 价格动量指标：计算5日价格百分比变化\n", "        self.momentum_5 = bt.indicators.PercentChange(self.data.close, period=5)\n", "        \n", "        # RSI指标，14日周期\n", "        self.rsi_14 = bt.indicators.RSI(self.data.close, period=14)\n", "        \n", "        # 布林带指标，默认20日均线和2倍标准差，返回上轨、均线和下轨\n", "        self.bb = bt.indicators.BollingerBands(self.data.close)\n", "\n", "        self.last_trade_type = None  # 记录上一次交易类型（buy/sell）\n", "        \n", "        self.value_history_dates = []\n", "        self.value_history_values = []\n", "\n", "    def next(self):\n", "        # 计算各个因子的当前值\n", "        momentum = self.momentum_5[0]\n", "        vol_ratio = (self.vol_5[0] / self.vol_10[0] - 1) if self.vol_10[0] != 0 else 0\n", "        rsi = self.rsi_14[0]\n", "        bb_upper = self.bb.top[0]  # 布林带上轨\n", "        bb_lower = self.bb.bot[0]  # 布林带下轨\n", "\n", "        # 构建特征向量：注意顺序需要与模型训练时一致\n", "        X = [[momentum, vol_ratio, rsi, bb_upper, bb_lower]]\n", "        pred_ret = self.model.predict(X)[0]\n", "\n", "        # 获取当前持仓状态\n", "        current_position = self.getposition().size\n", "\n", "        if pred_ret > 0 and current_position == 0:\n", "            # 只有当当前没有仓位时，才执行买入\n", "            self.order_target_percent(target=self.p.target_percent)\n", "            self.last_trade_type = \"BUY\"\n", "            print(f\"{self.datas[0].datetime.date(0)} => BUY signal, pred_ret={pred_ret:.6f}\")\n", "        \n", "        elif pred_ret <= 0 and current_position > 0:\n", "            # 只有当当前有仓位时，才执行卖出\n", "            self.order_target_percent(target=0.0)\n", "            self.last_trade_type = \"SELL\"\n", "            print(f\"{self.datas[0].datetime.date(0)} => SELL signal, pred_ret={pred_ret:.6f}\")\n", "\n", "        # 只在交易执行时打印仓位信息\n", "        if self.last_trade_type:\n", "            print(f\"Current position size: {self.getposition().size}, Value: {self.broker.getvalue()}\")\n", "\n", "        dt = self.data.datetime.date(0)\n", "        self.value_history_dates.append(dt)\n", "        self.value_history_values.append(self.broker.getvalue())\n"]}, {"cell_type": "code", "execution_count": 88, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["初始资金: 100000.00\n", "2024-04-09 => BUY signal, pred_ret=0.002981\n", "Current position size: 0, Value: 100000.0\n", "Current position size: 554, Value: 99167.288819989\n", "Current position size: 554, Value: 100740.65524454955\n", "Current position size: 554, Value: 98773.95355387572\n", "Current position size: 554, Value: 93472.16949625853\n", "Current position size: 554, Value: 91051.19220133666\n", "Current position size: 554, Value: 90131.55017252806\n", "Current position size: 554, Value: 87073.4678055847\n", "Current position size: 554, Value: 85477.95355387572\n", "Current position size: 554, Value: 82707.95355387572\n", "Current position size: 554, Value: 84164.9678055847\n", "Current position size: 554, Value: 93832.27456828002\n", "Current position size: 554, Value: 98291.9678055847\n", "Current position size: 554, Value: 97244.90814371947\n", "Current position size: 554, Value: 111515.95355387572\n", "Current position size: 554, Value: 105549.37118693236\n", "Current position size: 554, Value: 103726.71490641478\n", "Current position size: 554, Value: 103737.788819989\n", "Current position size: 554, Value: 104391.51321574095\n", "Current position size: 554, Value: 106369.288819989\n", "Current position size: 554, Value: 102518.99051066283\n", "Current position size: 554, Value: 100807.13253947142\n", "Current position size: 554, Value: 99283.63253947142\n", "Current position size: 554, Value: 97344.63253947142\n", "Current position size: 554, Value: 99239.31152506713\n", "Current position size: 554, Value: 102374.95355387572\n", "Current position size: 554, Value: 100402.71490641478\n", "Current position size: 554, Value: 100873.6098343933\n", "Current position size: 554, Value: 102325.09558268431\n", "Current position size: 554, Value: 100934.55017252806\n", "Current position size: 554, Value: 107388.65524454955\n", "Current position size: 554, Value: 103793.19220133666\n", "Current position size: 554, Value: 100264.21490641478\n", "Current position size: 554, Value: 103311.21490641478\n", "Current position size: 554, Value: 101931.75186320189\n", "Current position size: 554, Value: 101621.51321574095\n", "Current position size: 554, Value: 103061.90814371947\n", "Current position size: 554, Value: 102668.57287760619\n", "Current position size: 554, Value: 101676.90814371947\n", "Current position size: 554, Value: 100834.83423014525\n", "Current position size: 554, Value: 100962.25186320189\n", "Current position size: 554, Value: 102591.01321574095\n", "Current position size: 554, Value: 102336.16949625853\n", "Current position size: 554, Value: 100291.90814371947\n", "Current position size: 554, Value: 98557.89389201048\n", "Current position size: 554, Value: 102230.90814371947\n", "Current position size: 554, Value: 105100.63253947142\n", "Current position size: 554, Value: 102629.788819989\n", "Current position size: 554, Value: 107854.01321574095\n", "Current position size: 554, Value: 106424.69220133666\n", "Current position size: 554, Value: 104602.03592081908\n", "Current position size: 554, Value: 105399.788819989\n", "Current position size: 554, Value: 105161.57287760619\n", "Current position size: 554, Value: 107804.15524454955\n", "Current position size: 554, Value: 112801.22915812377\n", "Current position size: 554, Value: 113382.9308487976\n", "Current position size: 554, Value: 113637.77456828002\n", "Current position size: 554, Value: 120274.69220133666\n", "Current position size: 554, Value: 132130.28881998901\n", "Current position size: 554, Value: 140512.31152506714\n", "Current position size: 554, Value: 143354.33423014526\n", "Current position size: 554, Value: 144141.01321574097\n", "Current position size: 554, Value: 149343.06442423706\n", "Current position size: 554, Value: 149858.29727335816\n", "Current position size: 554, Value: 137542.87118693237\n", "Current position size: 554, Value: 141531.66949625855\n", "Current position size: 554, Value: 143974.81152506714\n", "Current position size: 554, Value: 146146.49051066284\n", "Current position size: 554, Value: 141681.2518632019\n", "Current position size: 554, Value: 142085.66949625855\n", "Current position size: 554, Value: 136529.05017252808\n", "Current position size: 554, Value: 143348.78881998901\n", "Current position size: 554, Value: 140506.77456828003\n", "Current position size: 554, Value: 123670.71490641478\n", "Current position size: 554, Value: 126030.75186320189\n", "Current position size: 554, Value: 125781.45355387572\n", "Current position size: 554, Value: 132595.65524454956\n", "Current position size: 554, Value: 127343.72915812377\n", "Current position size: 554, Value: 132579.0359208191\n", "Current position size: 554, Value: 124152.69220133666\n", "2024-08-02 => SELL signal, pred_ret=-0.000074\n", "Current position size: 554, Value: 119061.4308487976\n", "2024-08-05 => BUY signal, pred_ret=0.000202\n", "Current position size: 0, Value: 106493.84835879515\n", "Current position size: 524, Value: 106304.78883897091\n", "Current position size: 524, Value: 101651.66628037716\n", "Current position size: 524, Value: 105361.58723984982\n", "Current position size: 524, Value: 105969.42915879513\n", "Current position size: 524, Value: 104654.1920372131\n", "Current position size: 524, Value: 110072.35011826779\n", "Current position size: 524, Value: 106692.55171738888\n", "Current position size: 524, Value: 113378.78883897091\n", "Current position size: 524, Value: 114416.30660020138\n", "Current position size: 524, Value: 117874.70979844357\n", "Current position size: 524, Value: 117025.83235703732\n", "Current position size: 524, Value: 118162.91139756466\n", "Current position size: 524, Value: 111555.27107774044\n", "Current position size: 524, Value: 116617.11299668576\n", "Current position size: 524, Value: 112891.47267686154\n", "Current position size: 524, Value: 110795.47267686154\n", "Current position size: 524, Value: 108982.42915879513\n", "Current position size: 524, Value: 109260.14851914669\n", "Current position size: 524, Value: 113363.06947861935\n", "Current position size: 524, Value: 111523.83235703732\n", "Current position size: 524, Value: 116140.27107774044\n", "Current position size: 524, Value: 121778.50819932247\n", "Current position size: 524, Value: 111591.9469200256\n", "Current position size: 524, Value: 114494.91139756466\n", "Current position size: 524, Value: 119682.50819932247\n", "Current position size: 524, Value: 120709.55171738888\n", "Current position size: 524, Value: 121589.86787949826\n", "Current position size: 524, Value: 121841.38564072872\n", "Current position size: 524, Value: 120002.14851914669\n", "Current position size: 524, Value: 120573.30660020138\n", "Current position size: 524, Value: 120222.22755967404\n", "Current position size: 524, Value: 128983.50819932247\n", "Current position size: 524, Value: 126012.42915879513\n", "Current position size: 524, Value: 132169.42915879513\n", "Current position size: 524, Value: 134406.91139756466\n", "Current position size: 524, Value: 135847.9034019592\n", "Current position size: 524, Value: 134380.70979844357\n", "Current position size: 524, Value: 137650.46468125607\n", "Current position size: 524, Value: 138263.55171738888\n", "Current position size: 524, Value: 136371.9034019592\n", "Current position size: 524, Value: 131655.91139756466\n", "Current position size: 524, Value: 127275.27107774044\n", "Current position size: 524, Value: 132211.3501182678\n", "Current position size: 524, Value: 127364.35011826779\n", "Current position size: 524, Value: 129287.42915879513\n", "Current position size: 524, Value: 127479.63075791622\n", "Current position size: 524, Value: 126284.91139756466\n", "Current position size: 524, Value: 115296.63075791622\n", "Current position size: 524, Value: 116009.27107774044\n", "Current position size: 524, Value: 116224.11299668576\n", "Current position size: 524, Value: 117146.35011826779\n", "Current position size: 524, Value: 116915.78883897091\n", "2024-10-18 => SELL signal, pred_ret=-0.000074\n", "Current position size: 524, Value: 116816.22755967404\n", "Current position size: 0, Value: 115732.1485637512\n", "Current position size: 0, Value: 115732.1485637512\n", "Current position size: 0, Value: 115732.1485637512\n", "2024-10-24 => BUY signal, pred_ret=0.000946\n", "Current position size: 0, Value: 115732.1485637512\n", "Current position size: 435, Value: 121332.309273468\n", "Current position size: 435, Value: 118426.51245950315\n", "Current position size: 435, Value: 117125.85343240354\n", "Current position size: 435, Value: 116268.90290139768\n", "Current position size: 435, Value: 112919.41086648557\n", "Current position size: 435, Value: 112540.95635293577\n", "Current position size: 435, Value: 109870.0566184387\n", "Current position size: 435, Value: 113611.059273468\n", "Current position size: 435, Value: 129745.20768045042\n", "Current position size: 435, Value: 133390.50980447384\n", "Current position size: 435, Value: 143965.35874246212\n", "Current position size: 435, Value: 156484.65821145626\n", "Current position size: 435, Value: 147127.8039634094\n", "Current position size: 435, Value: 147889.0539634094\n", "Current position size: 435, Value: 139597.9550254211\n", "Current position size: 435, Value: 143747.85874246212\n", "Current position size: 435, Value: 151586.5539634094\n", "Current position size: 435, Value: 154744.65821145626\n", "Current position size: 435, Value: 153017.7076804504\n", "Current position size: 435, Value: 151978.06458352658\n", "Current position size: 435, Value: 157598.25714944454\n", "Current position size: 435, Value: 151521.30661843868\n", "Current position size: 435, Value: 151364.712990509\n", "Current position size: 435, Value: 149041.81458352658\n", "Current position size: 435, Value: 154379.25980447384\n", "2024-12-02 => SELL signal, pred_ret=-0.000636\n", "Current position size: 435, Value: 159568.80661843868\n", "Current position size: 0, Value: 157092.89165670774\n", "Current position size: 0, Value: 157092.89165670774\n", "Current position size: 0, Value: 157092.89165670774\n", "Current position size: 0, Value: 157092.89165670774\n", "Current position size: 0, Value: 157092.89165670774\n", "2024-12-10 => BUY signal, pred_ret=0.001681\n", "Current position size: 0, Value: 157092.89165670774\n", "Current position size: 383, Value: 162688.60851897582\n", "Current position size: 383, Value: 160134.00506438597\n", "Current position size: 383, Value: 167077.79693450316\n", "2024-12-16 => SELL signal, pred_ret=-0.020378\n", "Current position size: 383, Value: 177338.35851897582\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "2025-02-26 => BUY signal, pred_ret=0.018888\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 613, Value: 176215.11334722288\n", "Current position size: 613, Value: 182976.50259893187\n", "Current position size: 613, Value: 177870.20212285765\n", "Current position size: 613, Value: 170140.28110234984\n", "2025-03-05 => SELL signal, pred_ret=-0.002243\n", "Current position size: 613, Value: 174468.0596057678\n", "2025-03-06 => BUY signal, pred_ret=0.000279\n", "Current position size: 0, Value: 169955.14223922725\n", "回测结束资金: 169955.14\n", "=== 回测分析报告 ===\n", "夏普比率: 11.4347\n", "最大回撤比例: 32.17%\n", "最大回撤金额(自定义): 48206.63\n", "累计收益率: 53.04%\n", "年化收益率: 0.21%\n", "=== 交易详情 ===\n", "总交易笔数: 5\n", "胜率: 4 / 5\n"]}], "source": ["# 若想看最优参数的详细回测日志，可再手动调用:\n", "ml_result, ml_cerebro = run_backtest(\n", "    ticker=ticker,\n", "    df=df.iloc[split_idx:].copy(),\n", "    start_date=start_date,\n", "    end_date=end_date,\n", "    strategy=MLFactorStrategy,\n", "    initial_cash=100000,\n", "    strategy_params={'model': model, 'target_percent':0.98},\n", "    print_log=True,  # 这次打开日志\n", ")\n"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [], "source": ["\n", "# ===== 猴子补丁：为 numpy 添加 bool8 和 object 属性 =====\n", "import numpy as np\n", "if not hasattr(np, 'bool8'):\n", "    np.bool8 = np.bool_  # 使用 numpy 自带的 bool_ 类型\n", "if not hasattr(np, 'object'):\n", "    np.object = object  # 兼容 backtrader_plotting 的引用"]}, {"cell_type": "code", "execution_count": 90, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<style>\n", "    body {\n", "        background-color: white;\n", "        color: #222222;\n", "    }\n", "\n", "    table.metaDataTable {\n", "      border: 1px solid #1C6EA4;\n", "      background-color: #eeeeee;\n", "      width: 100%;\n", "      text-align: left;\n", "      border-collapse: collapse;\n", "    }\n", "    table.metaDataTable td, table.metaDataTable th {\n", "      border: 1px solid #eeeeee;\n", "      padding: 3px 2px;\n", "    }\n", "    table.metaDataTable tbody td {\n", "      font-size: 13px;\n", "    }\n", "    table.metaDataTable tr:nth-child(even) {\n", "      background: #fefefefe;\n", "    }\n", "    table.metaDataTable tfoot td {\n", "      font-size: 14px;\n", "    }\n", "    table.metaDataTable tfoot .links {\n", "      text-align: right;\n", "    }\n", "    table.metaDataTable tfoot .links a{\n", "      display: inline-block;\n", "      background: #1C6EA4;\n", "      color: #FFFFFF;\n", "      padding: 2px 8px;\n", "      border-radius: 5px;\n", "    }\n", "\n", "    pre {\n", "        background-color: #222222;\n", "        color: lightgrey;\n", "    }\n", "\n", "    #headline {\n", "        font-size: 200%;\n", "        font-family: \"Segoe UI\", \"Arial Black\", Gadget, sans-serif;\n", "        color: #333333;\n", "    }\n", "\n", "    .bk-root {\n", "        font-family: \"Segoe UI\", \"Arial Black\", Gadget, sans-serif;\n", "    }\n", "    .bk-toolbar {position: fixed;}\n", "\n", "    .slick-header-column {\n", "        background-color: #cccccc !important;\n", "        background-image: none !important;\n", "        font-size: 130%;\n", "    }\n", "\n", "    .slick-row.even {\n", "        background-color: #fefefefe !important;\n", "    }\n", "\n", "    .slick-row.odd {\n", "        background-color: #eeeeee !important;\n", "    }\n", "\n", "    .bk-root .bk-bs-nav-tabs>li.bk-bs-active>span {\n", "        background-color: #dddddd !important;\n", "        color: #111111 !important;\n", "        border-color: #dddddd !important;\n", "    }\n", "\n", "    .bk-root .bk-bs-nav>li>span:hover {\n", "        background-color: #dddddd !important;\n", "        border-color: #dddddd !important;\n", "        color: #111111 !important;\n", "    }\n", "\n", "    .bk-tooltip {\n", "        border-radius: 3px;\n", "        background-color: #f5f5f5 !important;\n", "        border-color: #f5f5f5 !important;\n", "    }\n", "\n", "    .bk-tooltip-row-label {\n", "        color: #848EFF !important;\n", "    }\n", "\n", "    .bk-tooltip-row-value {\n", "        color: #aaaaaa !important;\n", "    }\n", "</style>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "\n", "\n", "\n", "\n", "\n", "  <div class=\"bk-root\" id=\"085343b2-deeb-40c3-88e8-986295f67ff2\" data-root-id=\"13759\"></div>\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/javascript": ["(function(root) {\n", "  function embed_document(root) {\n", "    \n", "  var docs_json = {\"c10ac79d-9915-415d-8968-3c16df2ea87c\":{\"defs\":[],\"roots\":{\"references\":[{\"attributes\":{\"tabs\":[{\"id\":\"13557\"},{\"id\":\"13756\"},{\"id\":\"13758\"}]},\"id\":\"13759\",\"type\":\"Tabs\"},{\"attributes\":{\"line_color\":\"#000000\"},\"id\":\"13291\",\"type\":\"CrosshairTool\"},{\"attributes\":{\"axis\":{\"id\":\"12866\"},\"dimension\":1,\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"12869\",\"type\":\"Grid\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"12881\"},\"major_label_policy\":{\"id\":\"12902\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"12867\"}},\"id\":\"12866\",\"type\":\"LinearAxis\"},{\"attributes\":{},\"id\":\"12867\",\"type\":\"BasicTicker\"},{\"attributes\":{\"use_scientific\":false},\"id\":\"12881\",\"type\":\"BasicTickFormatter\"},{\"attributes\":{\"line_alpha\":0.1,\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6437861456\"}},\"id\":\"13299\",\"type\":\"Line\"},{\"attributes\":{\"callback\":null,\"formatters\":{\"@datetime\":\"datetime\"},\"mode\":\"vline\",\"renderers\":[{\"id\":\"13300\"}],\"tooltips\":[[\"Time\",\"@datetime{%F %R}\"],[\"RSI (14)\",\"@6437861456{0,0.000}\"]]},\"id\":\"13293\",\"type\":\"HoverTool\"},{\"attributes\":{\"axis\":{\"id\":\"13323\"},\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"13326\",\"type\":\"Grid\"},{\"attributes\":{},\"id\":\"13705\",\"type\":\"StringEditor\"},{\"attributes\":{},\"id\":\"13748\",\"type\":\"StringEditor\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"13298\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"rsi\",\"nonselection_glyph\":{\"id\":\"13299\"},\"view\":{\"id\":\"13301\"}},\"id\":\"13300\",\"type\":\"GlyphRenderer\"},{\"attributes\":{},\"id\":\"13328\",\"type\":\"BasicTicker\"},{\"attributes\":{},\"id\":\"13706\",\"type\":\"StringEditor\"},{\"attributes\":{},\"id\":\"13749\",\"type\":\"StringEditor\"},{\"attributes\":{\"line_alpha\":{\"value\":0.1},\"line_color\":{\"field\":\"6437759296colors_wicks\"},\"x0\":{\"field\":\"index\"},\"x1\":{\"field\":\"index\"},\"y0\":{\"field\":\"6437759296high\"},\"y1\":{\"field\":\"6437759296low\"}},\"id\":\"12893\",\"type\":\"Segment\"},{\"attributes\":{\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6437861456\"}},\"id\":\"13298\",\"type\":\"Line\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"13301\",\"type\":\"CDSView\"},{\"attributes\":{},\"id\":\"13707\",\"type\":\"StringEditor\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"13342\"},\"major_label_policy\":{\"id\":\"13363\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"13328\"}},\"id\":\"13327\",\"type\":\"LinearAxis\"},{\"attributes\":{\"background_fill_color\":\"#f5f5f5\",\"click_policy\":\"hide\",\"items\":[{\"id\":\"13313\"}],\"label_text_color\":\"#333333\",\"location\":\"top_left\",\"orientation\":\"horizontal\"},\"id\":\"13312\",\"type\":\"Legend\"},{\"attributes\":{},\"id\":\"13750\",\"type\":\"UnionRenderers\"},{\"attributes\":{\"callback\":null,\"formatters\":{\"@datetime\":\"datetime\"},\"mode\":\"vline\",\"renderers\":[{\"id\":\"12911\"}],\"tooltips\":[[\"Time\",\"@datetime{%F %R}\"],[\"Open\",\"@6437759296open{0,0.000}\"],[\"High\",\"@6437759296high{0,0.000}\"],[\"Low\",\"@6437759296low{0,0.000}\"],[\"Close\",\"@6437759296close{0,0.000}\"],[\"Volume\",\"@6437759296volume{(0,0.000)}\"],[\"BuySell (True, 0.015) - buy\",\"@6438030256{0,0.000}\"],[\"BuySell (True, 0.015) - sell\",\"@6438030352{0,0.000}\"]]},\"id\":\"12887\",\"type\":\"HoverTool\"},{\"attributes\":{},\"id\":\"13751\",\"type\":\"Selection\"},{\"attributes\":{\"line_color\":\"#000000\"},\"id\":\"12885\",\"type\":\"CrosshairTool\"},{\"attributes\":{},\"id\":\"13708\",\"type\":\"UnionRenderers\"},{\"attributes\":{},\"id\":\"13709\",\"type\":\"Selection\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"12892\"},\"hover_glyph\":null,\"muted_glyph\":null,\"nonselection_glyph\":{\"id\":\"12893\"},\"view\":{\"id\":\"12895\"}},\"id\":\"12894\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"args\":{\"axis\":{\"id\":\"12862\"},\"formatter\":{\"id\":\"12882\"},\"source\":{\"id\":\"12852\"}},\"code\":\"\\n                // We override this axis' formatter's `doFormat` method\\n                // with one that maps index ticks to dates. Some of those dates\\n                // are undefined (e.g. those whose ticks fall out of defined data\\n                // range) and we must filter out and account for those, otherwise\\n                // the formatter computes invalid visible span and returns some\\n                // labels as 'ERR'.\\n                // Note, after this assignment statement, on next plot redrawing,\\n                // our override `doFormat` will be called directly\\n                // -- FunctionTickFormatter.doFormat(), i.e. _this_ code, no longer\\n                // executes.\\n                axis.formatter.doFormat = function (ticks) {\\n                    const dates = ticks.map(i => source.data.datetime[source.data.index.indexOf(i)]),\\n                          valid = t => t !== undefined,\\n                          labels = formatter.doFormat(dates.filter(valid));\\n                    let i = 0;\\n                    return dates.map(t => valid(t) ? labels[i++] : '');\\n                };\\n\\n                // we do this manually only for the first time we are called\\n                const labels = axis.formatter.doFormat(ticks);\\n                return labels[index];\\n            \"},\"id\":\"12883\",\"type\":\"FuncTickFormatter\"},{\"attributes\":{\"bottom_units\":\"screen\",\"fill_alpha\":0.5,\"fill_color\":\"lightgrey\",\"left_units\":\"screen\",\"level\":\"overlay\",\"line_alpha\":1.0,\"line_color\":\"black\",\"line_dash\":[4,4],\"line_width\":2,\"right_units\":\"screen\",\"syncable\":false,\"top_units\":\"screen\"},\"id\":\"12874\",\"type\":\"BoxAnnotation\"},{\"attributes\":{},\"id\":\"12871\",\"type\":\"WheelZoomTool\"},{\"attributes\":{},\"id\":\"12870\",\"type\":\"PanTool\"},{\"attributes\":{},\"id\":\"13305\",\"type\":\"AllLabels\"},{\"attributes\":{\"overlay\":{\"id\":\"12874\"}},\"id\":\"12872\",\"type\":\"BoxZoomTool\"},{\"attributes\":{},\"id\":\"12873\",\"type\":\"ResetTool\"},{\"attributes\":{\"child\":{\"id\":\"13757\"},\"title\":\"Meta\"},\"id\":\"13758\",\"type\":\"Panel\"},{\"attributes\":{\"days\":[\"%d %b %R\"],\"hourmin\":[\"%H:%M:%S\"],\"hours\":[\"%d %b %R\"],\"minsec\":[\"%H:%M:%S\"],\"minutes\":[\"%H:%M\"],\"months\":[\"%d/%m/%y\"],\"seconds\":[\"%H:%M:%S\"],\"years\":[\"%Y %b\"]},\"id\":\"12882\",\"type\":\"DatetimeTickFormatter\"},{\"attributes\":{\"children\":[{\"id\":\"13754\"},{\"id\":\"13752\"}],\"sizing_mode\":\"stretch_width\"},\"id\":\"13755\",\"type\":\"Column\"},{\"attributes\":{\"child\":{\"id\":\"13755\"},\"title\":\"Analyzers\"},\"id\":\"13756\",\"type\":\"Panel\"},{\"attributes\":{\"active_multi\":null,\"tools\":[{\"id\":\"12870\"},{\"id\":\"12871\"},{\"id\":\"12872\"},{\"id\":\"12873\"},{\"id\":\"12885\"},{\"id\":\"12887\"}]},\"id\":\"12875\",\"type\":\"Toolbar\"},{\"attributes\":{},\"id\":\"13710\",\"type\":\"StringEditor\"},{\"attributes\":{\"text\":\"<h1>Strategy: MLFactorStrategy</h1>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>model</td>\\n  <td>GradientBoostingRegressor(learning<em>rate=0.01, random</em>state=42, subsample=0.8)</td>\\n</tr>\\n<tr>\\n  <td>target_percent</td>\\n  <td>0.98</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h2>Indicators:</h2>\\n\\n<h3>Volume@(TSLA)</h3>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td></td>\\n  <td></td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h3>SMA (5)@(Volume^vol@(TSLA)@(TSLA))</h3>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>period</td>\\n  <td>5</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h3>SMA (10)@(Volume^vol@(TSLA)@(TSLA))</h3>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>period</td>\\n  <td>10</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h3>PercentChange (5)@(TSLA^close)</h3>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>period</td>\\n  <td>5</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h3>RSI (14)@(TSLA^close)</h3>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>period</td>\\n  <td>14</td>\\n</tr>\\n<tr>\\n  <td>movav</td>\\n  <td>SmoothedMovingAverage</td>\\n</tr>\\n<tr>\\n  <td>upperband</td>\\n  <td>70.00</td>\\n</tr>\\n<tr>\\n  <td>lowerband</td>\\n  <td>30.00</td>\\n</tr>\\n<tr>\\n  <td>safediv</td>\\n  <td>False</td>\\n</tr>\\n<tr>\\n  <td>safehigh</td>\\n  <td>100.00</td>\\n</tr>\\n<tr>\\n  <td>safelow</td>\\n  <td>50.00</td>\\n</tr>\\n<tr>\\n  <td>lookback</td>\\n  <td>1</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h3>BollingerBands (20, 2.0)@(TSLA^close)</h3>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>period</td>\\n  <td>20</td>\\n</tr>\\n<tr>\\n  <td>devfactor</td>\\n  <td>2.00</td>\\n</tr>\\n<tr>\\n  <td>movav</td>\\n  <td>MovingAverageSimple</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<hr />\\n\\n<h1>Data Feeds</h1>\\n\\n<h2>PandasData</h2>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Property</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>DataName:</td>\\n  <td>close     high      low     open     volume  momentum_5  \\\\</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<p>Date<br />\\n2024-03-12 177.5400 179.4300 172.4100 177.7700   87391700     -0.0177<br />\\n2024-03-13 169.4800 176.0500 169.1500 173.0500  106524500     -0.0400<br />\\n2024-03-14 162.5000 171.1700 160.5100 167.7700  126325700     -0.0904<br />\\n2024-03-15 163.5700 165.1800 160.7600 163.1600   96971900     -0.0671<br />\\n2024-03-18 173.8000 174.7200 165.9000 170.0200  108214400     -0.0223<br />\\n...             ...      ...      ...      ...        ...         ...<br />\\n2025-02-28 292.9800 293.8800 273.6000 279.5000  115697000     -0.1327<br />\\n2025-03-03 284.6500 303.9400 277.3000 300.3400  115551400     -0.1388<br />\\n2025-03-04 272.0400 284.3500 261.8400 270.9300  126706600     -0.1016<br />\\n2025-03-05 279.1000 279.5500 267.7100 272.9200   94042900     -0.0402<br />\\n2025-03-06 263.4500 272.6500 260.0200 272.0600   98451600     -0.0656   </p>\\n\\n<pre><code>        vol_ratio  RSI_14  BB_upper  BB_middle  BB_lower  future_ret_1d\\n</code></pre>\\n\\n<p>Date<br />\\n2024-03-12    -0.0542 36.2619  209.9766   190.5725  171.1684        -0.0454<br />\\n2024-03-13    -0.0634 31.2571  211.1715   189.8455  168.5195        -0.0412<br />\\n2024-03-14    -0.0532 27.6926  212.9732   188.5350  164.0968         0.0066<br />\\n2024-03-15    -0.0444 29.0287  212.7655   186.6910  160.6165         0.0625<br />\\n2024-03-18     0.0244 40.3732  211.2894   185.3835  159.4776        -0.0143<br />\\n...               ...     ...       ...        ...       ...            ...<br />\\n2025-02-28     0.2645 30.4602  412.9799   346.4025  279.8251        -0.0284<br />\\n2025-03-03     0.2863 28.8091  406.5409   340.4050  274.2691        -0.0443<br />\\n2025-03-04     0.1697 26.4700  404.1738   334.8230  265.4722         0.0260<br />\\n2025-03-05     0.1253 29.9017  397.3141   329.1675  261.0209        -0.0561<br />\\n2025-03-06     0.0620 26.9042  393.4019   323.4315  253.4611        -0.0030  </p>\\n\\n<p>[247 rows x 12 columns]|\\n|Timezone:|None|\\n|Number of bars:|247|\\n|Bar Length:|5 Minutes|\\n|Time From:|2020-03-11 15:09:26.893907|\\n|Time To:|2025-03-10 15:09:26.893907|</p>\\n\\n<hr />\\n\\n<h1>Observers</h1>\\n\\n<h2>Broker</h2>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>fund</td>\\n  <td>None</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h2>Trades</h2>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>pnlcomm</td>\\n  <td>True</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<hr />\\n\\n<h1>Analyzers</h1>\\n\\n<h2>SharpeRatio</h2>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>timeframe</td>\\n  <td>Minute</td>\\n</tr>\\n<tr>\\n  <td>compression</td>\\n  <td>1</td>\\n</tr>\\n<tr>\\n  <td>riskfreerate</td>\\n  <td>0.01</td>\\n</tr>\\n<tr>\\n  <td>factor</td>\\n  <td>19656</td>\\n</tr>\\n<tr>\\n  <td>convertrate</td>\\n  <td>True</td>\\n</tr>\\n<tr>\\n  <td>annualize</td>\\n  <td>True</td>\\n</tr>\\n<tr>\\n  <td>stddev_sample</td>\\n  <td>False</td>\\n</tr>\\n<tr>\\n  <td>daysfactor</td>\\n  <td>None</td>\\n</tr>\\n<tr>\\n  <td>legacyannual</td>\\n  <td>False</td>\\n</tr>\\n<tr>\\n  <td>fund</td>\\n  <td>None</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h2>DrawDown</h2>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>fund</td>\\n  <td>None</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h2>Returns</h2>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td>timeframe</td>\\n  <td>None</td>\\n</tr>\\n<tr>\\n  <td>compression</td>\\n  <td>None</td>\\n</tr>\\n<tr>\\n  <td>_doprenext</td>\\n  <td>True</td>\\n</tr>\\n<tr>\\n  <td>tann</td>\\n  <td>None</td>\\n</tr>\\n<tr>\\n  <td>fund</td>\\n  <td>None</td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h2>TradeAnalyzer</h2>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td></td>\\n  <td></td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<h2>MoneyDrawDownAnalyzer</h2>\\n\\n<table class=\\\"metaDataTable\\\">\\n<thead>\\n<tr>\\n  <th>Parameter</th>\\n  <th>Value</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n  <td></td>\\n  <td></td>\\n</tr>\\n</tbody>\\n</table>\\n\\n<hr />\\n\"},\"id\":\"13757\",\"type\":\"Div\"},{\"attributes\":{},\"id\":\"13308\",\"type\":\"AllLabels\"},{\"attributes\":{},\"id\":\"13711\",\"type\":\"StringEditor\"},{\"attributes\":{\"line_color\":{\"field\":\"6437759296colors_wicks\"},\"x0\":{\"field\":\"index\"},\"x1\":{\"field\":\"index\"},\"y0\":{\"field\":\"6437759296high\"},\"y1\":{\"field\":\"6437759296low\"}},\"id\":\"12892\",\"type\":\"Segment\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"13133\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"sma\",\"nonselection_glyph\":{\"id\":\"13134\"},\"view\":{\"id\":\"13136\"}},\"id\":\"13135\",\"type\":\"GlyphRenderer\"},{\"attributes\":{},\"id\":\"13712\",\"type\":\"UnionRenderers\"},{\"attributes\":{},\"id\":\"13713\",\"type\":\"Selection\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"12895\",\"type\":\"CDSView\"},{\"attributes\":{\"bottom\":{\"field\":\"6437759296close\"},\"fill_color\":{\"field\":\"6437759296colors_bars\"},\"line_color\":{\"field\":\"6437759296colors_outline\"},\"top\":{\"field\":\"6437759296open\"},\"width\":{\"value\":0.5},\"x\":{\"field\":\"index\"}},\"id\":\"12909\",\"type\":\"VBar\"},{\"attributes\":{\"background_fill_color\":\"#f5f5f5\",\"click_policy\":\"hide\",\"items\":[{\"id\":\"12907\"},{\"id\":\"12944\"},{\"id\":\"12965\"},{\"id\":\"13020\"},{\"id\":\"13038\"}],\"label_text_color\":\"#333333\",\"location\":\"top_left\",\"orientation\":\"horizontal\"},\"id\":\"12906\",\"type\":\"Legend\"},{\"attributes\":{\"axis_label_text_color\":\"#aaaaaa\",\"axis_line_color\":\"#aaaaaa\",\"formatter\":{\"id\":\"12923\"},\"major_label_policy\":{\"id\":\"12940\"},\"major_label_text_color\":\"#aaaaaa\",\"major_tick_line_color\":\"#aaaaaa\",\"minor_tick_line_color\":\"#aaaaaa\",\"ticker\":{\"id\":\"12939\"},\"y_range_name\":\"axvol\"},\"id\":\"12925\",\"type\":\"LinearAxis\"},{\"attributes\":{},\"id\":\"13728\",\"type\":\"UnionRenderers\"},{\"attributes\":{},\"id\":\"13729\",\"type\":\"Selection\"},{\"attributes\":{},\"id\":\"13088\",\"type\":\"AllLabels\"},{\"attributes\":{},\"id\":\"13730\",\"type\":\"StringEditor\"},{\"attributes\":{},\"id\":\"13731\",\"type\":\"StringEditor\"},{\"attributes\":{},\"id\":\"13732\",\"type\":\"StringEditor\"},{\"attributes\":{\"axis\":{\"id\":\"13327\"},\"dimension\":1,\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"13330\",\"type\":\"Grid\"},{\"attributes\":{\"use_scientific\":false},\"id\":\"13342\",\"type\":\"BasicTickFormatter\"},{\"attributes\":{},\"id\":\"13332\",\"type\":\"WheelZoomTool\"},{\"attributes\":{},\"id\":\"13331\",\"type\":\"PanTool\"},{\"attributes\":{\"overlay\":{\"id\":\"13335\"}},\"id\":\"13333\",\"type\":\"BoxZoomTool\"},{\"attributes\":{},\"id\":\"13733\",\"type\":\"UnionRenderers\"},{\"attributes\":{},\"id\":\"13334\",\"type\":\"ResetTool\"},{\"attributes\":{},\"id\":\"13734\",\"type\":\"Selection\"},{\"attributes\":{},\"id\":\"13735\",\"type\":\"StringEditor\"},{\"attributes\":{},\"id\":\"13736\",\"type\":\"StringEditor\"},{\"attributes\":{\"line_alpha\":0.1,\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438029536\"}},\"id\":\"13354\",\"type\":\"Line\"},{\"attributes\":{},\"id\":\"13737\",\"type\":\"StringEditor\"},{\"attributes\":{\"children\":[[{\"id\":\"13569\"},0,0],[{\"id\":\"13584\"},0,1],[{\"id\":\"13596\"},1,0],[{\"id\":\"13688\"},1,1],[{\"id\":\"13700\"},2,0]]},\"id\":\"13752\",\"type\":\"GridBox\"},{\"attributes\":{},\"id\":\"13738\",\"type\":\"StringEditor\"},{\"attributes\":{\"callback\":null,\"formatters\":{\"@datetime\":\"datetime\"},\"mode\":\"vline\",\"renderers\":[{\"id\":\"13355\"}],\"tooltips\":[[\"Time\",\"@datetime{%F %R}\"],[\"Broker (None) - cash\",\"@6438029536{0,0.000}\"],[\"Broker (None) - value\",\"@6438029632{0,0.000}\"]]},\"id\":\"13348\",\"type\":\"HoverTool\"},{\"attributes\":{\"line_color\":\"#000000\"},\"id\":\"13346\",\"type\":\"CrosshairTool\"},{\"attributes\":{},\"id\":\"13739\",\"type\":\"UnionRenderers\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"13353\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"cash\",\"nonselection_glyph\":{\"id\":\"13354\"},\"view\":{\"id\":\"13356\"}},\"id\":\"13355\",\"type\":\"GlyphRenderer\"},{\"attributes\":{},\"id\":\"13740\",\"type\":\"Selection\"},{\"attributes\":{\"text\":\"SMA (5)@(Volume^vol@(TSLA)@(TSLA))\",\"text_color\":\"#333333\"},\"id\":\"13121\",\"type\":\"Title\"},{\"attributes\":{\"args\":{\"axis\":{\"id\":\"13323\"},\"formatter\":{\"id\":\"13343\"},\"source\":{\"id\":\"12852\"}},\"code\":\"\\n                // We override this axis' formatter's `doFormat` method\\n                // with one that maps index ticks to dates. Some of those dates\\n                // are undefined (e.g. those whose ticks fall out of defined data\\n                // range) and we must filter out and account for those, otherwise\\n                // the formatter computes invalid visible span and returns some\\n                // labels as 'ERR'.\\n                // Note, after this assignment statement, on next plot redrawing,\\n                // our override `doFormat` will be called directly\\n                // -- FunctionTickFormatter.doFormat(), i.e. _this_ code, no longer\\n                // executes.\\n                axis.formatter.doFormat = function (ticks) {\\n                    const dates = ticks.map(i => source.data.datetime[source.data.index.indexOf(i)]),\\n                          valid = t => t !== undefined,\\n                          labels = formatter.doFormat(dates.filter(valid));\\n                    let i = 0;\\n                    return dates.map(t => valid(t) ? labels[i++] : '');\\n                };\\n\\n                // we do this manually only for the first time we are called\\n                const labels = axis.formatter.doFormat(ticks);\\n                return labels[index];\\n            \"},\"id\":\"13344\",\"type\":\"FuncTickFormatter\"},{\"attributes\":{},\"id\":\"13099\",\"type\":\"LinearScale\"},{\"attributes\":{},\"id\":\"13741\",\"type\":\"StringEditor\"},{\"attributes\":{\"aspect_ratio\":3.0,\"below\":[{\"id\":\"13103\"}],\"center\":[{\"id\":\"13106\"},{\"id\":\"13110\"},{\"id\":\"13147\"}],\"left\":[{\"id\":\"13107\"}],\"output_backend\":\"webgl\",\"renderers\":[{\"id\":\"13135\"}],\"sizing_mode\":\"scale_width\",\"title\":{\"id\":\"13121\"},\"toolbar\":{\"id\":\"13116\"},\"toolbar_location\":null,\"x_range\":{\"id\":\"12854\"},\"x_scale\":{\"id\":\"13099\"},\"y_range\":{\"id\":\"13097\"},\"y_scale\":{\"id\":\"13101\"}},\"id\":\"13094\",\"subtype\":\"Figure\",\"type\":\"Plot\"},{\"attributes\":{\"bottom_units\":\"screen\",\"fill_alpha\":0.5,\"fill_color\":\"lightgrey\",\"left_units\":\"screen\",\"level\":\"overlay\",\"line_alpha\":1.0,\"line_color\":\"black\",\"line_dash\":[4,4],\"line_width\":2,\"right_units\":\"screen\",\"syncable\":false,\"top_units\":\"screen\"},\"id\":\"13335\",\"type\":\"BoxAnnotation\"},{\"attributes\":{},\"id\":\"13742\",\"type\":\"StringEditor\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"13124\"},\"major_label_policy\":{\"id\":\"13140\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"13104\"}},\"id\":\"13103\",\"type\":\"LinearAxis\"},{\"attributes\":{\"days\":[\"%d %b %R\"],\"hourmin\":[\"%H:%M:%S\"],\"hours\":[\"%d %b %R\"],\"minsec\":[\"%H:%M:%S\"],\"minutes\":[\"%H:%M\"],\"months\":[\"%d/%m/%y\"],\"seconds\":[\"%H:%M:%S\"],\"years\":[\"%Y %b\"]},\"id\":\"13343\",\"type\":\"DatetimeTickFormatter\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"13122\"},\"major_label_policy\":{\"id\":\"13143\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"13108\"}},\"id\":\"13107\",\"type\":\"LinearAxis\"},{\"attributes\":{\"range_padding\":0.5},\"id\":\"13097\",\"type\":\"DataRange1d\"},{\"attributes\":{},\"id\":\"13743\",\"type\":\"StringEditor\"},{\"attributes\":{},\"id\":\"13101\",\"type\":\"LinearScale\"},{\"attributes\":{\"active_multi\":null,\"tools\":[{\"id\":\"13331\"},{\"id\":\"13332\"},{\"id\":\"13333\"},{\"id\":\"13334\"},{\"id\":\"13346\"},{\"id\":\"13348\"}]},\"id\":\"13336\",\"type\":\"Toolbar\"},{\"attributes\":{},\"id\":\"13104\",\"type\":\"BasicTicker\"},{\"attributes\":{\"axis\":{\"id\":\"13103\"},\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"13106\",\"type\":\"Grid\"},{\"attributes\":{},\"id\":\"13744\",\"type\":\"StringEditor\"},{\"attributes\":{\"axis\":{\"id\":\"13107\"},\"dimension\":1,\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"13110\",\"type\":\"Grid\"},{\"attributes\":{\"text\":\"Trades - Net Profit/Loss (True)@(TSLA)\",\"text_color\":\"#333333\"},\"id\":\"13411\",\"type\":\"Title\"},{\"attributes\":{},\"id\":\"13745\",\"type\":\"StringEditor\"},{\"attributes\":{},\"id\":\"13108\",\"type\":\"BasicTicker\"},{\"attributes\":{\"use_scientific\":false},\"id\":\"13122\",\"type\":\"BasicTickFormatter\"},{\"attributes\":{},\"id\":\"13112\",\"type\":\"WheelZoomTool\"},{\"attributes\":{},\"id\":\"13111\",\"type\":\"PanTool\"},{\"attributes\":{\"overlay\":{\"id\":\"13115\"}},\"id\":\"13113\",\"type\":\"BoxZoomTool\"},{\"attributes\":{},\"id\":\"13114\",\"type\":\"ResetTool\"},{\"attributes\":{\"active_multi\":null,\"tools\":[{\"id\":\"13111\"},{\"id\":\"13112\"},{\"id\":\"13113\"},{\"id\":\"13114\"},{\"id\":\"13126\"},{\"id\":\"13128\"}]},\"id\":\"13116\",\"type\":\"Toolbar\"},{\"attributes\":{\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438029536\"}},\"id\":\"13353\",\"type\":\"Line\"},{\"attributes\":{},\"id\":\"13746\",\"type\":\"UnionRenderers\"},{\"attributes\":{},\"id\":\"13747\",\"type\":\"Selection\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"13356\",\"type\":\"CDSView\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"13136\",\"type\":\"CDSView\"},{\"attributes\":{\"line_color\":\"#0000ff\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438029632\"}},\"id\":\"13370\",\"type\":\"Line\"},{\"attributes\":{\"fill_color\":{\"value\":\"#00ff00\"},\"line_color\":{\"value\":\"#00ff00\"},\"marker\":{\"value\":\"triangle\"},\"size\":{\"value\":8.0},\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438030256\"}},\"id\":\"13004\",\"type\":\"Scatter\"},{\"attributes\":{\"background_fill_color\":\"#f5f5f5\",\"click_policy\":\"hide\",\"items\":[{\"id\":\"13368\"}],\"label_text_color\":\"#333333\",\"location\":\"top_left\",\"orientation\":\"horizontal\"},\"id\":\"13367\",\"type\":\"Legend\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"13370\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"value\",\"nonselection_glyph\":{\"id\":\"13371\"},\"view\":{\"id\":\"13373\"}},\"id\":\"13372\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"data\":{\"6437757040\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6437759296close\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6437759296colors_bars\":[\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\"],\"6437759296colors_outline\":[\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\"],\"6437759296colors_volume\":[\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#cc6073\",\"#aaaaaa\",\"#cc6073\",\"#aaaaaa\",\"#aaaaaa\",\"#cc6073\"],\"6437759296colors_wicks\":[\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#fc5d45\",\"#265371\",\"#fc5d45\",\"#265371\",\"#265371\",\"#fc5d45\"],\"6437759296high\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6437759296low\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6437759296open\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6437759296volume\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6437760592\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6437861456\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6437862224\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6437875968\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6438015520\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6438015616\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6438015712\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6438029536\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6438029632\":{\"__ndarray__\":\"AAAAAABq+EAAAAAAAGr4QAAAAAAAavhAAAAAAABq+EAAAAAAAGr4QAAAAAAAavhAAAAAAABq+EAAAAAAAGr4QAAAAAAAavhAAAAAAABq+EAAAAAAAGr4QAAAAAAAavhAAAAAAABq+EAAAAAAAGr4QAAAAAAAavhAAAAAAABq+EAAAAAAAGr4QAAAAAAAavhAAAAAAABq+EAAAAAAAGr4QHO1AZ/0NfhAc7Xhe0qY+EBztcFBXx34QHO1QbYC0vZAc7VBE7M69kBztYHNOAH2QHO1IXwXQvVAc7XBQV/e9EBztcFBPzH0QHO1IXxPjPRAc7WhZITo9kBztSF8P//3QHO1wYfOvfdAc7XBQb85+0BztWHw1cT5QHO1QXDrUvlAc7UBn5xT+UBztSE2eHz5QHO1AZ8U+PlAc7Uh2W8H+UBzteEecpz4QHO14R46PfhAc7XhHgrE90BztQH8dDr4QHO1wUFv/vhAc7VBcCuD+EBzteHBmaD4QHO1gYdR+/hAc7WBzWik+EBzteF7yjf6QHO1QRMTV/lAc7VBcIN6+EBztUFw8zj5QHO1oQe84vhAc7UhNljP+EBztcGHXin5QHO1gSrJEPlAc7XBh87S+EBztQFZLZ74QHO1oQckpvhAc7UhNvAL+UBztUG2Avz4QHO1wYc+fPhAc7VhTd4P+EBztcGHbvX4QHO14R7KqPlAc7UBn1wO+UBztSE24FT6QHO1QROL+/lAc7Uhk6CJ+UBztQGffLv5QHO1gSqZrPlAc7Xhe8JR+kBztaGqE4r7QHO1weRurvtAc7WhZFy++0BztUETK139QLragE8SIQBButoAfgInAUG62oCs0n8BQbraEBtomAFButrwg/g6AkG62tBgEksCQbraMPg2ygBButogW91GAUG62gB+NpMBQbrakOwT1wFButrQA4pLAUG62iBbLVgBQbrawGaIqgBButqAT6Z/AUG62lAy1iYBQXO1QXBrMf5Ac7WhB+zE/kBztcFBV7X+QLra8D2dLwBBc7WhqvsW/0C62pBJGC8AQXO1QROLT/5Ac7XB5FYR/UAHrOCS3f/5QN6cFZ8M9PlA3pwVqTrR+EDenFVlGbn5QN6c1d0W3/lA3pyVEuOM+UDenBWahd/6QN6c1dNIDPpA3pwVnyyu+0DenNXnBO/7QN6cVVsrx/xA3pxVUR2S/EDenBWVLtn8QN6cVVY0PPtA3pzVzpF4/EDenBWQt4/7QN6cFZC3DPtA3pzV3Wab+kDenFVgwqz6QN6clRwxrftA3pxVUT06+0DenFVWxFr8QN6clSEou/1A3pyVJn8++0DenBWV7vP7QN6clSEoOP1A3pzV01h4/UDenNXiXa/9QN6clSsWv/1A3pxVYCJM/UDenNXn1G/9QN6cFaTjWf1A3pyVIXh9/0DenNXdxsP+QG/O6m5LIgBBb86KSjdoAEFvzio6P5UAQW/Oqq1lZwBBb86qt5PNAEFvzuppvOAAQW/OKjqfpQBBb86KSj8SAEHenFVWtBL/QG/OCs2aIwBB3pwVmkUY/0DenNXddpD/QN6clRd6H/9A3pwVlc7U/kDenJUXCib8QN6cVVaUUvxA3pzVzgFg/EDenBWapZn8QN6cFZ88i/xA3pwVpAOF/EBMYoRgQkH8QExihGBCQfxATGKEYEJB/EBMYoRgQkH8QGm8yPJEn/1AabwIM6jp/EBpvKinXZj8QGm8SHLOYvxAabzoknaR+0BpvDhNz3n7QGm86Ofg0vpAabzI8rC8+0BpvKhSE63/QDReFBR0SABBNF603uqSAUE0XgREJRoDQTRehG6+9QFBNF6EbogNAkE0XuSjbwoBQTRetN4ejAFBNF6EbhSBAkE0XgRExeMCQTReVKnNrQJBNF5EhFCNAkE0XqQO8jwDQTRe9HMKfwJBNF40tCV6AkE0XkSEjjECQTReFBRa2AJBNF70c4Z6A0F46RwiJy0DQXjpHCInLQNBeOkcIictA0F46RwiJy0DQXjpHCInLQNBeOkcIictA0FhMj/eBNwDQWEyXwowjANBYTIfYC5lBEFhMj/e0qUFQS7drP+vOQZBLt2s/685BkEu3az/rzkGQS7drP+vOQZBLt2s/685BkEu3az/rzkGQS7drP+vOQZBLt2s/685BkEu3az/rzkGQS7drP+vOQZBLt2s/685BkEu3az/rzkGQS7drP+vOQZBLt2s/685BkEu3az/rzkGQS7drP+vOQZBLt2s/685BkEu3az/rzkGQS7drP+vOQZBLt2s/685BkEu3az/rzkGQS7drP+vOQZBLt2s/685BkEu3az/rzkGQS7drP+vOQZBLt2s/685BkEu3az/rzkGQS7drP+vOQZBLt2s/685BkEu3az/rzkGQS7drP+vOQZBLt2s/685BkEu3az/rzkGQS7drP+vOQZBLt2s/685BkEu3az/rzkGQS7drP+vOQZBLt2s/685BkEu3az/rzkGQS7drP+vOQZBLt2s/685BkEu3az/rzkGQS7drP+vOQZBLt2s/685BkEu3az/rzkGQS7drP+vOQZBLt2s/685BkG7liLouIIFQbuWUgUEVgZBu5bynXG2BUG7lrI/4sQEQbuWEnogTAVB6lFOIxm/BEE=\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6438030256\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6438030352\":{\"__ndarray__\":\"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H9mZmbGBd5pQAAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H+PwvW0MPlrQAAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H/////XZ5B2QAAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4f4TrUVj/s35AAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/mZmZ/dVLcUA=\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6438030880\":{\"__ndarray__\":\"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H91wAou2V25QAAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8msh1tJgvCQAAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H9H4WrHFzLkQAAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4f6+df+xGZNhAAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8=\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"6438030976\":{\"__ndarray__\":\"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/QbToxW2px8A=\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"datetime\":{\"__ndarray__\":\"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\",\"dtype\":\"float64\",\"order\":\"little\",\"shape\":[247]},\"index\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246]},\"selected\":{\"id\":\"12904\"},\"selection_policy\":{\"id\":\"12903\"}},\"id\":\"12852\",\"type\":\"ColumnDataSource\"},{\"attributes\":{\"label\":{\"value\":\"SMA (10)\"},\"renderers\":[{\"id\":\"13190\"}]},\"id\":\"13203\",\"type\":\"LegendItem\"},{\"attributes\":{\"line_alpha\":0.1,\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6437875968\"}},\"id\":\"13189\",\"type\":\"Line\"},{\"attributes\":{\"line_alpha\":0.1,\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6437862224\"}},\"id\":\"13244\",\"type\":\"Line\"},{\"attributes\":{\"line_alpha\":0.1,\"line_color\":\"#0000ff\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438029632\"}},\"id\":\"13371\",\"type\":\"Line\"},{\"attributes\":{\"axis\":{\"id\":\"12862\"},\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"12865\",\"type\":\"Grid\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"13412\"},\"major_label_policy\":{\"id\":\"13433\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"13398\"}},\"id\":\"13397\",\"type\":\"LinearAxis\"},{\"attributes\":{\"range_padding\":0.5},\"id\":\"13387\",\"type\":\"DataRange1d\"},{\"attributes\":{\"callback\":null,\"formatters\":{\"@datetime\":\"datetime\"},\"mode\":\"vline\",\"renderers\":[{\"id\":\"13245\"}],\"tooltips\":[[\"Time\",\"@datetime{%F %R}\"],[\"PercentChange (5)\",\"@6437862224{0,0.000}\"]]},\"id\":\"13238\",\"type\":\"HoverTool\"},{\"attributes\":{\"text\":\"TSLA | BollingerBands (20, 2.0)@(TSLA^close) | BuySell (True, 0.015)@(TSLA)\",\"text_color\":\"#333333\"},\"id\":\"12880\",\"type\":\"Title\"},{\"attributes\":{\"callback\":null,\"formatters\":{\"@datetime\":\"datetime\"},\"mode\":\"vline\",\"renderers\":[{\"id\":\"13190\"}],\"tooltips\":[[\"Time\",\"@datetime{%F %R}\"],[\"SMA (10)\",\"@6437875968{0,0.000}\"]]},\"id\":\"13183\",\"type\":\"HoverTool\"},{\"attributes\":{\"line_color\":\"#000000\"},\"id\":\"13236\",\"type\":\"CrosshairTool\"},{\"attributes\":{\"line_color\":\"#000000\"},\"id\":\"13181\",\"type\":\"CrosshairTool\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"13373\",\"type\":\"CDSView\"},{\"attributes\":{\"use_scientific\":false},\"id\":\"13412\",\"type\":\"BasicTickFormatter\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"13414\"},\"major_label_policy\":{\"id\":\"13430\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"13394\"}},\"id\":\"13393\",\"type\":\"LinearAxis\"},{\"attributes\":{\"aspect_ratio\":3.0,\"below\":[{\"id\":\"13393\"}],\"center\":[{\"id\":\"13396\"},{\"id\":\"13400\"},{\"id\":\"13437\"}],\"left\":[{\"id\":\"13397\"}],\"output_backend\":\"webgl\",\"renderers\":[{\"id\":\"13425\"},{\"id\":\"13442\"},{\"id\":\"13454\"}],\"sizing_mode\":\"scale_width\",\"title\":{\"id\":\"13411\"},\"toolbar\":{\"id\":\"13406\"},\"toolbar_location\":null,\"x_range\":{\"id\":\"12854\"},\"x_scale\":{\"id\":\"13389\"},\"y_range\":{\"id\":\"13387\"},\"y_scale\":{\"id\":\"13391\"}},\"id\":\"13384\",\"subtype\":\"Figure\",\"type\":\"Plot\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"13188\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"sma\",\"nonselection_glyph\":{\"id\":\"13189\"},\"view\":{\"id\":\"13191\"}},\"id\":\"13190\",\"type\":\"GlyphRenderer\"},{\"attributes\":{},\"id\":\"13389\",\"type\":\"LinearScale\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"13243\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"pctchange\",\"nonselection_glyph\":{\"id\":\"13244\"},\"view\":{\"id\":\"13246\"}},\"id\":\"13245\",\"type\":\"GlyphRenderer\"},{\"attributes\":{},\"id\":\"13391\",\"type\":\"LinearScale\"},{\"attributes\":{\"args\":{\"axis\":{\"id\":\"13158\"},\"formatter\":{\"id\":\"13178\"},\"source\":{\"id\":\"12852\"}},\"code\":\"\\n                // We override this axis' formatter's `doFormat` method\\n                // with one that maps index ticks to dates. Some of those dates\\n                // are undefined (e.g. those whose ticks fall out of defined data\\n                // range) and we must filter out and account for those, otherwise\\n                // the formatter computes invalid visible span and returns some\\n                // labels as 'ERR'.\\n                // Note, after this assignment statement, on next plot redrawing,\\n                // our override `doFormat` will be called directly\\n                // -- FunctionTickFormatter.doFormat(), i.e. _this_ code, no longer\\n                // executes.\\n                axis.formatter.doFormat = function (ticks) {\\n                    const dates = ticks.map(i => source.data.datetime[source.data.index.indexOf(i)]),\\n                          valid = t => t !== undefined,\\n                          labels = formatter.doFormat(dates.filter(valid));\\n                    let i = 0;\\n                    return dates.map(t => valid(t) ? labels[i++] : '');\\n                };\\n\\n                // we do this manually only for the first time we are called\\n                const labels = axis.formatter.doFormat(ticks);\\n                return labels[index];\\n            \"},\"id\":\"13179\",\"type\":\"FuncTickFormatter\"},{\"attributes\":{},\"id\":\"13394\",\"type\":\"BasicTicker\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"12883\"},\"major_label_policy\":{\"id\":\"12899\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"12863\"}},\"id\":\"12862\",\"type\":\"LinearAxis\"},{\"attributes\":{\"axis\":{\"id\":\"13393\"},\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"13396\",\"type\":\"Grid\"},{\"attributes\":{\"args\":{\"axis\":{\"id\":\"13213\"},\"formatter\":{\"id\":\"13233\"},\"source\":{\"id\":\"12852\"}},\"code\":\"\\n                // We override this axis' formatter's `doFormat` method\\n                // with one that maps index ticks to dates. Some of those dates\\n                // are undefined (e.g. those whose ticks fall out of defined data\\n                // range) and we must filter out and account for those, otherwise\\n                // the formatter computes invalid visible span and returns some\\n                // labels as 'ERR'.\\n                // Note, after this assignment statement, on next plot redrawing,\\n                // our override `doFormat` will be called directly\\n                // -- FunctionTickFormatter.doFormat(), i.e. _this_ code, no longer\\n                // executes.\\n                axis.formatter.doFormat = function (ticks) {\\n                    const dates = ticks.map(i => source.data.datetime[source.data.index.indexOf(i)]),\\n                          valid = t => t !== undefined,\\n                          labels = formatter.doFormat(dates.filter(valid));\\n                    let i = 0;\\n                    return dates.map(t => valid(t) ? labels[i++] : '');\\n                };\\n\\n                // we do this manually only for the first time we are called\\n                const labels = axis.formatter.doFormat(ticks);\\n                return labels[index];\\n            \"},\"id\":\"13234\",\"type\":\"FuncTickFormatter\"},{\"attributes\":{\"days\":[\"%d %b %R\"],\"hourmin\":[\"%H:%M:%S\"],\"hours\":[\"%d %b %R\"],\"minsec\":[\"%H:%M:%S\"],\"minutes\":[\"%H:%M\"],\"months\":[\"%d/%m/%y\"],\"seconds\":[\"%H:%M:%S\"],\"years\":[\"%Y %b\"]},\"id\":\"13178\",\"type\":\"DatetimeTickFormatter\"},{\"attributes\":{\"axis\":{\"id\":\"13397\"},\"dimension\":1,\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"13400\",\"type\":\"Grid\"},{\"attributes\":{\"days\":[\"%d %b %R\"],\"hourmin\":[\"%H:%M:%S\"],\"hours\":[\"%d %b %R\"],\"minsec\":[\"%H:%M:%S\"],\"minutes\":[\"%H:%M\"],\"months\":[\"%d/%m/%y\"],\"seconds\":[\"%H:%M:%S\"],\"years\":[\"%Y %b\"]},\"id\":\"13233\",\"type\":\"DatetimeTickFormatter\"},{\"attributes\":{\"aspect_ratio\":3.0,\"below\":[{\"id\":\"12862\"}],\"center\":[{\"id\":\"12865\"},{\"id\":\"12869\"},{\"id\":\"12906\"}],\"extra_y_ranges\":{\"axvol\":{\"id\":\"12924\"}},\"left\":[{\"id\":\"12866\"},{\"id\":\"12925\"}],\"output_backend\":\"webgl\",\"renderers\":[{\"id\":\"12894\"},{\"id\":\"12911\"},{\"id\":\"12930\"},{\"id\":\"12951\"},{\"id\":\"12969\"},{\"id\":\"12986\"},{\"id\":\"13006\"},{\"id\":\"13024\"}],\"sizing_mode\":\"scale_width\",\"title\":{\"id\":\"12880\"},\"toolbar\":{\"id\":\"12875\"},\"toolbar_location\":null,\"x_range\":{\"id\":\"12854\"},\"x_scale\":{\"id\":\"12858\"},\"y_range\":{\"id\":\"12856\"},\"y_scale\":{\"id\":\"12860\"}},\"id\":\"12853\",\"subtype\":\"Figure\",\"type\":\"Plot\"},{\"attributes\":{},\"id\":\"13398\",\"type\":\"BasicTicker\"},{\"attributes\":{},\"id\":\"12863\",\"type\":\"BasicTicker\"},{\"attributes\":{\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6437875968\"}},\"id\":\"13188\",\"type\":\"Line\"},{\"attributes\":{\"text\":\"PercentChange (5)@(TSLA^close)\",\"text_color\":\"#333333\"},\"id\":\"13231\",\"type\":\"Title\"},{\"attributes\":{},\"id\":\"13402\",\"type\":\"WheelZoomTool\"},{\"attributes\":{},\"id\":\"13401\",\"type\":\"PanTool\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"13191\",\"type\":\"CDSView\"},{\"attributes\":{},\"id\":\"13209\",\"type\":\"LinearScale\"},{\"attributes\":{\"overlay\":{\"id\":\"13405\"}},\"id\":\"13403\",\"type\":\"BoxZoomTool\"},{\"attributes\":{},\"id\":\"13404\",\"type\":\"ResetTool\"},{\"attributes\":{\"background_fill_color\":\"#f5f5f5\",\"click_policy\":\"hide\",\"items\":[{\"id\":\"13203\"}],\"label_text_color\":\"#333333\",\"location\":\"top_left\",\"orientation\":\"horizontal\"},\"id\":\"13202\",\"type\":\"Legend\"},{\"attributes\":{\"text\":\"SMA (10)@(Volume^vol@(TSLA)@(TSLA))\",\"text_color\":\"#333333\"},\"id\":\"13176\",\"type\":\"Title\"},{\"attributes\":{},\"id\":\"12858\",\"type\":\"LinearScale\"},{\"attributes\":{},\"id\":\"13154\",\"type\":\"LinearScale\"},{\"attributes\":{\"aspect_ratio\":3.0,\"below\":[{\"id\":\"13158\"}],\"center\":[{\"id\":\"13161\"},{\"id\":\"13165\"},{\"id\":\"13202\"}],\"left\":[{\"id\":\"13162\"}],\"output_backend\":\"webgl\",\"renderers\":[{\"id\":\"13190\"}],\"sizing_mode\":\"scale_width\",\"title\":{\"id\":\"13176\"},\"toolbar\":{\"id\":\"13171\"},\"toolbar_location\":null,\"x_range\":{\"id\":\"12854\"},\"x_scale\":{\"id\":\"13154\"},\"y_range\":{\"id\":\"13152\"},\"y_scale\":{\"id\":\"13156\"}},\"id\":\"13149\",\"subtype\":\"Figure\",\"type\":\"Plot\"},{\"attributes\":{\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6437862224\"}},\"id\":\"13243\",\"type\":\"Line\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"13246\",\"type\":\"CDSView\"},{\"attributes\":{\"label\":{\"value\":\"PercentChange (5)\"},\"renderers\":[{\"id\":\"13245\"}]},\"id\":\"13258\",\"type\":\"LegendItem\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"13179\"},\"major_label_policy\":{\"id\":\"13195\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"13159\"}},\"id\":\"13158\",\"type\":\"LinearAxis\"},{\"attributes\":{\"background_fill_color\":\"#f5f5f5\",\"click_policy\":\"hide\",\"items\":[{\"id\":\"13258\"}],\"label_text_color\":\"#333333\",\"location\":\"top_left\",\"orientation\":\"horizontal\"},\"id\":\"13257\",\"type\":\"Legend\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"13177\"},\"major_label_policy\":{\"id\":\"13198\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"13163\"}},\"id\":\"13162\",\"type\":\"LinearAxis\"},{\"attributes\":{\"range_padding\":0.5},\"id\":\"13152\",\"type\":\"DataRange1d\"},{\"attributes\":{\"fill_alpha\":{\"value\":0.1},\"fill_color\":{\"value\":\"#0000ff\"},\"line_alpha\":{\"value\":0.1},\"line_color\":{\"value\":\"#0000ff\"},\"size\":{\"value\":8.0},\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438030880\"}},\"id\":\"13424\",\"type\":\"Circle\"},{\"attributes\":{},\"id\":\"13156\",\"type\":\"LinearScale\"},{\"attributes\":{\"aspect_ratio\":3.0,\"below\":[{\"id\":\"13213\"}],\"center\":[{\"id\":\"13216\"},{\"id\":\"13220\"},{\"id\":\"13257\"}],\"left\":[{\"id\":\"13217\"}],\"output_backend\":\"webgl\",\"renderers\":[{\"id\":\"13245\"}],\"sizing_mode\":\"scale_width\",\"title\":{\"id\":\"13231\"},\"toolbar\":{\"id\":\"13226\"},\"toolbar_location\":null,\"x_range\":{\"id\":\"12854\"},\"x_scale\":{\"id\":\"13209\"},\"y_range\":{\"id\":\"13207\"},\"y_scale\":{\"id\":\"13211\"}},\"id\":\"13204\",\"subtype\":\"Figure\",\"type\":\"Plot\"},{\"attributes\":{},\"id\":\"13159\",\"type\":\"BasicTicker\"},{\"attributes\":{\"axis\":{\"id\":\"13158\"},\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"13161\",\"type\":\"Grid\"},{\"attributes\":{\"callback\":null,\"formatters\":{\"@datetime\":\"datetime\"},\"mode\":\"vline\",\"renderers\":[{\"id\":\"13425\"},{\"id\":\"13442\"}],\"tooltips\":[[\"Time\",\"@datetime{%F %R}\"],[\"Trades - Net Profit/Loss (True) - pnlplus\",\"@6438030880{0,0.000}\"],[\"Trades - Net Profit/Loss (True) - pnlminus\",\"@6438030976{0,0.000}\"]]},\"id\":\"13418\",\"type\":\"HoverTool\"},{\"attributes\":{\"line_color\":\"#000000\"},\"id\":\"13416\",\"type\":\"CrosshairTool\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"13234\"},\"major_label_policy\":{\"id\":\"13250\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"13214\"}},\"id\":\"13213\",\"type\":\"LinearAxis\"},{\"attributes\":{\"axis\":{\"id\":\"13162\"},\"dimension\":1,\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"13165\",\"type\":\"Grid\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"13232\"},\"major_label_policy\":{\"id\":\"13253\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"13218\"}},\"id\":\"13217\",\"type\":\"LinearAxis\"},{\"attributes\":{},\"id\":\"12854\",\"type\":\"DataRange1d\"},{\"attributes\":{\"range_padding\":0.5},\"id\":\"13207\",\"type\":\"DataRange1d\"},{\"attributes\":{},\"id\":\"13163\",\"type\":\"BasicTicker\"},{\"attributes\":{},\"id\":\"13211\",\"type\":\"LinearScale\"},{\"attributes\":{\"use_scientific\":false},\"id\":\"13177\",\"type\":\"BasicTickFormatter\"},{\"attributes\":{},\"id\":\"13167\",\"type\":\"WheelZoomTool\"},{\"attributes\":{},\"id\":\"13214\",\"type\":\"BasicTicker\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"13426\",\"type\":\"CDSView\"},{\"attributes\":{\"axis\":{\"id\":\"13213\"},\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"13216\",\"type\":\"Grid\"},{\"attributes\":{},\"id\":\"13166\",\"type\":\"PanTool\"},{\"attributes\":{\"overlay\":{\"id\":\"13170\"}},\"id\":\"13168\",\"type\":\"BoxZoomTool\"},{\"attributes\":{\"range_padding\":0.5,\"renderers\":[{\"id\":\"12911\"},{\"id\":\"12951\"},{\"id\":\"12969\"},{\"id\":\"12986\"},{\"id\":\"13006\"},{\"id\":\"13024\"}]},\"id\":\"12856\",\"type\":\"DataRange1d\"},{\"attributes\":{},\"id\":\"13169\",\"type\":\"ResetTool\"},{\"attributes\":{\"active_multi\":null,\"tools\":[{\"id\":\"13166\"},{\"id\":\"13167\"},{\"id\":\"13168\"},{\"id\":\"13169\"},{\"id\":\"13181\"},{\"id\":\"13183\"}]},\"id\":\"13171\",\"type\":\"Toolbar\"},{\"attributes\":{\"args\":{\"axis\":{\"id\":\"13393\"},\"formatter\":{\"id\":\"13413\"},\"source\":{\"id\":\"12852\"}},\"code\":\"\\n                // We override this axis' formatter's `doFormat` method\\n                // with one that maps index ticks to dates. Some of those dates\\n                // are undefined (e.g. those whose ticks fall out of defined data\\n                // range) and we must filter out and account for those, otherwise\\n                // the formatter computes invalid visible span and returns some\\n                // labels as 'ERR'.\\n                // Note, after this assignment statement, on next plot redrawing,\\n                // our override `doFormat` will be called directly\\n                // -- FunctionTickFormatter.doFormat(), i.e. _this_ code, no longer\\n                // executes.\\n                axis.formatter.doFormat = function (ticks) {\\n                    const dates = ticks.map(i => source.data.datetime[source.data.index.indexOf(i)]),\\n                          valid = t => t !== undefined,\\n                          labels = formatter.doFormat(dates.filter(valid));\\n                    let i = 0;\\n                    return dates.map(t => valid(t) ? labels[i++] : '');\\n                };\\n\\n                // we do this manually only for the first time we are called\\n                const labels = axis.formatter.doFormat(ticks);\\n                return labels[index];\\n            \"},\"id\":\"13414\",\"type\":\"FuncTickFormatter\"},{\"attributes\":{\"axis\":{\"id\":\"13217\"},\"dimension\":1,\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"13220\",\"type\":\"Grid\"},{\"attributes\":{},\"id\":\"13195\",\"type\":\"AllLabels\"},{\"attributes\":{},\"id\":\"13218\",\"type\":\"BasicTicker\"},{\"attributes\":{},\"id\":\"13250\",\"type\":\"AllLabels\"},{\"attributes\":{\"use_scientific\":false},\"id\":\"13232\",\"type\":\"BasicTickFormatter\"},{\"attributes\":{},\"id\":\"13222\",\"type\":\"WheelZoomTool\"},{\"attributes\":{\"bottom_units\":\"screen\",\"fill_alpha\":0.5,\"fill_color\":\"lightgrey\",\"left_units\":\"screen\",\"level\":\"overlay\",\"line_alpha\":1.0,\"line_color\":\"black\",\"line_dash\":[4,4],\"line_width\":2,\"right_units\":\"screen\",\"syncable\":false,\"top_units\":\"screen\"},\"id\":\"13405\",\"type\":\"BoxAnnotation\"},{\"attributes\":{},\"id\":\"13221\",\"type\":\"PanTool\"},{\"attributes\":{\"overlay\":{\"id\":\"13225\"}},\"id\":\"13223\",\"type\":\"BoxZoomTool\"},{\"attributes\":{\"days\":[\"%d %b %R\"],\"hourmin\":[\"%H:%M:%S\"],\"hours\":[\"%d %b %R\"],\"minsec\":[\"%H:%M:%S\"],\"minutes\":[\"%H:%M\"],\"months\":[\"%d/%m/%y\"],\"seconds\":[\"%H:%M:%S\"],\"years\":[\"%Y %b\"]},\"id\":\"13413\",\"type\":\"DatetimeTickFormatter\"},{\"attributes\":{},\"id\":\"13224\",\"type\":\"ResetTool\"},{\"attributes\":{\"active_multi\":null,\"tools\":[{\"id\":\"13221\"},{\"id\":\"13222\"},{\"id\":\"13223\"},{\"id\":\"13224\"},{\"id\":\"13236\"},{\"id\":\"13238\"}]},\"id\":\"13226\",\"type\":\"Toolbar\"},{\"attributes\":{\"active_multi\":null,\"tools\":[{\"id\":\"13401\"},{\"id\":\"13402\"},{\"id\":\"13403\"},{\"id\":\"13404\"},{\"id\":\"13416\"},{\"id\":\"13418\"}]},\"id\":\"13406\",\"type\":\"Toolbar\"},{\"attributes\":{\"fill_alpha\":{\"value\":0.1},\"fill_color\":{\"value\":\"#00ff00\"},\"line_alpha\":{\"value\":0.1},\"line_color\":{\"value\":\"#00ff00\"},\"marker\":{\"value\":\"triangle\"},\"size\":{\"value\":8.0},\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438030256\"}},\"id\":\"13005\",\"type\":\"Scatter\"},{\"attributes\":{},\"id\":\"13253\",\"type\":\"AllLabels\"},{\"attributes\":{\"bottom_units\":\"screen\",\"fill_alpha\":0.5,\"fill_color\":\"lightgrey\",\"left_units\":\"screen\",\"level\":\"overlay\",\"line_alpha\":1.0,\"line_color\":\"black\",\"line_dash\":[4,4],\"line_width\":2,\"right_units\":\"screen\",\"syncable\":false,\"top_units\":\"screen\"},\"id\":\"13225\",\"type\":\"BoxAnnotation\"},{\"attributes\":{\"line_alpha\":0.1,\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438015712\"}},\"id\":\"12985\",\"type\":\"Line\"},{\"attributes\":{},\"id\":\"12860\",\"type\":\"LinearScale\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"13423\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"pnlplus\",\"nonselection_glyph\":{\"id\":\"13424\"},\"view\":{\"id\":\"13426\"}},\"id\":\"13425\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"fill_color\":{\"value\":\"#0000ff\"},\"line_color\":{\"value\":\"#0000ff\"},\"size\":{\"value\":8.0},\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438030880\"}},\"id\":\"13423\",\"type\":\"Circle\"},{\"attributes\":{\"bottom_units\":\"screen\",\"fill_alpha\":0.5,\"fill_color\":\"lightgrey\",\"left_units\":\"screen\",\"level\":\"overlay\",\"line_alpha\":1.0,\"line_color\":\"black\",\"line_dash\":[4,4],\"line_width\":2,\"right_units\":\"screen\",\"syncable\":false,\"top_units\":\"screen\"},\"id\":\"13170\",\"type\":\"BoxAnnotation\"},{\"attributes\":{\"editor\":{\"id\":\"13707\"},\"field\":\"col2\",\"formatter\":{\"id\":\"13577\"},\"title\":\"Maximum\"},\"id\":\"13578\",\"type\":\"TableColumn\"},{\"attributes\":{\"source\":{\"id\":\"13570\"}},\"id\":\"13582\",\"type\":\"CDSView\"},{\"attributes\":{\"background_fill_color\":\"#f5f5f5\",\"click_policy\":\"hide\",\"items\":[{\"id\":\"13438\"}],\"label_text_color\":\"#333333\",\"location\":\"top_left\",\"orientation\":\"horizontal\"},\"id\":\"13437\",\"type\":\"Legend\"},{\"attributes\":{\"label\":{\"value\":\"Trades - Net Profit/Loss (True)\"},\"renderers\":[{\"id\":\"13425\"},{\"id\":\"13442\"}]},\"id\":\"13438\",\"type\":\"LegendItem\"},{\"attributes\":{\"data\":{\"col0\":[\"Won\",\"Lost\",\"Long\",\"Short\",\"Won / Long\",\"Won / Short\",\"Lost / Long\",\"Lost / Short\"],\"col1\":[163,5,168,0,163,0,5,0],\"col2\":[4,5,4,9223372036854775807,4,9223372036854775807,5,9223372036854775807],\"col3\":[80,5,80,0,80,0,5,0],\"col4\":[40.75,5.0,33.6,0.0,40.75,0.0,5.0,0.0]},\"selected\":{\"id\":\"13747\"},\"selection_policy\":{\"id\":\"13746\"}},\"id\":\"13668\",\"type\":\"ColumnDataSource\"},{\"attributes\":{\"format\":\"0,0.000\"},\"id\":\"13643\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"data\":{\"col0\":[\"rtot\",\"ravg\",\"rnorm\",\"rnorm100\"],\"col1\":[0.5303643470615566,0.002147224077172294,0.002149531013665454,0.21495310136654538]},\"selected\":{\"id\":\"13713\"},\"selection_policy\":{\"id\":\"13712\"}},\"id\":\"13585\",\"type\":\"ColumnDataSource\"},{\"attributes\":{\"columns\":[{\"id\":\"13641\"},{\"id\":\"13644\"},{\"id\":\"13647\"}],\"height\":75,\"index_position\":null,\"sizing_mode\":\"stretch_width\",\"source\":{\"id\":\"13639\"},\"view\":{\"id\":\"13651\"}},\"id\":\"13649\",\"type\":\"DataTable\"},{\"attributes\":{\"editor\":{\"id\":\"13710\"},\"field\":\"col0\",\"formatter\":{\"id\":\"13586\"},\"title\":\"Performance\"},\"id\":\"13587\",\"type\":\"TableColumn\"},{\"attributes\":{\"sizing_mode\":\"stretch_width\",\"style\":{\"font-size\":\"large\"},\"text\":\"Returns\"},\"id\":\"13595\",\"type\":\"Paragraph\"},{\"attributes\":{\"children\":[{\"id\":\"13595\"},{\"id\":\"13592\"}],\"sizing_mode\":\"stretch_width\"},\"id\":\"13596\",\"type\":\"Column\"},{\"attributes\":{},\"id\":\"13586\",\"type\":\"StringFormatter\"},{\"attributes\":{\"editor\":{\"id\":\"13714\"},\"field\":\"col0\",\"formatter\":{\"id\":\"13598\"},\"title\":\"\"},\"id\":\"13599\",\"type\":\"TableColumn\"},{\"attributes\":{\"source\":{\"id\":\"13585\"}},\"id\":\"13594\",\"type\":\"CDSView\"},{\"attributes\":{},\"id\":\"13589\",\"type\":\"StringFormatter\"},{\"attributes\":{\"children\":[{\"id\":\"13687\"},{\"id\":\"13610\"},{\"id\":\"13623\"},{\"id\":\"13636\"},{\"id\":\"13649\"},{\"id\":\"13665\"},{\"id\":\"13684\"}],\"sizing_mode\":\"stretch_width\"},\"id\":\"13688\",\"type\":\"Column\"},{\"attributes\":{\"columns\":[{\"id\":\"13587\"},{\"id\":\"13590\"}],\"height\":150,\"index_position\":null,\"sizing_mode\":\"stretch_width\",\"source\":{\"id\":\"13585\"},\"view\":{\"id\":\"13594\"}},\"id\":\"13592\",\"type\":\"DataTable\"},{\"attributes\":{\"editor\":{\"id\":\"13711\"},\"field\":\"col1\",\"formatter\":{\"id\":\"13589\"},\"title\":\"Value\"},\"id\":\"13590\",\"type\":\"TableColumn\"},{\"attributes\":{\"data\":{\"col0\":[\"Won\",\"Lost\"],\"col1\":[0,1],\"col2\":[4,1]},\"selected\":{\"id\":\"13724\"},\"selection_policy\":{\"id\":\"13723\"}},\"id\":\"13613\",\"type\":\"ColumnDataSource\"},{\"attributes\":{},\"id\":\"13430\",\"type\":\"AllLabels\"},{\"attributes\":{\"editor\":{\"id\":\"13727\"},\"field\":\"col2\",\"formatter\":{\"id\":\"13633\"},\"title\":\"Average\"},\"id\":\"13634\",\"type\":\"TableColumn\"},{\"attributes\":{},\"id\":\"13598\",\"type\":\"StringFormatter\"},{\"attributes\":{},\"id\":\"13714\",\"type\":\"StringEditor\"},{\"attributes\":{\"data\":{\"col0\":[\"Number of Trades\"],\"col1\":[5],\"col2\":[0],\"col3\":[5]},\"selected\":{\"id\":\"13719\"},\"selection_policy\":{\"id\":\"13718\"}},\"id\":\"13597\",\"type\":\"ColumnDataSource\"},{\"attributes\":{\"sizing_mode\":\"stretch_width\",\"style\":{\"font-size\":\"large\"},\"text\":\"Transaction Analyzer\"},\"id\":\"13687\",\"type\":\"Paragraph\"},{\"attributes\":{},\"id\":\"13715\",\"type\":\"StringEditor\"},{\"attributes\":{},\"id\":\"13607\",\"type\":\"NumberFormatter\"},{\"attributes\":{},\"id\":\"13614\",\"type\":\"StringFormatter\"},{\"attributes\":{\"source\":{\"id\":\"13626\"}},\"id\":\"13638\",\"type\":\"CDSView\"},{\"attributes\":{},\"id\":\"13601\",\"type\":\"NumberFormatter\"},{\"attributes\":{},\"id\":\"13716\",\"type\":\"StringEditor\"},{\"attributes\":{\"source\":{\"id\":\"13597\"}},\"id\":\"13612\",\"type\":\"CDSView\"},{\"attributes\":{},\"id\":\"13604\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"columns\":[{\"id\":\"13599\"},{\"id\":\"13602\"},{\"id\":\"13605\"},{\"id\":\"13608\"}],\"height\":75,\"index_position\":null,\"sizing_mode\":\"stretch_width\",\"source\":{\"id\":\"13597\"},\"view\":{\"id\":\"13612\"}},\"id\":\"13610\",\"type\":\"DataTable\"},{\"attributes\":{},\"id\":\"13433\",\"type\":\"AllLabels\"},{\"attributes\":{\"editor\":{\"id\":\"13715\"},\"field\":\"col1\",\"formatter\":{\"id\":\"13601\"},\"title\":\"Total\"},\"id\":\"13602\",\"type\":\"TableColumn\"},{\"attributes\":{},\"id\":\"13717\",\"type\":\"StringEditor\"},{\"attributes\":{\"data\":{\"col0\":[\"Gross Profit\",\"Net Profit (w/ Commissions)\",\"Short\",\"Long\",\"Won / Short\",\"Lost / Short\",\"Won / Long\",\"Lost / Long\"],\"col1\":[71322.35088500971,69955.14223922724,0.0,69955.14223922724,0.0,0.0,82069.99984143062,-12114.857602203383],\"col2\":[14264.470177001942,13991.028447845447,0.0,13991.028447845447,0.0,0.0,20517.499960357654,-12114.857602203383]},\"selected\":{\"id\":\"13729\"},\"selection_policy\":{\"id\":\"13728\"}},\"id\":\"13626\",\"type\":\"ColumnDataSource\"},{\"attributes\":{\"editor\":{\"id\":\"13716\"},\"field\":\"col2\",\"formatter\":{\"id\":\"13604\"},\"title\":\"Open\"},\"id\":\"13605\",\"type\":\"TableColumn\"},{\"attributes\":{\"editor\":{\"id\":\"13720\"},\"field\":\"col0\",\"formatter\":{\"id\":\"13614\"},\"title\":\"Streak\"},\"id\":\"13615\",\"type\":\"TableColumn\"},{\"attributes\":{\"editor\":{\"id\":\"13717\"},\"field\":\"col3\",\"formatter\":{\"id\":\"13607\"},\"title\":\"Closed\"},\"id\":\"13608\",\"type\":\"TableColumn\"},{\"attributes\":{},\"id\":\"13617\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"data\":{\"col0\":[\"Long\",\"Short\",\"All\"],\"col1\":[5,0,5],\"col2\":[4,0,4],\"col3\":[1,0,1]},\"selected\":{\"id\":\"13740\"},\"selection_policy\":{\"id\":\"13739\"}},\"id\":\"13652\",\"type\":\"ColumnDataSource\"},{\"attributes\":{\"columns\":[{\"id\":\"13615\"},{\"id\":\"13618\"},{\"id\":\"13621\"}],\"height\":100,\"index_position\":null,\"sizing_mode\":\"stretch_width\",\"source\":{\"id\":\"13613\"},\"view\":{\"id\":\"13625\"}},\"id\":\"13623\",\"type\":\"DataTable\"},{\"attributes\":{\"source\":{\"id\":\"13613\"}},\"id\":\"13625\",\"type\":\"CDSView\"},{\"attributes\":{},\"id\":\"13718\",\"type\":\"UnionRenderers\"},{\"attributes\":{},\"id\":\"13719\",\"type\":\"Selection\"},{\"attributes\":{},\"id\":\"13627\",\"type\":\"StringFormatter\"},{\"attributes\":{\"editor\":{\"id\":\"13726\"},\"field\":\"col1\",\"formatter\":{\"id\":\"13630\"},\"title\":\"Total\"},\"id\":\"13631\",\"type\":\"TableColumn\"},{\"attributes\":{},\"id\":\"13720\",\"type\":\"StringEditor\"},{\"attributes\":{},\"id\":\"13620\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"data\":{\"col0\":[\"Longest\"],\"col1\":[4],\"col2\":[1]},\"selected\":{\"id\":\"13734\"},\"selection_policy\":{\"id\":\"13733\"}},\"id\":\"13639\",\"type\":\"ColumnDataSource\"},{\"attributes\":{\"editor\":{\"id\":\"13721\"},\"field\":\"col1\",\"formatter\":{\"id\":\"13617\"},\"title\":\"Current\"},\"id\":\"13618\",\"type\":\"TableColumn\"},{\"attributes\":{\"editor\":{\"id\":\"13725\"},\"field\":\"col0\",\"formatter\":{\"id\":\"13627\"},\"title\":\"Profit & Loss\"},\"id\":\"13628\",\"type\":\"TableColumn\"},{\"attributes\":{\"editor\":{\"id\":\"13722\"},\"field\":\"col2\",\"formatter\":{\"id\":\"13620\"},\"title\":\"Longest\"},\"id\":\"13621\",\"type\":\"TableColumn\"},{\"attributes\":{},\"id\":\"13721\",\"type\":\"StringEditor\"},{\"attributes\":{},\"id\":\"13640\",\"type\":\"StringFormatter\"},{\"attributes\":{\"editor\":{\"id\":\"13730\"},\"field\":\"col0\",\"formatter\":{\"id\":\"13640\"},\"title\":\"Long\"},\"id\":\"13641\",\"type\":\"TableColumn\"},{\"attributes\":{\"editor\":{\"id\":\"13735\"},\"field\":\"col0\",\"formatter\":{\"id\":\"13653\"},\"title\":\"Trades\"},\"id\":\"13654\",\"type\":\"TableColumn\"},{\"attributes\":{},\"id\":\"13722\",\"type\":\"StringEditor\"},{\"attributes\":{\"columns\":[{\"id\":\"13628\"},{\"id\":\"13631\"},{\"id\":\"13634\"}],\"height\":250,\"index_position\":null,\"sizing_mode\":\"stretch_width\",\"source\":{\"id\":\"13626\"},\"view\":{\"id\":\"13638\"}},\"id\":\"13636\",\"type\":\"DataTable\"},{\"attributes\":{\"source\":{\"id\":\"13639\"}},\"id\":\"13651\",\"type\":\"CDSView\"},{\"attributes\":{\"format\":\"0,0.000\"},\"id\":\"13646\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"editor\":{\"id\":\"13731\"},\"field\":\"col1\",\"formatter\":{\"id\":\"13643\"},\"title\":\"Gross\"},\"id\":\"13644\",\"type\":\"TableColumn\"},{\"attributes\":{},\"id\":\"13723\",\"type\":\"UnionRenderers\"},{\"attributes\":{},\"id\":\"13653\",\"type\":\"StringFormatter\"},{\"attributes\":{\"editor\":{\"id\":\"13732\"},\"field\":\"col2\",\"formatter\":{\"id\":\"13646\"},\"title\":\"Net\"},\"id\":\"13647\",\"type\":\"TableColumn\"},{\"attributes\":{},\"id\":\"13724\",\"type\":\"Selection\"},{\"attributes\":{},\"id\":\"13656\",\"type\":\"NumberFormatter\"},{\"attributes\":{},\"id\":\"13725\",\"type\":\"StringEditor\"},{\"attributes\":{},\"id\":\"13662\",\"type\":\"NumberFormatter\"},{\"attributes\":{},\"id\":\"13726\",\"type\":\"StringEditor\"},{\"attributes\":{},\"id\":\"13659\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"columns\":[{\"id\":\"13654\"},{\"id\":\"13657\"},{\"id\":\"13660\"},{\"id\":\"13663\"}],\"height\":125,\"index_position\":null,\"sizing_mode\":\"stretch_width\",\"source\":{\"id\":\"13652\"},\"view\":{\"id\":\"13667\"}},\"id\":\"13665\",\"type\":\"DataTable\"},{\"attributes\":{\"editor\":{\"id\":\"13736\"},\"field\":\"col1\",\"formatter\":{\"id\":\"13656\"},\"title\":\"Total\"},\"id\":\"13657\",\"type\":\"TableColumn\"},{\"attributes\":{},\"id\":\"13727\",\"type\":\"StringEditor\"},{\"attributes\":{\"source\":{\"id\":\"13652\"}},\"id\":\"13667\",\"type\":\"CDSView\"},{\"attributes\":{\"editor\":{\"id\":\"13737\"},\"field\":\"col2\",\"formatter\":{\"id\":\"13659\"},\"title\":\"Won\"},\"id\":\"13660\",\"type\":\"TableColumn\"},{\"attributes\":{\"editor\":{\"id\":\"13738\"},\"field\":\"col3\",\"formatter\":{\"id\":\"13662\"},\"title\":\"Lost\"},\"id\":\"13663\",\"type\":\"TableColumn\"},{\"attributes\":{\"axis\":{\"id\":\"13052\"},\"dimension\":1,\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"13055\",\"type\":\"Grid\"},{\"attributes\":{\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6437760592\"}},\"id\":\"13133\",\"type\":\"Line\"},{\"attributes\":{},\"id\":\"13057\",\"type\":\"WheelZoomTool\"},{\"attributes\":{},\"id\":\"13056\",\"type\":\"PanTool\"},{\"attributes\":{\"overlay\":{\"id\":\"13060\"}},\"id\":\"13058\",\"type\":\"BoxZoomTool\"},{\"attributes\":{},\"id\":\"13059\",\"type\":\"ResetTool\"},{\"attributes\":{},\"id\":\"12899\",\"type\":\"AllLabels\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"12987\",\"type\":\"CDSView\"},{\"attributes\":{},\"id\":\"13198\",\"type\":\"AllLabels\"},{\"attributes\":{\"background_fill_color\":\"#f5f5f5\",\"click_policy\":\"hide\",\"items\":[{\"id\":\"13093\"}],\"label_text_color\":\"#333333\",\"location\":\"top_left\",\"orientation\":\"horizontal\"},\"id\":\"13092\",\"type\":\"Legend\"},{\"attributes\":{},\"id\":\"12902\",\"type\":\"AllLabels\"},{\"attributes\":{\"label\":{\"value\":\"Volume\"},\"renderers\":[{\"id\":\"13080\"}]},\"id\":\"13093\",\"type\":\"LegendItem\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"13081\",\"type\":\"CDSView\"},{\"attributes\":{},\"id\":\"12903\",\"type\":\"UnionRenderers\"},{\"attributes\":{\"line_alpha\":0.1,\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6437757040\"}},\"id\":\"13079\",\"type\":\"Line\"},{\"attributes\":{},\"id\":\"12904\",\"type\":\"Selection\"},{\"attributes\":{\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6437757040\"}},\"id\":\"13078\",\"type\":\"Line\"},{\"attributes\":{\"text\":\"Volume@(TSLA)\",\"text_color\":\"#333333\"},\"id\":\"13066\",\"type\":\"Title\"},{\"attributes\":{\"callback\":null,\"formatters\":{\"@datetime\":\"datetime\"},\"mode\":\"vline\",\"renderers\":[{\"id\":\"13080\"}],\"tooltips\":[[\"Time\",\"@datetime{%F %R}\"],[\"Volume\",\"@6437757040{0,0.000}\"]]},\"id\":\"13073\",\"type\":\"HoverTool\"},{\"attributes\":{\"line_color\":\"#000000\"},\"id\":\"13071\",\"type\":\"CrosshairTool\"},{\"attributes\":{\"bottom_units\":\"screen\",\"fill_alpha\":0.5,\"fill_color\":\"lightgrey\",\"left_units\":\"screen\",\"level\":\"overlay\",\"line_alpha\":1.0,\"line_color\":\"black\",\"line_dash\":[4,4],\"line_width\":2,\"right_units\":\"screen\",\"syncable\":false,\"top_units\":\"screen\"},\"id\":\"13060\",\"type\":\"BoxAnnotation\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"12984\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"bot\",\"nonselection_glyph\":{\"id\":\"12985\"},\"view\":{\"id\":\"12987\"}},\"id\":\"12986\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"13078\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"vol\",\"nonselection_glyph\":{\"id\":\"13079\"},\"view\":{\"id\":\"13081\"}},\"id\":\"13080\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438015712\"}},\"id\":\"12984\",\"type\":\"Line\"},{\"attributes\":{},\"id\":\"13085\",\"type\":\"AllLabels\"},{\"attributes\":{\"label\":{\"value\":\"TSLA\"},\"renderers\":[{\"id\":\"12894\"},{\"id\":\"12911\"}]},\"id\":\"12907\",\"type\":\"LegendItem\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"12967\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"top\",\"nonselection_glyph\":{\"id\":\"12968\"},\"view\":{\"id\":\"12970\"}},\"id\":\"12969\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"days\":[\"%d %b %R\"],\"hourmin\":[\"%H:%M:%S\"],\"hours\":[\"%d %b %R\"],\"minsec\":[\"%H:%M:%S\"],\"minutes\":[\"%H:%M\"],\"months\":[\"%d/%m/%y\"],\"seconds\":[\"%H:%M:%S\"],\"years\":[\"%Y %b\"]},\"id\":\"13068\",\"type\":\"DatetimeTickFormatter\"},{\"attributes\":{\"line_alpha\":0.1,\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438015616\"}},\"id\":\"12968\",\"type\":\"Line\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"12970\",\"type\":\"CDSView\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"13007\",\"type\":\"CDSView\"},{\"attributes\":{\"text\":\"RSI (14)@(TSLA^close)\",\"text_color\":\"#333333\"},\"id\":\"13286\",\"type\":\"Title\"},{\"attributes\":{},\"id\":\"13264\",\"type\":\"LinearScale\"},{\"attributes\":{\"aspect_ratio\":3.0,\"below\":[{\"id\":\"13268\"}],\"center\":[{\"id\":\"13271\"},{\"id\":\"13275\"},{\"id\":\"13312\"}],\"left\":[{\"id\":\"13272\"}],\"output_backend\":\"webgl\",\"renderers\":[{\"id\":\"13300\"}],\"sizing_mode\":\"scale_width\",\"title\":{\"id\":\"13286\"},\"toolbar\":{\"id\":\"13281\"},\"toolbar_location\":null,\"x_range\":{\"id\":\"12854\"},\"x_scale\":{\"id\":\"13264\"},\"y_range\":{\"id\":\"13262\"},\"y_scale\":{\"id\":\"13266\"}},\"id\":\"13259\",\"subtype\":\"Figure\",\"type\":\"Plot\"},{\"attributes\":{\"use_scientific\":false},\"id\":\"13287\",\"type\":\"BasicTickFormatter\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"13289\"},\"major_label_policy\":{\"id\":\"13305\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"13269\"}},\"id\":\"13268\",\"type\":\"LinearAxis\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"13287\"},\"major_label_policy\":{\"id\":\"13308\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"13273\"}},\"id\":\"13272\",\"type\":\"LinearAxis\"},{\"attributes\":{\"range_padding\":0.5},\"id\":\"13262\",\"type\":\"DataRange1d\"},{\"attributes\":{\"label\":{\"value\":\"BuySell (True, 0.015) buy\"},\"renderers\":[{\"id\":\"13006\"}]},\"id\":\"13020\",\"type\":\"LegendItem\"},{\"attributes\":{},\"id\":\"13266\",\"type\":\"LinearScale\"},{\"attributes\":{\"angle\":{\"units\":\"deg\",\"value\":180},\"fill_alpha\":{\"value\":0.1},\"fill_color\":{\"value\":\"#ff0000\"},\"line_alpha\":{\"value\":0.1},\"line_color\":{\"value\":\"#ff0000\"},\"marker\":{\"value\":\"triangle\"},\"size\":{\"value\":8.0},\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438030352\"}},\"id\":\"13023\",\"type\":\"Scatter\"},{\"attributes\":{},\"id\":\"13269\",\"type\":\"BasicTicker\"},{\"attributes\":{\"axis\":{\"id\":\"13268\"},\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"13271\",\"type\":\"Grid\"},{\"attributes\":{\"axis\":{\"id\":\"13272\"},\"dimension\":1,\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"13275\",\"type\":\"Grid\"},{\"attributes\":{},\"id\":\"13273\",\"type\":\"BasicTicker\"},{\"attributes\":{},\"id\":\"13277\",\"type\":\"WheelZoomTool\"},{\"attributes\":{},\"id\":\"13276\",\"type\":\"PanTool\"},{\"attributes\":{\"overlay\":{\"id\":\"13280\"}},\"id\":\"13278\",\"type\":\"BoxZoomTool\"},{\"attributes\":{},\"id\":\"13279\",\"type\":\"ResetTool\"},{\"attributes\":{\"angle\":{\"units\":\"deg\",\"value\":180},\"fill_color\":{\"value\":\"#ff0000\"},\"line_color\":{\"value\":\"#ff0000\"},\"marker\":{\"value\":\"triangle\"},\"size\":{\"value\":8.0},\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438030352\"}},\"id\":\"13022\",\"type\":\"Scatter\"},{\"attributes\":{\"label\":{\"value\":\"BuySell (True, 0.015) sell\"},\"renderers\":[{\"id\":\"13024\"}]},\"id\":\"13038\",\"type\":\"LegendItem\"},{\"attributes\":{},\"id\":\"13319\",\"type\":\"LinearScale\"},{\"attributes\":{\"active_multi\":null,\"tools\":[{\"id\":\"13056\"},{\"id\":\"13057\"},{\"id\":\"13058\"},{\"id\":\"13059\"},{\"id\":\"13071\"},{\"id\":\"13073\"}]},\"id\":\"13061\",\"type\":\"Toolbar\"},{\"attributes\":{\"label\":{\"value\":\"RSI (14)\"},\"renderers\":[{\"id\":\"13300\"}]},\"id\":\"13313\",\"type\":\"LegendItem\"},{\"attributes\":{\"args\":{\"axis\":{\"id\":\"13048\"},\"formatter\":{\"id\":\"13068\"},\"source\":{\"id\":\"12852\"}},\"code\":\"\\n                // We override this axis' formatter's `doFormat` method\\n                // with one that maps index ticks to dates. Some of those dates\\n                // are undefined (e.g. those whose ticks fall out of defined data\\n                // range) and we must filter out and account for those, otherwise\\n                // the formatter computes invalid visible span and returns some\\n                // labels as 'ERR'.\\n                // Note, after this assignment statement, on next plot redrawing,\\n                // our override `doFormat` will be called directly\\n                // -- FunctionTickFormatter.doFormat(), i.e. _this_ code, no longer\\n                // executes.\\n                axis.formatter.doFormat = function (ticks) {\\n                    const dates = ticks.map(i => source.data.datetime[source.data.index.indexOf(i)]),\\n                          valid = t => t !== undefined,\\n                          labels = formatter.doFormat(dates.filter(valid));\\n                    let i = 0;\\n                    return dates.map(t => valid(t) ? labels[i++] : '');\\n                };\\n\\n                // we do this manually only for the first time we are called\\n                const labels = axis.formatter.doFormat(ticks);\\n                return labels[index];\\n            \"},\"id\":\"13069\",\"type\":\"FuncTickFormatter\"},{\"attributes\":{\"aspect_ratio\":3.0,\"below\":[{\"id\":\"13323\"}],\"center\":[{\"id\":\"13326\"},{\"id\":\"13330\"},{\"id\":\"13367\"}],\"left\":[{\"id\":\"13327\"}],\"output_backend\":\"webgl\",\"renderers\":[{\"id\":\"13355\"},{\"id\":\"13372\"}],\"sizing_mode\":\"scale_width\",\"title\":{\"id\":\"13341\"},\"toolbar\":{\"id\":\"13336\"},\"toolbar_location\":null,\"x_range\":{\"id\":\"12854\"},\"x_scale\":{\"id\":\"13319\"},\"y_range\":{\"id\":\"13317\"},\"y_scale\":{\"id\":\"13321\"}},\"id\":\"13314\",\"subtype\":\"Figure\",\"type\":\"Plot\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"13025\",\"type\":\"CDSView\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"13344\"},\"major_label_policy\":{\"id\":\"13360\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"13324\"}},\"id\":\"13323\",\"type\":\"LinearAxis\"},{\"attributes\":{},\"id\":\"13053\",\"type\":\"BasicTicker\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"13022\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"sell\",\"nonselection_glyph\":{\"id\":\"13023\"},\"view\":{\"id\":\"13025\"}},\"id\":\"13024\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"range_padding\":0.5},\"id\":\"13317\",\"type\":\"DataRange1d\"},{\"attributes\":{\"text\":\"Broker (None)@(TSLA)\",\"text_color\":\"#333333\"},\"id\":\"13341\",\"type\":\"Title\"},{\"attributes\":{\"args\":{\"axis\":{\"id\":\"13268\"},\"formatter\":{\"id\":\"13288\"},\"source\":{\"id\":\"12852\"}},\"code\":\"\\n                // We override this axis' formatter's `doFormat` method\\n                // with one that maps index ticks to dates. Some of those dates\\n                // are undefined (e.g. those whose ticks fall out of defined data\\n                // range) and we must filter out and account for those, otherwise\\n                // the formatter computes invalid visible span and returns some\\n                // labels as 'ERR'.\\n                // Note, after this assignment statement, on next plot redrawing,\\n                // our override `doFormat` will be called directly\\n                // -- FunctionTickFormatter.doFormat(), i.e. _this_ code, no longer\\n                // executes.\\n                axis.formatter.doFormat = function (ticks) {\\n                    const dates = ticks.map(i => source.data.datetime[source.data.index.indexOf(i)]),\\n                          valid = t => t !== undefined,\\n                          labels = formatter.doFormat(dates.filter(valid));\\n                    let i = 0;\\n                    return dates.map(t => valid(t) ? labels[i++] : '');\\n                };\\n\\n                // we do this manually only for the first time we are called\\n                const labels = axis.formatter.doFormat(ticks);\\n                return labels[index];\\n            \"},\"id\":\"13289\",\"type\":\"FuncTickFormatter\"},{\"attributes\":{\"days\":[\"%d %b %R\"],\"hourmin\":[\"%H:%M:%S\"],\"hours\":[\"%d %b %R\"],\"minsec\":[\"%H:%M:%S\"],\"minutes\":[\"%H:%M\"],\"months\":[\"%d/%m/%y\"],\"seconds\":[\"%H:%M:%S\"],\"years\":[\"%Y %b\"]},\"id\":\"13288\",\"type\":\"DatetimeTickFormatter\"},{\"attributes\":{},\"id\":\"13324\",\"type\":\"BasicTicker\"},{\"attributes\":{\"active_multi\":null,\"tools\":[{\"id\":\"13276\"},{\"id\":\"13277\"},{\"id\":\"13278\"},{\"id\":\"13279\"},{\"id\":\"13291\"},{\"id\":\"13293\"}]},\"id\":\"13281\",\"type\":\"Toolbar\"},{\"attributes\":{\"aspect_ratio\":3.0,\"below\":[{\"id\":\"13048\"}],\"center\":[{\"id\":\"13051\"},{\"id\":\"13055\"},{\"id\":\"13092\"}],\"left\":[{\"id\":\"13052\"}],\"output_backend\":\"webgl\",\"renderers\":[{\"id\":\"13080\"}],\"sizing_mode\":\"scale_width\",\"title\":{\"id\":\"13066\"},\"toolbar\":{\"id\":\"13061\"},\"toolbar_location\":null,\"x_range\":{\"id\":\"12854\"},\"x_scale\":{\"id\":\"13044\"},\"y_range\":{\"id\":\"13042\"},\"y_scale\":{\"id\":\"13046\"}},\"id\":\"13039\",\"subtype\":\"Figure\",\"type\":\"Plot\"},{\"attributes\":{},\"id\":\"13044\",\"type\":\"LinearScale\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"13069\"},\"major_label_policy\":{\"id\":\"13085\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"13049\"}},\"id\":\"13048\",\"type\":\"LinearAxis\"},{\"attributes\":{\"range_padding\":0.5},\"id\":\"13042\",\"type\":\"DataRange1d\"},{\"attributes\":{},\"id\":\"13046\",\"type\":\"LinearScale\"},{\"attributes\":{\"bottom_units\":\"screen\",\"fill_alpha\":0.5,\"fill_color\":\"lightgrey\",\"left_units\":\"screen\",\"level\":\"overlay\",\"line_alpha\":1.0,\"line_color\":\"black\",\"line_dash\":[4,4],\"line_width\":2,\"right_units\":\"screen\",\"syncable\":false,\"top_units\":\"screen\"},\"id\":\"13280\",\"type\":\"BoxAnnotation\"},{\"attributes\":{\"axis_line_color\":\"#222222\",\"formatter\":{\"id\":\"13067\"},\"major_label_policy\":{\"id\":\"13088\"},\"major_label_text_color\":\"#333333\",\"major_tick_line_color\":\"#222222\",\"minor_tick_line_color\":\"#222222\",\"ticker\":{\"id\":\"13053\"}},\"id\":\"13052\",\"type\":\"LinearAxis\"},{\"attributes\":{},\"id\":\"13321\",\"type\":\"LinearScale\"},{\"attributes\":{},\"id\":\"13049\",\"type\":\"BasicTicker\"},{\"attributes\":{\"axis\":{\"id\":\"13048\"},\"grid_line_color\":\"#eeeeee\",\"ticker\":null},\"id\":\"13051\",\"type\":\"Grid\"},{\"attributes\":{},\"id\":\"13672\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"format\":\"0,0.000\"},\"id\":\"12923\",\"type\":\"NumeralTickFormatter\"},{\"attributes\":{},\"id\":\"13669\",\"type\":\"StringFormatter\"},{\"attributes\":{\"fill_color\":{\"value\":\"#ff0000\"},\"line_color\":{\"value\":\"#ff0000\"},\"size\":{\"value\":8.0},\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438030976\"}},\"id\":\"13440\",\"type\":\"Circle\"},{\"attributes\":{\"editor\":{\"id\":\"13741\"},\"field\":\"col0\",\"formatter\":{\"id\":\"13669\"},\"title\":\"Trade Length\"},\"id\":\"13670\",\"type\":\"TableColumn\"},{\"attributes\":{\"format\":\"0,0.000\"},\"id\":\"13577\",\"type\":\"NumberFormatter\"},{\"attributes\":{},\"id\":\"13678\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"bottom\":{\"field\":\"6437759296close\"},\"fill_alpha\":{\"value\":0.1},\"fill_color\":{\"field\":\"6437759296colors_bars\"},\"line_alpha\":{\"value\":0.1},\"line_color\":{\"field\":\"6437759296colors_outline\"},\"top\":{\"field\":\"6437759296open\"},\"width\":{\"value\":0.5},\"x\":{\"field\":\"index\"}},\"id\":\"12910\",\"type\":\"VBar\"},{\"attributes\":{\"background_fill_color\":\"#f5f5f5\",\"click_policy\":\"hide\",\"items\":[{\"id\":\"13148\"}],\"label_text_color\":\"#333333\",\"location\":\"top_left\",\"orientation\":\"horizontal\"},\"id\":\"13147\",\"type\":\"Legend\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"12912\",\"type\":\"CDSView\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"12909\"},\"hover_glyph\":null,\"muted_glyph\":null,\"nonselection_glyph\":{\"id\":\"12910\"},\"view\":{\"id\":\"12912\"}},\"id\":\"12911\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"editor\":{\"id\":\"13748\"},\"field\":\"col0\",\"formatter\":{\"id\":\"13690\"},\"title\":\"Performance\"},\"id\":\"13691\",\"type\":\"TableColumn\"},{\"attributes\":{\"fill_alpha\":{\"value\":0.1},\"fill_color\":{\"value\":\"#ff0000\"},\"line_alpha\":{\"value\":0.1},\"line_color\":{\"value\":\"#ff0000\"},\"size\":{\"value\":8.0},\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438030976\"}},\"id\":\"13441\",\"type\":\"Circle\"},{\"attributes\":{\"source\":{\"id\":\"13668\"}},\"id\":\"13686\",\"type\":\"CDSView\"},{\"attributes\":{\"range_padding\":3.0303030303030303,\"renderers\":[{\"id\":\"12930\"}],\"start\":0},\"id\":\"12924\",\"type\":\"DataRange1d\"},{\"attributes\":{\"line_color\":\"#a8a8a8\",\"line_dash\":[6],\"location\":0.0},\"id\":\"13454\",\"type\":\"Span\"},{\"attributes\":{\"label\":{\"value\":\"SMA (5)\"},\"renderers\":[{\"id\":\"13135\"}]},\"id\":\"13148\",\"type\":\"LegendItem\"},{\"attributes\":{},\"id\":\"13675\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"format\":\"0,0.000\"},\"id\":\"13681\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"callback\":null,\"formatters\":{\"@datetime\":\"datetime\"},\"mode\":\"vline\",\"renderers\":[{\"id\":\"13135\"}],\"tooltips\":[[\"Time\",\"@datetime{%F %R}\"],[\"SMA (5)\",\"@6437760592{0,0.000}\"]]},\"id\":\"13128\",\"type\":\"HoverTool\"},{\"attributes\":{\"editor\":{\"id\":\"13742\"},\"field\":\"col1\",\"formatter\":{\"id\":\"13672\"},\"title\":\"Total\"},\"id\":\"13673\",\"type\":\"TableColumn\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"13440\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"pnlminus\",\"nonselection_glyph\":{\"id\":\"13441\"},\"view\":{\"id\":\"13443\"}},\"id\":\"13442\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"line_color\":\"#000000\"},\"id\":\"13126\",\"type\":\"CrosshairTool\"},{\"attributes\":{\"columns\":[{\"id\":\"13670\"},{\"id\":\"13673\"},{\"id\":\"13676\"},{\"id\":\"13679\"},{\"id\":\"13682\"}],\"height\":250,\"index_position\":null,\"sizing_mode\":\"stretch_width\",\"source\":{\"id\":\"13668\"},\"view\":{\"id\":\"13686\"}},\"id\":\"13684\",\"type\":\"DataTable\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"13443\",\"type\":\"CDSView\"},{\"attributes\":{\"editor\":{\"id\":\"13743\"},\"field\":\"col2\",\"formatter\":{\"id\":\"13675\"},\"title\":\"Min\"},\"id\":\"13676\",\"type\":\"TableColumn\"},{\"attributes\":{\"data\":{\"col0\":[\"max_drawdown_money\"],\"col1\":[48206.630992980994]},\"selected\":{\"id\":\"13751\"},\"selection_policy\":{\"id\":\"13750\"}},\"id\":\"13689\",\"type\":\"ColumnDataSource\"},{\"attributes\":{\"editor\":{\"id\":\"13744\"},\"field\":\"col3\",\"formatter\":{\"id\":\"13678\"},\"title\":\"Max\"},\"id\":\"13679\",\"type\":\"TableColumn\"},{\"attributes\":{\"editor\":{\"id\":\"13745\"},\"field\":\"col4\",\"formatter\":{\"id\":\"13681\"},\"title\":\"Average\"},\"id\":\"13682\",\"type\":\"TableColumn\"},{\"attributes\":{\"editor\":{\"id\":\"13701\"},\"field\":\"col0\",\"formatter\":{\"id\":\"13559\"},\"title\":\"Name\"},\"id\":\"13560\",\"type\":\"TableColumn\"},{\"attributes\":{\"sizing_mode\":\"stretch_width\",\"style\":{\"font-size\":\"large\"},\"text\":\"Sharpe-Ratio (timeframe: Minute/factor: 19656/annualize: True)\"},\"id\":\"13568\",\"type\":\"Paragraph\"},{\"attributes\":{\"children\":[{\"id\":\"13699\"},{\"id\":\"13696\"}],\"sizing_mode\":\"stretch_width\"},\"id\":\"13700\",\"type\":\"Column\"},{\"attributes\":{},\"id\":\"13360\",\"type\":\"AllLabels\"},{\"attributes\":{\"source\":{\"id\":\"13558\"}},\"id\":\"13567\",\"type\":\"CDSView\"},{\"attributes\":{\"args\":{\"axis\":{\"id\":\"13103\"},\"formatter\":{\"id\":\"13123\"},\"source\":{\"id\":\"12852\"}},\"code\":\"\\n                // We override this axis' formatter's `doFormat` method\\n                // with one that maps index ticks to dates. Some of those dates\\n                // are undefined (e.g. those whose ticks fall out of defined data\\n                // range) and we must filter out and account for those, otherwise\\n                // the formatter computes invalid visible span and returns some\\n                // labels as 'ERR'.\\n                // Note, after this assignment statement, on next plot redrawing,\\n                // our override `doFormat` will be called directly\\n                // -- FunctionTickFormatter.doFormat(), i.e. _this_ code, no longer\\n                // executes.\\n                axis.formatter.doFormat = function (ticks) {\\n                    const dates = ticks.map(i => source.data.datetime[source.data.index.indexOf(i)]),\\n                          valid = t => t !== undefined,\\n                          labels = formatter.doFormat(dates.filter(valid));\\n                    let i = 0;\\n                    return dates.map(t => valid(t) ? labels[i++] : '');\\n                };\\n\\n                // we do this manually only for the first time we are called\\n                const labels = axis.formatter.doFormat(ticks);\\n                return labels[index];\\n            \"},\"id\":\"13124\",\"type\":\"FuncTickFormatter\"},{\"attributes\":{\"children\":[[{\"id\":\"12853\"},0,0],[{\"id\":\"13039\"},1,0],[{\"id\":\"13094\"},2,0],[{\"id\":\"13149\"},3,0],[{\"id\":\"13204\"},4,0],[{\"id\":\"13259\"},5,0],[{\"id\":\"13314\"},6,0],[{\"id\":\"13384\"},7,0]]},\"id\":\"13553\",\"type\":\"GridBox\"},{\"attributes\":{\"toolbar\":{\"id\":\"13554\"}},\"id\":\"13555\",\"type\":\"ToolbarBox\"},{\"attributes\":{\"sizing_mode\":\"stretch_width\",\"style\":{\"font-size\":\"large\"},\"text\":\"MoneyDrawDownAnalyzer\"},\"id\":\"13699\",\"type\":\"Paragraph\"},{\"attributes\":{\"data\":{\"col0\":[\"Sharpe-Ratio\"],\"col1\":[11.434727066118507]},\"selected\":{\"id\":\"13704\"},\"selection_policy\":{\"id\":\"13703\"}},\"id\":\"13558\",\"type\":\"ColumnDataSource\"},{\"attributes\":{},\"id\":\"13690\",\"type\":\"StringFormatter\"},{\"attributes\":{\"bottom_units\":\"screen\",\"fill_alpha\":0.5,\"fill_color\":\"lightgrey\",\"left_units\":\"screen\",\"level\":\"overlay\",\"line_alpha\":1.0,\"line_color\":\"black\",\"line_dash\":[4,4],\"line_width\":2,\"right_units\":\"screen\",\"syncable\":false,\"top_units\":\"screen\"},\"id\":\"13115\",\"type\":\"BoxAnnotation\"},{\"attributes\":{\"format\":\"0,0.000\"},\"id\":\"13574\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"children\":[{\"id\":\"13568\"},{\"id\":\"13565\"}],\"sizing_mode\":\"stretch_width\"},\"id\":\"13569\",\"type\":\"Column\"},{\"attributes\":{\"fill_alpha\":{\"value\":0.1},\"fill_color\":{\"field\":\"6437759296colors_volume\"},\"line_alpha\":{\"value\":0.1},\"top\":{\"field\":\"6437759296volume\"},\"width\":{\"value\":0.5},\"x\":{\"field\":\"index\"}},\"id\":\"12929\",\"type\":\"VBar\"},{\"attributes\":{\"source\":{\"id\":\"13689\"}},\"id\":\"13698\",\"type\":\"CDSView\"},{\"attributes\":{\"days\":[\"%d %b %R\"],\"hourmin\":[\"%H:%M:%S\"],\"hours\":[\"%d %b %R\"],\"minsec\":[\"%H:%M:%S\"],\"minutes\":[\"%H:%M\"],\"months\":[\"%d/%m/%y\"],\"seconds\":[\"%H:%M:%S\"],\"years\":[\"%Y %b\"]},\"id\":\"13123\",\"type\":\"DatetimeTickFormatter\"},{\"attributes\":{\"format\":\"0,0.000\"},\"id\":\"13633\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"columns\":[{\"id\":\"13572\"},{\"id\":\"13575\"},{\"id\":\"13578\"}],\"height\":125,\"index_position\":null,\"sizing_mode\":\"stretch_width\",\"source\":{\"id\":\"13570\"},\"view\":{\"id\":\"13582\"}},\"id\":\"13580\",\"type\":\"DataTable\"},{\"attributes\":{},\"id\":\"13693\",\"type\":\"StringFormatter\"},{\"attributes\":{\"toolbar\":{\"id\":\"13753\"},\"toolbar_location\":\"above\"},\"id\":\"13754\",\"type\":\"ToolbarBox\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"12931\",\"type\":\"CDSView\"},{\"attributes\":{\"columns\":[{\"id\":\"13691\"},{\"id\":\"13694\"}],\"height\":75,\"index_position\":null,\"sizing_mode\":\"stretch_width\",\"source\":{\"id\":\"13689\"},\"view\":{\"id\":\"13698\"}},\"id\":\"13696\",\"type\":\"DataTable\"},{\"attributes\":{\"fill_alpha\":{\"value\":0.5},\"fill_color\":{\"field\":\"6437759296colors_volume\"},\"line_alpha\":{\"value\":0.5},\"top\":{\"field\":\"6437759296volume\"},\"width\":{\"value\":0.5},\"x\":{\"field\":\"index\"}},\"id\":\"12928\",\"type\":\"VBar\"},{\"attributes\":{\"sizing_mode\":\"stretch_width\",\"style\":{\"font-size\":\"large\"},\"text\":\"Drawdown\"},\"id\":\"13583\",\"type\":\"Paragraph\"},{\"attributes\":{},\"id\":\"13363\",\"type\":\"AllLabels\"},{\"attributes\":{\"editor\":{\"id\":\"13749\"},\"field\":\"col1\",\"formatter\":{\"id\":\"13693\"},\"title\":\"Value\"},\"id\":\"13694\",\"type\":\"TableColumn\"},{\"attributes\":{},\"id\":\"12939\",\"type\":\"BasicTicker\"},{\"attributes\":{\"label\":{\"value\":\"Volume\"},\"renderers\":[{\"id\":\"12930\"}]},\"id\":\"12944\",\"type\":\"LegendItem\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"12928\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"Volume\",\"nonselection_glyph\":{\"id\":\"12929\"},\"view\":{\"id\":\"12931\"},\"y_range_name\":\"axvol\"},\"id\":\"12930\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"13004\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"buy\",\"nonselection_glyph\":{\"id\":\"13005\"},\"view\":{\"id\":\"13007\"}},\"id\":\"13006\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"columns\":[{\"id\":\"13560\"},{\"id\":\"13563\"}],\"height\":75,\"index_position\":null,\"sizing_mode\":\"stretch_width\",\"source\":{\"id\":\"13558\"},\"view\":{\"id\":\"13567\"}},\"id\":\"13565\",\"type\":\"DataTable\"},{\"attributes\":{},\"id\":\"12940\",\"type\":\"AllLabels\"},{\"attributes\":{},\"id\":\"13559\",\"type\":\"StringFormatter\"},{\"attributes\":{\"child\":{\"id\":\"13556\"},\"title\":\"Plots\"},\"id\":\"13557\",\"type\":\"Panel\"},{\"attributes\":{\"logo\":null},\"id\":\"13753\",\"type\":\"ProxyToolbar\"},{\"attributes\":{\"editor\":{\"id\":\"13705\"},\"field\":\"col0\",\"formatter\":{\"id\":\"13571\"},\"title\":\"Feature\"},\"id\":\"13572\",\"type\":\"TableColumn\"},{\"attributes\":{\"data\":{\"col0\":[\"Length\",\"Moneydown\",\"Drawdown\"],\"col1\":[4,13021.360359704617,7.116411219339061],\"col2\":[86,48206.630992980994,32.16814275224731]},\"selected\":{\"id\":\"13709\"},\"selection_policy\":{\"id\":\"13708\"}},\"id\":\"13570\",\"type\":\"ColumnDataSource\"},{\"attributes\":{},\"id\":\"13701\",\"type\":\"StringEditor\"},{\"attributes\":{\"format\":\"0,0.000\"},\"id\":\"13630\",\"type\":\"NumberFormatter\"},{\"attributes\":{\"children\":[{\"id\":\"13583\"},{\"id\":\"13580\"}],\"sizing_mode\":\"stretch_width\"},\"id\":\"13584\",\"type\":\"Column\"},{\"attributes\":{},\"id\":\"13702\",\"type\":\"StringEditor\"},{\"attributes\":{\"editor\":{\"id\":\"13706\"},\"field\":\"col1\",\"formatter\":{\"id\":\"13574\"},\"title\":\"Value\"},\"id\":\"13575\",\"type\":\"TableColumn\"},{\"attributes\":{},\"id\":\"13140\",\"type\":\"AllLabels\"},{\"attributes\":{\"editor\":{\"id\":\"13702\"},\"field\":\"col1\",\"formatter\":{\"id\":\"13562\"},\"title\":\"Value\"},\"id\":\"13563\",\"type\":\"TableColumn\"},{\"attributes\":{\"format\":\"0,0.000\"},\"id\":\"13562\",\"type\":\"NumberFormatter\"},{\"attributes\":{},\"id\":\"13571\",\"type\":\"StringFormatter\"},{\"attributes\":{\"logo\":null,\"toolbars\":[{\"id\":\"12875\"},{\"id\":\"13061\"},{\"id\":\"13116\"},{\"id\":\"13171\"},{\"id\":\"13226\"},{\"id\":\"13281\"},{\"id\":\"13336\"},{\"id\":\"13406\"}],\"tools\":[{\"id\":\"12870\"},{\"id\":\"12871\"},{\"id\":\"12872\"},{\"id\":\"12873\"},{\"id\":\"12885\"},{\"id\":\"12887\"},{\"id\":\"13056\"},{\"id\":\"13057\"},{\"id\":\"13058\"},{\"id\":\"13059\"},{\"id\":\"13071\"},{\"id\":\"13073\"},{\"id\":\"13111\"},{\"id\":\"13112\"},{\"id\":\"13113\"},{\"id\":\"13114\"},{\"id\":\"13126\"},{\"id\":\"13128\"},{\"id\":\"13166\"},{\"id\":\"13167\"},{\"id\":\"13168\"},{\"id\":\"13169\"},{\"id\":\"13181\"},{\"id\":\"13183\"},{\"id\":\"13221\"},{\"id\":\"13222\"},{\"id\":\"13223\"},{\"id\":\"13224\"},{\"id\":\"13236\"},{\"id\":\"13238\"},{\"id\":\"13276\"},{\"id\":\"13277\"},{\"id\":\"13278\"},{\"id\":\"13279\"},{\"id\":\"13291\"},{\"id\":\"13293\"},{\"id\":\"13331\"},{\"id\":\"13332\"},{\"id\":\"13333\"},{\"id\":\"13334\"},{\"id\":\"13346\"},{\"id\":\"13348\"},{\"id\":\"13401\"},{\"id\":\"13402\"},{\"id\":\"13403\"},{\"id\":\"13404\"},{\"id\":\"13416\"},{\"id\":\"13418\"}]},\"id\":\"13554\",\"type\":\"ProxyToolbar\"},{\"attributes\":{},\"id\":\"13703\",\"type\":\"UnionRenderers\"},{\"attributes\":{},\"id\":\"13704\",\"type\":\"Selection\"},{\"attributes\":{\"children\":[{\"id\":\"13553\"},{\"id\":\"13555\"}],\"sizing_mode\":\"scale_width\"},\"id\":\"13556\",\"type\":\"Row\"},{\"attributes\":{\"use_scientific\":false},\"id\":\"13067\",\"type\":\"BasicTickFormatter\"},{\"attributes\":{\"line_alpha\":0.1,\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6437760592\"}},\"id\":\"13134\",\"type\":\"Line\"},{\"attributes\":{\"line_alpha\":0.1,\"line_color\":\"#dc143c\",\"line_dash\":[6],\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438015520\"}},\"id\":\"12950\",\"type\":\"Line\"},{\"attributes\":{\"line_color\":\"#dc143c\",\"line_dash\":[6],\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438015520\"}},\"id\":\"12949\",\"type\":\"Line\"},{\"attributes\":{},\"id\":\"13143\",\"type\":\"AllLabels\"},{\"attributes\":{\"label\":{\"value\":\"BollingerBands (20, 2.0)\"},\"renderers\":[{\"id\":\"12951\"},{\"id\":\"12969\"},{\"id\":\"12986\"}]},\"id\":\"12965\",\"type\":\"LegendItem\"},{\"attributes\":{\"source\":{\"id\":\"12852\"}},\"id\":\"12952\",\"type\":\"CDSView\"},{\"attributes\":{\"data_source\":{\"id\":\"12852\"},\"glyph\":{\"id\":\"12949\"},\"hover_glyph\":null,\"muted_glyph\":null,\"name\":\"mid\",\"nonselection_glyph\":{\"id\":\"12950\"},\"view\":{\"id\":\"12952\"}},\"id\":\"12951\",\"type\":\"GlyphRenderer\"},{\"attributes\":{\"line_color\":\"#dc143c\",\"x\":{\"field\":\"index\"},\"y\":{\"field\":\"6438015616\"}},\"id\":\"12967\",\"type\":\"Line\"},{\"attributes\":{\"label\":{\"value\":\"Broker (None)\"},\"renderers\":[{\"id\":\"13355\"},{\"id\":\"13372\"}]},\"id\":\"13368\",\"type\":\"LegendItem\"}],\"root_ids\":[\"13759\"]},\"title\":\"Bokeh Application\",\"version\":\"2.3.3\"}};\n", "  var render_items = [{\"docid\":\"c10ac79d-9915-415d-8968-3c16df2ea87c\",\"root_ids\":[\"13759\"],\"roots\":{\"13759\":\"085343b2-deeb-40c3-88e8-986295f67ff2\"}}];\n", "  root.Bokeh.embed.embed_items_notebook(docs_json, render_items);\n", "\n", "  }\n", "  if (root.Bokeh !== undefined) {\n", "    embed_document(root);\n", "  } else {\n", "    var attempts = 0;\n", "    var timer = setInterval(function(root) {\n", "      if (root.Bokeh !== undefined) {\n", "        clearInterval(timer);\n", "        embed_document(root);\n", "      } else {\n", "        attempts++;\n", "        if (attempts > 100) {\n", "          clearInterval(timer);\n", "          console.log(\"Bokeh: ERROR: Unable to run BokehJS code because BokehJS library is missing\");\n", "        }\n", "      }\n", "    }, 10, root)\n", "  }\n", "})(window);"], "application/vnd.bokehjs_exec.v0+json": ""}, "metadata": {"application/vnd.bokehjs_exec.v0+json": {"id": "13759"}}, "output_type": "display_data"}], "source": ["plot_results(ml_cerebro)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 7. 对比 Buy&Hold 策略"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["初始资金: 100000.00\n", "2024-03-12 00:00:00 [买入] 执行买入并持有策略: 价格=177.54, 数量=535\n", "2024-03-13 00:00:00 [成交] 买单执行: 价格=173.10, 数量=535\n", "2025-03-06 00:00:00 [回测结束] Buy & Hold 策略最终市值: 148244.65\n", "2025-03-06 00:00:00 [回测结束] 总收益率: 48.24%\n", "回测结束资金: 148244.65\n", "=== 回测分析报告 ===\n", "夏普比率: 0.9372\n", "最大回撤比例: 43.85%\n", "最大回撤金额(自定义): 115779.34\n", "累计收益率: 39.37%\n", "年化收益率: 49.43%\n", "=== 交易详情 ===\n", "总交易笔数: 1\n", "胜率: 0 / 1\n"]}], "source": ["# 若想看最优参数的详细回测日志，可再手动调用:\n", "bh_result, bh_cerebro = run_backtest(\n", "    ticker=ticker,\n", "    df=test_data,\n", "    start_date=start_date,\n", "    end_date=end_date,\n", "    strategy=BuyAndHoldStrategy,\n", "    initial_cash=100000,\n", "    print_log=True,  # 这次打开日志\n", "    timeframe=bt.TimeFrame.Days,\n", "    compression=1\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 比较策略和Buy&Hold"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-04-09 => BUY signal, pred_ret=0.002981\n", "Current position size: 0, Value: 100000.0\n", "Current position size: 554, Value: 99167.288819989\n", "Current position size: 554, Value: 100740.65524454955\n", "Current position size: 554, Value: 98773.95355387572\n", "Current position size: 554, Value: 93472.16949625853\n", "Current position size: 554, Value: 91051.19220133666\n", "Current position size: 554, Value: 90131.55017252806\n", "Current position size: 554, Value: 87073.4678055847\n", "Current position size: 554, Value: 85477.95355387572\n", "Current position size: 554, Value: 82707.95355387572\n", "Current position size: 554, Value: 84164.9678055847\n", "Current position size: 554, Value: 93832.27456828002\n", "Current position size: 554, Value: 98291.9678055847\n", "Current position size: 554, Value: 97244.90814371947\n", "Current position size: 554, Value: 111515.95355387572\n", "Current position size: 554, Value: 105549.37118693236\n", "Current position size: 554, Value: 103726.71490641478\n", "Current position size: 554, Value: 103737.788819989\n", "Current position size: 554, Value: 104391.51321574095\n", "Current position size: 554, Value: 106369.288819989\n", "Current position size: 554, Value: 102518.99051066283\n", "Current position size: 554, Value: 100807.13253947142\n", "Current position size: 554, Value: 99283.63253947142\n", "Current position size: 554, Value: 97344.63253947142\n", "Current position size: 554, Value: 99239.31152506713\n", "Current position size: 554, Value: 102374.95355387572\n", "Current position size: 554, Value: 100402.71490641478\n", "Current position size: 554, Value: 100873.6098343933\n", "Current position size: 554, Value: 102325.09558268431\n", "Current position size: 554, Value: 100934.55017252806\n", "Current position size: 554, Value: 107388.65524454955\n", "Current position size: 554, Value: 103793.19220133666\n", "Current position size: 554, Value: 100264.21490641478\n", "Current position size: 554, Value: 103311.21490641478\n", "Current position size: 554, Value: 101931.75186320189\n", "Current position size: 554, Value: 101621.51321574095\n", "Current position size: 554, Value: 103061.90814371947\n", "Current position size: 554, Value: 102668.57287760619\n", "Current position size: 554, Value: 101676.90814371947\n", "Current position size: 554, Value: 100834.83423014525\n", "Current position size: 554, Value: 100962.25186320189\n", "Current position size: 554, Value: 102591.01321574095\n", "Current position size: 554, Value: 102336.16949625853\n", "Current position size: 554, Value: 100291.90814371947\n", "Current position size: 554, Value: 98557.89389201048\n", "Current position size: 554, Value: 102230.90814371947\n", "Current position size: 554, Value: 105100.63253947142\n", "Current position size: 554, Value: 102629.788819989\n", "Current position size: 554, Value: 107854.01321574095\n", "Current position size: 554, Value: 106424.69220133666\n", "Current position size: 554, Value: 104602.03592081908\n", "Current position size: 554, Value: 105399.788819989\n", "Current position size: 554, Value: 105161.57287760619\n", "Current position size: 554, Value: 107804.15524454955\n", "Current position size: 554, Value: 112801.22915812377\n", "Current position size: 554, Value: 113382.9308487976\n", "Current position size: 554, Value: 113637.77456828002\n", "Current position size: 554, Value: 120274.69220133666\n", "Current position size: 554, Value: 132130.28881998901\n", "Current position size: 554, Value: 140512.31152506714\n", "Current position size: 554, Value: 143354.33423014526\n", "Current position size: 554, Value: 144141.01321574097\n", "Current position size: 554, Value: 149343.06442423706\n", "Current position size: 554, Value: 149858.29727335816\n", "Current position size: 554, Value: 137542.87118693237\n", "Current position size: 554, Value: 141531.66949625855\n", "Current position size: 554, Value: 143974.81152506714\n", "Current position size: 554, Value: 146146.49051066284\n", "Current position size: 554, Value: 141681.2518632019\n", "Current position size: 554, Value: 142085.66949625855\n", "Current position size: 554, Value: 136529.05017252808\n", "Current position size: 554, Value: 143348.78881998901\n", "Current position size: 554, Value: 140506.77456828003\n", "Current position size: 554, Value: 123670.71490641478\n", "Current position size: 554, Value: 126030.75186320189\n", "Current position size: 554, Value: 125781.45355387572\n", "Current position size: 554, Value: 132595.65524454956\n", "Current position size: 554, Value: 127343.72915812377\n", "Current position size: 554, Value: 132579.0359208191\n", "Current position size: 554, Value: 124152.69220133666\n", "2024-08-02 => SELL signal, pred_ret=-0.000074\n", "Current position size: 554, Value: 119061.4308487976\n", "2024-08-05 => BUY signal, pred_ret=0.000202\n", "Current position size: 0, Value: 106493.84835879515\n", "Current position size: 524, Value: 106304.78883897091\n", "Current position size: 524, Value: 101651.66628037716\n", "Current position size: 524, Value: 105361.58723984982\n", "Current position size: 524, Value: 105969.42915879513\n", "Current position size: 524, Value: 104654.1920372131\n", "Current position size: 524, Value: 110072.35011826779\n", "Current position size: 524, Value: 106692.55171738888\n", "Current position size: 524, Value: 113378.78883897091\n", "Current position size: 524, Value: 114416.30660020138\n", "Current position size: 524, Value: 117874.70979844357\n", "Current position size: 524, Value: 117025.83235703732\n", "Current position size: 524, Value: 118162.91139756466\n", "Current position size: 524, Value: 111555.27107774044\n", "Current position size: 524, Value: 116617.11299668576\n", "Current position size: 524, Value: 112891.47267686154\n", "Current position size: 524, Value: 110795.47267686154\n", "Current position size: 524, Value: 108982.42915879513\n", "Current position size: 524, Value: 109260.14851914669\n", "Current position size: 524, Value: 113363.06947861935\n", "Current position size: 524, Value: 111523.83235703732\n", "Current position size: 524, Value: 116140.27107774044\n", "Current position size: 524, Value: 121778.50819932247\n", "Current position size: 524, Value: 111591.9469200256\n", "Current position size: 524, Value: 114494.91139756466\n", "Current position size: 524, Value: 119682.50819932247\n", "Current position size: 524, Value: 120709.55171738888\n", "Current position size: 524, Value: 121589.86787949826\n", "Current position size: 524, Value: 121841.38564072872\n", "Current position size: 524, Value: 120002.14851914669\n", "Current position size: 524, Value: 120573.30660020138\n", "Current position size: 524, Value: 120222.22755967404\n", "Current position size: 524, Value: 128983.50819932247\n", "Current position size: 524, Value: 126012.42915879513\n", "Current position size: 524, Value: 132169.42915879513\n", "Current position size: 524, Value: 134406.91139756466\n", "Current position size: 524, Value: 135847.9034019592\n", "Current position size: 524, Value: 134380.70979844357\n", "Current position size: 524, Value: 137650.46468125607\n", "Current position size: 524, Value: 138263.55171738888\n", "Current position size: 524, Value: 136371.9034019592\n", "Current position size: 524, Value: 131655.91139756466\n", "Current position size: 524, Value: 127275.27107774044\n", "Current position size: 524, Value: 132211.3501182678\n", "Current position size: 524, Value: 127364.35011826779\n", "Current position size: 524, Value: 129287.42915879513\n", "Current position size: 524, Value: 127479.63075791622\n", "Current position size: 524, Value: 126284.91139756466\n", "Current position size: 524, Value: 115296.63075791622\n", "Current position size: 524, Value: 116009.27107774044\n", "Current position size: 524, Value: 116224.11299668576\n", "Current position size: 524, Value: 117146.35011826779\n", "Current position size: 524, Value: 116915.78883897091\n", "2024-10-18 => SELL signal, pred_ret=-0.000074\n", "Current position size: 524, Value: 116816.22755967404\n", "Current position size: 0, Value: 115732.1485637512\n", "Current position size: 0, Value: 115732.1485637512\n", "Current position size: 0, Value: 115732.1485637512\n", "2024-10-24 => BUY signal, pred_ret=0.000946\n", "Current position size: 0, Value: 115732.1485637512\n", "Current position size: 435, Value: 121332.309273468\n", "Current position size: 435, Value: 118426.51245950315\n", "Current position size: 435, Value: 117125.85343240354\n", "Current position size: 435, Value: 116268.90290139768\n", "Current position size: 435, Value: 112919.41086648557\n", "Current position size: 435, Value: 112540.95635293577\n", "Current position size: 435, Value: 109870.0566184387\n", "Current position size: 435, Value: 113611.059273468\n", "Current position size: 435, Value: 129745.20768045042\n", "Current position size: 435, Value: 133390.50980447384\n", "Current position size: 435, Value: 143965.35874246212\n", "Current position size: 435, Value: 156484.65821145626\n", "Current position size: 435, Value: 147127.8039634094\n", "Current position size: 435, Value: 147889.0539634094\n", "Current position size: 435, Value: 139597.9550254211\n", "Current position size: 435, Value: 143747.85874246212\n", "Current position size: 435, Value: 151586.5539634094\n", "Current position size: 435, Value: 154744.65821145626\n", "Current position size: 435, Value: 153017.7076804504\n", "Current position size: 435, Value: 151978.06458352658\n", "Current position size: 435, Value: 157598.25714944454\n", "Current position size: 435, Value: 151521.30661843868\n", "Current position size: 435, Value: 151364.712990509\n", "Current position size: 435, Value: 149041.81458352658\n", "Current position size: 435, Value: 154379.25980447384\n", "2024-12-02 => SELL signal, pred_ret=-0.000636\n", "Current position size: 435, Value: 159568.80661843868\n", "Current position size: 0, Value: 157092.89165670774\n", "Current position size: 0, Value: 157092.89165670774\n", "Current position size: 0, Value: 157092.89165670774\n", "Current position size: 0, Value: 157092.89165670774\n", "Current position size: 0, Value: 157092.89165670774\n", "2024-12-10 => BUY signal, pred_ret=0.001681\n", "Current position size: 0, Value: 157092.89165670774\n", "Current position size: 383, Value: 162688.60851897582\n", "Current position size: 383, Value: 160134.00506438597\n", "Current position size: 383, Value: 167077.79693450316\n", "2024-12-16 => SELL signal, pred_ret=-0.020378\n", "Current position size: 383, Value: 177338.35851897582\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 0, Value: 182069.99984143063\n", "2025-02-26 => BUY signal, pred_ret=0.018888\n", "Current position size: 0, Value: 182069.99984143063\n", "Current position size: 613, Value: 176215.11334722288\n", "Current position size: 613, Value: 182976.50259893187\n", "Current position size: 613, Value: 177870.20212285765\n", "Current position size: 613, Value: 170140.28110234984\n", "2025-03-05 => SELL signal, pred_ret=-0.002243\n", "Current position size: 613, Value: 174468.0596057678\n", "2025-03-06 => BUY signal, pred_ret=0.000279\n", "Current position size: 0, Value: 169955.14223922725\n"]}], "source": ["results = ml_cerebro.run()  # cerebro.run() 返回一个列表，每个元素是一个策略实例\n", "ml_strategy_instance = results[0]  # 如果你只有一个策略，就取第一个"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'Lines_LineSeries_LineIterator_DataAccessor_Strateg' object has no attribute 'value_history_dates'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[86], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mml_strategy_instance\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalue_history_dates\u001b[49m\n", "File \u001b[0;32m~/miniforge3/envs/ta_arm/lib/python3.9/site-packages/backtrader/lineseries.py:461\u001b[0m, in \u001b[0;36mLineSeries.__getattr__\u001b[0;34m(self, name)\u001b[0m\n\u001b[1;32m    457\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m__getattr__\u001b[39m(\u001b[38;5;28mself\u001b[39m, name):\n\u001b[1;32m    458\u001b[0m     \u001b[38;5;66;03m# to refer to line by name directly if the attribute was not found\u001b[39;00m\n\u001b[1;32m    459\u001b[0m     \u001b[38;5;66;03m# in this object if we set an attribute in this object it will be\u001b[39;00m\n\u001b[1;32m    460\u001b[0m     \u001b[38;5;66;03m# found before we end up here\u001b[39;00m\n\u001b[0;32m--> 461\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mgetattr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlines\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[0;31mAttributeError\u001b[0m: 'Lines_LineSeries_LineIterator_DataAccessor_Strateg' object has no attribute 'value_history_dates'"]}], "source": ["ml_strategy_instance.value_history_dates"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'Cerebro' object has no attribute 'strategy'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[79], line 8\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mextract_value_series\u001b[39m(cerebro_instance):\n\u001b[1;32m      4\u001b[0m     \u001b[38;5;66;03m# 这里需要你在策略或observer中存储历史净值数据，示例仅作说明\u001b[39;00m\n\u001b[1;32m      5\u001b[0m     \u001b[38;5;66;03m# 假设 cerebro_instance.strategy[0].value_history 存储了净值的列表，和对应的日期列表\u001b[39;00m\n\u001b[1;32m      6\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m cerebro_instance\u001b[38;5;241m.\u001b[39mstrategy[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mvalue_history_dates, cerebro_instance\u001b[38;5;241m.\u001b[39mstrategy[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mvalue_history_values\n\u001b[0;32m----> 8\u001b[0m dates1, values1 \u001b[38;5;241m=\u001b[39m \u001b[43mextract_value_series\u001b[49m\u001b[43m(\u001b[49m\u001b[43mml_cerebro\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      9\u001b[0m dates2, values2 \u001b[38;5;241m=\u001b[39m extract_value_series(bh_cerebro)\n\u001b[1;32m     11\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mmatplotlib\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpyplot\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mplt\u001b[39;00m\n", "Cell \u001b[0;32mIn[79], line 6\u001b[0m, in \u001b[0;36mextract_value_series\u001b[0;34m(cerebro_instance)\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mextract_value_series\u001b[39m(cerebro_instance):\n\u001b[1;32m      4\u001b[0m     \u001b[38;5;66;03m# 这里需要你在策略或observer中存储历史净值数据，示例仅作说明\u001b[39;00m\n\u001b[1;32m      5\u001b[0m     \u001b[38;5;66;03m# 假设 cerebro_instance.strategy[0].value_history 存储了净值的列表，和对应的日期列表\u001b[39;00m\n\u001b[0;32m----> 6\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mcerebro_instance\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstrategy\u001b[49m[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mvalue_history_dates, cerebro_instance\u001b[38;5;241m.\u001b[39mstrategy[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mvalue_history_values\n", "\u001b[0;31mAttributeError\u001b[0m: 'Cerebro' object has no attribute 'strategy'"]}], "source": ["# 假设你在 run_backtest 中添加了 Value 观测器，并且你写好了一个函数 extract_value_series 来提取净值序列\n", "# 例如：\n", "def extract_value_series(cerebro_instance):\n", "    # 这里需要你在策略或observer中存储历史净值数据，示例仅作说明\n", "    # 假设 cerebro_instance.strategy[0].value_history 存储了净值的列表，和对应的日期列表\n", "    return cerebro_instance.strategy[0].value_history_dates, cerebro_instance.strategy[0].value_history_values\n", "\n", "dates1, values1 = extract_value_series(ml_cerebro)\n", "dates2, values2 = extract_value_series(bh_cerebro)\n", "\n", "import matplotlib.pyplot as plt\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(dates1, values1, label='Strategy1')\n", "plt.plot(dates2, values2, label='Strategy2')\n", "plt.xlabel('时间')\n", "plt.ylabel('资产净值')\n", "plt.title('回报曲线对比')\n", "plt.legend()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"data": {"text/plain": ["<backtrader.cerebro.Cerebro at 0x17e528070>"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["ml_cerebro"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"data": {"text/plain": ["169955.14223922725"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["ml_cerebro.broker.getvalue()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python (ta_env)", "language": "python", "name": "ta_arm"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}