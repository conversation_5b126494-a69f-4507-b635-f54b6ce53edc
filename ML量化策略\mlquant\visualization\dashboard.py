"""
策略性能综合仪表板

提供策略回测结果的综合可视化仪表板，包括：
- 资金曲线图
- 回撤分析图
- 收益分布图
- 交易统计图
- 风险指标表
- 月度收益热力图
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union
import warnings

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.figure_factory as ff
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    warnings.warn("plotly not available. StrategyDashboard will not work.")

from ..backtest.result import BacktestResult


class StrategyDashboard:
    """
    策略性能综合仪表板
    
    提供策略回测结果的全面可视化分析，包括性能指标、
    风险分析、交易统计等多个维度的图表展示。
    """
    
    def __init__(self, theme: str = "plotly_white"):
        """
        初始化仪表板
        
        Args:
            theme: 图表主题
        """
        if not PLOTLY_AVAILABLE:
            raise ImportError("plotly is required for StrategyDashboard")
        
        self.theme = theme
        self.colors = {
            'profit': '#00CC96',
            'loss': '#FF6692',
            'neutral': '#636EFA',
            'benchmark': '#FFA15A',
            'background': '#F8F9FA'
        }
    
    def create_equity_curve_chart(self, result: BacktestResult, 
                                 benchmark: Optional[pd.Series] = None) -> go.Figure:
        """
        创建资金曲线图
        
        Args:
            result: 回测结果
            benchmark: 基准收益序列
            
        Returns:
            Plotly图表对象
        """
        fig = go.Figure()
        
        # 策略资金曲线
        fig.add_trace(go.Scatter(
            x=result.equity_curve.index,
            y=result.equity_curve.values,
            mode='lines',
            name=f'{result.strategy_name}',
            line=dict(color=self.colors['neutral'], width=2),
            hovertemplate='日期: %{x}<br>资金: %{y:,.2f}<extra></extra>'
        ))
        
        # 基准曲线
        if benchmark is not None:
            fig.add_trace(go.Scatter(
                x=benchmark.index,
                y=benchmark.values,
                mode='lines',
                name='基准',
                line=dict(color=self.colors['benchmark'], width=1, dash='dash'),
                hovertemplate='日期: %{x}<br>基准: %{y:,.2f}<extra></extra>'
            ))
        
        # 添加买卖点标记
        if hasattr(result, 'trades') and len(result.trades) > 0:
            buy_trades = result.trades[result.trades['type'] == 'buy']
            sell_trades = result.trades[result.trades['type'] == 'sell']
            
            if len(buy_trades) > 0:
                fig.add_trace(go.Scatter(
                    x=buy_trades.index,
                    y=buy_trades['equity'],
                    mode='markers',
                    name='买入',
                    marker=dict(color=self.colors['profit'], size=8, symbol='triangle-up'),
                    hovertemplate='买入<br>日期: %{x}<br>资金: %{y:,.2f}<extra></extra>'
                ))
            
            if len(sell_trades) > 0:
                fig.add_trace(go.Scatter(
                    x=sell_trades.index,
                    y=sell_trades['equity'],
                    mode='markers',
                    name='卖出',
                    marker=dict(color=self.colors['loss'], size=8, symbol='triangle-down'),
                    hovertemplate='卖出<br>日期: %{x}<br>资金: %{y:,.2f}<extra></extra>'
                ))
        
        fig.update_layout(
            title='策略资金曲线',
            xaxis_title='日期',
            yaxis_title='资金',
            template=self.theme,
            hovermode='x unified',
            legend=dict(x=0, y=1, bgcolor='rgba(255,255,255,0.8)')
        )
        
        return fig
    
    def create_drawdown_chart(self, result: BacktestResult) -> go.Figure:
        """
        创建回撤分析图
        
        Args:
            result: 回测结果
            
        Returns:
            Plotly图表对象
        """
        # 计算回撤
        equity = result.equity_curve
        peak = equity.expanding().max()
        drawdown = (equity - peak) / peak
        
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('资金曲线与历史最高点', '回撤曲线'),
            vertical_spacing=0.1,
            row_heights=[0.6, 0.4]
        )
        
        # 上图：资金曲线和历史最高点
        fig.add_trace(go.Scatter(
            x=equity.index,
            y=equity.values,
            mode='lines',
            name='资金曲线',
            line=dict(color=self.colors['neutral']),
            hovertemplate='资金: %{y:,.2f}<extra></extra>'
        ), row=1, col=1)
        
        fig.add_trace(go.Scatter(
            x=peak.index,
            y=peak.values,
            mode='lines',
            name='历史最高点',
            line=dict(color=self.colors['profit'], dash='dash'),
            hovertemplate='最高点: %{y:,.2f}<extra></extra>'
        ), row=1, col=1)
        
        # 下图：回撤曲线
        fig.add_trace(go.Scatter(
            x=drawdown.index,
            y=drawdown.values * 100,
            mode='lines',
            name='回撤',
            fill='tonexty',
            line=dict(color=self.colors['loss']),
            hovertemplate='回撤: %{y:.2f}%<extra></extra>'
        ), row=2, col=1)
        
        # 标记最大回撤点
        max_dd_idx = drawdown.idxmin()
        max_dd_value = drawdown.min()
        
        fig.add_trace(go.Scatter(
            x=[max_dd_idx],
            y=[max_dd_value * 100],
            mode='markers',
            name=f'最大回撤 ({max_dd_value:.2%})',
            marker=dict(color='red', size=10, symbol='x'),
            hovertemplate=f'最大回撤: {max_dd_value:.2%}<br>日期: {max_dd_idx}<extra></extra>'
        ), row=2, col=1)
        
        fig.update_layout(
            title='回撤分析',
            template=self.theme,
            hovermode='x unified'
        )
        
        fig.update_yaxes(title_text="资金", row=1, col=1)
        fig.update_yaxes(title_text="回撤 (%)", row=2, col=1)
        fig.update_xaxes(title_text="日期", row=2, col=1)
        
        return fig
    
    def create_returns_distribution_chart(self, result: BacktestResult) -> go.Figure:
        """
        创建收益分布图
        
        Args:
            result: 回测结果
            
        Returns:
            Plotly图表对象
        """
        # 计算日收益率
        returns = result.equity_curve.pct_change().dropna()
        
        fig = make_subplots(
            rows=1, cols=2,
            subplot_titles=('收益率分布直方图', '收益率箱线图'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # 左图：直方图
        fig.add_trace(go.Histogram(
            x=returns * 100,
            nbinsx=50,
            name='收益率分布',
            marker_color=self.colors['neutral'],
            opacity=0.7,
            hovertemplate='收益率: %{x:.2f}%<br>频次: %{y}<extra></extra>'
        ), row=1, col=1)
        
        # 添加正态分布拟合线
        x_range = np.linspace(returns.min(), returns.max(), 100) * 100
        normal_dist = np.exp(-0.5 * ((x_range - returns.mean() * 100) / (returns.std() * 100)) ** 2)
        normal_dist = normal_dist / normal_dist.max() * returns.value_counts().max()
        
        fig.add_trace(go.Scatter(
            x=x_range,
            y=normal_dist,
            mode='lines',
            name='正态分布拟合',
            line=dict(color='red', dash='dash'),
            hovertemplate='正态分布<extra></extra>'
        ), row=1, col=1)
        
        # 右图：箱线图
        fig.add_trace(go.Box(
            y=returns * 100,
            name='收益率',
            marker_color=self.colors['neutral'],
            boxmean='sd'
        ), row=1, col=2)
        
        fig.update_layout(
            title='收益率分布分析',
            template=self.theme
        )
        
        fig.update_xaxes(title_text="收益率 (%)", row=1, col=1)
        fig.update_yaxes(title_text="频次", row=1, col=1)
        fig.update_yaxes(title_text="收益率 (%)", row=1, col=2)
        
        return fig
    
    def create_performance_metrics_table(self, result: BacktestResult) -> go.Figure:
        """
        创建性能指标表
        
        Args:
            result: 回测结果
            
        Returns:
            Plotly表格对象
        """
        # 准备指标数据
        metrics = {
            '总收益率': f"{result.total_return:.2%}",
            '年化收益率': f"{result.annualized_return:.2%}",
            '夏普比率': f"{result.sharpe_ratio:.2f}",
            '最大回撤': f"{result.max_drawdown:.2%}",
            '波动率': f"{result.volatility:.2%}",
            '胜率': f"{getattr(result, 'win_rate', 0):.2%}",
            '盈亏比': f"{getattr(result, 'profit_loss_ratio', 0):.2f}",
            '交易次数': f"{getattr(result, 'total_trades', 0)}",
            '平均持仓天数': f"{getattr(result, 'avg_holding_days', 0):.1f}",
            'Calmar比率': f"{getattr(result, 'calmar_ratio', 0):.2f}"
        }
        
        # 创建表格
        fig = go.Figure(data=[go.Table(
            header=dict(
                values=['指标', '数值'],
                fill_color=self.colors['neutral'],
                font=dict(color='white', size=14),
                align='center'
            ),
            cells=dict(
                values=[list(metrics.keys()), list(metrics.values())],
                fill_color=[['white', self.colors['background']] * len(metrics)],
                font=dict(size=12),
                align=['left', 'center'],
                height=30
            )
        )])
        
        fig.update_layout(
            title='策略性能指标',
            template=self.theme,
            height=400
        )
        
        return fig
    
    def create_monthly_returns_heatmap(self, result: BacktestResult) -> go.Figure:
        """
        创建月度收益热力图
        
        Args:
            result: 回测结果
            
        Returns:
            Plotly热力图对象
        """
        # 计算月度收益
        equity = result.equity_curve
        monthly_returns = equity.resample('M').last().pct_change().dropna()
        
        # 创建月度收益矩阵
        monthly_data = []
        years = sorted(monthly_returns.index.year.unique())
        months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        
        for year in years:
            year_data = []
            for month in range(1, 13):
                try:
                    value = monthly_returns[
                        (monthly_returns.index.year == year) & 
                        (monthly_returns.index.month == month)
                    ].iloc[0] * 100
                    year_data.append(value)
                except (IndexError, KeyError):
                    year_data.append(np.nan)
            monthly_data.append(year_data)
        
        # 创建热力图
        fig = go.Figure(data=go.Heatmap(
            z=monthly_data,
            x=months,
            y=years,
            colorscale='RdYlGn',
            zmid=0,
            text=[[f'{val:.1f}%' if not np.isnan(val) else '' for val in row] for row in monthly_data],
            texttemplate='%{text}',
            textfont={"size": 10},
            hovertemplate='年份: %{y}<br>月份: %{x}<br>收益率: %{z:.2f}%<extra></extra>'
        ))
        
        fig.update_layout(
            title='月度收益率热力图',
            xaxis_title='月份',
            yaxis_title='年份',
            template=self.theme
        )
        
        return fig
    
    def create_comprehensive_dashboard(self, result: BacktestResult, 
                                     benchmark: Optional[pd.Series] = None) -> go.Figure:
        """
        创建综合仪表板
        
        Args:
            result: 回测结果
            benchmark: 基准收益序列
            
        Returns:
            综合仪表板图表
        """
        # 创建子图布局
        fig = make_subplots(
            rows=3, cols=2,
            subplot_titles=(
                '资金曲线', '回撤分析',
                '收益率分布', '月度收益热力图',
                '性能指标', '交易统计'
            ),
            specs=[
                [{"colspan": 2}, None],
                [{"type": "histogram"}, {"type": "heatmap"}],
                [{"type": "table"}, {"type": "bar"}]
            ],
            vertical_spacing=0.08,
            row_heights=[0.4, 0.3, 0.3]
        )
        
        # 1. 资金曲线（占据第一行全宽）
        equity_fig = self.create_equity_curve_chart(result, benchmark)
        for trace in equity_fig.data:
            fig.add_trace(trace, row=1, col=1)
        
        # 2. 回撤分析（第二行左侧）
        returns = result.equity_curve.pct_change().dropna()
        fig.add_trace(go.Histogram(
            x=returns * 100,
            nbinsx=30,
            name='收益率分布',
            marker_color=self.colors['neutral'],
            showlegend=False
        ), row=2, col=1)
        
        # 3. 月度收益热力图（第二行右侧）
        monthly_heatmap = self.create_monthly_returns_heatmap(result)
        fig.add_trace(monthly_heatmap.data[0], row=2, col=2)
        
        # 更新布局
        fig.update_layout(
            title=f'{result.strategy_name} - 策略性能综合仪表板',
            template=self.theme,
            height=1000,
            showlegend=True
        )
        
        return fig
