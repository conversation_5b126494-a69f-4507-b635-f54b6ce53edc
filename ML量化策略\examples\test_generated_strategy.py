#!/usr/bin/env python3
"""
测试生成的策略文件

演示如何使用ML优化后自动生成的策略文件。
"""

import pandas as pd
import numpy as np
from data_generator import RandomDataGenerator

# 导入生成的策略
try:
    from dual_ma_strategy import DualMaStrategy
    strategy_available = True
    strategy_class = DualMaStrategy
except ImportError:
    try:
        from optimizeddualma_strategy import OptimizeddualmastrategyStrategy
        strategy_available = True
        strategy_class = OptimizeddualmastrategyStrategy
    except ImportError:
        print("策略文件未找到，请先运行demo.py生成策略文件")
        strategy_available = False
        strategy_class = None


def test_generated_strategy():
    """测试生成的策略"""
    if not strategy_available:
        return
    
    print("🧪 测试ML生成的策略文件")
    print("="*50)
    
    # 1. 创建策略实例
    print("1. 创建策略实例...")
    strategy = strategy_class()
    
    # 2. 显示策略信息
    print("\n2. 策略信息:")
    strategy_info = strategy.get_strategy_info()
    print(f"   策略名称: {strategy_info['name']}")
    print(f"   策略类型: {strategy_info['type']}")
    print(f"   优化方法: {strategy_info['optimization_method']}")
    print(f"   参数设置: {strategy_info['parameters']}")
    
    # 3. 显示历史性能
    print("\n3. 历史性能:")
    perf = strategy.historical_performance
    print(f"   总收益率: {perf['total_return']:.2%}")
    print(f"   夏普比率: {perf['sharpe_ratio']:.3f}")
    print(f"   最大回撤: {perf['max_drawdown']:.2%}")
    print(f"   胜率: {perf['win_rate']:.2%}")
    
    # 4. 生成测试数据
    print("\n4. 生成测试数据...")
    generator = RandomDataGenerator()
    test_data = generator.generate(days=100, random_seed=123)
    print(f"   测试数据: {len(test_data)} 天")
    
    # 5. 计算技术指标
    print("\n5. 计算技术指标...")
    data_with_indicators = strategy.calculate_indicators(test_data)
    print(f"   添加指标: {[col for col in data_with_indicators.columns if col.startswith('MA_')]}")
    
    # 6. 生成交易信号
    print("\n6. 生成交易信号...")
    signals = strategy.generate_signals(test_data)
    
    buy_signals = (signals == 1).sum()
    sell_signals = (signals == -1).sum()
    hold_signals = (signals == 0).sum()
    
    print(f"   买入信号: {buy_signals} 个")
    print(f"   卖出信号: {sell_signals} 个")
    print(f"   持有信号: {hold_signals} 个")
    
    # 7. 显示回测摘要
    print("\n7. 策略回测摘要:")
    print(strategy.backtest_summary())
    
    # 8. 保存信号到文件
    print("8. 保存结果...")
    result_df = pd.DataFrame({
        'close': test_data['close'],
        'MA_short': data_with_indicators['MA_short'],
        'MA_long': data_with_indicators['MA_long'],
        'signal': signals
    })
    
    result_df.to_csv('strategy_test_result.csv')
    print("   ✅ 结果已保存到 strategy_test_result.csv")
    
    print("\n" + "="*50)
    print("🎉 策略测试完成！")
    print("✅ 生成的策略文件可以正常使用")
    print("✅ 包含完整的策略逻辑和历史性能数据")
    print("✅ 可以直接集成到其他交易系统中")


def demonstrate_strategy_usage():
    """演示策略的实际使用方法"""
    if not strategy_available:
        return
    
    print("\n" + "="*60)
    print("📚 策略使用方法演示")
    print("="*60)
    
    print("""
🔧 如何在您的项目中使用生成的策略:

1. 导入策略类:
   from dual_ma_strategy import DualMaStrategy

2. 创建策略实例:
   strategy = DualMaStrategy()

3. 准备数据 (需要包含OHLCV列):
   data = pd.read_csv('your_data.csv', index_col=0, parse_dates=True)

4. 生成交易信号:
   signals = strategy.generate_signals(data)

5. 使用信号进行交易:
   for date, signal in signals.items():
       if signal == 1:
           print(f"{date}: 买入信号")
       elif signal == -1:
           print(f"{date}: 卖出信号")

6. 获取策略详细信息:
   info = strategy.get_strategy_info()
   print(info)

📊 策略文件包含的内容:
- ✅ 完整的策略实现代码
- ✅ ML优化后的参数设置
- ✅ 历史回测性能数据
- ✅ 详细的使用说明和注释
- ✅ 策略特点和适用场景说明

🎯 策略文件的优势:
- 🔄 可重复使用: 保存了优化后的最佳参数
- 📖 易于理解: 包含详细的策略说明
- 🔧 即插即用: 可直接集成到现有系统
- 📊 性能透明: 包含历史回测数据
- 🛠️ 易于修改: 开源代码，可根据需要调整
    """)


if __name__ == "__main__":
    test_generated_strategy()
    demonstrate_strategy_usage()
