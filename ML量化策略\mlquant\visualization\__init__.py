"""
可视化模块

提供量化交易策略的可视化功能，包括：
- StrategyDashboard: 策略性能综合仪表板
- InteractiveCharts: 交互式图表组件
- PerformanceVisualizer: 回测结果可视化工具
- ParameterOptimizationVisualizer: 参数优化过程可视化

支持的可视化库：
- Plotly: 交互式图表
- Matplotlib: 静态图表
- Seaborn: 统计图表
"""

import warnings

# 检查可视化库的可用性
try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    warnings.warn("plotly not available. Interactive charts will not work.")

try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    warnings.warn("matplotlib/seaborn not available. Static charts will not work.")

# 导入可视化组件
try:
    from .dashboard import StrategyDashboard
    DASHBOARD_AVAILABLE = True
except ImportError as e:
    DASHBOARD_AVAILABLE = False
    StrategyDashboard = None
    print(f"Warning: StrategyDashboard not available: {e}")

try:
    from .interactive_charts import InteractiveCharts
    INTERACTIVE_CHARTS_AVAILABLE = True
except ImportError as e:
    INTERACTIVE_CHARTS_AVAILABLE = False
    InteractiveCharts = None
    print(f"Warning: InteractiveCharts not available: {e}")

try:
    from .performance_visualizer import PerformanceVisualizer
    PERFORMANCE_VISUALIZER_AVAILABLE = True
except ImportError as e:
    PERFORMANCE_VISUALIZER_AVAILABLE = False
    PerformanceVisualizer = None
    print(f"Warning: PerformanceVisualizer not available: {e}")

try:
    from .optimization_visualizer import ParameterOptimizationVisualizer
    OPTIMIZATION_VISUALIZER_AVAILABLE = True
except ImportError as e:
    OPTIMIZATION_VISUALIZER_AVAILABLE = False
    ParameterOptimizationVisualizer = None
    print(f"Warning: ParameterOptimizationVisualizer not available: {e}")

# 导出可用的组件
__all__ = []

if DASHBOARD_AVAILABLE:
    __all__.append('StrategyDashboard')

if INTERACTIVE_CHARTS_AVAILABLE:
    __all__.append('InteractiveCharts')

if PERFORMANCE_VISUALIZER_AVAILABLE:
    __all__.append('PerformanceVisualizer')

if OPTIMIZATION_VISUALIZER_AVAILABLE:
    __all__.append('ParameterOptimizationVisualizer')

# 可视化组件注册表
AVAILABLE_VISUALIZERS = {
    'dashboard': StrategyDashboard if DASHBOARD_AVAILABLE else None,
    'interactive_charts': InteractiveCharts if INTERACTIVE_CHARTS_AVAILABLE else None,
    'performance': PerformanceVisualizer if PERFORMANCE_VISUALIZER_AVAILABLE else None,
    'optimization': ParameterOptimizationVisualizer if OPTIMIZATION_VISUALIZER_AVAILABLE else None
}

def get_available_visualizers():
    """获取所有可用的可视化组件"""
    return {k: v for k, v in AVAILABLE_VISUALIZERS.items() if v is not None}

def check_dependencies():
    """检查可视化依赖库的可用性"""
    status = {
        'plotly': PLOTLY_AVAILABLE,
        'matplotlib': MATPLOTLIB_AVAILABLE,
        'dashboard': DASHBOARD_AVAILABLE,
        'interactive_charts': INTERACTIVE_CHARTS_AVAILABLE,
        'performance_visualizer': PERFORMANCE_VISUALIZER_AVAILABLE,
        'optimization_visualizer': OPTIMIZATION_VISUALIZER_AVAILABLE
    }
    
    print("可视化模块依赖状态:")
    for component, available in status.items():
        status_str = "✅ 可用" if available else "❌ 不可用"
        print(f"  {component}: {status_str}")
    
    return status

def create_visualizer(visualizer_type: str, **kwargs):
    """
    创建可视化器实例
    
    Args:
        visualizer_type: 可视化器类型
        **kwargs: 初始化参数
        
    Returns:
        可视化器实例
    """
    available = get_available_visualizers()
    if visualizer_type not in available:
        raise ValueError(f"Visualizer '{visualizer_type}' not available. Available: {list(available.keys())}")
    
    visualizer_class = available[visualizer_type]
    return visualizer_class(**kwargs)
