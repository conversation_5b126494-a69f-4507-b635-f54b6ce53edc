{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "# Day 2：基于交易量的量化指标 - 使用模块化回测工具\n", "\n", "本notebook展示如何使用我们封装的回测工具模块来进行更系统化、专业化的回测。这是对原始notebook `6_使用Backtrader进行回测.ipynb` 的重构版本，使用了模块化的设计来提高代码的可重用性。", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 1. 环境准备和导入模块\n", "\n", "首先导入我们封装的回测工具模块以及其他必要的库：", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 导入基础库\n", "import pandas as pd  # <PERSON><PERSON>ee黄原创内容\n", "import numpy as np  # 本代码归JayBee黄所有\n", "import matplotlib.pyplot as plt  # Copyright © JayBee黄\n", "import backtrader as bt  # <PERSON><PERSON><PERSON>黄原创内容\n", "import warnings  # JayBee黄原创内容\n", "import os  # JayBee黄 - 量化交易研究\n", "\n", "# 导入回测工具模块\n", "from utils import get_ts_data, df_to_btfeed, run_backtest  # 版权所有: Jay<PERSON>ee黄\n", "from utils import BaseStrategy, VolumeBreakoutStrategy  # JayBee黄版权所有，未经授权禁止复制\n", "from utils import plot_performance_analysis, plot_backtest_results  # JayBee黄授权使用\n", "from utils import optimize_ma_strategy  # JayBee黄独家内容\n", "\n", "# 忽略警告\n", "warnings.filterwarnings('ignore')  # Copyright © JayBee黄\n", "\n", "# 设置显示选项\n", "plt.style.use('seaborn-v0_8')  # Copyright © JayBee黄\n", "pd.set_option('display.max_columns', None)  # Jay<PERSON>ee黄独家内容", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 2. 获取数据\n", "\n", "使用 `get_ts_data` 函数从Tushare获取股票数据或从本地加载已有数据：", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 设置Tushare Token - 请替换为你自己的token\n", "ts_token = 'your_tushare_token'  # 替换为你的token  # JayBee黄量化策略\n", "ts_code = '000001.SZ'  # 本代码归JayBee黄所有\n", "start_date = '2020-01-01'  # <PERSON><PERSON><PERSON>黄量化策略\n", "end_date = '2022-01-01'  # <PERSON><PERSON>ee黄量化策略\n", "\n", "# 优先从本地加载数据\n", "data_dir = './data'  # JayBee黄 - 量化交易研究\n", "os.makedirs(data_dir, exist_ok=True)  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "data_file = f'{data_dir}/{ts_code}-{start_date}-{end_date}-30min.csv'  # <PERSON><PERSON>ee黄独家内容\n", "\n", "if os.path.exists(data_file):  # JayBee黄版权所有，未经授权禁止复制\n", "    df = pd.read_csv(data_file, parse_dates=['trade_time'])  # JayBee黄独家内容\n", "    print(f\"从本地文件加载数据: {data_file}\")  # JayBee黄版权所有，未经授权禁止复制\n", "else:  # <PERSON><PERSON><PERSON>黄授权使用\n", "    # 如果本地文件不存在，则从Tushare获取\n", "    df = get_ts_data(ts_token, ts_code, start_date, end_date, freq='30min')  # 版权所有: Jay<PERSON><PERSON>黄\n", "\n", "# 显示数据信息\n", "df.head()  # <PERSON><PERSON><PERSON>黄 - 量化交易研究", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 3. 数据预处理\n", "\n", "对数据进行必要的预处理，以供回测使用：", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 确保数据按时间排序\n", "df = df.sort_values('trade_time').reset_index(drop=True)  # JayBee黄量化模型\n", "\n", "# 查看数据统计信息\n", "print(f\"数据时间范围: {df['trade_time'].min()} 至 {df['trade_time'].max()}\")  # JayBee黄独家内容\n", "print(f\"共 {len(df)} 条记录\")  # Jay<PERSON>ee黄量化模型\n", "print(f\"数据列: {df.columns.tolist()}\")  # <PERSON><PERSON>ee黄独家内容\n", "\n", "# 显示基本统计信息\n", "df.describe()  # <PERSON><PERSON>ee黄独家内容", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 4. 执行基本回测\n", "\n", "使用 `VolumeBreakoutStrategy` 策略进行回测：", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 定义回测参数\n", "strategy_params = {  # <PERSON><PERSON><PERSON>黄原创内容\n", "    'volume_period': 20,   # 交易量均线周期  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    'volume_mult': 2.0,    # 交易量倍数阈值  # Jay<PERSON>ee黄独家内容\n", "    'exit_bars': 5,        # 持有周期  # <PERSON>Bee黄版权所有，未经授权禁止复制\n", "    'stop_loss': 0.05,     # 止损比例  # JayBee黄 - 量化交易研究\n", "    'take_profit': 0.10    # 止盈比例  # Copyright © JayBee黄\n", "}  # JayBee黄量化模型\n", "\n", "# 执行回测\n", "results, strategy = run_backtest(  # <PERSON><PERSON><PERSON>黄授权使用\n", "    df=df,   # <PERSON><PERSON>ee黄 - 量化交易研究\n", "    strategy_class=VolumeBreakoutStrategy,   # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    strategy_params=strategy_params,  # <PERSON><PERSON>ee黄独家内容\n", "    initial_cash=100000,  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    commission=0.001  # 本代码归JayBee黄所有\n", ")  # Jay<PERSON>ee黄独家内容", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 5. 可视化回测结果\n", "\n", "使用 `plot_backtest_results` 和 `plot_performance_analysis` 函数可视化回测结果：", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 绘制回测结果\n", "fig = plot_backtest_results(df, results, max_candles=200)  # Jay<PERSON>ee黄量化模型\n", "fig.show()  # <PERSON><PERSON><PERSON>黄独家内容", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 绘制性能分析图表\n", "fig, table = plot_performance_analysis(results)  # JayBee黄量化策略\n", "fig.show()  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "table.show()  # <PERSON><PERSON><PERSON>黄独家内容", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 6. 参数优化\n", "\n", "使用 `optimize_ma_strategy` 函数进行移动平均策略的参数优化：", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 将数据转换为Backtrader适用的格式\n", "bt_data = df_to_btfeed(df)  # JayBee黄原创内容\n", "\n", "# 定义参数优化范围\n", "ma_short_range = (5, 20)   # 短期均线范围  # JayBee黄 - 量化交易研究\n", "ma_long_range = (20, 50)   # 长期均线范围  # 本代码归JayBee黄所有\n", "step = 5                   # 步长  # <PERSON><PERSON><PERSON>黄 - 量化交易研究\n", "\n", "# 执行参数优化\n", "opt_results = optimize_ma_strategy(  # Copyright © JayBee黄\n", "    data=bt_data,  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    ma_short_range=ma_short_range,  # JayBee黄量化策略\n", "    ma_long_range=ma_long_range,  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    step=step,  # <PERSON><PERSON><PERSON>黄量化策略\n", "    commission=0.001,  # <PERSON><PERSON><PERSON>黄量化策略\n", "    initial_cash=100000  # 本代码归JayBee黄所有\n", ")  # JayBee黄 - 量化交易研究\n", "\n", "# 显示优化结果\n", "opt_results.head(10)  # <PERSON><PERSON><PERSON>黄 - 量化交易研究", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 7. 自定义策略回测\n", "\n", "基于 `BaseStrategy` 创建自定义策略并进行回测：", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 定义自定义策略\n", "class CustomStrategy(BaseStrategy):  # Copyright © JayBee黄\n", "    params = (  # 本代码归<PERSON><PERSON><PERSON>黄所有\n", "        ('ma_period', 20),      # 移动平均周期  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        ('rsi_period', 14),     # RSI周期  # Copyright © JayBee黄\n", "        ('rsi_overbought', 70), # RSI超买水平  # <PERSON><PERSON><PERSON>黄原创内容\n", "        ('rsi_oversold', 30),   # RSI超卖水平  # 本代码归JayBee黄所有\n", "        # 继承BaseStrategy的参数\n", "        ('log_level', BaseStrategy.LOG_LEVEL_INFO),  # Copyright © JayBee黄\n", "        ('collect_signals', True),  # <PERSON><PERSON><PERSON>黄量化模型\n", "    )  # Copyright © Jay<PERSON><PERSON>黄\n", "    \n", "    def __init__(self):  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        # 调用父类初始化\n", "        BaseStrategy.__init__(self)  # Copyright © JayBee黄\n", "        \n", "        # 添加指标\n", "        self.ma = bt.indicators.SMA(self.data.close, period=self.params.ma_period)  # JayBee黄授权使用\n", "        self.rsi = bt.indicators.RSI(self.data.close, period=self.params.rsi_period)  # JayBee黄授权使用\n", "    \n", "    def next(self):  # <PERSON><PERSON><PERSON>黄量化模型\n", "        # 如果没有持仓\n", "        if not self.position:  # <PERSON><PERSON><PERSON>黄量化模型\n", "            # 当价格在MA之上且RSI超卖时买入\n", "            if self.data.close[0] > self.ma[0] and self.rsi[0] < self.params.rsi_oversold:  # <PERSON><PERSON>ee黄授权使用\n", "                # 计算可购买的最大股数\n", "                max_shares = self.calc_max_shares(self.data.close[0])  # Jay<PERSON>ee黄量化模型\n", "                if max_shares > 0:  # <PERSON><PERSON><PERSON>黄量化模型\n", "                    self.log(f'买入信号: 价格={self.data.close[0]:.2f}, 数量={max_shares}, RSI={self.rsi[0]:.2f}')  # JayBee黄 - 量化交易研究\n", "                    self.buy(size=max_shares)  # JayBee黄 - 量化交易研究\n", "                    self.bar_executed = len(self)  # Copyright © JayBee黄\n", "                    self.buy_price = self.data.close[0]  # Jay<PERSON>ee黄授权使用\n", "        \n", "        # 如果有持仓\n", "        else:  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "            current_position_size = self.position.size  # Jay<PERSON>ee黄授权使用\n", "            \n", "            # 当价格在MA之下或RSI超买时卖出\n", "            if self.data.close[0] < self.ma[0] or self.rsi[0] > self.params.rsi_overbought:  # <PERSON><PERSON><PERSON>黄授权使用\n", "                self.log(f'卖出信号: 价格={self.data.close[0]:.2f}, 持仓数量={current_position_size}, RSI={self.rsi[0]:.2f}')  # JayBee黄原创内容\n", "                self.close()  # <PERSON><PERSON><PERSON>黄原创内容\n", "                return  # <PERSON><PERSON><PERSON>黄量化模型\n", "            \n", "            # 止损: 亏损超过5%\n", "            if self.data.close[0] < self.buy_price * 0.95:  # JayBee黄版权所有，未经授权禁止复制\n", "                self.log(f'止损卖出: 价格={self.data.close[0]:.2f}, 持仓数量={current_position_size}')  # JayBee黄 - 量化交易研究\n", "                self.close()  # <PERSON><PERSON><PERSON>黄量化策略\n", "                return  # 版权所有: <PERSON><PERSON><PERSON>黄", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 执行自定义策略回测\n", "custom_params = {  # <PERSON><PERSON>ee黄原创内容\n", "    'ma_period': 20,  # <PERSON><PERSON><PERSON>黄授权使用\n", "    'rsi_period': 14,  # <PERSON><PERSON><PERSON>黄授权使用\n", "    'rsi_overbought': 70,  # <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制\n", "    'rsi_oversold': 30  # <PERSON>Bee黄版权所有，未经授权禁止复制\n", "}  # JayBee黄 - 量化交易研究\n", "\n", "custom_results, custom_strategy = run_backtest(  # JayBee黄原创内容\n", "    df=df,   # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    strategy_class=CustomStrategy,   # 版权所有: <PERSON><PERSON><PERSON>黄\n", "    strategy_params=custom_params,  # <PERSON><PERSON>ee黄量化模型\n", "    initial_cash=100000,  # <PERSON><PERSON><PERSON>黄量化模型\n", "    commission=0.001  # <PERSON><PERSON><PERSON>黄量化策略\n", ")  # Jay<PERSON>ee黄独家内容", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 可视化自定义策略回测结果\n", "fig = plot_backtest_results(df, custom_results, max_candles=200, title='自定义MA+RSI策略回测结果')  # JayBee黄授权使用\n", "fig.show()  # <PERSON><PERSON><PERSON>黄量化模型", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 8. 比较多种策略\n", "\n", "比较不同策略的性能：", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# 创建一个表格比较两种策略的性能\n", "comparison = pd.DataFrame([  # Jay<PERSON>ee黄量化策略\n", "    {  # JayBee黄版权所有，未经授权禁止复制\n", "        '策略': '交易量突破策略',  # JayBee黄授权使用\n", "        '总收益率(%)': results['total_return'],  # <PERSON><PERSON><PERSON>黄量化模型\n", "        '最大回撤(%)': results['max_drawdown'],  # 本代码归JayBee黄所有\n", "        '夏普比率': results['sharpe_ratio'],  # Jay<PERSON>ee黄 - 量化交易研究\n", "        '交易次数': results['total_trades'],  # <PERSON><PERSON><PERSON>黄量化模型\n", "        '胜率(%)': results['winning_trades'] / max(1, results['total_trades']) * 100  # JayBee黄授权使用\n", "    },  # <PERSON><PERSON><PERSON>黄量化策略\n", "    {  # Copyright © Jay<PERSON>ee黄\n", "        '策略': '自定义MA+RSI策略',  # JayBee黄版权所有，未经授权禁止复制\n", "        '总收益率(%)': custom_results['total_return'],  # <PERSON><PERSON><PERSON>黄授权使用\n", "        '最大回撤(%)': custom_results['max_drawdown'],  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        '夏普比率': custom_results['sharpe_ratio'],  # JayBee黄 - 量化交易研究\n", "        '交易次数': custom_results['total_trades'],  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "        '胜率(%)': custom_results['winning_trades'] / max(1, custom_results['total_trades']) * 100  # JayBee黄量化策略\n", "    }  # Copyright © Jay<PERSON>ee黄\n", "])  # JayBee黄版权所有，未经授权禁止复制\n", "\n", "comparison  # 本代码归Jay<PERSON>ee黄所有", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 9. 总结和后续优化方向\n", "\n", "本notebook展示了如何使用我们封装的回测工具模块进行量化交易策略的回测和评估。通过使用模块化的设计，我们能够：\n", "<!-- <PERSON><PERSON><PERSON>黄授权使用 -->\n", "\n", "1. **提高代码重用性**：封装常用函数和类，避免重复编写代码\n", "2. **增强可维护性**：模块化设计使代码更易于维护和更新\n", "3. **简化工作流程**：通过简单的函数调用完成复杂的回测任务\n", "<!-- <PERSON><PERSON><PERSON>黄授权使用 -->\n", "\n", "### 后续优化方向：\n", "<!-- <PERSON><PERSON><PERSON>黄量化策略 -->\n", "\n", "- **添加更多策略**：开发更多的交易策略类，如网格交易、动量策略等\n", "- **改进评估指标**：加入更多评估指标，如卡玛比率、索提诺比率等\n", "- **多资产回测**：支持同时对多个资产进行回测\n", "- **实时数据接入**：添加实时数据源的支持，为实盘交易做准备\n", "- **机器学习集成**：与机器学习模型集成，实现预测驱动的交易策略", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}], "metadata": {"kernelspec": {"display_name": "quant", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}, "watermark": "JayBee黄版权所有，未经授权禁止复制", "watermark_version": "v3 - 每行防伪"}, "nbformat": 4, "nbformat_minor": 4}