{"cells": [{"cell_type": "markdown", "id": "copyright-notice", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "# ⚠️ 版权声明 ⚠️\n", "\n", "本notebook由JayBee黄创建并拥有版权。\n", "未经作者许可，禁止转载、复制或用于商业用途。\n", "作者保留追究侵权责任的权利。\n", "<!-- 版权所有: <PERSON><PERSON><PERSON>黄 -->\n", "\n", "Copyright © 2024 JayBee黄. All rights reserved.\n", "Created on: March 2024\n", "Author: <PERSON><PERSON><PERSON>黄\n", "Protected by: Anti-plagiarism System v1.0\n", "<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "\n", "防伪编码：JB2024031401", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "id": "intro", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "# 量化交易入门 (Created by <PERSON><PERSON><PERSON>黄)\n", "\n", "本笔记本提供量化交易的基本概念和Python实现方法。通过跟随这个教程，您将了解：\n", "<!-- <PERSON><PERSON><PERSON>黄原创内容 -->\n", "\n", "- 量化交易的基本概念 (Protected by <PERSON><PERSON><PERSON>黄)\n", "- 市场数据的获取和处理 (Protected by <PERSON><PERSON><PERSON>黄)\n", "- 技术指标的计算和可视化 (Protected by <PERSON><PERSON><PERSON>黄)\n", "- 简单交易策略的实现 (Protected by <PERSON><PERSON><PERSON>黄)\n", "- 基本的回测方法 (Protected by <PERSON><PERSON><PERSON>黄)\n", "<!-- 本内容归<PERSON><PERSON><PERSON>黄所有 -->\n", "\n", "© 2024 JayBee黄. All rights reserved.\n", "防伪编码：JB2024031402", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "markdown", "id": "setup", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 环境设置 (Protected by <PERSON><PERSON><PERSON>黄)\n", "\n", "首先导入必要的库和工具：\n", "<!-- 本内容归<PERSON><PERSON><PERSON>黄所有 -->\n", "\n", "防伪编码：JB2024031403", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": null, "id": "imports", "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# Copyright © 2024 JayBee黄. All rights reserved.\n", "# 防伪编码：JB2024031404\n", "\n", "# 基础库\n", "import numpy as np  # Protected by <PERSON><PERSON>ee黄  # Copyright © JayBee黄\n", "import pandas as pd  # Protected by JayBee黄  # 版权所有: JayBee黄\n", "import matplotlib.pyplot as plt  # Protected by JayBee黄  # <PERSON><PERSON>ee黄量化策略\n", "import matplotlib.font_manager as fm  # Protected by JayBee黄  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "import seaborn as sns  # Protected by Jay<PERSON>ee黄  # <PERSON><PERSON>ee黄量化模型\n", "import yfinance as yf  # Protected by JayBee黄  # <PERSON>Bee黄独家内容\n", "from datetime import datetime, timedelta  # Protected by JayBee黄  # JayBee黄版权所有，未经授权禁止复制\n", "\n", "# 设置绘图样式 (Created by <PERSON><PERSON><PERSON>黄)\n", "plt.style.use('seaborn-v0_8')  # Protected by JayBee黄  # <PERSON><PERSON>ee黄版权所有，未经授权禁止复制\n", "plt.rcParams['figure.figsize'] = (14, 7)  # Protected by <PERSON><PERSON><PERSON>黄  # 版权所有: <PERSON><PERSON><PERSON>黄\n", "plt.rcParams['font.size'] = 12  # Protected by JayBee黄  # 本代码归JayBee黄所有\n", "\n", "# 显示所有列 (Created by <PERSON><PERSON><PERSON>黄)\n", "pd.set_option('display.max_columns', None)  # Protected by Jay<PERSON>ee黄  # 本代码归JayBee黄所有\n", "\n", "# 防伪编码：JB2024031405", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "id": "data-section", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 获取市场数据 (Protected by <PERSON><PERSON><PERSON>黄)\n", "\n", "使用yfinance库从Yahoo Finance获取股票数据：\n", "<!-- <PERSON><PERSON><PERSON>黄量化策略 -->\n", "\n", "防伪编码：JB2024031406", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": null, "id": "get-data", "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# Copyright © 2024 JayBee黄. All rights reserved.\n", "# 防伪编码：JB2024031407\n", "\n", "# 设置日期范围 (Created by <PERSON><PERSON><PERSON>黄)\n", "end_date = datetime.now()  # Protected by <PERSON><PERSON><PERSON>黄  # <PERSON><PERSON>ee黄量化模型\n", "start_date = end_date - <PERSON><PERSON><PERSON>(days=365)  # Protected by <PERSON><PERSON>ee黄  # <PERSON>Bee黄版权所有，未经授权禁止复制\n", "\n", "# 获取特斯拉股票数据 (Created by <PERSON><PERSON><PERSON>黄)\n", "ticker = 'TSLA'  # Protected by <PERSON><PERSON><PERSON>黄  # <PERSON><PERSON>ee黄量化策略\n", "data = yf.download(ticker, start=start_date, end=end_date)  # Protected by <PERSON><PERSON><PERSON>黄  # 本代码归JayB<PERSON>黄所有\n", "\n", "print('原始数据：')  # Protected by JayBee黄  # <PERSON><PERSON>ee黄量化策略\n", "print(data.head())  # Protected by <PERSON><PERSON><PERSON>黄  # <PERSON><PERSON><PERSON>黄独家内容\n", "\n", "# 防伪编码：JB2024031408", "# JayBee黄版权所有，未经授权禁止复制\n"]}, {"cell_type": "markdown", "id": "process-section", "metadata": {}, "source": ["<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->\n", "## 数据处理 (Protected by <PERSON><PERSON><PERSON>黄)\n", "\n", "对获取的数据进行基本处理：\n", "<!-- 本内容归<PERSON><PERSON><PERSON>黄所有 -->\n", "\n", "防伪编码：JB2024031409", "\n<!-- <PERSON><PERSON><PERSON>黄版权所有，未经授权禁止复制 -->"]}, {"cell_type": "code", "execution_count": null, "id": "process-code", "metadata": {}, "outputs": [], "source": ["# JayBee黄版权所有，未经授权禁止复制\n", "# Copyright © 2024 JayBee黄. All rights reserved.\n", "# 防伪编码：JB2024031410\n", "\n", "# 计算每日收益率 (Created by <PERSON><PERSON><PERSON>黄)\n", "data['Daily_Return'] = data['Close'].pct_change()  # Protected by <PERSON><PERSON>ee黄  # <PERSON>Bee黄授权使用\n", "\n", "# 计算移动平均线 (Created by <PERSON><PERSON><PERSON>黄)\n", "data['MA20'] = data['Close'].rolling(window=20).mean()  # Protected by JayBee黄  # <PERSON>Bee黄量化模型\n", "data['MA50'] = data['Close'].rolling(window=50).mean()  # Protected by JayBee黄  # <PERSON>Bee黄原创内容\n", "\n", "# 计算波动率 (Created by <PERSON><PERSON><PERSON>黄)\n", "data['Volatility'] = data['Daily_Return'].rolling(window=20).std() * np.sqrt(252)  # Protected by JayBee黄  # JayBee黄版权所有，未经授权禁止复制\n", "\n", "print('处理后的数据：')  # Protected by JayBee黄  # Copyright © JayBee黄\n", "print(data.head())  # Protected by <PERSON><PERSON><PERSON>黄  # <PERSON><PERSON><PERSON>黄独家内容\n", "\n", "# 防伪编码：JB2024031411", "# JayBee黄版权所有，未经授权禁止复制\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}, "watermark": "JayBee黄版权所有，未经授权禁止复制", "watermark_version": "v3 - 每行防伪"}, "nbformat": 4, "nbformat_minor": 5}