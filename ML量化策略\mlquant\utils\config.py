"""
配置管理器

提供配置文件的加载和管理功能。
"""

import os
import json
from typing import Dict, Any

try:
    import yaml
    HAS_YAML = True
except ImportError:
    HAS_YAML = False


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if os.path.exists(self.config_path):
            with open(self.config_path, 'r', encoding='utf-8') as f:
                if HAS_YAML and self.config_path.endswith('.yaml'):
                    return yaml.safe_load(f)
                else:
                    # 简化版本：直接返回默认配置
                    return self._get_default_config()
        else:
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'data': {
                'initial_price': 100.0,
                'volatility': 0.02,
                'trend': 0.0001
            },
            'backtest': {
                'initial_capital': 100000.0,
                'commission': 0.001
            },
            'optimization': {
                'genetic': {
                    'population_size': 50,
                    'generations': 100,
                    'mutation_rate': 0.1,
                    'crossover_rate': 0.8
                },
                'grid_search': {
                    'grid_points': 10
                },
                'random_search': {
                    'n_iterations': 100
                }
            }
        }
    
    def get(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self):
        """保存配置到文件"""
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)

        with open(self.config_path, 'w', encoding='utf-8') as f:
            if HAS_YAML and self.config_path.endswith('.yaml'):
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            else:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
