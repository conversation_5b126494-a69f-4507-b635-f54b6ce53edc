"""
双均线策略

基于双移动平均线交叉的交易策略实现。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List

from .base import BaseStrategy, SignalType
from ..indicators.moving_average import SimpleMovingAverage
from ..utils.exceptions import StrategyError, ValidationError


class DualMAStrategy(BaseStrategy):
    """双均线交叉策略"""
    
    def __init__(self, short_window: int = 20, long_window: int = 50,
                 ma_type: str = 'sma', column: str = 'close',
                 signal_delay: int = 1, **kwargs):
        """
        初始化双均线策略
        
        Args:
            short_window: 短期均线窗口
            long_window: 长期均线窗口
            ma_type: 均线类型 ('sma', 'ema', 'wma')
            column: 计算列名
            signal_delay: 信号延迟天数
            **kwargs: 其他参数
        """
        super().__init__(
            name=f"DualMA_{short_window}_{long_window}",
            short_window=short_window,
            long_window=long_window,
            ma_type=ma_type,
            column=column,
            signal_delay=signal_delay,
            **kwargs
        )
        
        self.short_window = short_window
        self.long_window = long_window
        self.ma_type = ma_type.lower()
        self.column = column
        self.signal_delay = signal_delay
        
        # 创建指标实例
        self._create_indicators()
    
    def _validate_params(self, **params) -> None:
        """验证策略参数"""
        short_window = params.get('short_window', self.short_window)
        long_window = params.get('long_window', self.long_window)
        ma_type = params.get('ma_type', self.ma_type)
        signal_delay = params.get('signal_delay', self.signal_delay)
        
        # 验证窗口大小
        if not isinstance(short_window, int) or short_window <= 0:
            raise ValidationError(
                "short_window必须是正整数",
                "short_window",
                short_window,
                "positive integer"
            )
        
        if not isinstance(long_window, int) or long_window <= 0:
            raise ValidationError(
                "long_window必须是正整数",
                "long_window", 
                long_window,
                "positive integer"
            )
        
        if short_window >= long_window:
            raise ValidationError(
                "short_window必须小于long_window",
                "window_comparison",
                f"short={short_window}, long={long_window}",
                "short < long"
            )
        
        # 验证均线类型
        valid_ma_types = ['sma', 'ema', 'wma']
        if ma_type not in valid_ma_types:
            raise ValidationError(
                f"ma_type必须是 {valid_ma_types} 之一",
                "ma_type",
                ma_type,
                f"one of {valid_ma_types}"
            )
        
        # 验证信号延迟
        if not isinstance(signal_delay, int) or signal_delay < 0:
            raise ValidationError(
                "signal_delay必须是非负整数",
                "signal_delay",
                signal_delay,
                "non-negative integer"
            )
    
    def _create_indicators(self) -> None:
        """创建技术指标实例"""
        try:
            if self.ma_type == 'sma':
                from ..indicators.moving_average import SimpleMovingAverage
                self.short_ma = SimpleMovingAverage(self.short_window, self.column)
                self.long_ma = SimpleMovingAverage(self.long_window, self.column)
            elif self.ma_type == 'ema':
                from ..indicators.moving_average import ExponentialMovingAverage
                self.short_ma = ExponentialMovingAverage(self.short_window, self.column)
                self.long_ma = ExponentialMovingAverage(self.long_window, self.column)
            elif self.ma_type == 'wma':
                from ..indicators.moving_average import WeightedMovingAverage
                self.short_ma = WeightedMovingAverage(self.short_window, self.column)
                self.long_ma = WeightedMovingAverage(self.long_window, self.column)
            else:
                raise StrategyError(f"不支持的均线类型: {self.ma_type}", self.name, self.params)
                
        except ImportError as e:
            raise StrategyError(f"导入指标模块失败: {e}", self.name, self.params)
    
    def get_required_columns(self) -> List[str]:
        """获取策略所需的数据列"""
        return [self.column]
    
    def generate_signals(self, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        生成双均线交叉信号
        
        Args:
            data: 价格数据
            **kwargs: 额外参数
            
        Returns:
            交易信号序列
        """
        try:
            self.logger.info(f"生成双均线信号: {self.short_window}/{self.long_window}")
            
            # 计算移动平均线
            short_ma = self.short_ma.calculate(data)
            long_ma = self.long_ma.calculate(data)
            
            # 生成信号
            signals = pd.Series(index=data.index, dtype=float, name='signals')
            signals.fillna(0, inplace=True)
            
            # 计算均线交叉
            ma_diff = short_ma - long_ma
            ma_diff_prev = ma_diff.shift(1)
            
            # 金叉：短均线上穿长均线 -> 买入信号
            golden_cross = (ma_diff > 0) & (ma_diff_prev <= 0)
            signals[golden_cross] = SignalType.BUY.value
            
            # 死叉：短均线下穿长均线 -> 卖出信号
            death_cross = (ma_diff < 0) & (ma_diff_prev >= 0)
            signals[death_cross] = SignalType.SELL.value
            
            # 记录信号统计
            buy_signals = (signals == SignalType.BUY.value).sum()
            sell_signals = (signals == SignalType.SELL.value).sum()
            
            self.logger.info(f"信号生成完成: 买入信号 {buy_signals} 个, 卖出信号 {sell_signals} 个")
            
            # 存储均线数据用于分析
            self._short_ma = short_ma
            self._long_ma = long_ma
            
            return signals
            
        except Exception as e:
            self.logger.error(f"生成双均线信号失败: {e}")
            raise StrategyError(f"生成信号失败: {e}", self.name, self.params)
    
    def get_indicators(self) -> Dict[str, pd.Series]:
        """
        获取计算的技术指标
        
        Returns:
            指标字典
        """
        indicators = {}
        
        if hasattr(self, '_short_ma'):
            indicators[f'MA{self.short_window}'] = self._short_ma
        
        if hasattr(self, '_long_ma'):
            indicators[f'MA{self.long_window}'] = self._long_ma
        
        return indicators
    
    def analyze_crossovers(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        分析均线交叉情况
        
        Args:
            data: 价格数据
            
        Returns:
            交叉分析结果
        """
        try:
            if not hasattr(self, '_short_ma') or not hasattr(self, '_long_ma'):
                # 如果还没有计算指标，先计算
                self.generate_signals(data)
            
            short_ma = self._short_ma
            long_ma = self._long_ma
            
            # 计算交叉点
            ma_diff = short_ma - long_ma
            crossovers = ma_diff.diff()
            
            # 统计交叉情况
            golden_crosses = (crossovers > 0).sum()  # 金叉次数
            death_crosses = (crossovers < 0).sum()   # 死叉次数
            
            # 计算均线距离统计
            ma_distance = (short_ma - long_ma) / long_ma * 100  # 百分比距离
            
            analysis = {
                'golden_crosses': golden_crosses,
                'death_crosses': death_crosses,
                'total_crosses': golden_crosses + death_crosses,
                'avg_ma_distance': ma_distance.mean(),
                'max_ma_distance': ma_distance.max(),
                'min_ma_distance': ma_distance.min(),
                'ma_distance_std': ma_distance.std(),
                'short_ma_trend': 'up' if short_ma.iloc[-1] > short_ma.iloc[-10] else 'down',
                'long_ma_trend': 'up' if long_ma.iloc[-1] > long_ma.iloc[-10] else 'down',
                'current_position': 'above' if short_ma.iloc[-1] > long_ma.iloc[-1] else 'below'
            }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析均线交叉失败: {e}")
            raise StrategyError(f"分析交叉失败: {e}", self.name, self.params)
    
    def optimize_parameters(self, data: pd.DataFrame, 
                          short_range: tuple = (5, 30),
                          long_range: tuple = (30, 100),
                          step: int = 5) -> Dict[str, Any]:
        """
        优化策略参数
        
        Args:
            data: 价格数据
            short_range: 短均线窗口范围
            long_range: 长均线窗口范围
            step: 步长
            
        Returns:
            优化结果
        """
        try:
            self.logger.info("开始参数优化")
            
            best_params = None
            best_score = -np.inf
            results = []
            
            for short_win in range(short_range[0], short_range[1] + 1, step):
                for long_win in range(long_range[0], long_range[1] + 1, step):
                    if short_win >= long_win:
                        continue
                    
                    # 创建临时策略
                    temp_strategy = DualMAStrategy(
                        short_window=short_win,
                        long_window=long_win,
                        ma_type=self.ma_type,
                        column=self.column,
                        signal_delay=self.signal_delay
                    )
                    
                    # 运行策略
                    result = temp_strategy.run(data)
                    
                    # 计算评分（这里使用简单的信号频率作为评分）
                    score = result['stats']['signal_frequency']
                    
                    results.append({
                        'short_window': short_win,
                        'long_window': long_win,
                        'score': score,
                        'total_trades': result['stats']['total_trades']
                    })
                    
                    if score > best_score:
                        best_score = score
                        best_params = (short_win, long_win)
            
            optimization_result = {
                'best_params': best_params,
                'best_score': best_score,
                'all_results': results,
                'total_combinations': len(results)
            }
            
            self.logger.info(f"参数优化完成: 最佳参数 {best_params}, 得分 {best_score:.4f}")
            return optimization_result
            
        except Exception as e:
            self.logger.error(f"参数优化失败: {e}")
            raise StrategyError(f"参数优化失败: {e}", self.name, self.params)
