"""
技术指标计算模块

提供常用技术指标的计算功能，参考quant-learning-main项目的指标库。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional


class TechnicalIndicators:
    """技术指标计算器"""
    
    @staticmethod
    def sma(data: pd.Series, window: int) -> pd.Series:
        """
        简单移动平均线
        
        Args:
            data: 价格序列
            window: 窗口大小
            
        Returns:
            SMA序列
        """
        return data.rolling(window=window).mean()
    
    @staticmethod
    def ema(data: pd.Series, window: int) -> pd.Series:
        """
        指数移动平均线
        
        Args:
            data: 价格序列
            window: 窗口大小
            
        Returns:
            EMA序列
        """
        return data.ewm(span=window, adjust=False).mean()
    
    @staticmethod
    def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """
        MACD指标
        
        Args:
            data: 价格序列
            fast: 快线周期
            slow: 慢线周期
            signal: 信号线周期
            
        Returns:
            包含MACD线、信号线和柱状图的字典
        """
        ema_fast = TechnicalIndicators.ema(data, fast)
        ema_slow = TechnicalIndicators.ema(data, slow)
        
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
    
    @staticmethod
    def rsi(data: pd.Series, window: int = 14) -> pd.Series:
        """
        相对强弱指数
        
        Args:
            data: 价格序列
            window: 窗口大小
            
        Returns:
            RSI序列
        """
        delta = data.diff()
        up = delta.clip(lower=0)
        down = -1 * delta.clip(upper=0)
        
        ema_up = up.ewm(com=window-1, adjust=False).mean()
        ema_down = down.ewm(com=window-1, adjust=False).mean()
        
        rs = ema_up / ema_down
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    @staticmethod
    def bollinger_bands(data: pd.Series, window: int = 20, std_dev: float = 2) -> Dict[str, pd.Series]:
        """
        布林带
        
        Args:
            data: 价格序列
            window: 窗口大小
            std_dev: 标准差倍数
            
        Returns:
            包含上轨、中轨、下轨的字典
        """
        middle = data.rolling(window=window).mean()
        std = data.rolling(window=window).std()
        
        upper = middle + (std * std_dev)
        lower = middle - (std * std_dev)
        
        return {
            'upper': upper,
            'middle': middle,
            'lower': lower
        }
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
        """
        平均真实范围
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            window: 窗口大小
            
        Returns:
            ATR序列
        """
        prev_close = close.shift(1)
        
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=window).mean()
        
        return atr
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, 
                   k_window: int = 14, d_window: int = 3) -> Dict[str, pd.Series]:
        """
        随机指标
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            k_window: K线窗口
            d_window: D线窗口
            
        Returns:
            包含%K和%D的字典
        """
        lowest_low = low.rolling(window=k_window).min()
        highest_high = high.rolling(window=k_window).max()
        
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_window).mean()
        
        return {
            'k_percent': k_percent,
            'd_percent': d_percent
        }
    
    @staticmethod
    def obv(close: pd.Series, volume: pd.Series) -> pd.Series:
        """
        能量潮指标
        
        Args:
            close: 收盘价序列
            volume: 成交量序列
            
        Returns:
            OBV序列
        """
        price_change = close.diff()
        obv = pd.Series(index=close.index, dtype=float)
        obv.iloc[0] = volume.iloc[0]
        
        for i in range(1, len(close)):
            if price_change.iloc[i] > 0:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif price_change.iloc[i] < 0:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]
        
        return obv
    
    @staticmethod
    def vwap(high: pd.Series, low: pd.Series, close: pd.Series, volume: pd.Series) -> pd.Series:
        """
        成交量加权平均价格
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            volume: 成交量序列
            
        Returns:
            VWAP序列
        """
        typical_price = (high + low + close) / 3
        vwap = (typical_price * volume).cumsum() / volume.cumsum()
        
        return vwap
    
    @classmethod
    def calculate_all_indicators(cls, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算所有技术指标
        
        Args:
            data: 包含OHLCV的价格数据
            
        Returns:
            包含所有指标的数据框
        """
        result = data.copy()
        
        # 移动平均线
        for window in [5, 10, 20, 50, 200]:
            result[f'sma_{window}'] = cls.sma(data['close'], window)
            result[f'ema_{window}'] = cls.ema(data['close'], window)
        
        # MACD
        macd_data = cls.macd(data['close'])
        result['macd'] = macd_data['macd']
        result['macd_signal'] = macd_data['signal']
        result['macd_histogram'] = macd_data['histogram']
        
        # RSI
        result['rsi'] = cls.rsi(data['close'])
        
        # 布林带
        bb_data = cls.bollinger_bands(data['close'])
        result['bb_upper'] = bb_data['upper']
        result['bb_middle'] = bb_data['middle']
        result['bb_lower'] = bb_data['lower']
        
        # ATR
        result['atr'] = cls.atr(data['high'], data['low'], data['close'])
        
        # 随机指标
        stoch_data = cls.stochastic(data['high'], data['low'], data['close'])
        result['stoch_k'] = stoch_data['k_percent']
        result['stoch_d'] = stoch_data['d_percent']
        
        # 成交量指标
        result['obv'] = cls.obv(data['close'], data['volume'])
        result['vwap'] = cls.vwap(data['high'], data['low'], data['close'], data['volume'])
        
        # 波动率
        result['volatility'] = data['close'].pct_change().rolling(window=20).std() * np.sqrt(252)
        
        return result
