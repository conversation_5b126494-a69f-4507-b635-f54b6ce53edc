# 欢迎添加微信：sis_and_bro，加入社群：jbhuang.circle.so

这是一个系统化学习量化金融的项目，用于学习和实践量化交易策略。

## 项目结构

- `data/`: 存储市场数据和其他数据集
- `strategies/`: 包含不同的交易策略实现
- `notebooks/`: Jupyter notebooks用于交互式学习和策略回测
- `utils/`: 实用工具函数

## 环境设置

推荐使用Anaconda创建一个独立的Python环境：

```bash
# 创建新环境
conda create -n quant python=3.8
# 激活环境
conda activate quant
# 安装依赖
pip install -r requirements.txt
```

## 快速开始

1. 安装所有依赖
2. 打开Jupyter Notebook: `jupyter notebook`
3. 打开`notebooks/01_introduction.ipynb`开始学习

## 学习路径

1. 量化交易核心概念
2. 数据获取与处理
3. 基础交易策略实现
4. 策略回测与评估
5. 策略优化
6. 风险管理 
