"""
回测结果可视化工具

专门用于可视化回测结果的工具类，提供：
- 详细的性能分析图表
- 风险指标可视化
- 交易分析图表
- 收益归因分析
- 基准对比分析
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union
import warnings

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    warnings.warn("plotly not available. PerformanceVisualizer will not work.")

from ..backtest.result import BacktestResult


class PerformanceVisualizer:
    """
    回测结果可视化工具
    
    提供专业的回测结果可视化功能，包括性能分析、
    风险评估、交易统计等多个维度的图表。
    """
    
    def __init__(self, theme: str = "plotly_white"):
        """
        初始化性能可视化工具
        
        Args:
            theme: 图表主题
        """
        if not PLOTLY_AVAILABLE:
            raise ImportError("plotly is required for PerformanceVisualizer")
        
        self.theme = theme
        self.colors = {
            'profit': '#00CC96',
            'loss': '#FF6692',
            'neutral': '#636EFA',
            'benchmark': '#FFA15A',
            'warning': '#FECB52',
            'info': '#19D3F3'
        }
    
    def plot_equity_curve_with_stats(self, result: BacktestResult,
                                   benchmark: Optional[pd.Series] = None) -> go.Figure:
        """
        绘制带统计信息的资金曲线
        
        Args:
            result: 回测结果
            benchmark: 基准收益序列
            
        Returns:
            Plotly图表对象
        """
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('资金曲线', '滚动夏普比率', '滚动波动率', '月度收益'),
            specs=[[{"colspan": 2}, None],
                   [{"type": "scatter"}, {"type": "bar"}]],
            vertical_spacing=0.1,
            row_heights=[0.6, 0.4]
        )
        
        # 1. 资金曲线
        equity = result.equity_curve
        fig.add_trace(go.Scatter(
            x=equity.index,
            y=equity.values,
            mode='lines',
            name=result.strategy_name,
            line=dict(color=self.colors['neutral'], width=2)
        ), row=1, col=1)
        
        if benchmark is not None:
            fig.add_trace(go.Scatter(
                x=benchmark.index,
                y=benchmark.values,
                mode='lines',
                name='基准',
                line=dict(color=self.colors['benchmark'], width=1, dash='dash')
            ), row=1, col=1)
        
        # 2. 滚动夏普比率
        returns = equity.pct_change().dropna()
        rolling_sharpe = returns.rolling(252).mean() / returns.rolling(252).std() * np.sqrt(252)
        
        fig.add_trace(go.Scatter(
            x=rolling_sharpe.index,
            y=rolling_sharpe.values,
            mode='lines',
            name='滚动夏普比率',
            line=dict(color=self.colors['info']),
            showlegend=False
        ), row=2, col=1)
        
        # 添加夏普比率参考线
        fig.add_hline(y=1.0, line_dash="dash", line_color="green", 
                     annotation_text="优秀", row=2, col=1)
        fig.add_hline(y=0.5, line_dash="dash", line_color="orange", 
                     annotation_text="一般", row=2, col=1)
        
        # 3. 月度收益
        monthly_returns = equity.resample('M').last().pct_change().dropna() * 100
        colors = [self.colors['profit'] if x > 0 else self.colors['loss'] for x in monthly_returns]
        
        fig.add_trace(go.Bar(
            x=monthly_returns.index,
            y=monthly_returns.values,
            name='月度收益',
            marker_color=colors,
            showlegend=False
        ), row=2, col=2)
        
        fig.update_layout(
            title=f'{result.strategy_name} - 性能分析',
            template=self.theme,
            height=700
        )
        
        fig.update_yaxes(title_text="资金", row=1, col=1)
        fig.update_yaxes(title_text="夏普比率", row=2, col=1)
        fig.update_yaxes(title_text="收益率 (%)", row=2, col=2)
        
        return fig
    
    def plot_risk_analysis(self, result: BacktestResult) -> go.Figure:
        """
        绘制风险分析图表
        
        Args:
            result: 回测结果
            
        Returns:
            Plotly图表对象
        """
        equity = result.equity_curve
        returns = equity.pct_change().dropna()
        
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('回撤分析', 'VaR分析', '收益率分布', '滚动波动率'),
            vertical_spacing=0.15
        )
        
        # 1. 回撤分析
        peak = equity.expanding().max()
        drawdown = (equity - peak) / peak * 100
        
        fig.add_trace(go.Scatter(
            x=drawdown.index,
            y=drawdown.values,
            mode='lines',
            fill='tonexty',
            name='回撤',
            line=dict(color=self.colors['loss']),
            showlegend=False
        ), row=1, col=1)
        
        # 标记最大回撤
        max_dd_idx = drawdown.idxmin()
        max_dd_value = drawdown.min()
        fig.add_trace(go.Scatter(
            x=[max_dd_idx],
            y=[max_dd_value],
            mode='markers',
            name=f'最大回撤 ({max_dd_value:.2f}%)',
            marker=dict(color='red', size=10, symbol='x'),
            showlegend=False
        ), row=1, col=1)
        
        # 2. VaR分析
        var_levels = [0.01, 0.05, 0.1]
        var_values = [np.percentile(returns * 100, level * 100) for level in var_levels]
        
        fig.add_trace(go.Bar(
            x=[f'{int(level*100)}% VaR' for level in var_levels],
            y=var_values,
            name='VaR',
            marker_color=self.colors['warning'],
            showlegend=False
        ), row=1, col=2)
        
        # 3. 收益率分布
        fig.add_trace(go.Histogram(
            x=returns * 100,
            nbinsx=50,
            name='收益率分布',
            marker_color=self.colors['neutral'],
            opacity=0.7,
            showlegend=False
        ), row=2, col=1)
        
        # 添加正态分布拟合
        x_range = np.linspace(returns.min(), returns.max(), 100) * 100
        normal_dist = np.exp(-0.5 * ((x_range - returns.mean() * 100) / (returns.std() * 100)) ** 2)
        normal_dist = normal_dist / normal_dist.max() * len(returns) / 20
        
        fig.add_trace(go.Scatter(
            x=x_range,
            y=normal_dist,
            mode='lines',
            name='正态分布',
            line=dict(color='red', dash='dash'),
            showlegend=False
        ), row=2, col=1)
        
        # 4. 滚动波动率
        rolling_vol = returns.rolling(30).std() * np.sqrt(252) * 100
        
        fig.add_trace(go.Scatter(
            x=rolling_vol.index,
            y=rolling_vol.values,
            mode='lines',
            name='30日滚动波动率',
            line=dict(color=self.colors['info']),
            showlegend=False
        ), row=2, col=2)
        
        fig.update_layout(
            title='风险分析',
            template=self.theme,
            height=600
        )
        
        # 更新坐标轴标题
        fig.update_yaxes(title_text="回撤 (%)", row=1, col=1)
        fig.update_yaxes(title_text="VaR (%)", row=1, col=2)
        fig.update_yaxes(title_text="频次", row=2, col=1)
        fig.update_yaxes(title_text="波动率 (%)", row=2, col=2)
        fig.update_xaxes(title_text="收益率 (%)", row=2, col=1)
        
        return fig
    
    def plot_trade_analysis(self, result: BacktestResult) -> go.Figure:
        """
        绘制交易分析图表
        
        Args:
            result: 回测结果
            
        Returns:
            Plotly图表对象
        """
        if not hasattr(result, 'trades') or len(result.trades) == 0:
            # 如果没有交易数据，创建一个简单的提示图
            fig = go.Figure()
            fig.add_annotation(
                text="暂无交易数据",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=20)
            )
            fig.update_layout(title="交易分析", template=self.theme)
            return fig
        
        trades = result.trades
        
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('交易盈亏分布', '持仓时间分布', '月度交易次数', '累计交易盈亏'),
            vertical_spacing=0.15
        )
        
        # 1. 交易盈亏分布
        if 'pnl' in trades.columns:
            pnl = trades['pnl']
            colors = [self.colors['profit'] if x > 0 else self.colors['loss'] for x in pnl]
            
            fig.add_trace(go.Bar(
                x=range(len(pnl)),
                y=pnl,
                name='交易盈亏',
                marker_color=colors,
                showlegend=False
            ), row=1, col=1)
        
        # 2. 持仓时间分布
        if 'holding_days' in trades.columns:
            holding_days = trades['holding_days']
            fig.add_trace(go.Histogram(
                x=holding_days,
                nbinsx=20,
                name='持仓时间',
                marker_color=self.colors['neutral'],
                showlegend=False
            ), row=1, col=2)
        
        # 3. 月度交易次数
        monthly_trades = trades.resample('M').size()
        fig.add_trace(go.Bar(
            x=monthly_trades.index,
            y=monthly_trades.values,
            name='月度交易次数',
            marker_color=self.colors['info'],
            showlegend=False
        ), row=2, col=1)
        
        # 4. 累计交易盈亏
        if 'pnl' in trades.columns:
            cumulative_pnl = trades['pnl'].cumsum()
            fig.add_trace(go.Scatter(
                x=cumulative_pnl.index,
                y=cumulative_pnl.values,
                mode='lines',
                name='累计盈亏',
                line=dict(color=self.colors['neutral']),
                showlegend=False
            ), row=2, col=2)
        
        fig.update_layout(
            title='交易分析',
            template=self.theme,
            height=600
        )
        
        return fig
    
    def plot_benchmark_comparison(self, result: BacktestResult,
                                benchmark: pd.Series,
                                benchmark_name: str = "基准") -> go.Figure:
        """
        绘制基准对比分析
        
        Args:
            result: 回测结果
            benchmark: 基准收益序列
            benchmark_name: 基准名称
            
        Returns:
            Plotly图表对象
        """
        strategy_returns = result.equity_curve.pct_change().dropna()
        benchmark_returns = benchmark.pct_change().dropna()
        
        # 对齐数据
        common_index = strategy_returns.index.intersection(benchmark_returns.index)
        strategy_returns = strategy_returns.loc[common_index]
        benchmark_returns = benchmark_returns.loc[common_index]
        
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('累计收益对比', '收益率散点图', '滚动相关性', '超额收益'),
            vertical_spacing=0.15
        )
        
        # 1. 累计收益对比
        strategy_cumret = (1 + strategy_returns).cumprod() - 1
        benchmark_cumret = (1 + benchmark_returns).cumprod() - 1
        
        fig.add_trace(go.Scatter(
            x=strategy_cumret.index,
            y=strategy_cumret.values * 100,
            mode='lines',
            name=result.strategy_name,
            line=dict(color=self.colors['neutral'])
        ), row=1, col=1)
        
        fig.add_trace(go.Scatter(
            x=benchmark_cumret.index,
            y=benchmark_cumret.values * 100,
            mode='lines',
            name=benchmark_name,
            line=dict(color=self.colors['benchmark'])
        ), row=1, col=1)
        
        # 2. 收益率散点图
        fig.add_trace(go.Scatter(
            x=benchmark_returns * 100,
            y=strategy_returns * 100,
            mode='markers',
            name='收益率对比',
            marker=dict(color=self.colors['neutral'], opacity=0.6),
            showlegend=False
        ), row=1, col=2)
        
        # 添加45度线
        min_ret = min(benchmark_returns.min(), strategy_returns.min()) * 100
        max_ret = max(benchmark_returns.max(), strategy_returns.max()) * 100
        fig.add_trace(go.Scatter(
            x=[min_ret, max_ret],
            y=[min_ret, max_ret],
            mode='lines',
            name='45度线',
            line=dict(color='red', dash='dash'),
            showlegend=False
        ), row=1, col=2)
        
        # 3. 滚动相关性
        rolling_corr = strategy_returns.rolling(60).corr(benchmark_returns)
        fig.add_trace(go.Scatter(
            x=rolling_corr.index,
            y=rolling_corr.values,
            mode='lines',
            name='60日滚动相关性',
            line=dict(color=self.colors['info']),
            showlegend=False
        ), row=2, col=1)
        
        # 4. 超额收益
        excess_returns = strategy_returns - benchmark_returns
        cumulative_excess = excess_returns.cumsum() * 100
        
        fig.add_trace(go.Scatter(
            x=cumulative_excess.index,
            y=cumulative_excess.values,
            mode='lines',
            name='累计超额收益',
            line=dict(color=self.colors['profit']),
            showlegend=False
        ), row=2, col=2)
        
        fig.update_layout(
            title=f'{result.strategy_name} vs {benchmark_name}',
            template=self.theme,
            height=600
        )
        
        # 更新坐标轴标题
        fig.update_yaxes(title_text="累计收益 (%)", row=1, col=1)
        fig.update_yaxes(title_text="策略收益 (%)", row=1, col=2)
        fig.update_xaxes(title_text="基准收益 (%)", row=1, col=2)
        fig.update_yaxes(title_text="相关性", row=2, col=1)
        fig.update_yaxes(title_text="超额收益 (%)", row=2, col=2)
        
        return fig
    
    def create_performance_report(self, result: BacktestResult,
                                benchmark: Optional[pd.Series] = None) -> Dict[str, go.Figure]:
        """
        创建完整的性能报告
        
        Args:
            result: 回测结果
            benchmark: 基准收益序列
            
        Returns:
            图表字典
        """
        report = {}
        
        # 1. 性能分析
        report['performance'] = self.plot_equity_curve_with_stats(result, benchmark)
        
        # 2. 风险分析
        report['risk'] = self.plot_risk_analysis(result)
        
        # 3. 交易分析
        report['trades'] = self.plot_trade_analysis(result)
        
        # 4. 基准对比（如果有基准）
        if benchmark is not None:
            report['benchmark'] = self.plot_benchmark_comparison(result, benchmark)
        
        return report
