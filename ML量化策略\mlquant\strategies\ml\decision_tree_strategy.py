"""
基于决策树的交易策略

该策略使用决策树算法来预测价格走势并生成交易信号。
决策树具有良好的可解释性，能够提供特征重要性分析。

主要特点：
- 高可解释性，可以理解决策逻辑
- 支持特征重要性分析
- 对异常值相对鲁棒
- 训练速度快
- 容易过拟合，需要适当的剪枝
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Any, Optional, List
import warnings

try:
    from sklearn.tree import DecisionTreeClassifier
    from sklearn.model_selection import train_test_split
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import accuracy_score, classification_report
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    warnings.warn("scikit-learn not available. DecisionTreeStrategy will not work.")

from ..base import BaseStrategy


class DecisionTreeStrategy(BaseStrategy):
    """
    基于决策树的交易策略
    
    该策略使用决策树分类器来预测价格走势（上涨/下跌），
    并根据预测结果生成交易信号。
    """
    
    def __init__(self, name: str = "DecisionTreeStrategy"):
        """
        初始化决策树策略
        
        Args:
            name: 策略名称
        """
        super().__init__(name)
        self.model = None
        self.scaler = StandardScaler()
        self.feature_columns = []
        self.feature_importance = None
        self.model_trained = False
        
        if not SKLEARN_AVAILABLE:
            raise ImportError("scikit-learn is required for DecisionTreeStrategy")
    
    def prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        准备机器学习特征
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            包含特征的DataFrame
        """
        df = data.copy()
        
        # 价格特征
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        df['price_change'] = df['close'] - df['open']
        df['high_low_ratio'] = df['high'] / df['low']
        df['volume_change'] = df['volume'].pct_change()
        
        # 技术指标特征
        # 移动平均线
        for window in [5, 10, 20]:
            df[f'sma_{window}'] = df['close'].rolling(window).mean()
            df[f'price_sma_{window}_ratio'] = df['close'] / df[f'sma_{window}']
        
        # RSI
        delta = df['close'].diff()
        up = delta.clip(lower=0)
        down = -1 * delta.clip(upper=0)
        ema_up = up.ewm(com=13, adjust=False).mean()
        ema_down = down.ewm(com=13, adjust=False).mean()
        rs = ema_up / ema_down
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 布林带
        df['bb_middle'] = df['close'].rolling(20).mean()
        bb_std = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # MACD
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema_12 - ema_26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # 波动率特征
        df['volatility'] = df['returns'].rolling(20).std()
        df['volume_sma'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        # 滞后特征
        for lag in [1, 2, 3, 5]:
            df[f'returns_lag_{lag}'] = df['returns'].shift(lag)
            df[f'volume_change_lag_{lag}'] = df['volume_change'].shift(lag)
        
        return df
    
    def create_labels(self, data: pd.DataFrame, forward_periods: int = 1) -> pd.Series:
        """
        创建预测标签
        
        Args:
            data: 价格数据
            forward_periods: 前瞻期数
            
        Returns:
            标签序列 (1: 上涨, 0: 下跌)
        """
        future_returns = data['close'].shift(-forward_periods) / data['close'] - 1
        labels = (future_returns > 0).astype(int)
        return labels
    
    def train_model(self, data: pd.DataFrame, **params) -> None:
        """
        训练决策树模型
        
        Args:
            data: 训练数据
            **params: 模型参数
        """
        # 准备特征
        features_df = self.prepare_features(data)
        
        # 创建标签
        labels = self.create_labels(data, params.get('forward_periods', 1))
        
        # 选择特征列
        feature_columns = [
            'returns', 'log_returns', 'price_change', 'high_low_ratio', 'volume_change',
            'price_sma_5_ratio', 'price_sma_10_ratio', 'price_sma_20_ratio',
            'rsi', 'bb_position', 'macd', 'macd_histogram', 'volatility', 'volume_ratio'
        ]
        
        # 添加滞后特征
        for lag in [1, 2, 3, 5]:
            feature_columns.extend([f'returns_lag_{lag}', f'volume_change_lag_{lag}'])
        
        # 过滤存在的特征列
        self.feature_columns = [col for col in feature_columns if col in features_df.columns]
        
        # 准备训练数据
        X = features_df[self.feature_columns].dropna()
        y = labels.loc[X.index]
        
        if len(X) == 0:
            raise ValueError("No valid training data after feature preparation")
        
        # 分割训练和验证集
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 标准化特征
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        
        # 训练决策树模型
        self.model = DecisionTreeClassifier(
            max_depth=params.get('max_depth', 10),
            min_samples_split=params.get('min_samples_split', 20),
            min_samples_leaf=params.get('min_samples_leaf', 10),
            max_features=params.get('max_features', 'sqrt'),
            random_state=42
        )
        
        self.model.fit(X_train_scaled, y_train)
        
        # 验证模型
        y_pred = self.model.predict(X_val_scaled)
        accuracy = accuracy_score(y_val, y_pred)
        
        # 保存特征重要性
        self.feature_importance = pd.DataFrame({
            'feature': self.feature_columns,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        self.model_trained = True
        
        print(f"模型训练完成 - 验证集准确率: {accuracy:.4f}")
        print("前5个重要特征:")
        print(self.feature_importance.head())
    
    def generate_signals(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        生成交易信号
        
        Args:
            data: 价格数据
            **params: 策略参数
            
        Returns:
            交易信号序列 (1: 买入, -1: 卖出, 0: 持有)
        """
        if not self.model_trained:
            # 如果模型未训练，先训练模型
            self.train_model(data, **params)
        
        # 准备特征
        features_df = self.prepare_features(data)
        
        # 提取特征
        X = features_df[self.feature_columns].fillna(method='ffill').fillna(0)
        
        # 标准化特征
        X_scaled = self.scaler.transform(X)
        
        # 预测
        predictions = self.model.predict(X_scaled)
        probabilities = self.model.predict_proba(X_scaled)[:, 1]  # 上涨概率
        
        # 生成信号
        signals = pd.Series(0, index=data.index)
        
        # 设置信号阈值
        buy_threshold = params.get('buy_threshold', 0.6)
        sell_threshold = params.get('sell_threshold', 0.4)
        
        # 基于概率生成信号
        signals[probabilities > buy_threshold] = 1   # 买入
        signals[probabilities < sell_threshold] = -1  # 卖出
        
        return signals
    
    def get_param_ranges(self) -> Dict[str, Tuple[float, float]]:
        """
        获取参数搜索范围
        
        Returns:
            参数名称到范围的映射
        """
        return {
            'max_depth': (3, 20),
            'min_samples_split': (10, 50),
            'min_samples_leaf': (5, 30),
            'buy_threshold': (0.55, 0.8),
            'sell_threshold': (0.2, 0.45),
            'forward_periods': (1, 5)
        }
    
    def get_feature_importance(self) -> Optional[pd.DataFrame]:
        """
        获取特征重要性
        
        Returns:
            特征重要性DataFrame
        """
        return self.feature_importance
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns:
            策略信息字典
        """
        info = super().get_strategy_info()
        info.update({
            'algorithm': 'Decision Tree',
            'model_trained': self.model_trained,
            'n_features': len(self.feature_columns) if self.feature_columns else 0,
            'interpretable': True,
            'supports_feature_importance': True
        })
        return info
