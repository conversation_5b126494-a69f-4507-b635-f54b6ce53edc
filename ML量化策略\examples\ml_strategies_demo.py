"""
高级ML策略和可视化系统演示

本脚本演示了新实现的高级机器学习策略和可视化系统的功能，包括：
1. 决策树策略
2. 随机森林策略
3. XGBoost策略
4. LSTM策略
5. 综合可视化仪表板
6. 交互式图表
7. 性能分析可视化
8. 参数优化可视化
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 导入核心模块
from mlquant.data import RandomDataGenerator, DataLoader, TechnicalIndicators
from mlquant.backtest import BacktestEngine
from mlquant.performance import PerformanceAnalyzer

# 导入ML策略
try:
    from mlquant.strategies.ml import (
        DecisionTreeStrategy, 
        RandomForestStrategy, 
        XGBoostStrategy, 
        LSTMStrategy,
        get_available_strategies
    )
    ML_STRATEGIES_AVAILABLE = True
except ImportError as e:
    print(f"ML策略模块导入失败: {e}")
    ML_STRATEGIES_AVAILABLE = False

# 导入可视化模块
try:
    from mlquant.visualization import (
        StrategyDashboard,
        InteractiveCharts,
        PerformanceVisualizer,
        ParameterOptimizationVisualizer,
        check_dependencies
    )
    VISUALIZATION_AVAILABLE = True
except ImportError as e:
    print(f"可视化模块导入失败: {e}")
    VISUALIZATION_AVAILABLE = False


def generate_sample_data(days: int = 1000) -> pd.DataFrame:
    """生成示例数据"""
    print("生成示例数据...")
    generator = RandomDataGenerator()
    data = generator.generate(
        days=days,
        initial_price=100,
        volatility=0.02,
        trend=0.0001
    )
    
    # 添加技术指标
    indicators = TechnicalIndicators.calculate_all_indicators(data)
    print(f"生成了 {len(data)} 天的数据，包含 {len(indicators.columns)} 个指标")
    
    return indicators


def demo_ml_strategies():
    """演示ML策略"""
    if not ML_STRATEGIES_AVAILABLE:
        print("❌ ML策略模块不可用，跳过演示")
        return None
    
    print("\n" + "="*50)
    print("🤖 高级ML策略演示")
    print("="*50)
    
    # 检查可用策略
    available_strategies = get_available_strategies()
    print(f"可用的ML策略: {list(available_strategies.keys())}")
    
    # 生成数据
    data = generate_sample_data(500)  # 使用较少数据以加快演示速度
    
    results = {}
    
    # 1. 决策树策略
    if 'decision_tree' in available_strategies:
        print("\n🌳 测试决策树策略...")
        try:
            dt_strategy = DecisionTreeStrategy()
            dt_signals = dt_strategy.generate_signals(data, max_depth=8, buy_threshold=0.6)
            
            # 回测
            engine = BacktestEngine()
            dt_result = engine.run(data, dt_signals, "DecisionTree")
            results['DecisionTree'] = dt_result
            
            print(f"  ✅ 决策树策略完成")
            print(f"     总收益率: {dt_result.total_return:.2%}")
            print(f"     夏普比率: {dt_result.sharpe_ratio:.2f}")
            
            # 显示特征重要性
            importance = dt_strategy.get_feature_importance()
            if importance is not None:
                print(f"     前3个重要特征: {importance.head(3)['feature'].tolist()}")
                
        except Exception as e:
            print(f"  ❌ 决策树策略失败: {e}")
    
    # 2. 随机森林策略
    if 'random_forest' in available_strategies:
        print("\n🌲 测试随机森林策略...")
        try:
            rf_strategy = RandomForestStrategy()
            rf_signals = rf_strategy.generate_signals(
                data, 
                n_estimators=50,  # 减少树的数量以加快速度
                max_depth=10,
                buy_threshold=0.65
            )
            
            # 回测
            rf_result = engine.run(data, rf_signals, "RandomForest")
            results['RandomForest'] = rf_result
            
            print(f"  ✅ 随机森林策略完成")
            print(f"     总收益率: {rf_result.total_return:.2%}")
            print(f"     夏普比率: {rf_result.sharpe_ratio:.2f}")
            
            # 显示模型统计
            model_stats = rf_strategy.get_model_stats()
            if model_stats:
                print(f"     OOB分数: {model_stats.get('oob_score', 'N/A'):.3f}")
                
        except Exception as e:
            print(f"  ❌ 随机森林策略失败: {e}")
    
    # 3. XGBoost策略
    if 'xgboost' in available_strategies:
        print("\n🚀 测试XGBoost策略...")
        try:
            xgb_strategy = XGBoostStrategy()
            xgb_signals = xgb_strategy.generate_signals(
                data,
                max_depth=6,
                learning_rate=0.1,
                n_estimators=50,  # 减少迭代次数
                buy_threshold=0.6
            )
            
            # 回测
            xgb_result = engine.run(data, xgb_signals, "XGBoost")
            results['XGBoost'] = xgb_result
            
            print(f"  ✅ XGBoost策略完成")
            print(f"     总收益率: {xgb_result.total_return:.2%}")
            print(f"     夏普比率: {xgb_result.sharpe_ratio:.2f}")
            
        except Exception as e:
            print(f"  ❌ XGBoost策略失败: {e}")
    
    # 4. LSTM策略（可能需要较长时间）
    if 'lstm' in available_strategies:
        print("\n🧠 测试LSTM策略...")
        try:
            lstm_strategy = LSTMStrategy()
            lstm_signals = lstm_strategy.generate_signals(
                data,
                sequence_length=30,  # 减少序列长度
                epochs=20,  # 减少训练轮数
                batch_size=32,
                buy_threshold=0.6
            )
            
            # 回测
            lstm_result = engine.run(data, lstm_signals, "LSTM")
            results['LSTM'] = lstm_result
            
            print(f"  ✅ LSTM策略完成")
            print(f"     总收益率: {lstm_result.total_return:.2%}")
            print(f"     夏普比率: {lstm_result.sharpe_ratio:.2f}")
            
        except Exception as e:
            print(f"  ❌ LSTM策略失败: {e}")
    
    return results, data


def demo_visualization(results, data):
    """演示可视化功能"""
    if not VISUALIZATION_AVAILABLE:
        print("❌ 可视化模块不可用，跳过演示")
        return
    
    print("\n" + "="*50)
    print("📊 可视化系统演示")
    print("="*50)
    
    # 检查依赖
    print("检查可视化依赖:")
    check_dependencies()
    
    if not results:
        print("❌ 没有可用的回测结果，跳过可视化演示")
        return
    
    try:
        # 1. 策略仪表板
        print("\n📈 创建策略仪表板...")
        dashboard = StrategyDashboard()
        
        # 选择一个结果进行演示
        result_name, result = next(iter(results.items()))
        
        # 创建资金曲线图
        equity_fig = dashboard.create_equity_curve_chart(result)
        print(f"  ✅ 资金曲线图已创建")
        
        # 创建回撤分析图
        drawdown_fig = dashboard.create_drawdown_chart(result)
        print(f"  ✅ 回撤分析图已创建")
        
        # 创建收益分布图
        returns_fig = dashboard.create_returns_distribution_chart(result)
        print(f"  ✅ 收益分布图已创建")
        
        # 创建性能指标表
        metrics_fig = dashboard.create_performance_metrics_table(result)
        print(f"  ✅ 性能指标表已创建")
        
        # 2. 交互式图表
        print("\n🎯 创建交互式图表...")
        charts = InteractiveCharts()
        
        # 创建K线图
        candlestick_fig = charts.create_candlestick_chart(
            data.tail(100),  # 只显示最后100天
            title=f"{result_name}策略 - K线图"
        )
        print(f"  ✅ K线图已创建")
        
        # 创建策略对比图
        if len(results) > 1:
            equity_curves = {name: res.equity_curve for name, res in results.items()}
            comparison_fig = charts.create_strategy_comparison_chart(equity_curves)
            print(f"  ✅ 策略对比图已创建")
        
        # 3. 性能可视化
        print("\n📊 创建性能分析图表...")
        perf_viz = PerformanceVisualizer()
        
        # 性能分析图
        perf_fig = perf_viz.plot_equity_curve_with_stats(result)
        print(f"  ✅ 性能分析图已创建")
        
        # 风险分析图
        risk_fig = perf_viz.plot_risk_analysis(result)
        print(f"  ✅ 风险分析图已创建")
        
        # 4. 参数优化可视化
        print("\n🔧 创建参数优化可视化...")
        opt_viz = ParameterOptimizationVisualizer()
        
        # 模拟优化历史数据
        mock_history = []
        for i in range(20):
            fitness_values = np.random.normal(0.5 + i*0.02, 0.1, 10)
            mock_history.append({'fitness': fitness_values.tolist()})
        
        opt_history_fig = opt_viz.plot_optimization_history(mock_history)
        print(f"  ✅ 优化历史图已创建")
        
        # 模拟参数敏感性数据
        mock_sensitivity = {
            'max_depth': {str(i): 0.5 + 0.1*np.sin(i/3) for i in range(3, 15)},
            'n_estimators': {str(i): 0.6 + 0.05*np.cos(i/10) for i in range(50, 200, 10)}
        }
        
        sensitivity_fig = opt_viz.plot_parameter_sensitivity(mock_sensitivity)
        print(f"  ✅ 参数敏感性图已创建")
        
        print("\n✅ 所有可视化图表创建完成！")
        print("💡 提示: 在实际使用中，可以调用 fig.show() 来显示图表")
        print("💡 提示: 可以使用 fig.write_html('filename.html') 保存为HTML文件")
        
    except Exception as e:
        print(f"❌ 可视化演示失败: {e}")


def main():
    """主演示函数"""
    print("🚀 ML量化策略和可视化系统演示")
    print("="*60)
    
    # 演示ML策略
    results, data = demo_ml_strategies()
    
    # 演示可视化
    if results and data is not None:
        demo_visualization(results, data)
    
    # 总结
    print("\n" + "="*60)
    print("📋 演示总结")
    print("="*60)
    
    if results:
        print(f"✅ 成功测试了 {len(results)} 个ML策略:")
        for name, result in results.items():
            print(f"   • {name}: 收益率 {result.total_return:.2%}, 夏普比率 {result.sharpe_ratio:.2f}")
    else:
        print("❌ 没有成功的ML策略测试")
    
    if VISUALIZATION_AVAILABLE:
        print("✅ 可视化系统功能正常")
    else:
        print("❌ 可视化系统不可用")
    
    print("\n🎉 演示完成！")
    print("💡 提示: 要使用完整功能，请确保安装所有依赖:")
    print("   pip install scikit-learn xgboost tensorflow plotly")


if __name__ == "__main__":
    main()
