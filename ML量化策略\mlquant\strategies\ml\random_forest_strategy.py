"""
基于随机森林的交易策略

随机森林是一种集成学习方法，通过组合多个决策树来提高预测准确性和减少过拟合。
该策略特别适合处理复杂的非线性关系和特征交互。

主要特点：
- 集成学习，减少过拟合风险
- 对噪声和异常值鲁棒
- 提供特征重要性分析
- 支持并行训练
- 相对较好的泛化能力
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Any, Optional, List
import warnings

try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.model_selection import train_test_split, cross_val_score
    from sklearn.preprocessing import StandardScaler
    from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    warnings.warn("scikit-learn not available. RandomForestStrategy will not work.")

from ..base import BaseStrategy


class RandomForestStrategy(BaseStrategy):
    """
    基于随机森林的交易策略
    
    使用随机森林集成学习算法来预测价格走势，
    通过组合多个决策树来提高预测准确性和稳定性。
    """
    
    def __init__(self, name: str = "RandomForestStrategy"):
        """
        初始化随机森林策略
        
        Args:
            name: 策略名称
        """
        super().__init__(name)
        self.model = None
        self.scaler = StandardScaler()
        self.feature_columns = []
        self.feature_importance = None
        self.model_trained = False
        self.cross_val_scores = None
        
        if not SKLEARN_AVAILABLE:
            raise ImportError("scikit-learn is required for RandomForestStrategy")
    
    def prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        准备机器学习特征（增强版特征工程）
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            包含特征的DataFrame
        """
        df = data.copy()
        
        # 基础价格特征
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        df['price_change'] = df['close'] - df['open']
        df['high_low_ratio'] = df['high'] / df['low']
        df['open_close_ratio'] = df['open'] / df['close']
        df['volume_change'] = df['volume'].pct_change()
        
        # 多时间框架移动平均线
        for window in [5, 10, 20, 50]:
            df[f'sma_{window}'] = df['close'].rolling(window).mean()
            df[f'ema_{window}'] = df['close'].ewm(span=window).mean()
            df[f'price_sma_{window}_ratio'] = df['close'] / df[f'sma_{window}']
            df[f'sma_{window}_slope'] = df[f'sma_{window}'].diff(5)
        
        # 移动平均线交叉特征
        df['sma_5_20_cross'] = (df['sma_5'] > df['sma_20']).astype(int)
        df['sma_10_50_cross'] = (df['sma_10'] > df['sma_50']).astype(int)
        
        # RSI及其变种
        delta = df['close'].diff()
        up = delta.clip(lower=0)
        down = -1 * delta.clip(upper=0)
        ema_up = up.ewm(com=13, adjust=False).mean()
        ema_down = down.ewm(com=13, adjust=False).mean()
        rs = ema_up / ema_down
        df['rsi'] = 100 - (100 / (1 + rs))
        df['rsi_oversold'] = (df['rsi'] < 30).astype(int)
        df['rsi_overbought'] = (df['rsi'] > 70).astype(int)
        
        # 布林带特征
        df['bb_middle'] = df['close'].rolling(20).mean()
        bb_std = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        df['bb_squeeze'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        
        # MACD特征
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema_12 - ema_26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        df['macd_cross'] = (df['macd'] > df['macd_signal']).astype(int)
        
        # 波动率特征
        for window in [10, 20, 50]:
            df[f'volatility_{window}'] = df['returns'].rolling(window).std()
            df[f'volatility_{window}_rank'] = df[f'volatility_{window}'].rolling(100).rank(pct=True)
        
        # 成交量特征
        for window in [10, 20]:
            df[f'volume_sma_{window}'] = df['volume'].rolling(window).mean()
            df[f'volume_ratio_{window}'] = df['volume'] / df[f'volume_sma_{window}']
        
        # 价格位置特征
        for window in [20, 50, 100]:
            df[f'price_rank_{window}'] = df['close'].rolling(window).rank(pct=True)
            df[f'high_rank_{window}'] = df['high'].rolling(window).rank(pct=True)
            df[f'low_rank_{window}'] = df['low'].rolling(window).rank(pct=True)
        
        # 滞后特征
        for lag in [1, 2, 3, 5, 10]:
            df[f'returns_lag_{lag}'] = df['returns'].shift(lag)
            df[f'volume_change_lag_{lag}'] = df['volume_change'].shift(lag)
            df[f'rsi_lag_{lag}'] = df['rsi'].shift(lag)
        
        # 统计特征
        df['returns_mean_20'] = df['returns'].rolling(20).mean()
        df['returns_std_20'] = df['returns'].rolling(20).std()
        df['returns_skew_20'] = df['returns'].rolling(20).skew()
        df['returns_kurt_20'] = df['returns'].rolling(20).kurt()
        
        return df
    
    def create_labels(self, data: pd.DataFrame, forward_periods: int = 1, 
                     threshold: float = 0.001) -> pd.Series:
        """
        创建预测标签（支持阈值过滤）
        
        Args:
            data: 价格数据
            forward_periods: 前瞻期数
            threshold: 最小变动阈值
            
        Returns:
            标签序列 (1: 上涨, 0: 下跌)
        """
        future_returns = data['close'].shift(-forward_periods) / data['close'] - 1
        
        # 使用阈值过滤小幅波动
        labels = pd.Series(0, index=data.index)
        labels[future_returns > threshold] = 1
        labels[future_returns < -threshold] = 0
        
        # 对于小幅波动，可以选择不交易（保持前一个信号）
        small_moves = (future_returns.abs() <= threshold)
        
        return labels
    
    def train_model(self, data: pd.DataFrame, **params) -> None:
        """
        训练随机森林模型
        
        Args:
            data: 训练数据
            **params: 模型参数
        """
        # 准备特征
        features_df = self.prepare_features(data)
        
        # 创建标签
        labels = self.create_labels(
            data, 
            params.get('forward_periods', 1),
            params.get('threshold', 0.001)
        )
        
        # 选择特征列（排除包含NaN的列）
        potential_features = [col for col in features_df.columns 
                            if col not in ['open', 'high', 'low', 'close', 'volume']]
        
        # 检查特征的有效性
        valid_features = []
        for col in potential_features:
            if not features_df[col].isna().all() and features_df[col].var() > 1e-10:
                valid_features.append(col)
        
        self.feature_columns = valid_features[:50]  # 限制特征数量避免过拟合
        
        # 准备训练数据
        X = features_df[self.feature_columns].fillna(method='ffill').fillna(0)
        y = labels.loc[X.index]
        
        # 移除标签为NaN的样本
        valid_idx = ~y.isna()
        X = X[valid_idx]
        y = y[valid_idx]
        
        if len(X) == 0:
            raise ValueError("No valid training data after feature preparation")
        
        # 分割训练和验证集
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 标准化特征（随机森林对此不敏感，但保持一致性）
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        
        # 训练随机森林模型
        self.model = RandomForestClassifier(
            n_estimators=params.get('n_estimators', 100),
            max_depth=params.get('max_depth', 15),
            min_samples_split=params.get('min_samples_split', 10),
            min_samples_leaf=params.get('min_samples_leaf', 5),
            max_features=params.get('max_features', 'sqrt'),
            bootstrap=True,
            oob_score=True,
            random_state=42,
            n_jobs=-1  # 使用所有CPU核心
        )
        
        self.model.fit(X_train_scaled, y_train)
        
        # 交叉验证
        cv_scores = cross_val_score(self.model, X_train_scaled, y_train, cv=5)
        self.cross_val_scores = cv_scores
        
        # 验证模型
        y_pred = self.model.predict(X_val_scaled)
        accuracy = accuracy_score(y_val, y_pred)
        
        # 保存特征重要性
        self.feature_importance = pd.DataFrame({
            'feature': self.feature_columns,
            'importance': self.model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        self.model_trained = True
        
        print(f"模型训练完成:")
        print(f"  验证集准确率: {accuracy:.4f}")
        print(f"  交叉验证平均分: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
        print(f"  OOB分数: {self.model.oob_score_:.4f}")
        print("前5个重要特征:")
        print(self.feature_importance.head())
    
    def generate_signals(self, data: pd.DataFrame, **params) -> pd.Series:
        """
        生成交易信号
        
        Args:
            data: 价格数据
            **params: 策略参数
            
        Returns:
            交易信号序列 (1: 买入, -1: 卖出, 0: 持有)
        """
        if not self.model_trained:
            # 如果模型未训练，先训练模型
            self.train_model(data, **params)
        
        # 准备特征
        features_df = self.prepare_features(data)
        
        # 提取特征
        X = features_df[self.feature_columns].fillna(method='ffill').fillna(0)
        
        # 标准化特征
        X_scaled = self.scaler.transform(X)
        
        # 预测
        predictions = self.model.predict(X_scaled)
        probabilities = self.model.predict_proba(X_scaled)[:, 1]  # 上涨概率
        
        # 生成信号
        signals = pd.Series(0, index=data.index)
        
        # 设置信号阈值（随机森林通常需要更高的置信度）
        buy_threshold = params.get('buy_threshold', 0.65)
        sell_threshold = params.get('sell_threshold', 0.35)
        
        # 基于概率生成信号
        signals[probabilities > buy_threshold] = 1   # 买入
        signals[probabilities < sell_threshold] = -1  # 卖出
        
        return signals
    
    def get_param_ranges(self) -> Dict[str, Tuple[float, float]]:
        """
        获取参数搜索范围
        
        Returns:
            参数名称到范围的映射
        """
        return {
            'n_estimators': (50, 200),
            'max_depth': (5, 25),
            'min_samples_split': (5, 30),
            'min_samples_leaf': (2, 20),
            'buy_threshold': (0.6, 0.8),
            'sell_threshold': (0.2, 0.4),
            'forward_periods': (1, 5),
            'threshold': (0.0005, 0.005)
        }
    
    def get_feature_importance(self) -> Optional[pd.DataFrame]:
        """
        获取特征重要性
        
        Returns:
            特征重要性DataFrame
        """
        return self.feature_importance
    
    def get_model_stats(self) -> Dict[str, Any]:
        """
        获取模型统计信息
        
        Returns:
            模型统计信息字典
        """
        if not self.model_trained:
            return {}
        
        return {
            'n_estimators': self.model.n_estimators,
            'oob_score': self.model.oob_score_,
            'cv_mean': self.cross_val_scores.mean() if self.cross_val_scores is not None else None,
            'cv_std': self.cross_val_scores.std() if self.cross_val_scores is not None else None,
            'n_features': len(self.feature_columns)
        }
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns:
            策略信息字典
        """
        info = super().get_strategy_info()
        info.update({
            'algorithm': 'Random Forest',
            'model_trained': self.model_trained,
            'n_features': len(self.feature_columns) if self.feature_columns else 0,
            'ensemble_method': True,
            'supports_feature_importance': True,
            'overfitting_resistant': True
        })
        
        if self.model_trained:
            info.update(self.get_model_stats())
        
        return info
